﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Models;
using System.Data.SqlClient;

namespace RepasseConvenio.Services
{
  public class LogLoteGlosaService : ServiceBase
  {
    public LogLoteGlosaService(RepasseEntities Contexto)
        : base(Contexto)
    { }
    public LogLoteGlosaService()
       : base()
    { }

    public LogLoteGlosaService(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public LogLoteGlosaService(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public void Create(EnumTipoLogLoteGlosa tipoLog, int idLote, string mensagemLog = "")
    {
      int? IdUsuario = ContextoUsuario.UserLogged != null ? (int?)ContextoUsuario.UserLogged.IdUsuario : null;

      string Query = @"INSERT INTO
                        R_LogLoteGlosa
                        (
	                        LLG_Log,
	                        LLG_IdTipoLog,
	                        LLG_Data,
	                        LLG_IdLoteGlosa,
	                        LLG_IdUsuario
                        )
                        SELECT
	                        @Log [LLG_Log],
	                        TLG.TLG_Id [LLG_IdTipoLog],
	                        GETDATE() [LLG_Data],
	                        @IdLote [LLG_IdLoteGlosa],
	                        @IdUsuario [LLG_IdUsuario]
                        FROM R_TipoLogLoteGlosa TLG
                        WHERE TLG.TLG_Enum = @TipoLog";

      Contexto.Database.ExecuteSqlCommand(Query, new SqlParameter("@Log", mensagemLog), new SqlParameter("@IdLote", idLote)
        , new SqlParameter("@TipoLog", (int)tipoLog), new SqlParameter("@IdUsuario", IdUsuario));
    }
  }
}
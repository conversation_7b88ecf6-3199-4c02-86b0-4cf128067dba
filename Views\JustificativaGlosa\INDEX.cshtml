﻿@using RepasseConvenio.Infrastructure
@using RepasseConvenio.Models
@using PagedList
@using PagedList.Mvc;
@model IPagedList<JustificativaGlosaModel>

@{
  ViewBag.Title = "Justificativa de glosa";
}


@Styles.Render("~/Views/JustificativaGlosa/JustificativaGlosa.css")
@Scripts.Render("~/Views/JustificativaGlosa/JustificativaGlosa.js?ia=b")



<div class="row">
  <section class="col-lg-12 connectedSortable ui-sortable">

    <div class="card">
      <div class="card-header ui-sortable-handle">
        <div class="row" style="justify-content:space-between;">
          <h3 class="card-title">
            <i class="fas fa-book mr-1"></i>
            Justificativa de glosa
          </h3>

          <div class="card-tools">
            @if (ContextoUsuario.UserLogged.IsInPermissao(Guid.Parse("A8DC5D34-487E-485D-B37D-3A13E4CC1FFD")))
            {
              <button id="CreateGRD" onclick="window.location.href='@Url.Action("Create", "JustificativaGlosa")'" style="padding: 0.1rem 0.1rem;" type="button" class="btn btn-sm btn-block btn-outline-primary">
                <i class="fas fa-plus"></i> Novo
              </button>
            }
          </div>

        </div>
      </div>

      <div class="card-body">
        <div class="tab-content p-0">
          <div class="box-body">

            <div class="card-acoes col-4">
              @if (ContextoUsuario.UserLogged.IsInPermissao(Guid.Parse("3B7644BB-402F-45E7-941C-182DE1B1C9BB")))
              {
                <a class="btn btn-block btn-outline-primary disabled" id="JustificativaEdit"> Editar </a>
              }
              @if (ContextoUsuario.UserLogged.IsInPermissao(Guid.Parse("8A468F0B-E533-4560-BF0C-1B657F31ED55")))
              {

                <a class="btn btn-block btn-outline-primary disabled" id="JustificativaDelete"> Excluir </a>
              }

            </div>
            <div class="row">
              <div class="col-md-12 table-responsive p-0">
                <table id="TableTela" class="table-striped table table-sm table-hover  text-nowrap">
                  <thead>
                    <tr>

                      <th>
                        Código
                      </th>
                      <th>
                        Descrição
                      </th>
                      <th>

                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    @foreach (JustificativaGlosaModel item in Model)
                    {
                      <tr class="TrSelecionavel" data-codigojustificativa="@item.Codigo">

                        <td>
                          @item.CodigoJustificativa
                        </td>
                        <td>
                          @item.Descricao
                        </td>



                        @*<td class="pull-rigth">
                            <a href="@Url.Action("Edit", "JustificativaGlosa", new { id = item.Codigo  })" class="btn btn-sm TIcones btn-outline-warning" title="Atualizar">
                              <i class="fa fa-edit"></i>
                            </a>

                            <a onclick="confirmaDelete('@String.Format("Deseja excluir a justificativa de glosa")', '@Url.Action("Delete", "JustificativaGlosa", new { id = item.Codigo })')" class="btn btn-sm TIcones btn-outline-danger" title="Excluir">
                              <i class="fa fa-trash"></i>
                            </a>

                          </td>*@

                      </tr>
                    }
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="box-footer with-border" style="padding: 0 10pt;">
        <div class="row">
          <div class="col-md-8 ">
            @Html.PagedListPager(Model, page => Url.Action("Index", new { page }))
          </div>
        </div>
      </div>
    </div>
  </section>
</div>
@*Html.Partial("_ModalNovaGRD", new ModalNovaGRD())*@
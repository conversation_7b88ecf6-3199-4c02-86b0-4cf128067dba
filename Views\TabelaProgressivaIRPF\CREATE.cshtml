﻿@using PagedList.Mvc
@using RepasseConvenio.Models
@using RepasseConvenio.Infrastructure
@using RepasseConvenio.Infrastructure.Controls
@model TabelaProgressivaIRPFModel

@{
  ViewBag.Title = "Nova Tabela IRPF";
  Layout = "~/Views/Shared/_Layout.cshtml";
}

@using (Html.BeginForm("Create", "TabelaProgressivaIRPF", FormMethod.Post))
{

  <div class="row">
    <section class="col-lg-12 connectedSortable ui-sortable">
      <div class="card">
        <div class="card-header ui-sortable-handle">
          <h3 class="card-title">
            <i class="fas fa-chart-pie mr-1"> Tabela IRPF</i>

          </h3>
          <div class="card-tools">
          </div>
        </div><!-- /.card-header -->
        <div class="card-body">
          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                @Html.LabelFor(m => m.Descricao)
                @Html.LibEditorFor(m => m.Descricao, new { @class = "form-control" })
                @Html.ValidationMessageFor(m => m.Descricao, "", new { @class = "text-danger" })
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-group">
                @Html.LabelFor(m => m.DtInicioVigencia)
                @Html.LibEditorFor(m => m.DtInicioVigencia, new { @class = "form-control airDatePickerDate Data" })
                @Html.ValidationMessageFor(m => m.DtInicioVigencia, "", new { @class = "text-danger" })
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-group">
                @Html.LabelFor(m => m.DtFimVigencia)
                @Html.LibEditorFor(m => m.DtFimVigencia, new { @class = "form-control airDatePickerDate Data" })
                @Html.ValidationMessageFor(m => m.DtFimVigencia, "", new { @class = "text-danger" })
              </div>
            </div>
            @*<div class="col-md-2">
                <div class="form-group">
                  @Html.LabelFor(m => m.ValorInicial)
                  @Html.LibEditorFor(m => m.ValorInicial, new { @class = "form-control money" })
                  @Html.ValidationMessageFor(m => m.ValorInicial, "", new { @class = "text-danger" })
                </div>
              </div>
              <div class="col-md-2">
                <div class="form-group">
                  @Html.LabelFor(m => m.ValorFinal)
                  @Html.LibEditorFor(m => m.ValorFinal, new { @class = "form-control money" })
                  @Html.ValidationMessageFor(m => m.ValorFinal, "", new { @class = "text-danger" })
                </div>
              </div>
              <div class="col-md-2">
                <div class="form-group">
                  @Html.LabelFor(m => m.AliquotaProgressiva)
                  @Html.LibEditorFor(m => m.AliquotaProgressiva, new { @class = "form-control numeros" })
                  @Html.ValidationMessageFor(m => m.AliquotaProgressiva, "", new { @class = "text-danger" })
                </div>
              </div>*@
          </div>
        </div><!-- /.card-body -->
        <div class="card-footer">
          <button type="button" value="Voltar" class="btn btn-secondary" onclick="window.location.href='@Url.Action("Index","TabelaProgressivaIRPF")'">Voltar</button>
          <button type="submit" value="Create" class="btn btn-info pull-rigth">Salvar</button>
        </div>
      </div>
    </section>
  </div>
}
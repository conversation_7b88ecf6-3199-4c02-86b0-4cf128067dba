﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Controls;
using RepasseConvenio.Models;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Transactions;
using System.Web;
using System.Web.Mvc;

namespace RepasseConvenio.Controllers
{
  public class DepositoRepasseController : LibController
  {
    private DepositoRepasseService DepositoRepasseService
    {
      get
      {
        if (_DepositoRepasseService == null)
          _DepositoRepasseService = new DepositoRepasseService(ContextoUsuario.UserLogged);

        return _DepositoRepasseService;
      }
    }
    private DepositoRepasseService _DepositoRepasseService;

    [HttpPost]
    public JsonResult Delete(int codigoDepositoRepasse, int IdRepasse)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        DepositoRepasseService.Delete(codigoDepositoRepasse, IdRepasse);
        retornoAjax.Erro = false;
        retornoAjax.messageType = MessageType.Success;
        retornoAjax.Mensagem = " Depósito Repasse removida com sucesso.";
        retornoAjax.Titulo = "Sucesso.";
        return Json(retornoAjax);
      }
      catch (CustomException ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.messageType = MessageType.Error;
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Titulo = "Erro.";
        return Json(retornoAjax);
      }
      catch (Exception ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.messageType = MessageType.Error;
        retornoAjax.Mensagem = "Houve um erro no momento de processar sua solicitação.";
        retornoAjax.Titulo = "Erro.";
        return Json(retornoAjax);
      }


    }

    [HttpGet]
    public JsonResult GetInserirDepositoRepasse(int IdBanco)
    {
      try
      {
        DepositoRepasseService repasseService = new DepositoRepasseService();

        R_DepositoRepasse model = repasseService.GetInseirDepositoRepasse(IdBanco);
        var Data = model.DR_DataDeposito.ToShortDateString();
        return Json(new { status = "success", DataDeposito = Data, ValorDeposito = model.DR_ValorDeposito, NumeroDocumento = model.DR_NumeroDocumento, ValorUtilizado = model.DR_ValorUtilizado }, JsonRequestBehavior.AllowGet);
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Success, "Repasse editado com suceso.", "12000"));
        return Json(new { status = "Error" }, JsonRequestBehavior.AllowGet);
      }

    }

    [HttpGet]
    public ActionResult GetBancoDepositoRepasseSelect(string term)
    {
      DepositoRepasseService BancoServices = new DepositoRepasseService();
      List<Select2Model> Lista = new List<Select2Model>();
      Lista = BancoServices.GetByTerm(term).Select(list => list.ToSelect2Model()).ToList();
      return Json(new { items = Lista }, JsonRequestBehavior.AllowGet);
    }

    [HttpPost]
    public JsonResult InserteDepositoRepasse(int IdRepasse, int IdBanco)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        using (TransactionScope scope = new TransactionScope(TransactionScopeOption.RequiresNew))
        {
          RepasseService repasseService = new RepasseService();
          R_Repasse repasse = new R_Repasse();
          repasse = repasseService.GetById(IdRepasse);
          repasse.R_IdDepositoRepasse = IdBanco;
          repasseService.Edit(repasse);
          scope.Complete();
          return Json(new { status = "success", message = "Depósito Inserido com sucesso!", IdDeposito = IdBanco }, JsonRequestBehavior.AllowGet);
        }

      }

      catch (Exception ex)
      {

        return Json(new { status = "error", message = ex.Message }, JsonRequestBehavior.AllowGet);
      }


    }

  }
}







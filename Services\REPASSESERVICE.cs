﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Models;
using RepasseConvenio.WSPortalCooperado;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading;
using System.Transactions;
using System.Web;

namespace RepasseConvenio.Services
{
  public class RepasseService : ServiceBase
  {
    public RepasseService()
   : base()
    {
    }

    public RepasseService(RepasseEntities Contexto)
       : base(Contexto)
    {
    }

    public RepasseService(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
      if (semaphore == null)
        semaphore = new Semaphore(1, 1);
    }

    public RepasseService(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public List<RepassesStatus> GetRepassesStatus(List<int> IdGuiaAtendimento)
    {
      string query = @"SELECT 
                        SR.SR_Enum [EnumStatusRepasse],
                        SR.SR_Descricao [Descricao]
                        FROM R_GuiaDemonstrativo GD
                       INNER JOIN R_DemonstrativoConvenio DC ON DC.DC_Id = GD.GD_IdDemonstrativoConvenio AND GD.GD_IdGuia IN (#ids#)
                       INNER JOIN R_Repasse R ON R.R_Id = DC.DC_IdRepasse
                       INNER JOIN R_StatusRepasse SR ON SR.SR_Id = R.R_IdStatusRepasse";

      query = query.Replace("#ids#", string.Join(",", IdGuiaAtendimento.ToArray()));

      return Contexto.Database.SqlQuery<RepassesStatus>(query).ToList();
    }

    public string GetCNPJConvenioByIdRepasse(int IdRepasse)
    {
      string query = @"SELECT
                        C.C_CNPJ
                       FROM R_Repasse R
                       INNER JOIN R_Convenio C ON C.C_Id = R.R_IdConvenio AND R.R_Id = @IdRepasse";

      return Contexto.Database.SqlQuery<string>(query, new SqlParameter("@IdRepasse", IdRepasse)).FirstOrDefault();
    }

    public int GetIdCNPJConvenioByIdRepasse(int IdRepasse)
    {
      string query = @"SELECT
                        C.C_Id
                       FROM R_Repasse R
                       INNER JOIN R_Convenio C ON C.C_Id = R.R_IdConvenio AND R.R_Id = @IdRepasse";

      return Contexto.Database.SqlQuery<int>(query, new SqlParameter("@IdRepasse", IdRepasse)).FirstOrDefault();
    }


    public List<R_RepasseModel> GetAllRepasseModel()
    {
      string query = @"SELECT
                      R_Id AS Codigo, 
                      R_Numero AS Numero,
                      R_IdUsuarioResponsavel AS CodigoResponsavel, 
                      R_DataCriacao AS DataCriacao, 
                      R_DataInicio AS DataInicio, 
                      R_IdStatusRepasse AS CodigoStatusRepasse, 
                      R_NF AS NF, 
                      R_Valorizacao AS Valorizacao, 
                      R_Deposito AS Deposito, 
                      R_IdConvenio AS CodigoConvenio, 
                      R_NumeroDeposito AS NumDeposito, 
                      R_IdBancoDeposito AS CodigoBancoDeposito, 
                      R_AgenciaDeposito AS AgenciaDeposito, 
                      R_ContaDeposito AS ContaDeposito, 
                      R_DataDeposito AS DataDeposito, 
                      R_ValorDeposito AS ValorDeposito, 
                      R_IdUsuarioCriacao AS CodigoUsuarioCriacao, 
                      R_Valor AS Valor, 
                      R_Imposto AS Imposto, 
                      R_TaxaAdministrativa AS TaxaAdministrativa
                      FROM R_Repasse";

      return Contexto.Database.SqlQuery<R_RepasseModel>(query).ToList();
    }

    public R_RepasseModel GetRepasseModelById(int Id)
    {
      string query = @"SELECT
                      R_Id AS Codigo, 
                      R_Numero AS Numero,
                      R_IdUsuarioResponsavel AS CodigoResponsavel, 
                      R_DataCriacao AS DataCriacao, 
                      R_DataInicio AS DataInicio, 
                      R_IdStatusRepasse AS CodigoStatusRepasse, 
                      R_NF AS NF, 
                      R_Valorizacao AS Valorizacao, 
                      R_Deposito AS Deposito, 
                      R_IdConvenio AS CodigoConvenio, 
                      R_NumeroDeposito AS NumDeposito, 
                      R_IdBancoDeposito AS CodigoBancoDeposito, 
                      R_AgenciaDeposito AS AgenciaDeposito, 
                      R_ContaDeposito AS ContaDeposito, 
                      R_DataDeposito AS DataDeposito, 
                      R_ValorDeposito AS ValorDeposito, 
                      R_IdUsuarioCriacao AS CodigoUsuarioCriacao, 
                      R_Valor AS Valor, 
                      R_Imposto AS Imposto, 
                      R_TaxaAdministrativa AS TaxaAdministrativa
                      FROM R_Repasse
                      WHERE 
                      R_Id = @Id
                      ";

      return Contexto.Database.SqlQuery<R_RepasseModel>(query, new SqlParameter("@Id", Id)).FirstOrDefault();
    }

    public List<RepasseModelIndex> GetAllRepasseModelIndex()
    {
      string query = @"SELECT
                        RE.R_Id AS Codigo, 
                        RE.R_Numero AS Numero,
                        RES.U_Nome AS Responsavel,
                        RE.R_DataCriacao AS DataCriacao, 
                        RE.R_DataInicio AS DataInicio, 
                        ST.SR_Descricao AS StatusRepasse,
                        RE.R_IdStatusRepasse AS Status,
                        CON.C_RazaoSocial AS Convenio,
                        ST.SR_Enum [StatusRepasseEnum]
                       FROM R_Repasse RE
                       INNER JOIN R_Usuario RES ON RES.U_Id = RE.R_IdUsuarioResponsavel
                       INNER JOIN R_Convenio CON ON CON.C_Id = RE.R_IdConvenio
                       INNER JOIN R_StatusRepasse ST ON ST.SR_Id = RE.R_IdStatusRepasse";

      return Contexto.Database.SqlQuery<RepasseModelIndex>(query).ToList();
    }

    public R_Repasse GetById(int id)
    {
      return Contexto.R_Repasse.Where(a => a.R_Id == id).FirstOrDefault();
    }

    public R_Repasse GetByIdDepositoRepasse(int id)
    {
      return Contexto.R_Repasse.Where(a => a.R_IdDepositoRepasse != id).FirstOrDefault();
    }

    public R_Repasse GetByIdRepasse(int idDepositoRepasse,int IdRepasse)
    {
      return Contexto.R_Repasse.Where(a => a.R_IdDepositoRepasse == idDepositoRepasse && a.R_Id == IdRepasse).FirstOrDefault();
    }

    public R_Repasse GetByIdSQL(int id)
    {
      string query = @"SELECT 
                        * 
                       FROM R_Repasse
                       WHERE R_Id = @id";

      return Contexto.Database.SqlQuery<R_Repasse>(query, new SqlParameter("@id", id)).FirstOrDefault();
    }

    public string GerarNumRepasse()
    {
      List<R_Repasse> ListRepasse = Contexto.Database.SqlQuery<R_Repasse>("select * from R_Repasse").ToList();

      R_Repasse R_Repasse = new R_Repasse();

      if (ListRepasse.Count == 0)
        return "0000000001";

      R_Repasse = ListRepasse.LastOrDefault();
      int aux = int.Parse(R_Repasse.R_Numero);
      aux++;

      if (aux < 10)
        return "000000000" + aux;
      if (aux < 100)
        return "00000000" + aux;
      if (aux < 1000)
        return "0000000" + aux;
      if (aux < 10000)
        return "000000" + aux;
      if (aux < 100000)
        return "00000" + aux;
      if (aux < 1000000)
        return "0000" + aux;
      if (aux < 10000000)
        return "000" + aux;
      if (aux < 100000000)
        return "000" + aux;
      if (aux < 1000000000)
        return "00" + aux;
      else
        return aux.ToString();
    }

    public int Create(R_RepasseModel repasseModel, int IdDeposito = 0)
    {
      try
      {
        if (semaphore.WaitOne())
        {
          repasseModel.Numero = GerarNumRepasse();
          R_Repasse entity = repasseModel.ToR_RepasseCreate(User.IdUsuario, IdDeposito);
          Create(entity);
          return entity.R_Id;
        }
        else
          throw new Exception("timeout Esperando semaforo");
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
      finally
      {
        semaphore.Release();
      }
    }

    public void Edit(R_RepasseModel repasseModel)
    {
      R_Repasse entity = GetById(repasseModel.Codigo);

      if (entity != null)
      {
        entity = repasseModel.ToR_RepasseEdit(entity);
        if (entity.R_IdDepositoRepasse.HasValue)
        {
          StatusRepasseServices statusRepasseServices = new StatusRepasseServices(Contexto);
          int IdStatusRepasseEmConferencia = statusRepasseServices.GetIdByEnum(EnumStatusRepasse.EMCONFERENCIA);
          entity.R_IdStatusRepasse = IdStatusRepasseEmConferencia;
        }
        Edit(entity);
      }
    }

    public List<LotePortalModel> BuscarLote(BuscaLote model)
    {
      BuscaLoteRepasse busca = new BuscaLoteRepasse();
      ConvenioServices convenioServices = new ConvenioServices();
      string cnpj = convenioServices.GetCNPJById(model.BL_IdConvenio);
      busca = model.ToBuscaLoteRepasseModel(cnpj);
      List<LotePortalModel> lista = new List<LotePortalModel>();

      try
      {
        IntegraRepasse servico = new IntegraRepasse();
        ResultPesquisaLote retorno = servico.BuscaBasicaLotes(busca);

        if (retorno != null && !retorno.Erro)
          lista = retorno.ListaLotesBasic.ToList().Select(a => a.ToLoteBasicoWSModel(model.BL_IdRepasse)).ToList();

      }
      catch (Exception)
      {
        lista = new List<LotePortalModel>();
      }
      return lista;

    }

    public void InserirLotes(List<LotePortalModel> model)
    {
      try
      {
        if (model.Count > 0)
        {
          int IdRepasse = model.FirstOrDefault().BLM_IdRepasse;
          R_Repasse repasse = GetById(IdRepasse);
          List<int> listIdLote = model.Select(a => a.BLM_NumLote).ToList();

          ConvenioServices convenioServices = new ConvenioServices();
          string cnpj = convenioServices.GetCNPJById(repasse.R_IdConvenio);

          IntegraRepasse servico = new IntegraRepasse();
          BuscaAvancadaRepasse busca = new BuscaAvancadaRepasse();
          busca.NumerosLotes = listIdLote.ToArray();
          busca.CNPJConvenio = cnpj;
          ResultPesquisaLote retorno = servico.BuscaAvancadaLotes(busca);

          if (retorno != null && !retorno.Erro)
            foreach (var lote in retorno.ListaLotesBasic)
              foreach (var guia in lote.ListaGuiasRepasse)
              {
                try
                {
                  using (var scope = new TransactionScope(TransactionScopeOption.RequiresNew))
                  {
                    //PlanilhaRecebimentoService planilhaRecebimentoService = new PlanilhaRecebimentoService(User, Contexto);
                    //R_PlanilhaRecebimento planEntity = new R_PlanilhaRecebimento();

                    //if (guia.DataAtendimento.HasValue)
                    //  planEntity = planilhaRecebimentoService.Get(guia.AtendimentoHospital, guia.CodCarteiraPaciente, guia.DataAtendimento.Value);
                    //else
                    //  planEntity = null;

                    //if (planEntity == null)
                    //{
                    //  planEntity = new R_PlanilhaRecebimento();
                    //  planEntity = guia.ToR_PlanilhaRecebimento(IdRepasse, User.IdUsuario);
                    //  Create(planEntity);
                    //}
                    //else
                    //{
                    //  planEntity = guia.ToR_PlanilhaRecebimentoEditByIntegracaoLote(planEntity, User.IdUsuario);
                    //  Edit(planEntity);
                    //}

                    ConvenioServices ConvenioServices = new ConvenioServices(Contexto);
                    int idConvenio = ConvenioServices.GetIdByCNPJ(guia.CNPJConvenio);

                    R_GuiaAtendimento guiaAtendimento = guia.ToR_GuiaAtendimento(idConvenio);
                    Create(guiaAtendimento);

                    MedicoService medicoService = new MedicoService(Contexto);
                    R_Medico medico = medicoService.GetByCPF(guia.ParticipacaoGuia.MedicoRepasse.CPF);

                    if (medico == null)
                    {
                      medico = new R_Medico();
                      medico = guia.ParticipacaoGuia.MedicoRepasse.ToR_Medico();
                      Create(medico);
                    }

                    R_PartGuiaAtendimento PartGuia = guia.ParticipacaoGuia.ToR_PartGuiaAtendimento(guiaAtendimento.GA_Id, medico.M_Id);
                    Create(PartGuia);

                    foreach (var procedimento in guia.Procedimentos)
                    {
                      R_ProcGuiaAtendimento entityProc = procedimento.ToR_Procedimentos(guiaAtendimento.GA_Id);
                      Create(entityProc);
                    }
                    servico = new IntegraRepasse();
                    IntegracoesGuiasRepasseWS parameter = new IntegracoesGuiasRepasseWS();
                    parameter.MarcarIntegracao = true;
                    parameter.NroGuias = new string[1];
                    parameter.NroGuias[0] = guia.NroUnicooper;
                    RepasseRetornoWS retorn = servico.IntegracaoGuiaRepasse(parameter);

                    if (retorno == null || retorno.Erro)
                      throw new Exception($"{retorno.Mensagem}");

                    scope.Complete();
                  }
                }
                catch (Exception ex)
                {
                }
              }
        }
      }
      catch (Exception ex)
      {
        throw ex;
      }
    }

    public void AtualizaRepasseProcessamento(R_Repasse repasse)
    {
      if (repasse != null)
      {
        DemonstrativoConvenioService demonstrativoConvenioService = new DemonstrativoConvenioService(User, Contexto);
        List<R_DemonstrativoConvenio> ListDemonstrativoConvenio = demonstrativoConvenioService.GetListaRDemonstrativoConvenio(repasse.R_Id);

        List<R_DemonstrativoConvenio> processadas = ListDemonstrativoConvenio.Where(a => a.DC_Processado == true).ToList();

        if (ListDemonstrativoConvenio.Count == processadas.Count)
          repasse.R_IdStatusRepasse = new StatusRepasseServices(User, Contexto).GetIdByEnum(EnumStatusRepasse.PROCESSADO);
        else
          repasse.R_IdStatusRepasse = new StatusRepasseServices(User, Contexto).GetIdByEnum(EnumStatusRepasse.PROCESSADOPARCIALMENTE);

        Edit(repasse);
      }
    }

    public EnumStatusRepasse ProcessarRepasse(int idRepasse)
    {
      DemonstrativoConvenioService demonstrativoConvenioService = new DemonstrativoConvenioService();
      GuiaDemonstrativoService guiaDemonstrativoService = new GuiaDemonstrativoService();
      ProcGuiaDemonstrativoService procGuiaDemonstrativoService = new ProcGuiaDemonstrativoService();
      LogExtratoMedicoServices logExtratoMedicoServices = new LogExtratoMedicoServices(User);

      R_Repasse repasse = GetById(idRepasse);
      if (!repasse.R_Valorizacao)
        throw new CustomException("Para processar o repasse é necessário adicionar pelo menos um demontrativo.");

      if (!repasse.R_Deposito)
        throw new CustomException("Para processar o repasse é necessário informar todos os dados bancários.");

      if (!repasse.R_NF)
        throw new CustomException("Para processar o repasse é adicionar uma nota fiscal e guias.");

      IntegraRepasse integraRepasse = new IntegraRepasse();

      StatusRepasseServices statusRepasseServices = new StatusRepasseServices();
      R_StatusRepasse statusRepasseEmConferencia = statusRepasseServices.GetByEnum(EnumStatusRepasse.EMCONFERENCIA);
      R_StatusRepasse statusRepasseProcessadoParcialmente = statusRepasseServices.GetByEnum(EnumStatusRepasse.PROCESSADOPARCIALMENTE);

      if (repasse.R_IdStatusRepasse == statusRepasseEmConferencia.SR_Id || repasse.R_IdStatusRepasse == statusRepasseProcessadoParcialmente.SR_Id)
      {
        List<R_DemonstrativoConvenio> ListDemonstrativoConvenio = demonstrativoConvenioService.GetListaDemonstrativoConvenio(idRepasse, false);

        int IdClassProcessamentoRepasse = new ClassificacaoRepasseServices(Contexto).GetIdByEnum(EnumClassificacaoRepasse.PROCESSAMENTODEREPASSE);
        foreach (R_DemonstrativoConvenio demonstrativoConvenio in ListDemonstrativoConvenio)
        {
          List<R_GuiaDemonstrativo> ListGuiaDemonstrativo = guiaDemonstrativoService.GetListGuiaDemonstrativoNaoProcessado(demonstrativoConvenio.DC_Id);
          foreach (R_GuiaDemonstrativo guiaDemonstrativo in ListGuiaDemonstrativo)
          {
            List<R_ProcGuiaDemonstrativo> ListaProcGuiaDemonstrativo = procGuiaDemonstrativoService.GetListaProcGuiaDemonstrativo(guiaDemonstrativo.GD_Id);
            CodDocumentoService codDocumentoService = new CodDocumentoService();
            R_GuiaAtendimento guia = GetGuiaByNumero(guiaDemonstrativo.GD_NumeroGuia);
            if (guia != null && guia.R_PartGuiaAtendimento.Count() > 0)
            {
              R_PartGuiaAtendimento PartGuiaAtendimento = guia.R_PartGuiaAtendimento.FirstOrDefault();
              R_Medico medicoGuia = PartGuiaAtendimento.R_Medico;
              int IdHospital = new HospitalServices(Contexto).GetIdByNome(guia.GA_NomeSolicitante);
              string CodigoDocumento = codDocumentoService.GetSequencialDocumento();
              try
              {
                using (var scope = new TransactionScope(TransactionScopeOption.RequiresNew))
                {
                  RetornoMedicoIntegraGuia retornoMedicoIntegraGuia = integraRepasse.GetMedicoIntegraGuia(medicoGuia.M_CPF, "12D43618-E331-43ED-9525-5674AE350D37");
                  if (retornoMedicoIntegraGuia.Erro)
                    throw new Exception(retornoMedicoIntegraGuia.Mensagem);
                  else
                  {
                    StatusCooperado statusCooperado = retornoMedicoIntegraGuia.medicoIntegraGuia.statusCooperado;
                    if (statusCooperado != StatusCooperado.Ativo)
                      medicoGuia.M_Ativo = false;
                    else
                      medicoGuia.M_Ativo = true;
                    Edit(medicoGuia);
                  }

                  foreach (R_ProcGuiaDemonstrativo procedimento in ListaProcGuiaDemonstrativo)
                  {
                    RegraCartaConversaoServices regraCartaConversaoServices = new RegraCartaConversaoServices();
                    RegraCarta regraCarta = new RegraCarta();
                    regraCarta.RazaoSocialConvenio = guia.R_Convenio.C_RazaoSocial.ToLower();
                    regraCarta.ValorProcedimento = procedimento.PGD_TotalPago;
                    regraCarta.RazaoSocialHospital = guia.GA_NomeSolicitante.ToLower();
                    regraCarta.AtendimentoHospital = guia.GA_AtendimentoHospital.ToLower();
                    regraCarta.NroGuiaUnicooper = guia.GA_NroUnicooper.ToLower();
                    regraCarta.NomePaciente = guia.GA_NomePaciente.ToLower();
                    regraCarta.NroCarteirinhaPaciente = guia.GA_CodCarteiraPaciente.ToLower();
                    regraCarta.Acomodacao = guia.GA_DescTipoAcomodacao.ToLower();
                    regraCarta.DescricaoProcedimento = procedimento.PGD_DescricaoProcedimento.ToLower();
                    regraCarta.CodigoProcedimento = procedimento.PGD_CodigoProcedimento.ToLower();
                    regraCarta.Senha = !string.IsNullOrEmpty(guia.GA_CodSenhaGuia) ? guia.GA_CodSenhaGuia.ToLower() : "";
                    regraCarta.CodigoGuiaPrincipal = !string.IsNullOrEmpty(guia.GA_CodGuiaPrincipal) ? guia.GA_CodGuiaPrincipal.ToLower() : "";
                    regraCarta.SolicitacaoInternacao = !string.IsNullOrEmpty(guia.GA_SolicitacaoInternacao) ? guia.GA_SolicitacaoInternacao.ToLower() : "";
                    regraCarta.NomeMedico = medicoGuia.M_Nome.ToLower();
                    regraCarta.CPFMedico = medicoGuia.M_CPF.ToLower();
                    regraCarta.CRMMedico = medicoGuia.M_CRM.ToLower();
                    RetornoValidacaoRegra retornoValidacaoRegra = regraCartaConversaoServices.ProcesarRegras(medicoGuia.M_Id, regraCarta);

                    R_RateioMedico rateioMedico = GetRoteioMedico(medicoGuia.M_Id, IdHospital, procedimento.PGD_CodigoProcedimento);

                    #region [INICIO - PROCESSAMENTO EXTRATO SEM RATEIO]
                    if (rateioMedico == null)
                    {
                      R_ExtratoMedico extratoMedico = new R_ExtratoMedico();
                      extratoMedico = extratoMedico.CreateByProcessaRepasse(IdClassProcessamentoRepasse, guia, procedimento.PGD_TotalPago, medicoGuia.M_Id, retornoValidacaoRegra, User.IdUsuario, CodigoDocumento, idRepasse, "Extrato criado para pagamento total do procedimento.", EnumTipoLancamento.C, false);
                      Create(extratoMedico);

                      if (procedimento.PGD_TaxaAdministrativa != 0)
                      {
                        extratoMedico = extratoMedico.CreateByProcessaRepasse(IdClassProcessamentoRepasse, guia, procedimento.PGD_TotalPago, medicoGuia.M_Id, retornoValidacaoRegra, User.IdUsuario, CodigoDocumento, idRepasse, string.Format("Extrato criado para cobrança taxa de {0} sobre o procedimento {1}.", procedimento.PGD_TaxaAdministrativa, procedimento.PGD_DescricaoProcedimento), EnumTipoLancamento.D, false, null, true, procedimento.PGD_TaxaAdministrativa);
                        Create(extratoMedico);
                      }

                      logExtratoMedicoServices.Create(extratoMedico.EX_Id, "Extrato Criado");
                    }
                    #endregion  [FIM - PROCESSAMENTO EXTRATO SEM RATEIO]
      
                    #region [INICIO - PROCESSAMENTO EXTRATO COM RATEIO]
                    else
                    {
                      #region [INICIO - RATEIO FIXO]
                      List<R_RateioFixo> rateiosFixo = GetRateioFixo(rateioMedico.RM_Id);
                      if (rateiosFixo.Count > 0)
                      {
                        foreach (R_RateioFixo rateio in rateiosFixo)
                        {
                          R_ExtratoMedico extratoMedico = new R_ExtratoMedico();
                          extratoMedico = extratoMedico.CreateByProcessaRepasse(IdClassProcessamentoRepasse, guia, procedimento.PGD_TotalPago, rateio.RF_IdMedico, retornoValidacaoRegra, User.IdUsuario, CodigoDocumento, idRepasse, "Extrato criado para pagamento com rateio fixo para procedimento.", EnumTipoLancamento.C, true, rateio.RF_Percentual);
                          Create(extratoMedico);

                          if (procedimento.PGD_TaxaAdministrativa != 0)
                          {
                            extratoMedico = extratoMedico.CreateByProcessaRepasse(IdClassProcessamentoRepasse, guia, procedimento.PGD_TotalPago, rateio.RF_IdMedico, retornoValidacaoRegra, User.IdUsuario, CodigoDocumento, idRepasse, string.Format("Extrato criado para cobrança taxa de {0} sobre o procedimento {1}.", procedimento.PGD_TaxaAdministrativa, procedimento.PGD_DescricaoProcedimento), EnumTipoLancamento.D, false, null, true, procedimento.PGD_TaxaAdministrativa);
                            Create(extratoMedico);
                          }

                          logExtratoMedicoServices.Create(extratoMedico.EX_Id, "Extrato Criado");
                        }
                      }
                      #endregion  [FIM - RATEIO FIXO]
                      #region [INICIO - RATEIO POR GRAU DE PARTICIPAÇÃO]
                      else
                      {
                        int IdGrauPart = new GrauParticipacaoServices(Contexto).GetIdByCodigo(PartGuiaAtendimento.PGA_CodGrauPart);
                        List<R_RateioGrauParticipacao> RateiosGrauParticipacao = GeRateioGrauParticipacao(rateioMedico.RM_Id, IdGrauPart);
                        foreach (R_RateioGrauParticipacao rateio in RateiosGrauParticipacao)
                        {
                          R_ExtratoMedico extratoMedico = new R_ExtratoMedico();
                          extratoMedico = extratoMedico.CreateByProcessaRepasse(IdClassProcessamentoRepasse, guia, procedimento.PGD_TotalPago, rateio.RG_IdMedico, retornoValidacaoRegra, User.IdUsuario, CodigoDocumento, idRepasse, "Extrato criado para pagamento com rateio fixo para procedimento.", EnumTipoLancamento.C, true, rateio.RG_Percentual);
                          Create(extratoMedico);

                          if (procedimento.PGD_TaxaAdministrativa != 0)
                          {
                            extratoMedico = extratoMedico.CreateByProcessaRepasse(IdClassProcessamentoRepasse, guia, procedimento.PGD_TotalPago, rateio.RG_IdMedico, retornoValidacaoRegra, User.IdUsuario, CodigoDocumento, idRepasse, string.Format("Extrato criado para cobrança taxa de {0} sobre o procedimento {1}.", procedimento.PGD_TaxaAdministrativa, procedimento.PGD_DescricaoProcedimento), EnumTipoLancamento.D, true, rateio.RG_Percentual, true, procedimento.PGD_TaxaAdministrativa);
                            Create(extratoMedico);
                          }

                          logExtratoMedicoServices.Create(extratoMedico.EX_Id, "Extrato Criado");
                        }

                      }
                      #endregion  [FIM -RATEIO POR GRAU DE PARTICIPAÇÃO]
                    }
                    #endregion  [FIM - PROCESSAMENTO EXTRATO COM RATEIO]
                  }
                  guiaDemonstrativoService.ProcessaGuiaDemonstrativo(guiaDemonstrativo, StatusGuiaDemonstrativoEnum.Processado, "Processamento concluído.");

                  scope.Complete();
                }
              }
              catch (Exception ex)
              {
                guiaDemonstrativoService.ProcessaGuiaDemonstrativo(guiaDemonstrativo, StatusGuiaDemonstrativoEnum.NaoProcessado, ex.Message);
              }
            }
            else
              guiaDemonstrativoService.ProcessaGuiaDemonstrativo(guiaDemonstrativo, StatusGuiaDemonstrativoEnum.NaoProcessado, "Nenhuma guia identificada");
          }
          demonstrativoConvenioService.ProcessaDemonstrativo(demonstrativoConvenio);
        }
        AtualizaRepasseProcessamento(repasse);
      }
      else
        throw new CustomException("Repasse precisa estar Em Conferência ou Processado Parcialmente para poder ser processado.");

      return (EnumStatusRepasse)repasse.R_IdStatusRepasse;
    }

    public List<R_GuiaAtendimento> GetGuias(List<string> listNumeroGuias)
    {
      return Contexto.R_GuiaAtendimento.Where(a => listNumeroGuias.Contains(a.GA_NroUnicooper)).ToList();
    }

    public R_GuiaAtendimento GetGuiaByNumero(string NumeroGuias)
    {
      return Contexto.R_GuiaAtendimento.Where(a => a.GA_NroUnicooper.Equals(NumeroGuias)).FirstOrDefault();
    }

    public R_RateioMedico GetRoteioMedico(int IdMedico, int IdHospital, string CodProcedimento)
    {
      return Contexto.R_RateioMedico.Where(a => a.RM_IdMedico == IdMedico
                                           && a.RM_IdHospital == IdHospital
                                           && a.RM_CodigoProcedimento.Equals(CodProcedimento)
                                           ).FirstOrDefault();
    }

    public List<R_RateioFixo> GetRateioFixo(int IdRateioMedico)
    {
      return Contexto.R_RateioFixo.Where(a => a.RF_IdRateioMedico == IdRateioMedico
                                           ).ToList();
    }

    public List<R_RateioGrauParticipacao> GeRateioGrauParticipacao(int IdRateioMedico, int IdGrauPart)
    {
      return Contexto.R_RateioGrauParticipacao.Where(a => a.RG_IdRateioMedico == IdRateioMedico
                                                     && a.RG_IdGrauParticipacao == IdGrauPart
                                                      ).ToList();
    }

    public void UpdateStatus(EnumStatusRepasse enumStatusRepasse, int IdRepasse)
    {
      R_Repasse repasse = GetById(IdRepasse);
      repasse.R_IdStatusRepasse = new StatusRepasseServices().GetByEnum(enumStatusRepasse).SR_Id;
      Edit(repasse);
    }

    public void CancelarProcessamento(int IdRepasse)
    {
      R_Repasse repasse = GetById(IdRepasse);
      StatusRepasseServices statusRepasseServices = new StatusRepasseServices();
      int IdStatusRepasseProcessadoGlosado = statusRepasseServices.GetIdByEnum(EnumStatusRepasse.ProcessadoGlosado);

      using (TransactionScope scope = new TransactionScope(TransactionScopeOption.RequiresNew))
      {
        List<int> idsRegistro = Contexto.Database.SqlQuery<int>("SELECT EX_IdRegistroPagamento FROM R_ExtratoMedico WHERE EX_CodigoRepasse = @Id AND EX_IdRegistroPagamento IS NOT NULL", new SqlParameter("@Id", IdRepasse)).ToList();
        List<int> idsExtrato = Contexto.Database.SqlQuery<int>("SELECT EX_Id FROM R_ExtratoMedico WHERE EX_CodigoRepasse = @Id", new SqlParameter("@Id", IdRepasse)).ToList();
        string queryDeleteLogExtrato = "DELETE FROM R_LogExtratoMedico WHERE LEM_IdExtratoMedico IN (#idsExtrato#)".Replace("#idsExtrato#", string.Join(",", idsExtrato.ToArray()));
        Contexto.Database.ExecuteSqlCommand(queryDeleteLogExtrato);
        Contexto.Database.ExecuteSqlCommand("DELETE FROM R_ExtratoMedico WHERE EX_CodigoRepasse = @Id", new SqlParameter("@Id", IdRepasse));

        if (idsRegistro.Count() != 0)
        {
          string Query = string.Format("DELETE FROM R_RegistroPagamento WHERE RP_Id IN ({0})", string.Join(", ", idsRegistro));
          Contexto.Database.ExecuteSqlCommand(Query);
        }
        Contexto.Database.ExecuteSqlCommand("UPDATE R_Repasse SET R_IdStatusRepasse = (SELECT TOP(1) SR_Id FROM R_StatusRepasse WHERE SR_Descricao = 'Em conferência') WHERE R_Id=@Id", new SqlParameter("@Id", IdRepasse));

        if (repasse.R_IdStatusRepasse == IdStatusRepasseProcessadoGlosado)
        {
          LoteGlosaServices loteGlosaServices = new LoteGlosaServices();
          loteGlosaServices.CancelarProcessamentoRepasse(IdRepasse);
        }

        Contexto.Database.ExecuteSqlCommand(@"UPDATE R_DemonstrativoConvenio SET 
	                                            DC_Processado = 0, 
	                                            DC_DataProcessamento = NULL, 
	                                            DC_InfoProcessamento = 'Não Processado', 
	                                            DC_IdStatusDemonstrativoConvenio = (SELECT SDC_Id FROM R_StatusDemonstrativoConvenio WHERE SDC_Enum = @EnumNaoProcessado)
                                            WHERE 
	                                            DC_IdRepasse=@Id", new SqlParameter("@Id", IdRepasse)
                                                               , new SqlParameter("@EnumNaoProcessado", (int)StatusDemonstrativoEnum.NaoProcessado));

        StatusGuiaDemonstrativoConvenioServices statusGuiaDemonstrativoConvenioServices = new StatusGuiaDemonstrativoConvenioServices();
        int IdStatusNaoProcessadoGuia = statusGuiaDemonstrativoConvenioServices.GetIdByEnum(StatusGuiaDemonstrativoEnum.NaoProcessado);
        string UpdateGuiaDemonstrativo = @"UPDATE GD SET 
                                            GD.DC_IdStatusGuiaDemonstrativoConvenio = @IdStatusNaoProcessadoGuia,
                                            GD.GD_InfoProcessamento = NULL,
                                            GD.GD_DataProcessamento = NULL
                                           FROM R_GuiaDemonstrativo GD
                                           INNER JOIN R_DemonstrativoConvenio DC ON DC.DC_Id = GD.GD_IdDemonstrativoConvenio AND DC.DC_IdRepasse = @IdRepasse";

        Contexto.Database.ExecuteSqlCommand(UpdateGuiaDemonstrativo, new SqlParameter("@IdStatusNaoProcessadoGuia", IdStatusNaoProcessadoGuia)
                                                                   , new SqlParameter("@IdRepasse", IdRepasse));

        scope.Complete();
      }
    }

    public void RemoverRepasse(int IdRepasse)
    {
      CancelarProcessamento(IdRepasse);
      DemonstrativoConvenioService demonstrativoConvenioService = new DemonstrativoConvenioService(User);
      List<int> ListIdDemonstrativos = demonstrativoConvenioService.GetListIdDemonstrativoByRepasse(IdRepasse);
      demonstrativoConvenioService.Delete(ListIdDemonstrativos, IdRepasse);

      R_Repasse repasse = GetById(IdRepasse);
      Delete(repasse);
    }

    public R_Repasse GetRepasseByIdNotaFiscal(int IdNotaFiscal)
    {
      string query = @"SELECT
                        R.*
                       FROM R_Repasse R
                       INNER JOIN R_NotaFiscaRepasse NFR ON NFR.NF_IdRepasse = R.R_Id AND NFR.NF_Id = @IdNotaFiscal";

      return Contexto.Database.SqlQuery<R_Repasse>(query, new SqlParameter("@IdNotaFiscal", IdNotaFiscal)).FirstOrDefault();
    }

    public List<R_Repasse> GetByTerm(string term)
    {
      if (string.IsNullOrEmpty(term))
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_Repasse");

        return Contexto.Database.SqlQuery<R_Repasse>(query).ToList();

      }
      else
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_Repasse
                                       WHERE R_Numero LIKE @termo");

        return Contexto.Database.SqlQuery<R_Repasse>(query, new SqlParameter("@termo", string.Format("%{0}%", term))).ToList();

      }
    }
  }
}
﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using PagedList;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RepasseConvenio.Services
{
  public class StatusControleMensalServices : ServiceBase
  {
    public StatusControleMensalServices(RepasseEntities Contexto)
        : base(Contexto)
    { }
    public StatusControleMensalServices()
       : base()
    { }

    public StatusControleMensalServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public StatusControleMensalServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public int GetIdByEnum(StatusControlheMensalEnum enumStatus)
    {
      return Contexto.R_StatusControleMensal.Where(a => a.SCM_Enum == (int)enumStatus).Select(a => a.SCM_Id).FirstOrDefault();
    }
    public R_StatusControleMensal GetByEnum(StatusControlheMensalEnum enumStatus)
    {
      return Contexto.R_StatusControleMensal.Where(a => a.SCM_Enum == (int)enumStatus).FirstOrDefault();
    }

    public R_StatusControleMensal GetById(int Id)
    {
      return Contexto.R_StatusControleMensal.Where(a => a.SCM_Id == Id).FirstOrDefault();
    }

  }
}


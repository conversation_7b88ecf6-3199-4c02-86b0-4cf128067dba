﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Controls;
using RepasseConvenio.Models;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace RepasseConvenio.Controllers
{
  [Authorize]
  public class ClassificacaoRepasseController : LibController
  {
    // GET: CassificacaoRepasse
    public ActionResult Index(int? page, string search)
    {
      return View(new ClassificacaoRepasseServices().GetAll(page));
    }





    public ActionResult Create()
    {

      return View(new ClassificacaoRepasseModel());

    }
    [HttpPost]
    public ActionResult Create(ClassificacaoRepasseModel model)
    {
      try
      {
        if (ModelState.IsValid)
        {
          new ClassificacaoRepasseServices().Create(model);
          MessageListToast.Add(new Message(MessageType.Success, "Feito", "12000"));
          return RedirectToAction("Index");
        }
        MessageListToast.Add(new Message(MessageType.Error, "Verifique os campos", "12000"));
        return View(model);
      }
      catch (CustomException ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message, "12000"));
        return View(model);
      }

    }


    public ActionResult Edit(int id)
    {

      return View(new ClassificacaoRepasseServices().GetById(id).ToModel());

    }
    [HttpPost]
    public ActionResult Edit(ClassificacaoRepasseModel model)
    {
      try
      {
        if (ModelState.IsValid)
        {
          new ClassificacaoRepasseServices().Edit(model);
          MessageListToast.Add(new Message(MessageType.Success, "Feito", "12000"));
          return RedirectToAction("Index");
        }
        MessageListToast.Add(new Message(MessageType.Error, "Verifique os campos", "12000"));
        return View(model);
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message, "12000"));
        return View(model);
      }
    }
  }
}
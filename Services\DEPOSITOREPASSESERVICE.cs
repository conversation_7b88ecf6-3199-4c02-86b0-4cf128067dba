﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure.Controls;
using RepasseConvenio.Models;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Transactions;
using System.Web;

namespace RepasseConvenio.Services
{
  public class DepositoRepasseService : ServiceBase
  {
    public DepositoRepasseService() : base()
    { }
    public DepositoRepasseService(RepasseEntities Contexto)
       : base(Contexto)
    { }

    public DepositoRepasseService(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public DepositoRepasseService(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }
    public int Create(R_DepositoRepasse model)
    {

      Contexto.R_DepositoRepasse.Add(model);
      Contexto.SaveChanges();
      return model.DR_Id;


    }

    public void DeleteId(int id)
    {
      String query = @"DELETE FROM R_DepositoRepasse WHERE DR_Id =@id ";
      Contexto.Database.SqlQuery<DepositoRepasseModel>(query, new SqlParameter("id", id)).ToList();
    }

    public List<LookupModel> GetLookupBanco(string term)
    {
      if (string.IsNullOrEmpty(term))
        return Contexto.R_BancosUnicooper.Where(o => true)
                                 .Take(15)
                                 .ToList()
                                 .Select(a => a.ToLookupModel())
                                 .OrderBy(a => a.Description)
                                 .ToList();
      else
      {
        term = term.Trim().ToLower();
        return Contexto.R_BancosUnicooper.Where(a => a.BU_Descricao.ToLower().StartsWith(term))
                                 .ToList()
                                 .Select(a => a.ToLookupModel())
                                 .OrderBy(a => a.Description)
                                 .ToList();
      }
    }

    public List<DepositoRepasseModel> GetDepositoRepasse()
    {
      String query = @"SELECT
                            DP.DR_Id AS Codigo,
		                        DP.DR_DataDeposito As DataDeposito,
		                        DP.DR_DataCriacao As DataCriacao,
		                        DP.DR_IdBancosUnicooper AS IdBancoUnicooper,
		                        DP.DR_ValorDeposito AS NumeroDocumento,
		                        DP.DR_ValorUtilizado As ValorUtilizado,
		                        DP.DR_ValorDeposito As ValorDeposito
								            
                     FROM R_DepositoRepasse DP
					      ";
      return Contexto.Database.SqlQuery<DepositoRepasseModel>(query).ToList();
    }

    public R_DepositoRepasse GetInseirDepositoRepasse(int idBanco)
    {
      String query = @"SELECT
                            DP.DR_Id ,
		                        DP.DR_DataDeposito ,
		                        DP.DR_DataCriacao ,
		                        DP.DR_IdBancosUnicooper ,
		                        DP.DR_ValorDeposito ,
		                        DP.DR_ValorUtilizado  ,
                            DP.DR_NumeroDocumento,
							              Dp.DR_IdUsuarioCriacao
								            
                     FROM R_DepositoRepasse DP 
					 where DP.DR_Id =@idBanco
					      ";
      return Contexto.Database.SqlQuery<R_DepositoRepasse>(query, new SqlParameter("idBanco", idBanco)
                                                                 ).FirstOrDefault();
    }

    public List<DepositoRepasseModel> GetById(int? IdDepositoRepasse)
    {
      String query = @"SELECT
                        DP.DR_Id AS Codigo,
		                    DP.DR_DataDeposito As DataDeposito,
		                    DP.DR_DataCriacao As DataCriacao,
		                    DP.DR_IdBancosUnicooper AS IdBancoUnicooper,
		                    DP.DR_ValorDeposito AS ValorDeposito,
		                    DP.DR_ValorUtilizado As ValorUtilizado,
		                    DP.DR_ValorDeposito As ValorDeposito,
                        DP.DR_NumeroDocumento As NumeroDocumento
                        FROM  R_DepositoRepasse DP   
                       LEFT JOIN R_Repasse ON R_IdBancoDeposito = DP.DR_Id
					             WHERE DP.DR_Id = @IdDepositoRepasse ";

      return Contexto.Database.SqlQuery<DepositoRepasseModel>(query
        , new SqlParameter("IdDepositoRepasse", IdDepositoRepasse == null ? (object)DBNull.Value : IdDepositoRepasse.Value)).ToList();
    }

    public R_DepositoRepasse GetByIdDepositoRepasse(int id)
    {
      return Contexto.R_DepositoRepasse.Where(a => a.DR_Id == id).FirstOrDefault();
    }

    public bool IFHaveOtherRepasseWithDeposito(int IdDeposito)
    {
      string query = @"SELECT
                        COUNT(1)
                       FROM R_Repasse 
                       WHERE R_IdDepositoRepasse = @IdDeposito";

      int Quantidade = Contexto.Database.SqlQuery<int>(query, new SqlParameter("@IdDeposito", IdDeposito)).FirstOrDefault();

      if (Quantidade == 0)
        return false;
      else
        return true;
    }

    public void Delete(int IdDepositoRepasse, int Idrepasse)
    {
      RepasseService service = new RepasseService(Contexto);
      R_Repasse repasseModel = service.GetByIdRepasse(IdDepositoRepasse, Idrepasse);
      using (TransactionScope scope = new TransactionScope(TransactionScopeOption.RequiresNew))
      {
        repasseModel.R_IdDepositoRepasse = null;
        Edit(repasseModel);

        bool HaveOtherRepasseWithDeposito = IFHaveOtherRepasseWithDeposito(IdDepositoRepasse);

        if (!HaveOtherRepasseWithDeposito)
          DeleteId(IdDepositoRepasse);
        scope.Complete();
      }
    }

  public List<R_DepositoRepasse> GetByTerm(string term)
  {
    if (string.IsNullOrEmpty(term))
    {
      string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                     R_DepositoRepasse");

      return Contexto.Database.SqlQuery<R_DepositoRepasse>(query).ToList();

    }
    else
    {
      string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                    R_DepositoRepasse
                                                      ");

      return Contexto.Database.SqlQuery<R_DepositoRepasse>(query, new SqlParameter("@termo", string.Format("%{0}%", term))).ToList();

    }
  }

  public ImportPlanilhaDeposito GetImportPlanilhaDeposito(int IdRepasse)
  {
    string query = @"SELECT
                         R.R_IdDepositoRepasse [IdDepositoRepasse],
                         DR.DR_ValorDeposito [ValorDeposito],
                         DR.DR_ValorUtilizado [ValorUtilizado]
                        FROM R_DepositoRepasse DR
                        INNER JOIN R_Repasse R ON R.R_IdDepositoRepasse = DR.DR_Id AND R.R_Id = @IdRepasse";

    return Contexto.Database.SqlQuery<ImportPlanilhaDeposito>(query, new SqlParameter("@IdRepasse", IdRepasse)).FirstOrDefault();
  }

}
}
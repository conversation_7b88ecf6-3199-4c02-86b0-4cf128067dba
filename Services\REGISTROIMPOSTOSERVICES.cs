﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using PagedList;
using RepasseConvenio.Infrastructure;
using RepasseConvenio.Models;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Globalization;
using System.Linq;
using System.Transactions;
using System.Web;

namespace RepasseConvenio.Services 
{
  public class RegistroImpostoServices : ServiceBase
  {
    public RegistroImpostoServices(RepasseEntities Contexto)
        : base(Contexto)
    { }
    public RegistroImpostoServices()
       : base()
    { }

    public RegistroImpostoServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public RegistroImpostoServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public R_RegistroImposto GetById(int Id)
    {
      return Contexto.R_RegistroImposto.Where(a => a.RI_Id == Id).FirstOrDefault();
    }

    public List<R_RegistroImposto> GetByTerm(string term)
    {
      if (string.IsNullOrEmpty(term))
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_RegistroImposto");

        return Contexto.Database.SqlQuery<R_RegistroImposto>(query).ToList();

      }
      else
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_RegistroImposto
                                       WHERE RI_CPFCNPJPagamento LIKE @termo");

        return Contexto.Database.SqlQuery<R_RegistroImposto>(query, new SqlParameter("@termo", string.Format("%{0}%", term))).ToList();

      }
    }

    public IPagedList<RegistroImpostoModel> Get(int page, string search, string data)
    {
      string query = @"SELECT 
	                        RI_Id [Codigo]
	                        ,RI_DataGeracao [DataGeracao]
	                        ,RI_IdUsuarioProcessamento [UsuarioProcessamento]
	                        ,RI_IdMedico [CodigoMedico]
	                        ,M_Nome [NomeMedico]
	                        ,RI_Banco [Banco]
	                        ,RI_Conta [Conta]
	                        ,RI_Agencia [Agencia]
	                        ,RI_Valor [Valor]
	                        ,RI_DataPagamento [DataPagamento]
	                        ,TRI.TRI_Enum [TipoImposto]
                        FROM R_RegistroImposto R
                        INNER JOIN R_Medico M ON R.RI_IdMedico = M.M_Id
                        INNER JOIN R_TipoRegistroImposto TRI ON TRI.TRI_Id = R.RI_IdTipoImposto";

      List<string> condicao = new List<string>();
      List<SqlParameter> listparametrs = new List<SqlParameter>();

      if (!string.IsNullOrEmpty(search))
      {
        search = search.ToLower();
        condicao.Add(String.Format(@" LOWER(M.M_Nome) LIKE ('%'+ @Search  +'%')"));
        listparametrs.Add(new SqlParameter("@Search", search));
      }

      if (!string.IsNullOrEmpty(data))
      {
        DateTime dtPag = Convert.ToDateTime(data, new CultureInfo("pt-Br"));
        condicao.Add(String.Format(@" R.RI_DataPagamento = @DtPag"));
        listparametrs.Add(new SqlParameter("@DtPag", dtPag));
      }

      if (listparametrs.Count > 0)
      {
        query = query.Replace("[CONDICAO]", String.Format("WHERE {0}", String.Join(" AND ", condicao.ToArray())));
        return Contexto.Database.SqlQuery<RegistroImpostoModel>(query, listparametrs.ToArray()).ToList().ToPagedList(page, PageSize);
      }
      else
      {
        query = query.Replace("[CONDICAO]", "");
        return Contexto.Database.SqlQuery<RegistroImpostoModel>(query).ToList().ToPagedList(page, PageSize);
      }

    }

    public void GerarRegistroPagamento(List<ExtratoMedicoRegistroImpostoModel> ListaExtratoMedicoRegistroPagamentoModel)
    {
      ExtratoMedicoServices extratoMedicoServices = new ExtratoMedicoServices();
      MedicoService medicoService = new MedicoService();
      StatusRegistroPagamentoServices statusRegistroPagamentoServices = new StatusRegistroPagamentoServices();
      List<R_TipoRegistroImposto> listTiposImposto = new TipoRegistroImpostoServices().GetAll();

      #region[Processamento Repasse]
      var GroupByProcessamentoRepasse = ListaExtratoMedicoRegistroPagamentoModel.GroupBy(a => new { a.IdMedico, a.Banco, a.Agencia, a.Conta, a.TipoImposto }).ToList();
      List<ExtratoRegistroImpostoCreateModel> extratosImpostos = GroupByProcessamentoRepasse.Select(a => new ExtratoRegistroImpostoCreateModel()
      {
        IdTipoImposto = listTiposImposto.Where(b => b.TRI_Enum == (int)a.Key.TipoImposto).Select(b => b.TRI_Id).FirstOrDefault(),
        IdMedico = a.Key.IdMedico,
        Agencia = a.Key.Agencia,
        Banco = a.Key.Banco,
        Conta = a.Key.Conta,
        ValorTotal = a.Sum(b => b.Valor),
        Extratos = a.ToList(),
      }).ToList();

      foreach (ExtratoRegistroImpostoCreateModel CreateModel in extratosImpostos)
      {
        using (var scope = new TransactionScope(TransactionScopeOption.RequiresNew))
        {
          R_RegistroImposto registroImposto = Create(CreateModel);
          List<int> ListaIdExtrato = CreateModel.Extratos.Select(a => a.Id).ToList();
          extratoMedicoServices.UpdateExtratosComImposto(ListaIdExtrato, registroImposto.RI_Id);
          scope.Complete();
        }
      }
      #endregion
    }

    public R_RegistroImposto Create(ExtratoRegistroImpostoCreateModel extratoRegistroImpostoCreateModel)
    {
      R_RegistroImposto registroImposto = extratoRegistroImpostoCreateModel.toCreateRegistroImposto(User.IdUsuario);

      Create(registroImposto);
      return registroImposto;
    }

    public List<ExtratoMedicoRegistroImpostoModel> GetExtratoImposto(DateTime? datade, DateTime? dataate, int? idMedico, int? idTipoImposto)
    {
      try
      {
        string query = @"SELECT
                          EXM.EX_Id [Id],
                          EXM.EX_Valor [Valor],
                          EXM.EX_IdMedico [IdMedico],
                          EXM.EX_TipoLancamento [TipoLancamento],
                          ISNULL(EM.EM_AgenciaEmpresa, M.M_AgenciaMedico) [Agencia],
                          ISNULL(EM.EM_BancoEmpresa, M.M_BancoMedico) [Banco],
                          ISNULL(EM.EM_ContaEmpresa, M.M_ContaMedico) [Conta],
						              TRI.TRI_Enum [TipoImposto]
                        FROM R_ExtratoMedico EXM
                        INNER JOIN R_ClassificacaoRepasse CR ON CR.CR_Id = EXM.EX_IdClassificacao
                        INNER JOIN R_TipoRegistroImposto TRI ON TRI.TRI_Descricao = CR.CR_Descricao
						            LEFT JOIN R_EmpresaMedico EM ON EXM.EX_IdMedico = EM.EM_IdMedico AND EM.EM_CNPJ = EXM.EX_CPFCNPJDeposito
						            LEFT JOIN R_Medico M ON EXM.EX_IdMedico = M.M_Id
						            WHERE EX_IdRegistroImposto IS NULL";

        List<SqlParameter> param = new List<SqlParameter>();

        if (dataate.HasValue && datade.HasValue)
        {
          query = query + " AND Convert(date,EXM.EX_DataApuracao) BETWEEN Convert(date,@datade) AND Convert(date,@dataate)";

          param.Add(new SqlParameter("@datade", datade.Value));
          param.Add(new SqlParameter("@dataate", dataate.Value));
        }

        if (idMedico.HasValue && idMedico != 0)
        {
          query = query + " AND EXM.EX_IdMedico = @idMedico";
          param.Add(new SqlParameter("@idMedico", idMedico.Value));
        }

        if (idTipoImposto.HasValue && idTipoImposto != 0)
        {
          query = query + " AND TRI.TRI_Id = @idTipoImposto";
          param.Add(new SqlParameter("@idTipoImposto", idTipoImposto.Value));
        }

        return Contexto.Database.SqlQuery<ExtratoMedicoRegistroImpostoModel>(query, param.ToArray()).ToList();
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }

    public void RealizarPagamento(DateTime DataPagamento, List<int> ListIdRegistro)
    {
      ExtratoMedicoServices extratoMedicoServices = new ExtratoMedicoServices();

      foreach (var item in ListIdRegistro)
      {
        using (TransactionScope scope = new TransactionScope(TransactionScopeOption.RequiresNew))
        {
          #region[Registro Pagamento]
          R_RegistroImposto registroImposto = GetById(item);
          if (!registroImposto.RI_DataPagamento.HasValue)
          {
            registroImposto.RI_DataPagamento = DataPagamento;
            Edit(registroImposto);
            #endregion

            #region[Pagamento Extrato]
            extratoMedicoServices.RealizarPagamentoImposto(item, DataPagamento);
            #endregion
          }
          scope.Complete();
        }
      }
    }

    public void DeleteRegistros(List<int> idsRegistro)
    {
      using (var scope = new TransactionScope(TransactionScopeOption.RequiresNew))
      {
        foreach (int id in idsRegistro)
        {
          R_RegistroImposto registroImposto = GetById(id);

          if (registroImposto.RI_DataPagamento.HasValue)
            throw new CustomException(string.Format("Pagamento de imposto para {0} de R${1} já realizado, logo não pode ser excluído.", registroImposto.R_Medico.M_Nome, registroImposto.RI_Valor));

          new ExtratoMedicoServices().RemoveIdRegistroImposto(registroImposto.RI_Id);
          Delete(registroImposto);
        }

        scope.Complete();
      }
    }

    public List<ExtratoMedicoRegistroImposto> GetExtratos(int idRegistro)
    {
      string Query = @"SELECT
	                        EX.EX_Id Codigo,
	                        GA.GA_NroUnicooper NroGuiaAtendimento,
	                        EX.EX_Valor Valor,
	                        EX.EX_DataApuracao DataProcessamento,
	                        TRI.TRI_Enum TipoImposto
                        FROM R_ExtratoMedico EX
                        LEFT JOIN R_GuiaAtendimento GA ON GA.GA_Id=EX.EX_IdGuiaAtendimento
                        INNER JOIN R_RegistroImposto RI ON RI.RI_Id=EX.EX_IdRegistroImposto
                        INNER JOIN R_TipoRegistroImposto TRI ON TRI.TRI_Id=RI.RI_IdTipoImposto
                        WHERE EX.EX_IdRegistroImposto = @Id";

      return Contexto.Database.SqlQuery<ExtratoMedicoRegistroImposto>(Query, new SqlParameter("@Id", idRegistro)).ToList();
    }
  }
}
﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using iTextSharp.text;
using iTextSharp.text.pdf;
using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace RepasseConvenio.Infrastructure.Helpers
{
  public static class DirectoryHelper
  {
    public static string CreateDiretorioIfNotExist(string Caminho)
    {
      try
      {
        return Caminho;
      }
      catch (Exception ex)
      {
        throw ex;
      }
    }
  }
}
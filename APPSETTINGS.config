﻿<appSettings>
	<add key="URLBase" value="http://localhost/RepasseConvenio/" />

	<add key="ServidorSMTP" value="email-ssl.com.br" />
	<add key="UsuarioEmail" value="<EMAIL>" />
	<add key="SenhaEmail" value="MWSoftw@r32020" />
	<add key="FromEmail" value="<EMAIL>" />
	<add key="PortaEmail" value="587" />
	<add key="UsaSegEmail" value="true" />

	<add key="webpages:Version" value="*******" />
	<add key="webpages:Enabled" value="false" />
	<add key="ClientValidationEnabled" value="true" />
	<add key="UnobtrusiveJavaScriptEnabled" value="true" />

	<add key="QtdeRegistroPagina" value="20" />
	<add key="PrefixoDocumento" value="DOC" />
	<add key="PathProtocoloGlosas" value="D:\Backup\Projetos\RepasseConvenio\RepasseConvenio\Files\ProtocoloGlosa\" />
	<add key="PathReports" value="D:\Backup\Projetos\RepasseConvenio\RepasseConvenio\Reports\" />
	<add key="PathTempFiles" value="D:\Backup\Projetos\RepasseConvenio\RepasseConvenio\Files" />
	<add key="PathLogsEmail" value="D:\Backup\Projetos\RepasseConvenio\RepasseConvenio\TempFiles\PathTempFiles\LogsEmail" />

</appSettings>
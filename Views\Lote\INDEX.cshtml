﻿@using RepasseConvenio.Models
@using PagedList
@using PagedList.Mvc;
@model IPagedList<LoteModel>

@{
  ViewBag.Title = "Acompanhamento de lote";
}

@Scripts.Render("~/Views/Lote/Lote.js?ia=b")
@Styles.Render("~/Views/Lote/Lote.css")


<div class="row">
  <section class="col-lg-12 connectedSortable ui-sortable">

    <div class="card">
      <div class="card-header ui-sortable-handle">
        <div class="row" style="justify-content:space-between;">
          <h3 class="card-title">
            <i class="fas fa-book mr-1"></i>
             Acompanhamento de lote Faturado
          </h3>

        </div>
      </div>

      <div class="card-body">
        <div class="tab-content p-0">
          <div class="box-body">
            <div class="card-acoes col-2">
              <a class="btn btn-block btn-outline-primary disabled" id="Loteid"> Detalhes </a>
            </div>

            <div class="row">
              <div class="col-md-12 table-responsive p-0">
                <table id="TableTela" class="table-striped table table-sm table-hover  text-nowrap">
                  <thead>
                    <tr>
                      <th>

                      </th>
                      <th>
                        Número
                      </th>
                      <th>
                        Emissão
                      </th>
                      <th>
                        Vencimento
                      </th>

                      <th>
                        Status
                      </th>
                      <th>
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    @foreach (LoteModel item in Model)
                    {
                      <tr class="TrSelecionavel" data-codigoirpf="@item.Codigo">
                        <td>
                          @if (item.DataVencimento.HasValue)
                          {
                            if (item.DataVencimento < DateTime.Now)
                            {
                              <i class="fa fa-circle text-red" data-toggle="tooltip" data-placement="top" title="Vencido" style="color:#dd4b39 !important; cursor:pointer"></i>
                            }
                            if (item.DataVencimento > DateTime.Now)
                            {
                              <i class="fa fa-circle text-green" data-toggle="tooltip" data-placement="top" title="Dentro do prazo" style="color:#04e17c !important; cursor:pointer"></i>
                            }
                          }
                          else
                          {
                            <i class="fa fa-circle text-red" data-toggle="tooltip" data-placement="top" title="Vencido" style="color:#dd4b39 !important; cursor:pointer"></i>
                          }
                        </td>
                        <td>
                          @item.NumeroLote
                        </td>
                        <td>
                          @item.DataCriacao.ToString("dd/MM/yyyy")
                        </td>
                        <td>
                          @if (item.DataVencimento.HasValue)
                          {
                            @item.DataVencimento.Value.ToString("dd/MM/yyyy")
                          }
                          else
                          {
                            <text> - </text>
                          }
                        </td>
                        <td>
                          @item.StatusLote

                        </td>

                        @*<td>
                          <a href="@Url.Action("Detalhes", "Lote", new { id = item.Codigo })" class="btn btn-warning TIcones btn-circle" title="Detalhes">
                            <i class="fa fa-eye"></i>
                          </a>
                        </td>*@

                      </tr>
                    }
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="box-footer with-border" style="padding: 0 10pt;">
        <div class="row">
          <div class="col-md-8 ">
            @Html.PagedListPager(Model, page => Url.Action("Index", new { page }))
          </div>
        </div>
      </div>
    </div>
  </section>
</div>
@*Html.Partial("_ModalNovaGRD", new ModalNovaGRD())*@
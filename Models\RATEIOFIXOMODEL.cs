﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;

namespace RepasseConvenio.Models
{
  public class RateioFixoModel
  {
    public int Codigo { get; set; }
    public int CodigoRateio { get; set; }

    [DisplayName("Função")]
    public string Funcao { get; set; }
    public int CodigoMedico { get; set; }

    public decimal Percentual { get; set; }

    [DisplayName("Selecione o Médico")]
    [URLSelect("Select2/GetMedicoSelect")]
    [PlaceHolderAttr("Selecione o Médico")]
    public Select2Model MedicoSelect
    {
      get
      {
        MedicoService MedicoService = new MedicoService();
        return MedicoService.GetById(this.CodigoMedico).ToSelect2Model();
      }
      set
      {
        if (value != null && !String.IsNullOrEmpty(value.id) && !value.id.Equals("0"))
          this.CodigoMedico = int.Parse(value.id);
      }
    }
  }

  public class RateioFixoIndex
  {
    public int Codigo { get; set; }
    public string Procedimento { get; set; }
    public string Hospital { get; set; }
    public string NomeMedico { get; set; }
    public string CRMMedico { get; set; }
    public string Funcao { get; set; }
    public decimal Percentual { get; set; }
  }

  public static class RateioFixoModelConversion
  {

    public static R_RateioFixo toRateioFixoCreate(this RateioFixoModel model)
    {
      R_RateioFixo entity = new R_RateioFixo();

      entity.RF_IdMedico = model.CodigoMedico;
      entity.RF_IdRateioMedico = model.CodigoRateio;
      entity.RF_Funcao = model.Funcao;
      entity.RF_Percentual = model.Percentual;
      return entity;
    }
    public static R_RateioFixo toRateioFixoEdit(this RateioFixoModel model)
    {
      R_RateioFixo entity = new R_RateioFixo();
      entity.RF_Id = model.Codigo;
      entity.RF_IdMedico = model.CodigoMedico;
      entity.RF_IdRateioMedico = model.CodigoRateio;
      entity.RF_Funcao = model.Funcao;
      entity.RF_Percentual = model.Percentual;

      return entity;

    }
  }
}
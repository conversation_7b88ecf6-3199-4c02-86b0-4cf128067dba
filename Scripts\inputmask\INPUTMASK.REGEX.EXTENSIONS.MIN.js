/*!
* inputmask.regex.extensions.min.js
* https://github.com/RobinHerbots/Inputmask
* Copyright (c) 2010 - 2017 <PERSON>
* Licensed under the MIT license (http://www.opensource.org/licenses/mit-license.php)
* Version: 3.3.7
*/

!function(a){"function"==typeof define&&define.amd?define(["./dependencyLibs/inputmask.dependencyLib","./inputmask"],a):"object"==typeof exports?module.exports=a(require("./dependencyLibs/inputmask.dependencyLib"),require("./inputmask")):a(window.dependencyLib||jQuery,window.Inputmask)}(function(a,b){return b.extendAliases({Regex:{mask:"r",greedy:!1,repeat:"*",regex:null,regexTokens:null,tokenizer:/\[\^?]?(?:[^\\\]]+|\\[\S\s]?)*]?|\\(?:0(?:[0-3][0-7]{0,2}|[4-7][0-7]?)?|[1-9][0-9]*|x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4}|c[A-Za-z]|[\S\s]?)|\((?:\?[:=!]?)?|(?:[?*+]|\{[0-9]+(?:,[0-9]*)?\})\??|[^.?*+^${[()|\\]+|./g,quantifierFilter:/[0-9]+[^,]/,isComplete:function(a,b){return new RegExp(b.regex,b.casing?"i":"").test(a.join(""))},definitions:{r:{validator:function(b,c,d,e,f){function g(a,b){this.matches=[],this.isGroup=a||!1,this.isQuantifier=b||!1,this.quantifier={min:1,max:1},this.repeaterPart=void 0}function h(b,c){var d=!1;c&&(l+="(",n++);for(var e=0;e<b.matches.length;e++){var g=b.matches[e];if(!0===g.isGroup)d=h(g,!0);else if(!0===g.isQuantifier){var j=a.inArray(g,b.matches),k=b.matches[j-1],m=l;if(isNaN(g.quantifier.max)){for(;g.repeaterPart&&g.repeaterPart!==l&&g.repeaterPart.length>l.length&&!(d=h(k,!0)););d=d||h(k,!0),d&&(g.repeaterPart=l),l=m+g.quantifier.max}else{for(var o=0,p=g.quantifier.max-1;o<p&&!(d=h(k,!0));o++);l=m+"{"+g.quantifier.min+","+g.quantifier.max+"}"}}else if(void 0!==g.matches)for(var q=0;q<g.length&&!(d=h(g[q],c));q++);else{var r;if("["==g.charAt(0)){r=l,r+=g;for(var s=0;s<n;s++)r+=")";var t=new RegExp("^("+r+")$",f.casing?"i":"");d=t.test(i)}else for(var u=0,v=g.length;u<v;u++)if("\\"!==g.charAt(u)){r=l,r+=g.substr(0,u+1),r=r.replace(/\|$/,"");for(var s=0;s<n;s++)r+=")";var t=new RegExp("^("+r+")$",f.casing?"i":"");if(d=t.test(i))break}l+=g}if(d)break}return c&&(l+=")",n--),d}var i,j,k=c.buffer.slice(),l="",m=!1,n=0;null===f.regexTokens&&function(){var a,b,c=new g,d=[];for(f.regexTokens=[];a=f.tokenizer.exec(f.regex);)switch(b=a[0],b.charAt(0)){case"(":d.push(new g(!0));break;case")":j=d.pop(),d.length>0?d[d.length-1].matches.push(j):c.matches.push(j);break;case"{":case"+":case"*":var e=new g(!1,!0);b=b.replace(/[{}]/g,"");var h=b.split(","),i=isNaN(h[0])?h[0]:parseInt(h[0]),k=1===h.length?i:isNaN(h[1])?h[1]:parseInt(h[1]);if(e.quantifier={min:i,max:k},d.length>0){var l=d[d.length-1].matches;a=l.pop(),a.isGroup||(j=new g(!0),j.matches.push(a),a=j),l.push(a),l.push(e)}else a=c.matches.pop(),a.isGroup||(j=new g(!0),j.matches.push(a),a=j),c.matches.push(a),c.matches.push(e);break;default:d.length>0?d[d.length-1].matches.push(b):c.matches.push(b)}c.matches.length>0&&f.regexTokens.push(c)}(),k.splice(d,0,b),i=k.join("");for(var o=0;o<f.regexTokens.length;o++){var p=f.regexTokens[o];if(m=h(p,p.isGroup))break}return m},cardinality:1}}}}),b});
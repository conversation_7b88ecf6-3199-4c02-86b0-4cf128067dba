﻿@using RepasseConvenio.Models
@model List<EmpresaMedicoModel>

@{
  ViewBag.Title = "Lista Empresa Médico";
}

<div class="row">
  <section class="col-lg-12 connectedSortable ui-sortable">
    <div class="card">
      <div class="card-header ui-sortable-handle">
        <h3 class="card-title">
          <i class="fas fa-paperclip mr-1"></i>
          Empresa Médico
        </h3>
        <div class="card-tools">
        </div>
      </div><!-- /.card-header -->
      <div class="card-body">
        <div class="tab-content p-0">
          <div class="box-body">
            @*<button type="button" value="Create" class="btn btn-primary pull-rigth" onclick="location.href = '@Url.Action("Create", "RateioMedico", new {codMedico = @ViewBag.CodigoMedico})'">Novo</button>*@
            <div class="row">
              <div class="col-md-12">
                <table class="table table-hover table-striped">
                  <thead>
                    <tr>
                      <th>
                        Razão Social
                      </th>
                      <th>
                        CNPJ
                      </th>
                      @*<th>
                      </th>*@
                    </tr>
                  </thead>
                  <tbody>
                    @foreach (EmpresaMedicoModel item in Model)
                    {
                      <tr>
                        <td>
                          @item.RazaoSocial
                        </td>
                        <td>
                          @item.CNPJ
                        </td>
                        @*<td>
                          <a href="@Url.Action("Index", "RateioFixo", new { codRateio = item.Codigo })" class="btn btn-warning" title="Rateio Fixo">
                            Rateio Fixo
                          </a>
                          <a href="@Url.Action("Create", "RegraCartaConversao", new { CodigoEmpresaMedico = item.Codigo })" class="btn btn-warning" title="Configurar Carta de Emissão">
                            Configurar Carta de Conversão
                          </a>
                        </td>*@
                      </tr>
                    }
                  </tbody>
                </table>
              </div>
            </div>
            <hr />
            <div class="row">
              <div class="col-md-3">
                <button type="button" value="Voltar" class="btn btn-secondary" onclick="location.href='@Url.Action("Index", "Medico")'">Voltar</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>
﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace RepasseConvenio.Controllers
{

 [Authorize]
 [Security("36BDA6AF-BD29-460A-B212-FA86D5166CE2")]
  public class EmpresaMedicoController : LibController
  {
    [Security("BD4D7AD2-ED55-445D-8373-19D60E7DC3D1")]
    public ActionResult Index(int id)
    {
      try
      {
        List<EmpresaMedicoModel> empresa = new EmpresaMedicoService().GetbyIdMedico(id);

        return View(empresa);
      }
      catch (Exception)
      {
        throw;
      }
    }
   
  }
}
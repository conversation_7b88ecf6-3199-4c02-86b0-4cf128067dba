﻿@using RepasseConvenio.Models
@model List<LogLoteGlosa>

<table class="table table-sm table-striped table-hover text-nowrap">
  <thead>
    <tr>
      <th>
        Data
      </th>
      <th>
        Executado por
      </th>
      <th>
        Descrição
      </th>
      <th>
        Tipo
      </th>
    </tr>
  </thead>
  <tbody>
    @foreach (LogLoteGlosa item in Model)
    {
    <tr>
      <td>
        @item.Data.ToString("dd/MM/yyyy HH:mm")
      </td>
      <td>
        @item.NomeUsuario
      </td>
      <td>
        @item.Log
      </td>
      <td>
        @RepasseConvenio.Infrastructure.Helpers.EnumHelper.Description(item.TipoLog)
      </td>
    </tr>
    }
  </tbody>
</table>

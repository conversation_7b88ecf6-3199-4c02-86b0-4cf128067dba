﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using PagedList;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Web;

namespace RepasseConvenio.Models
{
  public class ProtocoloGlosaIndexCabecalho
  {
    public int IdLoteGlosa { get; set; }
    public IPagedList<int> QuantidadeProtocoloGlosa { get; set; }

    public List<ProtocoloGlosaIndex> ListaProtocoloGlosaIndex { get; set; }
  }

  public class ProtocoloGlosaIndex
  {
    public int Codigo { get; set; }

    public string NumeroProtocolo { get; set; }

    public DateTime DataEmissao { get; set; }

    public DateTime DataPrazo { get; set; }

    public DateTime DataCriacao { get; set; }
  }

  public class ProtocoloGlosaCreate
  {
    public HttpPostedFileBase[] Files { get; set; }
    public int CodigoLoteGlosa { get; set; }
    [DisplayName("Número Protocolo")]
    public string NumeroProtocolo { get; set; }
    [DisplayName("Emitida Em")]
    public DateTime? DataEmissao { get; set; }
    [DisplayName("Prazo Retorno Recurso")]
    public DateTime? DataPrazo { get; set; }
    [DisplayName("Observaçõs Gerais")]
    public string ObservacoesGerais { get; set; }
  }

  public class ProtocoloGlosaDetalhes
  {
    [DisplayName("Número Protocolo")]
    public string NumeroProtocolo { get; set; }
    [DisplayName("Emitida Em")]
    public DateTime DataEmissao { get; set; }
    [DisplayName("Prazo Retorno Recurso")]
    public DateTime DataPrazo { get; set; }
    [DisplayName("Observaçõs Gerais")]
    public string ObservacoesGerais { get; set; }
    public int CodigoLoteGlosa { get; set; }
    public List<AnexoProtocoloGlosaDetalhes> ListaAnexoProtocoloGlosaDetalhes { get; set; }
  }


  public static class ProtocoloGlosaConversions
  {
    public static R_ProtocoloGlosa ModelToEntityCreate(this ProtocoloGlosaCreate protocoloGlosaCreate, int IdUsuario)
    {
      return new R_ProtocoloGlosa()
      {
        PG_DataCriacao = DateTime.Now,
        PG_DataEmissao = protocoloGlosaCreate.DataEmissao.Value,
        PG_DataPrazoRetornoRecurso = protocoloGlosaCreate.DataPrazo.Value,
        PG_NumeroProtocolo = protocoloGlosaCreate.NumeroProtocolo,
        PG_ObservacoesGerais = protocoloGlosaCreate.ObservacoesGerais,
        PG_IdUsuarioCriacao = IdUsuario,
        PG_IdLoteGlosa = protocoloGlosaCreate.CodigoLoteGlosa
      };
    }
  }
}
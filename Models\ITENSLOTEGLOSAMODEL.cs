﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Principal;
using System.Web;

namespace RepasseConvenio.Models
{
  public class ItensLoteGlosaIndex
  {
    public int CodigoProcDemonstrativo { get; set; }

    public string NumeroGuia { get; set; }

    public string CodigoGlosa { get; set; }

    public string DescricaoGlosa { get; set; }

    public decimal TotalApresentado { get; set; }

    public decimal TotalFaturado { get; set; }

    public decimal TotalGlosado { get; set; }

    public decimal TotalPago { get; set; }

    public string CodigoProcedimento { get; set; }

    public string DescricaoProcedimento { get; set; }

    public string Justificativa { get; set; }

    public int CodigoGuiaAtendimento { get; set; }

    public string DescricaoStatus { get; set; }
  }

  public class ItensLoteMotivoGlosaJustificativa
  {
    public string CodigoGlosa { get; set; }
    public string CodigoJustificativa { get; set; }
  }


  public static class ItensLoteGlosaConversions
  {
    public static R_ItensLoteGlosa ModelToEntityCreate(this LoteGlosaProcessa loteGlosaProcessa, int IdLoteGlosa, int IdRepasse)
    {
      return new R_ItensLoteGlosa()
      {
        ILG_IdGuiaAtendimento = loteGlosaProcessa.IdGuiaAtendimento,
        ILG_IdGuiaDemonstrativo = loteGlosaProcessa.IdGuiaDemonstrativo,
        ILG_IdLoteGlosa = IdLoteGlosa,
        ILG_IdRepasse = IdRepasse
      };
    }
  }
}
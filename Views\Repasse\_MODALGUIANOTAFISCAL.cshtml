﻿@using RepasseConvenio.Models
@*Modal Nota Fiscal*@
<div class="modal fade" id="ModalGuiaNotaFiscal" data-keyboard="false" data-backdrop="static" style="z-index:1051;">
  <div class="modal-dialog" style="max-width: 90%;">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">Nota Fiscal</h4>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">×</span>
        </button>
      </div>
      <div class="modal-body" style="height: calc(100vh - 150px);">
        <div class="row">
          <div class="col-md-3">
            <div class="form-group">
              <label for="NumeroGuia">Nro Guia</label>
              <input class="form-control numeros text-box single-line" id="NumeroGuia" name="NumeroGuia" type="text">
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label for="NumeroLote">Nro Lote</label>
              <input class="form-control numeros text-box single-line" id="NumeroLote" name="NumeroLote" type="text">
            </div>
          </div>
          <div class="col-md-1" style=" display: flex; align-items: center;">
            <button type="button" class="btn btn-info" id="PesquisarLoteGuia">Pesquisar</button>
          </div>
        </div>

        <div class="row" style="overflow-x: scroll">
          <div class="col-md-12 container">
            <div id="PartialGuiaNotaFiscal" style="display: flex;">

            </div>
          </div>
        </div>
      </div>

      <div class="box-footer">
        <button type="button" class="btn btn-outline-dark pull-left" data-dismiss="modal">Cancelar</button>
        <button type="button" value="Create" class="btn btn-info pull-right" id="btnCreateGuiaNotaFiscal">Salvar</button>
      </div>
    </div>
  </div>
</div>
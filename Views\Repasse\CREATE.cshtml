﻿@using RepasseConvenio.Infrastructure.Controls
@using RepasseConvenio.Models
@model R_RepasseModel

@{
  ViewBag.Title = "Novo Repasse";
  ViewBag.DescricaoTela = "Repasse";
  ViewBag.ResumoTela = "Novo Repasse";
  Layout = "~/Views/Shared/_Layout.cshtml";
}
@Styles.Render("~/Views/Repasse/Repasse.css?lp=tgb")

@using (Html.BeginForm("Create", "Repasse", FormMethod.Post, new { enctype = "multipart/form-data" }))
{
  @Html.AntiForgeryToken()
  @Html.ValidationSummary(true, "", new { @class = "text-danger" })

  <div class="row">
    <section class="col-lg-12 connectedSortable ui-sortable">
      <div class="card">
        <div class="card-header ui-sortable-handle">
          <h3 class="card-title">
            <i class="fas fa-chart-pie mr-1"></i>
            Repasses
          </h3>
          <div class="card-tools">
          </div>
        </div><!-- /.card-header -->
        <div class="card-body">
          <div class="row">
            @*<div class="col-md-2">
              <div class="form-group">
                @Html.LabelFor(model => model.Numero)
                @Html.EditorFor(model => model.Numero, new { htmlAttributes = new { @class = "form-control numeros" } })
                @Html.ValidationMessageFor(model => model.Numero, "", new { @class = "text-danger" })
              </div>
            </div>*@
            <div class="col-md-2">
              <div class="form-group">
                @Html.LabelFor(model => model.DataInicio)
                @Html.EditorFor(model => model.DataInicio, new { htmlAttributes = new { @class = "form-control airDatePickerDate Data" } })
                @Html.ValidationMessageFor(model => model.DataInicio, "", new { @class = "text-danger" })
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                @Html.LabelFor(m => m.ResponsavelSelect)
                @Html.LibSelect2For(m => m.ResponsavelSelect, new { @class = "form-control" })
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                @Html.LabelFor(m => m.ConvenioSelect)
                @Html.LibSelect2For(m => m.ConvenioSelect, new { @class = "form-control" })
              </div>
            </div>
            @*<div class="col-md-2 divcheck">
                <div class="checkbox form-group">
                  @Html.CheckBoxFor(m => m.NF, new { @class = "icheck" })
                  @Html.LabelFor(m => m.NF)
                </div>
              </div>
              <div class="col-md-2 divcheck">
                <div class="checkbox form-group">
                  @Html.CheckBoxFor(m => m.Valorizacao, new { @class = "icheck" })
                  @Html.LabelFor(m => m.Valorizacao)
                </div>
              </div>
              <div class="col-md-2 divcheck">
                <div class="checkbox form-group">
                  @Html.CheckBoxFor(m => m.Deposito, new { @class = "icheck" })
                  @Html.LabelFor(m => m.Deposito)
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  @Html.LabelFor(m => m.StatusRepasseSelect)
                  @Html.LibSelect2For(m => m.StatusRepasseSelect, new { @class = "form-control" })
                </div>
              </div>*@
          </div>
        </div><!-- /.card-body -->
        <div class="card-footer">
          <button type="button" value="Voltar" class="btn btn-secondary" onclick="window.history.back();">Cancelar</button>
          <button type="submit" value="Create" class="btn btn-info pull-rigth">Salvar</button>
        </div>
      </div>
    </section>
  </div>

}

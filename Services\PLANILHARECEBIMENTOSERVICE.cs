﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

//using RepasseConvenio.Models;
//using RepasseConvenio.WSPortalCooperado;
//using System;
//using System.Collections;
//using System.Collections.Generic;
//using System.Configuration;
//using System.Data;
//using System.Data.Entity;
//using System.Data.SqlClient;
//using System.IO;
//using System.Linq;
//using System.Text;
//using System.Transactions;
//using System.Web;
//using System.Data.OleDb;
//using RepasseConvenio.Infrastructure;
//using Microsoft.Ajax.Utilities;

//namespace RepasseConvenio.Services
//{
//  public class PlanilhaRecebimentoService : ServiceBase
//  {
//    public PlanilhaRecebimentoService()
//   : base()
//    { }
//    public PlanilhaRecebimentoService(RepasseEntities Contexto)
//       : base(Contexto)
//    { }

//    public PlanilhaRecebimentoService(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
//    {
//    }

//    public PlanilhaRecebimentoService(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
//           : base(UsuarioLogado, Contexto)
//    { }

//    public List<PlanilhaRecebimentoModel> GetAllPlanilhaRecebimentoModel(int IdRepasse)
//    {
//      string query = @"SELECT
//                       PR_Id AS PRM_Codigo
//                      ,PR_Nome AS PRM_Nome
//                      ,PR_NumeroGuia AS PRM_NumeroGuia
//                      ,PR_ValorApresentado AS PRM_ValorApresentado
//                      ,PR_TotalFaturado AS PRM_TotalFaturado
//                      ,PR_TotalGlosado AS PRM_TotalGlosado
//                      ,PR_TotalPago AS PRM_TotalPago
//                      ,PR_IdRepasse AS PRM_CodigoRepasse
//                      ,PR_DataCriacao AS PRM_DataCriacao
//                      ,PR_IdUsuarioCriacao AS PRM_CodigoUsuarioCriacao
//                      ,RP_DataAlteracao AS PRM_DataAlteracao
//                      ,RP_IdUsuarioAlteracao AS PRM_CodigoUsuarioAlteracao
//                      ,PR_Processado AS PRM_Processado
//                      ,PR_DataProcessamento AS PRM_DataProcessamento
//                      ,PR_ObservacaoProcessamento AS PRM_ObsProcessamento
//                      FROM R_PlanilhaRecebimento
//                      WHERE PR_IdRepasse = @IdRepasse
//                      ";

//      return Contexto.Database.SqlQuery<PlanilhaRecebimentoModel>(query, new SqlParameter("@IdRepasse", IdRepasse)).ToList();
//    }

//    public PlanilhaRecebimentoModel GetRepasseModelById(int Id)
//    {
//      string query = @"SELECT
//                       PR_Id AS PRM_Codigo
//                      ,PR_Nome AS PRM_Nome
//                      ,PR_NumeroGuia AS PRM_NumeroGuia
//                      ,PR_TotalFaturado AS PRM_TotalFaturado
//                      ,PR_TotalGlosado AS PRM_TotalGlosado
//                      ,PR_TotalPago AS PRM_TotalPago
//                      ,PR_IdRepasse AS PRM_CodigoRepasse
//                      ,PR_DataCriacao AS PRM_DataCriacao
//                      ,PR_IdUsuarioCriacao AS PRM_CodigoUsuarioCriacao
//                      ,RP_DataAlteracao AS PRM_DataAlteracao
//                      ,RP_IdUsuarioAlteracao AS PRM_CodigoUsuarioAlteracao
//                      FROM R_PlanilhaRecebimento
//                      WHERE 
//                      PR_Id = @Id
//                      ";

//      return Contexto.Database.SqlQuery<PlanilhaRecebimentoModel>(query, new SqlParameter("@Id", Id)).FirstOrDefault();
//    }
//    public void EditToProcessamento(R_PlanilhaRecebimento planilha, bool processado, string observacao)
//    {
//      planilha.PR_ObservacaoProcessamento = observacao;
//      planilha.PR_Processado = processado;
//      planilha.PR_DataProcessamento = DateTime.Now;
//      Edit(planilha);
//    }
//    public R_PlanilhaRecebimento GetById(int id)
//    {
//      return Contexto.R_PlanilhaRecebimento.Where(a => a.PR_Id == id).FirstOrDefault();
//    }

//    public List<PlanilhaRecebimentoDelete> GetAllToDelete(List<int> ListaIdItensDelete)
//    {
//      string query = @"SELECT
//                        PR_Id [Codigo],
//                        PR_NumeroGuia [NumeroGuia]
//                       FROM R_PlanilhaRecebimento
//                       [condicao]";

//      List<string> ListaCondicao = new List<string>();
//      foreach (int ItItenDelete in ListaIdItensDelete)
//      {
//        ListaCondicao.Add(string.Format(" PR_Id = {0} ", ItItenDelete));
//      }

//      string condicao = "Where " + string.Join("OR", ListaCondicao.ToArray());
//      query = query.Replace("[condicao]", condicao);

//      return Contexto.Database.SqlQuery<PlanilhaRecebimentoDelete>(query).ToList();
//    }

//    public List<R_PlanilhaRecebimento> GetByIdRepasse(int id)
//    {
//      return Contexto.R_PlanilhaRecebimento.Where(a => a.PR_IdRepasse == id).ToList();
////    }
//public List<R_PlanilhaRecebimento> GetByIdRepasse(int id, bool processado)
//{
//  if (processado)
//    return Contexto.R_PlanilhaRecebimento.Where(a => a.PR_IdRepasse == id && a.PR_Processado == processado).ToList();
//  else
//    return Contexto.R_PlanilhaRecebimento.Where(a => a.PR_IdRepasse == id && (!a.PR_Processado.HasValue || a.PR_Processado == processado)).ToList();
//}

//    public R_PlanilhaRecebimento Get(string NumAtendimento, string CarteiraBeneficiario, DateTime DataAtendimento)
//    {
//      return Contexto.R_PlanilhaRecebimento.Where(a => (!string.IsNullOrEmpty(a.PR_NumAtendimento) && a.PR_NumAtendimento.Equals(NumAtendimento)) &&
//                                                 (!string.IsNullOrEmpty(a.PR_CarteiraBeneficiario) && a.PR_CarteiraBeneficiario.Equals(CarteiraBeneficiario)) &&
//                                                 (a.PR_DataAtendimento.HasValue && a.PR_DataAtendimento == DataAtendimento))
//                                                 .FirstOrDefault();
//    }

//    public void Create(PlanilhaRecebimentoModel repasseModel)
//    {
//      R_PlanilhaRecebimento entity = repasseModel.ToPlanilhaRecebimentoCreate(User.IdUsuario);
//      Create(entity);
//    }
//    public void Edit(PlanilhaRecebimentoModel repasseModel)
//    {
//      R_PlanilhaRecebimento entity = GetById(repasseModel.PRM_Codigo);
//      if (entity != null)
//      {
//        if (entity.PR_Processado == true)
//          throw new CustomException("Não é possível atualizar, planilha já processada.");

//        entity = repasseModel.ToPlanilhaRecebimentoEdit(entity);
//        Edit(entity);
//      }
//    }
//    public void Delete(List<int> IdItensToDelete, int IdRepasse)
//    {
//      ExtratoMedicoServices extratoMedicoServices = new ExtratoMedicoServices();
//      bool haveExtrato = extratoMedicoServices.IfHaveExtratoMedicoByRepasse(IdRepasse.ToString());

//      if (haveExtrato)
//        throw new CustomException("Você deve deletar os extratos antes de excluir uma planilha de recebimento.");

//      List<PlanilhaRecebimentoDelete> ListaPlanilhaRecebimentoDelete = GetAllToDelete(IdItensToDelete);

//      foreach (PlanilhaRecebimentoDelete planilhaRecebimentoDelete in ListaPlanilhaRecebimentoDelete)
//      {
//        RepasseRetornoWS retorno = new RepasseRetornoWS();

//        if (!string.IsNullOrEmpty(planilhaRecebimentoDelete.NumeroGuia))
//        {
//          IntegraRepasse servico = new IntegraRepasse();
//          IntegracoesGuiasRepasseWS parameter = new IntegracoesGuiasRepasseWS();
//          parameter.MarcarIntegracao = false;
//          parameter.NroGuias = new string[1];
//          parameter.NroGuias[0] = planilhaRecebimentoDelete.NumeroGuia;
//          retorno = servico.IntegracaoGuiaRepasse(parameter);
//        }

//        if (!retorno.Erro)
//        {
//          using (var scope = new TransactionScope(TransactionScopeOption.RequiresNew))
//          {
//            DeleteDetalhePlanilhaRecebimentoByIdPlanilha(id);
//            DeleteComandSql(planilhaRecebimentoDelete.Codigo);
//            scope.Complete();
//          }
//        }
//      }
//    }
//    public void DeleteDetalhePlanilhaRecebimentoByIdPlanilha(int id)
//    {
//      List<R_DetalhePlanilhaRecebimento> list = Contexto.R_DetalhePlanilhaRecebimento.Where(a => a.DP_IdPlanilhaRecebimento == id).ToList();

//      foreach (R_DetalhePlanilhaRecebimento item in list)
//        Delete(item);

//    }
//    public void DeleteComandSql(int IdPlanilhaRecebimento)
//    {
//      string query = @"DELETE FROM R_PlanilhaRecebimento WHERE PR_Id = @IdPlanilhaRecebimento";
//      Contexto.Database.ExecuteSqlCommand(query, new SqlParameter("@IdPlanilhaRecebimento", IdPlanilhaRecebimento));
//    }
//    public void ImportacaoPlanilha(Anexo anexo, int IdRepasse)
//    {
//      StringBuilder sb = new StringBuilder();
//      string Diretorio = ConfigurationManager.AppSettings["PathTempFiles"];
//      string name = anexo.FileName + "_" + DateTime.Now.ToString("ddMMyyyyHHmmss");
//      string outpath = Path.Combine(Diretorio, name);

//      using (Stream file = new FileStream(outpath, FileMode.OpenOrCreate))
//      {
//        file.Write(anexo.DocAnexo, 0, anexo.DocAnexo.Length);
//        file.Close();
//      }

//      string connectionString = ConfigurationManager.ConnectionStrings["DefaultConnection"].ConnectionString;
//      SqlConnection con = new SqlConnection(connectionString);

//      connectionString = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + outpath + ";Extended Properties=\"Excel 12.0;HDR=YES;IMEX=1\"";
//      using (var conn = new OleDbConnection(connectionString))
//      {
//        try
//        {
//          conn.Open();

//          var sheets = conn.GetOleDbSchemaTable(System.Data.OleDb.OleDbSchemaGuid.Tables, new object[] { null, null, null, "TABLE" });

//          if (sheets.Rows.Count > 0)
//          {
//            for (int i = 0; i < sheets.Rows.Count; i++)
//            {
//              try
//              {
//                var ds = new DataSet();

//                using (var cmd = conn.CreateCommand())
//                {
//                  cmd.CommandText = "SELECT * FROM [" + sheets.Rows[i]["TABLE_NAME"].ToString() + "] ";

//                  var adapter = new OleDbDataAdapter(cmd);
//                  adapter.Fill(ds);
//                }

//                con.Open();
//                int linha = 1;
//                #region Processando Planilha
//                foreach (DataRow item in ds.Tables[0].Rows)
//                {
//                  string query = "";
//                  PlanilhaRecebimentoModel planilha = new PlanilhaRecebimentoModel();
//                  #region Inicio -Pegando valores das colunas
//                  try
//                  {
//                    planilha.PRM_NumAtendimento = item["Numero_Atendimento"].ToString().Replace(".", "").Trim();
//                    planilha.PRM_CarteiraBeneficiario = item["Carteira_Beneficiario"].ToString().Replace(".", "").Trim();
//                    DateTime DataAtendimento;
//                    DateTime.TryParse(item["Data_Atendimento"].ToString().Replace(".", "").Trim(), out DataAtendimento);
//                    planilha.PRM_DataAtendimento = DataAtendimento == new DateTime(0001, 01, 01) ? (DateTime?)null : DataAtendimento;
//                    planilha. = item["Itens_Glosados"].ToString();

//                    decimal val;
//                    decimal.TryParse(item["Valor_Apresentado"].ToString().Replace("R$", "").Replace(" ", ""), out val);
//                    planilha.PRM_ValorApresentado = val;
//                    planilha.PRM_TotalGlosadoText = item["Valor_Glosado"].ToString().Trim();
//                    planilha.PRM_TotalPagoText = item["Valor_Pago"].ToString().Trim();

//                  }
//                  catch (Exception ex)
//                  {
//                    throw new Exception("Favor verificar os nomes das células do cabeçalho.");
//                  }
//                  #endregion Fim -Pegando valores das colunas

//                  #region Criando Item

//                  try
//                  {
//                    planilha.PRM_Nome = sheets.Rows[i]["TABLE_NAME"].ToString().Replace("'", "").Replace("$", "");
//                    planilha.PRM_NumeroGuia = "";
//                    planilha.PRM_CodigoRepasse = IdRepasse;
//                    planilha.PRM_CodigoUsuarioCriacao = User.IdUsuario;

//                    if (PreenchimentoCorreto(planilha))
//                    {
//                      R_PlanilhaRecebimento planEntity = Get(planilha.PRM_NumAtendimento, planilha.PRM_CarteiraBeneficiario, planilha.PRM_DataAtendimento.Value);

//                      if (planEntity == null)
//                        Create(planilha);
//                      else
//                      {
//                        planilha.PRM_CodigoUsuarioAlteracao = User.IdUsuario;
//                        planilha.PRM_DataAlteracao = DateTime.Now;
//                        planEntity = planilha.ToPlanilhaRecebimentoEditImportacaoPlan(planEntity);
//                        Edit(planEntity);
//                      }
//                    }
//                  }
//                  catch (Exception ex)
//                  {
//                    throw new Exception("Favor verificar a planilha pois alguns dados apresentam formato inválido.");
//                  }

//                  #endregion fim - Insert

//                  linha++;
//                }
//                #endregion

//              }
//              catch (Exception ex)
//              {
//                throw ex;
//              }
//              finally
//              {
//                con.Close();
//              }
//            }
//          }
//        }
//        catch (Exception ex)
//        {
//          throw ex;
//        }
//        finally
//        {
//          con.Close();
//          conn.Close();
//        }
//      }
//    }
//    public bool PreenchimentoCorreto(PlanilhaRecebimentoModel planilha)
//    {
//      if (string.IsNullOrEmpty(planilha.PRM_NumAtendimento))
//        return false;
//      else
//      if (string.IsNullOrEmpty(planilha.PRM_CarteiraBeneficiario))
//        return false;
//      else
//      if (!planilha.PRM_DataAtendimento.HasValue)
//        return false;
//      else
//      if (!planilha.PRM_ValorApresentado.HasValue)
//        return false;

//      return true;
//    }

//    public void RemoveStatusProcessada(int idRepasse)
//    {
//      string query = @"update R_PlanilhaRecebimento set PR_Processado = 0 WHERE PR_IdRepasse = @idRepasse
//                       update R_PlanilhaRecebimento set PR_DataProcessamento = NULL WHERE PR_IdRepasse = @idRepasse
//                       update R_PlanilhaRecebimento set PR_ObservacaoProcessamento = NULL WHERE PR_IdRepasse = @idRepasse";

//      Contexto.Database.ExecuteSqlCommand(query, new SqlParameter("@idRepasse", idRepasse));
//    }
//  }
//}
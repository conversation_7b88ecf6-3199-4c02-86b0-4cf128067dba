﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace RepasseConvenio.Models
{
  [DisplayName("Descrição")]
  public class TabelaProgressivaIRPFModel
  {

    public TabelaProgressivaIRPFModel()
    {
      ValoresTabelaIRPF = new List<ValoresTabelaProgressivaIRPFModel>();
    }
    public int Codigo { get; set; }



    [Required]
    [DisplayName("Início da Vigência")]
    public DateTime DtInicioVigencia { get; set; }

    [DisplayName("Fim da Vigência")]
    public DateTime? DtFimVigencia { get; set; }

    [Required]
    [DisplayName("Tabela de IRPF")]
    public string Descricao { get; set; }
    public List<ValoresTabelaProgressivaIRPFModel> ValoresTabelaIRPF { get; set; }

  }
  public static class TabelaProgressivaIRPFModelConversion
  {
    public static R_TabelaProgressivaIRPF ToEntityIREdit(this R_TabelaProgressivaIRPF entity, TabelaProgressivaIRPFModel model)
    {
      entity.T_Descricao = model.Descricao;
      entity.T_DtInicioVigencia = model.DtInicioVigencia;
      entity.T_DtFimVigencia = model.DtFimVigencia;

      return entity;
    }
    public static R_TabelaProgressivaIRPF ToEntityIRCreate(this TabelaProgressivaIRPFModel model)
    {
      R_TabelaProgressivaIRPF entity = new R_TabelaProgressivaIRPF();
      entity.T_Descricao = model.Descricao;
      entity.T_DtInicioVigencia = model.DtInicioVigencia;
      entity.T_DtFimVigencia = model.DtFimVigencia;


      return entity;
    }
    public static TabelaProgressivaIRPFModel OffEntityIRToModel(this R_TabelaProgressivaIRPF entity)
    {
      TabelaProgressivaIRPFModel model = new TabelaProgressivaIRPFModel();
      model.Codigo = entity.T_Id;
      model.Descricao = entity.T_Descricao;
      model.DtInicioVigencia = entity.T_DtInicioVigencia;
      model.DtFimVigencia = entity.T_DtFimVigencia;
      model.Descricao = entity.T_Descricao;
      //model.ValorInicial = entity.T_ValorInicial;
      //model.ValorFinal = entity.T_ValorFinal;
      //model.AliquotaProgressiva = entity.T_AliquotaProgressiva;

      return model;
    }
  }
}
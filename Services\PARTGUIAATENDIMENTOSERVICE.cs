﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Models;
using RepasseConvenio.WSPortalCooperado;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.Entity;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Text;
using System.Transactions;
using System.Web;
using System.Data.OleDb;
using RepasseConvenio.Infrastructure;
using Microsoft.Ajax.Utilities;

namespace RepasseConvenio.Services
{
  public class PartGuiaAtendimentoService : ServiceBase
  {
    public PartGuiaAtendimentoService()
   : base()
    { }
    public PartGuiaAtendimentoService(RepasseEntities Contexto)
       : base(Contexto)
    { }

    public PartGuiaAtendimentoService(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public PartGuiaAtendimentoService(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public void IntegraPartGuiaAtendimento(ParticipacaoGuiaRepasseWS participacaoGuiaRepasseWS, int IdGuiaAtendimento, string CPFCooperado)
    {
      MedicoService medicoService = new MedicoService();
      int IdMedico = medicoService.GetIdMedicoByCPF(CPFCooperado);
      R_PartGuiaAtendimento partGuiaAtendimento = participacaoGuiaRepasseWS.ToPartGuiaAtendimentoCreate(IdGuiaAtendimento, IdMedico);
      Create(partGuiaAtendimento);
    }
  }
}
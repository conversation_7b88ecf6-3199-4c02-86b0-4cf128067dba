﻿@using RepasseConvenio.Models
@using RepasseConvenio.Infrastructure.Controls
@model JustificativaItemGlosaModel

<div class="modal fade" id="ModalAnaliseLote" data-keyboard="false" data-backdrop="static">
  <div class="modal-dialog" style="max-width: 50%;">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">Justificar Item da Glosa</h4>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">×</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="col-md-6">
          <div class="form-group">
            @Html.LabelFor(m => m.JustificativaSelect)
            <br />
            @Html.LibSelect2For(m => m.JustificativaSelect, new { @class = "form-control" })
          </div>
        </div>
        <div class="col-md-8">
          <div class="form-group">
            @Html.LabelFor(m => m.Comentario)
            @Html.TextAreaFor(m => m.Comentario, new { @class = "form-control", @maxlength = "512", @style = "height: auto !important" })
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary SalvarJustificativaLote" >Justificar Glosa</button>
        <button type="button" class="btn btn-primary AceitarGlosaLote">Aceitar Glosa</button>
      </div>
    </div>
  </div>
</div>
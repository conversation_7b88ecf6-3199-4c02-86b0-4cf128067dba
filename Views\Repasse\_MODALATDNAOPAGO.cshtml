﻿@using RepasseConvenio.Infrastructure.Controls
@using RepasseConvenio.Models
@using System.Globalization;
@model GlosarNaoPago

<div class="modal fade" id="ModalAtdNaoPago" data-keyboard="false" data-backdrop="static">
  <div class="modal-dialog" style="max-width: 90%;">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">Atendimentos Não Pagos</h4>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">×</span>
        </button>
      </div>
      <div class="modal-body">
      </div>
      <div class="modal-footer">
        <div class="row">
          <div class="form-group" style="max-width: 400px">
            @Html.LabelFor(m => m.MotivoSelect)
            <br />
            @Html.LibSelect2For(m => m.MotivoSelect, new { @class = "form-control" })
          </div>
          <div class="form-group">
            @Html.LabelFor(a => a.DataInicioGlosa)
            @Html.EditorFor(a => a.DataInicioGlosa, new { htmlAttributes = new { @class = "form-control airDatePickerDate Data", @readonly = "false" } })
          </div>
        </div>
        <button class="btn btn-outline-primary" type="button" id="GerarGlosaNaoPg">Gerar Glosas</button>
      </div>
    </div>
  </div>
</div>

<style>
  #ModalAtdNaoPago .form-group {
    margin-right: 20px;
  }

    #ModalAtdNaoPago .form-group .select2-selection {
      max-width: 400px;
    }
</style>
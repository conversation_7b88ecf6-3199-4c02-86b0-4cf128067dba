﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Web;

namespace RepasseConvenio.Models
{
  public class RepassesStatus
  {
    public string Descricao { get; set; }

    public EnumStatusRepasse EnumStatusRepasse { get; set; }
  }

  public class R_RepasseModel
  {
    public R_RepasseModel()
    {
      ListNotaFiscalRepasse = new List<NotaFiscalRepasseModel>();
      ListPlanilhaRecebimento = new List<PlanilhaRecebimentoModel>();
      ListDepositoRepasse = new List<DepositoRepasseModel>();
    }

    public int Codigo { get; set; }
    [DisplayName("Número")]
    public string Numero { get; set; }
    [DisplayName("Responsável")]
    public int CodigoResponsavel { get; set; }
    public DateTime DataCriacao { get; set; }

    [DisplayName("Data")]
    public DateTime DataInicio { get; set; }

    [DisplayName("Status")]
    public int CodigoStatusRepasse { get; set; }

    [DisplayName("NF")]
    public bool NF { get; set; }

    [DisplayName("Demonstrativo")]
    public bool Valorizacao { get; set; }

    [DisplayName("Depósito")]
    public bool Deposito { get; set; }

    public int CodigoConvenio { get; set; }

    [DisplayName("Número do Documento")]
    public string NumDeposito { get; set; }

    [DisplayName("Codigo do Banco")]
    public int? CodigoBancoDeposito { get; set; }

    [DisplayName("Agência")]
    public string AgenciaDeposito { get; set; }

    [DisplayName("Conta")]
    public string ContaDeposito { get; set; }
    [DisplayName("Data do Deposito")]
    public DateTime? DataDeposito { get; set; }
    [DisplayName("Valor do Deposito")]
    public decimal? ValorDeposito { get; set; }
    public int CodigoUsuarioCriacao { get; set; }

    [DisplayName("Valor")]
    public decimal? Valor { get; set; }

    public int? IdDepositoRepasse { get; set; }

    [DisplayName("Imposto")]
    public decimal? Imposto { get; set; }

    [DisplayName("Taxa Administrativa")]
    public decimal? TaxaAdministrativa { get; set; }

    [DisplayName("Responsável")]
    [URLSelect("Select2/GetUsuarioSelect")]
    [PlaceHolderAttr("Selecione o Responsável")]
    public Select2Model ResponsavelSelect
    {
      get
      {
        UsuarioServices usuarioServices = new UsuarioServices();
        return usuarioServices.GetById(this.CodigoResponsavel).ToSelect2Model();
      }
      set
      {
        if (value != null && !String.IsNullOrEmpty(value.id) && !value.id.Equals("0"))
          this.CodigoResponsavel = int.Parse(value.id);
      }
    }

    [DisplayName("Convênio")]
    [URLSelect("Select2/GetConvenioSelect")]
    [PlaceHolderAttr("Selecione o Convênio")]
    public Select2Model ConvenioSelect
    {
      get
      {
        ConvenioServices convenioServices = new ConvenioServices();
        return convenioServices.GetById(this.CodigoConvenio).ToSelect2Model();
      }
      set
      {
        if (value != null && !String.IsNullOrEmpty(value.id) && !value.id.Equals("0"))
          this.CodigoConvenio = int.Parse(value.id);
      }
    }

    [DisplayName("Selecione o Status")]
    [URLSelect("Select2/GetStatusRepasseSelect")]
    [PlaceHolderAttr("Selecione o Status")]
    public Select2Model StatusRepasseSelect
    {
      get
      {
        StatusRepasseServices StatusRepasseServices = new StatusRepasseServices();
        return StatusRepasseServices.GetById(this.CodigoStatusRepasse).ToSelect2Model();
      }
      set
      {
        if (value != null && !String.IsNullOrEmpty(value.id) && !value.id.Equals("0"))
          this.CodigoStatusRepasse = int.Parse(value.id);
      }
    }

    [DisplayName("Selecione o Banco")]
    [URLSelect("Select2/GetBancoSelect")]
    [PlaceHolderAttr("Selecione o Banco")]
    public Select2Model BancoSelect
    {
      get
      {
        BancoServices BancoServices = new BancoServices();
        if (this.CodigoBancoDeposito.HasValue)
          return BancoServices.GetById(this.CodigoBancoDeposito.Value).ToSelect2Model();
        else
          return new Select2Model();
      }
      set
      {
        if (value != null && !String.IsNullOrEmpty(value.id) && !value.id.Equals("0"))
          this.CodigoBancoDeposito = int.Parse(value.id);
      }
    }

    [DisplayName("Convênio")]
    public string RazaoSocialConvenio
    {
      get
      {
        ConvenioServices convenioServices = new ConvenioServices();
        string name = convenioServices.GetRazaoSocialById(this.CodigoConvenio);
        return string.IsNullOrEmpty(name) ? "" : name;
      }
    }

    public bool DadosBancariosCompletos
    {
      get
      {
        if (this.CodigoBancoDeposito.HasValue && !string.IsNullOrEmpty(NumDeposito)
            && !string.IsNullOrEmpty(AgenciaDeposito) && !string.IsNullOrEmpty(ContaDeposito)
            && (DataDeposito.HasValue) && (ValorDeposito.HasValue && ValorDeposito > 0))
          return true;
        else
          return false;
      }
    }

    public List<NotaFiscalRepasseModel> ListNotaFiscalRepasse { get; set; }
    public List<PlanilhaRecebimentoModel> ListPlanilhaRecebimento { get; set; }
    public List<DemonstrativoConvenioGrid> ListDemonstrativoConvenioGrid { get; set; }
    public List<DepositoRepasseModel> ListDepositoRepasse { get; set; }

  }

  public class RepasseModelIndex
  {
    public int Codigo { get; set; }
    public string Numero { get; set; }
    public string Responsavel { get; set; }
    public DateTime DataCriacao { get; set; }
    public DateTime DataInicio { get; set; }
    public string StatusRepasse { get; set; }
    public int Status { get; set; }
    public string Convenio { get; set; }

    public EnumStatusRepasse StatusRepasseEnum { get; set; }
  }

  public class ExtratoRepasseModal
  {
    public int IdRepasseExtrato { get; set; }

    [DisplayName("Nome Médico")]
    public string NomeMedico { get; set; }

    public int? CodigoClassificacao { get; set; }

    [DisplayName("Selecione a Classificação")]
    [URLSelect("Select2/GetClassificacaoSelect")]
    [PlaceHolderAttr("Selecione a Classificação")]
    public Select2Model ClassificacaoSelect
    {
      get
      {
        BancoServices BancoServices = new BancoServices();
        if (this.CodigoClassificacao.HasValue)
          return BancoServices.GetById(this.CodigoClassificacao.Value).ToSelect2Model();
        else
          return new Select2Model();
      }
      set
      {
        if (value != null && !String.IsNullOrEmpty(value.id) && !value.id.Equals("0"))
          this.CodigoClassificacao = int.Parse(value.id);
      }
    }
  }

  public static class R_RepasseModelConversions
  {
    public static R_Repasse ToR_RepasseCreate(this R_RepasseModel model, int IdUsuario, int IdDeposito)
    {
      R_Repasse entity = new R_Repasse();
      //entity.R_Id = model.Codigo;
      entity.R_Numero = model.Numero;
      entity.R_IdUsuarioResponsavel = model.CodigoResponsavel;
      entity.R_DataCriacao = DateTime.Now;
      entity.R_DataInicio = model.DataInicio;
      entity.R_IdStatusRepasse = new StatusRepasseServices().GetIdByEnum(EnumStatusRepasse.ELABORACAO);
      entity.R_NF = model.NF;
      entity.R_Valorizacao = model.Valorizacao;
      entity.R_Deposito = model.Deposito;
      if (IdDeposito != 0)
        entity.R_IdDepositoRepasse = IdDeposito;
      entity.R_IdConvenio = model.CodigoConvenio;
      entity.R_NumeroDeposito = model.NumDeposito;
      entity.R_IdBancoDeposito = model.CodigoBancoDeposito;
      entity.R_AgenciaDeposito = model.AgenciaDeposito;
      entity.R_ContaDeposito = model.ContaDeposito;
      entity.R_DataDeposito = model.DataDeposito;
      entity.R_ValorDeposito = model.ValorDeposito;
      entity.R_IdUsuarioCriacao = IdUsuario;
      entity.R_Valor = model.Valor;
      entity.R_Imposto = model.Imposto;
      entity.R_TaxaAdministrativa = model.TaxaAdministrativa;
      return entity;
    }

    public static R_Repasse ToR_RepasseEdit(this R_RepasseModel model, R_Repasse entity)
    {
      entity.R_IdUsuarioResponsavel = model.CodigoResponsavel;
      //entity.R_DataInicio = model.DataInicio;
      entity.R_IdConvenio = model.CodigoConvenio;
      entity.R_NumeroDeposito = model.NumDeposito;
      entity.R_IdBancoDeposito = model.CodigoBancoDeposito;
      entity.R_AgenciaDeposito = model.AgenciaDeposito;
      entity.R_ContaDeposito = model.ContaDeposito;
      entity.R_DataDeposito = model.DataDeposito;
      entity.R_Valor = model.Valor;
      entity.R_Imposto = model.Imposto;
      entity.R_TaxaAdministrativa = model.TaxaAdministrativa;
      entity.R_ValorDeposito = model.ValorDeposito;
      entity.R_Valorizacao = entity.R_DemonstrativoConvenio.Count > 0 ? true : false;
      entity.R_NF = entity.R_NotaFiscaRepasse.Count > 0 ? true : false;
      //entity.R_Deposito = model.DadosBancariosCompletos;
      if (entity.R_Valorizacao && entity.R_NF && entity.R_Deposito)
        entity.R_IdStatusRepasse = new StatusRepasseServices().GetIdByEnum(EnumStatusRepasse.EMCONFERENCIA);
      return entity;
    }
    public static R_RepasseModel ToR_RepasseModel(this R_Repasse entity)
    {
      if (entity == null)
        return null;

      R_RepasseModel model = new R_RepasseModel();
      model.Codigo = entity.R_Id;
      model.Numero = entity.R_Numero;
      model.CodigoResponsavel = entity.R_IdUsuarioResponsavel;
      model.DataCriacao = entity.R_DataCriacao;
      model.DataInicio = entity.R_DataInicio;
      model.CodigoStatusRepasse = entity.R_IdStatusRepasse;
      model.CodigoConvenio = entity.R_IdConvenio;
      model.NumDeposito = entity.R_NumeroDeposito;
      model.CodigoBancoDeposito = entity.R_IdBancoDeposito;
      model.AgenciaDeposito = entity.R_AgenciaDeposito;
      model.ContaDeposito = entity.R_ContaDeposito;
      model.DataDeposito = entity.R_DataDeposito;
      model.ValorDeposito = entity.R_ValorDeposito;
      model.CodigoUsuarioCriacao = entity.R_IdUsuarioCriacao;
      model.Valor = entity.R_Valor;
      model.Imposto = entity.R_Imposto;
      model.TaxaAdministrativa = entity.R_TaxaAdministrativa;
      model.NF = entity.R_NF;
      model.Deposito = entity.R_Deposito;
      model.Valorizacao = entity.R_Valorizacao;
      model.IdDepositoRepasse = entity.R_IdDepositoRepasse;
      return model;
    }
  }

}
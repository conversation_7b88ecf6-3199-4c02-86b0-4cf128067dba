﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using PagedList;
using RepasseConvenio.Infrastructure.Helpers;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Web;
using RepasseConvenio.Services;

namespace RepasseConvenio.Models
{
  public class RegistroImpostoModel
  {
    public int Codigo { get; set; }
    public DateTime DataGeracao { get; set; }
    public int UsuarioProcessamento { get; set; }
    public int CodigoMedico { get; set; }
    public string NomeMedico { get; set; }
    public string Agencia { get; set; }
    public string Banco { get; set; }
    public string Conta { get; set; }
    public decimal Valor { get; set; }
    public DateTime? DataPagamento { get; set; }
    public EnumTipoRegistroImposto TipoImposto { get; set; }
  }

  public class RegistroImpostoIndex
  {
    public IPagedList<RegistroImpostoModel> ListRegistro { get; set; }

    [DisplayName("Data De")]
    public DateTime? DataDe { get; set; }

    [DisplayName("Data Até")]
    public DateTime? DataAte { get; set; }

    public int? CodigoMedico { get; set; }

    public int? CodigoTipoImposto { get; set; }

    [DisplayName("Médico")]
    [URLSelect("Select2/GetMedicoSelect")]
    [PlaceHolderAttr("Selecione o Médico")]
    public Select2Model MedicoSelect
    {
      get
      {
        MedicoService MedicoService = new MedicoService();
        return MedicoService.GetById(this.CodigoMedico.GetValueOrDefault()).ToSelect2Model();
      }
      set
      {
        if (value != null && !string.IsNullOrEmpty(value.id) && !value.id.Equals("0"))
          this.CodigoMedico = int.Parse(value.id);
      }
    }

    [DisplayName("Tipo do Imposto")]
    [URLSelect("Select2/GetTipoRegistroImpostoSelect")]
    [PlaceHolderAttr("Selecione o Tipo do Imposto")]
    public Select2Model TipoImpostoSelect
    {
      get
      {
        TipoRegistroImpostoServices TipoImpostoServices = new TipoRegistroImpostoServices();
        return TipoImpostoServices.GetById(this.CodigoTipoImposto.GetValueOrDefault()).ToSelect2Model();
      }
      set
      {
        if (value != null && !string.IsNullOrEmpty(value.id) && !value.id.Equals("0"))
          this.CodigoTipoImposto = int.Parse(value.id);
      }
    }
  }

  public class ExtratoMedicoRegistroImpostoModel
  {
    public int Id { get; set; }
    public int IdMedico { get; set; }
    public decimal Valor { get; set; }
    public string Agencia { get; set; }
    public string Banco { get; set; }
    public string Conta { get; set; }
    public EnumTipoRegistroImposto TipoImposto { get; set; }
  }

  public class ExtratoRegistroImpostoCreateModel
  {
    public int IdMedico { get; set; }
    public int IdTipoImposto { get; set; }
    public decimal ValorTotal { get; set; }
    public string Agencia { get; set; }
    public string Banco { get; set; }
    public string Conta { get; set; }

    public List<ExtratoMedicoRegistroImpostoModel> Extratos { get; set; }
  }

  public class ExtratoMedicoRegistroImposto
  {
    public int Codigo { get; set; }
    public string NroGuiaAtendimento { get; set; }
    public decimal Valor { get; set; }
    public DateTime DataProcessamento { get; set; }
    public EnumTipoRegistroImposto TipoImposto { get; set; }
    public string DataProcessamentoAux
    {
      get
      {
        return DataProcessamento.ToString("dd/MM/yyyy HH:ss");
      }
    }
  }

  public static class RegistroImpostoModelConversion
  {
    public static R_RegistroImposto toCreateRegistroImposto(this ExtratoRegistroImpostoCreateModel model
                                                                , int IdUsuarioProcessamento)
    {
      return new R_RegistroImposto()
      {
        RI_DataGeracao = DateTime.Now,
        RI_IdUsuarioProcessamento = IdUsuarioProcessamento,
        RI_IdMedico = model.IdMedico,
        RI_Banco = model.Banco,
        RI_Agencia = model.Agencia,
        RI_Conta = model.Conta,
        RI_Valor = model.ValorTotal,
        RI_IdTipoImposto = model.IdTipoImposto
      };
    }

    public static R_RegistroImposto toEditRegistroImposto(this RegistroImpostoModel model)
    {
      R_RegistroImposto entity = new R_RegistroImposto();

      entity.RI_Id = model.Codigo;
      entity.RI_IdMedico = model.CodigoMedico;
      entity.RI_IdUsuarioProcessamento = model.UsuarioProcessamento;
      entity.RI_Agencia = model.Agencia;
      entity.RI_Banco = model.Banco;
      entity.RI_Conta = model.Conta;
      entity.RI_DataGeracao = model.DataGeracao;
      entity.RI_DataPagamento = model.DataPagamento;
      entity.RI_Valor = model.Valor;

      return entity;
    }
  }
}
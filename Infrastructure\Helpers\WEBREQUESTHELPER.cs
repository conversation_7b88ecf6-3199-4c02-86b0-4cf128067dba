﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */


using RepasseConvenio.Models;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Net;
using System.Text;
using System.Web.Script.Serialization;
using System.Xml;

namespace RepasseConvenio.Infrastructure.Helpers
{
  public class RetornoResponse
  {
    public RetornoResponse()
    {
      Error = false;
    }

    public bool Error { get; set; }
  }

  public class WebRequestHelper
  {
    public static RetornoBaseProtheus Post(string url, string ContentType, Dictionary<string, string> header, string data)
    {
      RetornoBaseProtheus RetornoResponse = new RetornoBaseProtheus();

      try
      {
        return RetornoResponse;
      }
      catch (WebException e)
      {
        return RetornoResponse;
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }

    public static GetNotaProteus PostProtheusAux(string url, string ContentType, Dictionary<string, string> header, string data)
    {
      GetNotaProteus RetornoResponse = new GetNotaProteus();

      try
      {
        return RetornoResponse;
      }
      catch (WebException e)
      {
        return RetornoResponse;
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }

    public static NotaProtheus PostProtheus(string url, string ContentType, Dictionary<string, string> header, string data)
    {
      NotaProtheus RetornoResponse = new NotaProtheus();

      try
      {
        return RetornoResponse;
      }
      catch (WebException e)
      {
        return RetornoResponse;
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }

    public static RetornoResponse PostAuxiliar(string url, string ContentType, Dictionary<string, string> header, string data)
    {
      RetornoResponse RetornoResponse = new RetornoResponse();

      try
      {
        return RetornoResponse;
      }
      catch (WebException e)
      {
        return RetornoResponse;
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }

    public static RetornoResponse PostRedundante(List<string> urls, string ContentType, Dictionary<string, string> header, string data, string mensagemconexao = "")
    {
      RetornoResponse RetornoResponse = new RetornoResponse();
      try
      {
        return RetornoResponse;
      }
      catch (WebException e)
      {
        return RetornoResponse;
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }

      return RetornoResponse;
    }

    public static string Get(string url, Dictionary<string, string> header)
    {
      string myResponse = "";
      try
      {
        return myResponse;
      }
      catch (WebException e)
      {
        return myResponse;
      }
      catch (Exception ex)
      {
        return myResponse;
      }
    }

  }
  public class WebServiceHelper
  {
    private static void InsertSoapEnvelopeIntoWebRequest(XmlDocument soapEnvelopeXml, HttpWebRequest webRequest)
    {
    }

    public static string Post(Dictionary<string, string> Header, XmlDocument data, string ContetType, string Element, string URLWS)
    {
      string myResponse = "";

      try
      {
        return myResponse;
      }
      catch (Exception)
      {
        throw;
      }
    }

    public static string PrintXML(string xml)
    {
      string result = "";

      return result;
    }
  }
}

﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using PagedList;
using RepasseConvenio.Domain.Infrastructure.Helpers;
using RepasseConvenio.Infrastructure;
using RepasseConvenio.Models;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;

namespace RepasseConvenio.Services
{
  public class RelacaoResponsavelConvenioServices : ServiceBase
  {
    public RelacaoResponsavelConvenioServices()
   : base()
    { }
    public RelacaoResponsavelConvenioServices(RepasseEntities Contexto)
       : base(Contexto)
    { }

    public RelacaoResponsavelConvenioServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public RelacaoResponsavelConvenioServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public R_RelacaoResponsavelConvenio GetById(int id)
    {
      string query = @"SELECT * FROM R_RelacaoResponsavelConvenio WHERE RRC_Id = @id";
      return Contexto.Database.SqlQuery<R_RelacaoResponsavelConvenio>(query, new SqlParameter("@id", id)).FirstOrDefault();
    }

    public List<R_ResponsavelConvenio> GetByTerm(string term, int id)
    {
      LoteGlosaServices loteGlosaServices = new LoteGlosaServices();
      R_LoteGlosa loteGlosa = loteGlosaServices.GetById(id);

      if (string.IsNullOrEmpty(term))
      {
        string query = string.Format(@"SELECT 
                                        RC.*
                                       FROM R_RelacaoResponsavelConvenio RRC
                                       INNER JOIN R_ResponsavelConvenio RC ON RC.RC_Id = RRC.RRC_IdResponsavel AND RRC.RRC_IdConvenio = @IdConvenio");

        return Contexto.Database.SqlQuery<R_ResponsavelConvenio>(query, new SqlParameter("@IdConvenio", loteGlosa.LG_IdConvenio)).ToList();
      }
      else
      {
        string query = string.Format(@"SELECT 
                                        RC.*
                                       FROM R_RelacaoResponsavelConvenio RRC
                                       INNER JOIN R_ResponsavelConvenio RC ON RC.RC_Id = RRC.RRC_IdResponsavel AND RRC.RRC_IdConvenio = @IdConvenio
                                       WHERE RC.RC_Nome LIKE @termo");

        return Contexto.Database.SqlQuery<R_ResponsavelConvenio>(query, new SqlParameter("@termo", string.Format("%{0}%", term))
                                                          , new SqlParameter("@termo", loteGlosa.LG_IdConvenio)).ToList();

      }
    }

  }
}
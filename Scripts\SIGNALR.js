﻿var SignalR;

SignalR = function () {
};

SignalR.tryingToReconnect = false;

SignalR.init = function () {
  SignalR.Connection = $.connection;
  SignalR.HubRef = SignalR.Connection.signalHub;
  SignalR.Connection.hub.start().done(function () {
    console.log($.connection.hub.id);
  });

  SignalR.Connection.hub.reconnecting(function () {
    SignalR.tryingToReconnect = true;
  });

  SignalR.Connection.hub.reconnected(function () {
    SignalR.tryingToReconnect = false;
  });

  SignalR.Connection.hub.disconnected(function () {
    console.log("Desconectado");
    if (!SignalR.tryingToReconnect) {
      setTimeout(function () {
        console.log("Conectando");

        $.connection.hub.start();
        console.log($.connection.hub.id);
      }, 1000);
    }
  });
}


$(document).ready(function () {
  SignalR.init();
  RepasseConvenio.SignalRGeral();
});

﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using PagedList;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RepasseConvenio.Services
{
  public class StatusGuiaNotaFiscalServices : ServiceBase
  {
    public StatusGuiaNotaFiscalServices(RepasseEntities Contexto)
        : base(Contexto)
    { }
    public StatusGuiaNotaFiscalServices()
       : base()
    { }

    public StatusGuiaNotaFiscalServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public StatusGuiaNotaFiscalServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public int GetIdByEnum(StatusGuiaNotaFiscalEnum statusGuiaNotaFiscalEnum)
    {
      return Contexto.R_StatusGuiaNotaFiscal.Where(a => a.SGNF_Enum == (int)statusGuiaNotaFiscalEnum).Select(a => a.SGNF_Id).FirstOrDefault();
    }
    public R_StatusGuiaNotaFiscal GetByEnum(StatusGuiaNotaFiscalEnum statusGuiaNotaFiscalEnum)
    {
      return Contexto.R_StatusGuiaNotaFiscal.Where(a => a.SGNF_Enum == (int)statusGuiaNotaFiscalEnum).FirstOrDefault();
    }

    public R_StatusGuiaNotaFiscal GetById(int Id)
    {
      return Contexto.R_StatusGuiaNotaFiscal.Where(a => a.SGNF_Id == Id).FirstOrDefault();
    }

    public List<R_StatusGuiaNotaFiscal> GetAll()
    {
      string query = @"SELECT * FROM R_StatusGuiaNotaFiscal";
      return Contexto.Database.SqlQuery<R_StatusGuiaNotaFiscal>(query).ToList();
    }
  }
}


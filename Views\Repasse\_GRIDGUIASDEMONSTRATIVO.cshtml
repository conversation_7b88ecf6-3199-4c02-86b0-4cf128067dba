﻿@using RepasseConvenio.Models
@model List<GuiaDemonstrativoIndex>

<div class="row">
  <div class="col-md-12 table-responsive p-0 ">
    <table class="table table-sm table-striped table-hover text-nowrap">
      <thead>
        <tr>
          <th>
            @*@Html.CheckBox("CheckAllPlanRecebimento")*@
          </th>
          <th>
            Número Gui<PERSON>
          </th>
          <th>
            Número Car<PERSON>irinh<PERSON>
          </th>
          <th>
            Número Atendimento
          </th>
          <th>
            Data Atendimento
          </th>
          <th>
            Procedimento
          </th>
          <th>
            Total Faturado
          </th>
          <th>
            Valor Apresentado
          </th>
          <th>
            Total Glosado
          </th>
          <th>
            Total Pago
          </th>
          <th>

          </th>
        </tr>
      </thead>
      <tbody>
        @foreach (GuiaDemonstrativoIndex item in Model)
        {
          <tr>
            <td>
              @*<input class="isSelected" data-codigoplanilha="@item.Codigo" data-val="true" data-val-required="O campo isSelected é obrigatório." name="item.isSelected" type="checkbox" value="true">*@
            </td>
            <td>
              @item.NumeroGuia
            </td>
            <td>
              @item.NumeroCarteirinha
            </td>
            <td>
              @item.NumeroAtendimento
            </td>
            <td>
              @item.DataAtendimento.ToString("dd/MM/yyyy")
            </td>
            <td>
              @item.ValorFaturado
            </td>
            <td>
              @item.ValorApresentado
            </td>
            <td>
              @item.ValorGlosado
            </td>
            <td>
              @item.ValorPago
            </td>
            <td>
              <a class="btn btn-outline-warning btn-circle btn-sm VisualizarGuiaAtendimento" data-codigoguiaatendimento="@item.CodigoGuia" title="Visualizar">
                Visualizar
              </a>
            </td>
          </tr>
        }
      </tbody>
    </table>
  </div>

</div>

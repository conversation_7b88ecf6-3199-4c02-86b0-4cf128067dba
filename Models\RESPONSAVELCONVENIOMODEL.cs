﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace RepasseConvenio.Models
{
  public class ResponsavelConvenioModel
  {
    public int Codigo { get; set; }
    public int? CodigoRelacao { get; set; }

    [DisplayName("Nome")]
    [Required]
    public string NomeResponsavel { get; set; }

    [Required]
    public string CPF { get; set; }

    [Required]
    public string Email { get; set; }

    [Required]
    public int CodigoConvenio { get; set; }
  }

  public class ResponsavalConvenioUserLogged
  {
    public int IdConvenio { get; set; }
    public int IdResponsavelConvenio { get; set; }
  }

  public static class ResponsavelConvenioConversions
  {
    public static R_ResponsavelConvenio ModelToEntityCreate(this ResponsavelConvenioModel responsavel)
    {
      return new R_ResponsavelConvenio()
      {
        RC_Nome = responsavel.NomeResponsavel,
        RC_CPF = responsavel.CPF,
        RC_Email = responsavel.Email
      };
    }

    public static ResponsavelConvenioModel EntityToModel(this R_ResponsavelConvenio responsavel)
    {
      return new ResponsavelConvenioModel()
      {
        Codigo = responsavel.RC_Id,
        NomeResponsavel = responsavel.RC_Nome,
        CPF = responsavel.RC_CPF,
        Email = responsavel.RC_Email
      };
    }

    public static bool ToCadastroValido(this ResponsavelConvenioModel model)
    {
      if (string.IsNullOrEmpty(model.CPF))
        return false;

      if (string.IsNullOrEmpty(model.Email))
        return false;

      if (string.IsNullOrEmpty(model.NomeResponsavel))
        return false;

      return true;
    }
  }
}
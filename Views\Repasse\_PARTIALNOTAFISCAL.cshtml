﻿@using RepasseConvenio.Infrastructure.Controls
@using RepasseConvenio.Models
@model NotaFiscalRepasseModel

@{
  ViewBag.Title = "Novo Nota Fiscal Repasse";
  ViewBag.DescricaoTela = "Nota Fiscal Repasse";
  ViewBag.ResumoTela = "Novo Nota Fiscal Repasse";
}

@using (Html.BeginForm("Create", "NotaFiscalRepasse", FormMethod.Post, new { id = "PostNotaFiscal", @style = "" }))
{
  @Html.AntiForgeryToken()
  @Html.HiddenFor(model => model.NFM_Codigo)
  @Html.HiddenFor(model => model.NFM_CodigoRepasse)
  @Html.HiddenFor(model => model.NFM_DataCriacao)

  <div class="box box-primary">
    <div class="box-body">
      @Html.ValidationSummary(true, "", new { @class = "text-danger" })
      <div class="row">
        <div class="col-md-2">
          <div class="form-group">
            @Html.LabelFor(model => model.NFM_Numero)
            @Html.EditorFor(model => model.NFM_Numero, new { htmlAttributes = new { @class = "form-control numeros" } })
            @Html.ValidationMessageFor(model => model.NFM_Numero, "", new { @class = "text-danger" })
          </div>
        </div>

        <div class="col-md-2">
          <div class="checkbox form-group">
            @Html.LabelFor(model => model.NFM_Serie)
            @Html.EditorFor(model => model.NFM_Serie, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.NFM_Serie, "", new { @class = "text-danger" })
          </div>
        </div>
        <div class="col-md-2">
          <div class="form-group">
            @Html.LabelFor(model => model.NFM_DataEmissao)
            @Html.EditorFor(model => model.NFM_DataEmissao, new { htmlAttributes = new { @class = "form-control airDatePickerDate Data" } })
            @Html.ValidationMessageFor(model => model.NFM_DataEmissao, "", new { @class = "text-danger" })
          </div>
        </div>
        <div class="col-md-2">
          <div class="form-group">
            @Html.LabelFor(model => model.NFM_ValorTotalText)
            @Html.EditorFor(model => model.NFM_ValorTotalText, new { htmlAttributes = new { @class = "form-control money" } })
            @Html.ValidationMessageFor(model => model.NFM_ValorTotalText, "", new { @class = "text-danger" })
          </div>
        </div>
        <div class="col-md-2">
          <div class="form-group">
            @Html.LabelFor(model => model.NFM_ValorLiquidoText)
            @Html.EditorFor(model => model.NFM_ValorLiquidoText, new { htmlAttributes = new { @class = "form-control money" } })
            @Html.ValidationMessageFor(model => model.NFM_ValorLiquidoText, "", new { @class = "text-danger" })
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-md-2">
          <div class="form-group">
            @Html.LabelFor(model => model.IISText)
            @Html.EditorFor(model => model.IISText, new { htmlAttributes = new { @class = "form-control money" } })
            @Html.ValidationMessageFor(model => model.IISText, "", new { @class = "text-danger" })
          </div>
        </div>
        <div class="col-md-2">
          <div class="form-group">
            @Html.LabelFor(model => model.PISText)
            @Html.EditorFor(model => model.PISText, new { htmlAttributes = new { @class = "form-control money" } })
            @Html.ValidationMessageFor(model => model.PISText, "", new { @class = "text-danger" })
          </div>
        </div>
        <div class="col-md-2">
          <div class="form-group">
            @Html.LabelFor(model => model.COFINSText)
            @Html.EditorFor(model => model.COFINSText, new { htmlAttributes = new { @class = "form-control money" } })
            @Html.ValidationMessageFor(model => model.COFINSText, "", new { @class = "text-danger" })
          </div>
        </div>
        <div class="col-md-2">
          <div class="form-group">
            @Html.LabelFor(model => model.CSLLText)
            @Html.EditorFor(model => model.CSLLText, new { htmlAttributes = new { @class = "form-control money" } })
            @Html.ValidationMessageFor(model => model.CSLLText, "", new { @class = "text-danger" })
          </div>
        </div>
      </div>

      <div class="row" style=" height: 40vh;">
        <div class="col-md-12 container">
          <div id="PartialGuiasNotaFiscalEdit" style="display: flex;">
            @Html.Partial("_PartialGuiaNotaFiscalEdit", Model.ListGuiaNotaFiscalEdit)
          </div>
        </div>
      </div>

    </div>
  </div>
}

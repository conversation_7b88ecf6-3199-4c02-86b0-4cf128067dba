﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Controls;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace RepasseConvenio.Controllers
{
  [Authorize]
  [Security("334752BB-91FB-4F4D-9B64-AB09168FBA4F")]

  public class RegraCartaConversaoController : LibController
  {
  [Security("5DF01A58-ED2A-42D0-8406-51AFA4C5EE70")]

    public ActionResult Index(int CodigoMedico, int? Pagina)
    {
      Pagina = Pagina ?? 1;
      RegraCartaConversaoServices regraCartaConversaoServices = new RegraCartaConversaoServices();

      RegraCartaConversaoCabecalhoIndex regraCartaConversaoCabecalhoIndex = new RegraCartaConversaoCabecalhoIndex();
      regraCartaConversaoCabecalhoIndex.CodigoMedico = CodigoMedico;
      regraCartaConversaoCabecalhoIndex.ListaRegraCartaConversaoIndex = regraCartaConversaoServices.GetListRegraCartaConversaoIndex(CodigoMedico, Pagina.Value);
      return View(regraCartaConversaoCabecalhoIndex);
    }

    [HttpGet]
    [Security("E9A4CB74-BB81-4D01-A789-6F5BBD594712")]
    public ActionResult Create(int CodigoMedico)
    {
      try
      {
        RegraCartaConversaoModel regraCartaConversaoModel = new RegraCartaConversaoModel();
        regraCartaConversaoModel.CodigoMedico = CodigoMedico;
        RegraCartaConversaoServices regraCartaConversaoServices = new RegraCartaConversaoServices();
        //R_RegraCartaConversao regraCartaConversao = regraCartaConversaoServices.GetByIdMedico(CodigoMedico);
        //if (regraCartaConversao != null)
        //  return RedirectToAction("Edit", new { IdRegraCartaConversao = regraCartaConversao.RCC_Id });

        regraCartaConversaoModel = regraCartaConversaoServices.Create(regraCartaConversaoModel);
        return RedirectToAction("Edit", new { IdRegraCartaConversao = regraCartaConversaoModel.Codigo });
      }
      catch (Exception ex)
      {
        return View();
      }
    }


    //[HttpGet]
    //public ActionResult Create(int CodigoEmpresaMedico)
    //{
    //  try
    //  {
    //    RegraCartaConversaoModel regraCartaConversaoModel = new RegraCartaConversaoModel();
    //    regraCartaConversaoModel.CodigoEmpresaMedico = CodigoEmpresaMedico;
    //    RegraCartaConversaoServices regraCartaConversaoServices = new RegraCartaConversaoServices();
    //    R_RegraCartaConversao regraCartaConversao = regraCartaConversaoServices.GetByIdEmpresaMedico(CodigoEmpresaMedico);
    //    if (regraCartaConversao != null)
    //      return RedirectToAction("Edit", new { IdRegraCartaConversao = regraCartaConversao.RCC_Id });

    //    regraCartaConversaoModel = regraCartaConversaoServices.Create(regraCartaConversaoModel, regraCartaConversao);
    //    return RedirectToAction("Edit", new { IdRegraCartaConversao = regraCartaConversaoModel.Codigo });
    //  }
    //  catch (Exception ex)
    //  {
    //    return View();
    //  }
    //}

    [HttpGet]
    [Security("36F57CC0-9A9D-40ED-A79C-0EC5E12E0CFB")]

    public ActionResult Edit(int IdRegraCartaConversao)
    {
      RegraCartaConversaoServices regraCartaConversaoServices = new RegraCartaConversaoServices();
      RegraCartaConversaoModel regraCartaConversaoModel = regraCartaConversaoServices.GetRegraCartaConversaoModelById(IdRegraCartaConversao);

      if (regraCartaConversaoModel.Prioridade == 0)
        return RedirectToAction("Index", new { CodigoMedico = regraCartaConversaoModel.CodigoMedico });

      return View(regraCartaConversaoModel);
    }

    [HttpPost]

    public JsonResult CreateCondicao(RegraCartaConversaoCondicaoModel regraCartaConversaoCondicaoModel)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        int idRegraCartaConversao = Convert.ToInt32(Request.Headers.Get("idRegraConversao"));
        bool insertUltimo = Convert.ToBoolean(Request.Headers.Get("insertUltimo"));
        bool insertAfter = Convert.ToBoolean(Request.Headers.Get("insertAfter"));
        bool insertBefore = Convert.ToBoolean(Request.Headers.Get("insertBefore"));
        int sequenciaItem = Convert.ToInt32(Request.Headers.Get("sequenciaItem"));
        RegraCartaConversaoServices regraCartaConversaoServices = new RegraCartaConversaoServices();
        regraCartaConversaoServices.CreateCondicao(regraCartaConversaoCondicaoModel
                                                                                          , idRegraCartaConversao
                                                                                          , insertUltimo
                                                                                          , insertAfter
                                                                                          , insertBefore
                                                                                          , sequenciaItem);

        retornoAjax.Erro = false;
        retornoAjax.Mensagem = "Adicionado com sucesso.";
        retornoAjax.Titulo = "Sucesso.";
        return Json(retornoAjax);
      }
      catch (CustomException ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Titulo = "Error.";
        return Json(retornoAjax);
      }
      catch (Exception ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.Mensagem = "Ocorreu uma falha na sua solicitação.";
        retornoAjax.Titulo = "Error.";
        return Json(retornoAjax);
      }
    }

    [HttpPost]
    public JsonResult EditCondicao(RegraCartaConversaoCondicaoModel regraCartaConversaoCondicaoModel)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        int idRegraCartaConversao = Convert.ToInt32(Request.Headers.Get("idRegraConversao"));
        int sequenciaItem = Convert.ToInt32(Request.Headers.Get("sequenciaItem"));
        RegraCartaConversaoServices regraCartaConversaoServices = new RegraCartaConversaoServices();
        regraCartaConversaoServices.EditCondicao(regraCartaConversaoCondicaoModel, idRegraCartaConversao, sequenciaItem);

        retornoAjax.Erro = false;
        retornoAjax.Mensagem = "Editado com sucesso.";
        retornoAjax.Titulo = "Sucesso.";
        return Json(retornoAjax);
      }
      catch (CustomException ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Titulo = "Error.";
        return Json(retornoAjax);
      }
      catch (Exception ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.Mensagem = "Ocorreu uma falha na sua solicitação.";
        retornoAjax.Titulo = "Error.";
        return Json(retornoAjax);
      }
    }

    [HttpPost]
    public JsonResult DeleteCondicao(int idRegraConversao, int sequenciaItem)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        RegraCartaConversaoServices regraCartaConversaoServices = new RegraCartaConversaoServices();
        regraCartaConversaoServices.Delete(idRegraConversao, sequenciaItem);

        retornoAjax.Erro = false;
        retornoAjax.Mensagem = "Deletado com sucesso.";
        retornoAjax.Titulo = "Sucesso.";
        return Json(retornoAjax);
      }
      catch (CustomException ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Titulo = "Error.";
        return Json(retornoAjax);
      }
      catch (Exception ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.Mensagem = "Ocorreu uma falha na sua solicitação.";
        retornoAjax.Titulo = "Error.";
        return Json(retornoAjax);
      }
    }

    public PartialViewResult PartialRegra(int idRegra)
    {
      RegraCartaConversaoServices regraCartaConversaoServices = new RegraCartaConversaoServices();
      List<RegraCartaConversaoCondicaoModel> ListaRegraCartaConversaoCondicaoModel = regraCartaConversaoServices.GetListRegraCartaConversaoCondicaoModel(idRegra);
      return PartialView("_PartialRegra", ListaRegraCartaConversaoCondicaoModel);
    }

    [HttpPost]
    [MultiButton(MatchFormKey = "action", MatchFormValue = "SalvarValidarRegras")]
    public ActionResult SalvarValidarRegras(RegraCartaConversaoModel regraCartaConversaoModel)
    {
      try
      {
        if (regraCartaConversaoModel.Prioridade.HasValue && regraCartaConversaoModel.Prioridade.Value <= 0)
          throw new CustomException("A prioridade deve ser maior que zero.");

        new RegraCartaConversaoServices().SalvarValidarRegras(regraCartaConversaoModel);
        MessageListToast.Add(new Message(MessageType.Success, "Regra adicionada com sucesso."));
        return RedirectToAction("Index", "RegraCartaConversao", new { CodigoMedico = regraCartaConversaoModel.CodigoMedico });
      }
      catch (CustomException ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return RedirectToAction("Edit", new { IdRegraCartaConversao = regraCartaConversaoModel.Codigo });
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, "Tivemos uma falha na sua solicitação."));
        return RedirectToAction("Edit", new { IdRegraCartaConversao = regraCartaConversaoModel.Codigo });
      }
    }

    [HttpPost]
    [MultiButton(MatchFormKey = "action", MatchFormValue = "SalvarRegra")]
    public ActionResult SalvarRegra(RegraCartaConversaoModel regraCartaConversaoModel)
    {
      try
      {
        if (regraCartaConversaoModel.Prioridade.HasValue && regraCartaConversaoModel.Prioridade.Value <= 0)
          throw new CustomException("A prioridade deve ser maior que zero.");

        RegraCartaConversaoServices regraCartaConversaoServices = new RegraCartaConversaoServices();
        regraCartaConversaoServices.Salvar(regraCartaConversaoModel);
        MessageListToast.Add(new Message(MessageType.Success, "Regra salva com sucesso."));
        return RedirectToAction("Index", "RegraCartaConversao", new { CodigoMedico = regraCartaConversaoModel.CodigoMedico });
      }
      catch (CustomException ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return RedirectToAction("Edit", new { IdRegraCartaConversao = regraCartaConversaoModel.Codigo });
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, "Tivemos uma falha na sua solicitação."));
        return RedirectToAction("Edit", new { IdRegraCartaConversao = regraCartaConversaoModel.Codigo });
      }
    }

  }
}
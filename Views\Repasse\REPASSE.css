﻿.boxperson {
  border: 1px solid #ccc !important;
  padding: 0.70em 18px;
  border-radius: 16px;
  box-sizing: inherit;
}

h3, .h3 {
  font-size: 1.25rem !important;
}

hr {
  margin-top: 0.5rem !important;
  margin-bottom: 0.5rem !important;
}

.btn {
  padding: 0.08rem 0.15rem !important;
  margin: 0px 4px 0px 4px;
}

.divtable {
  padding: 10px;
  overflow-y: auto;
  max-height: 250px;
}

  .divtable::-webkit-scrollbar {
    width: 7px;
  }

  .divtable::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
  }

  .divtable::-webkit-scrollbar-thumb {
    background-color: #79a6d4;
    outline: 1px solid #007bff;
  }

.divcheck {
  padding-top: 30px;
}


.formGuiaAtendimento > input {
  height: 17px !important;
  font-size: 15px !important;
  padding: 1px 5px !important;
  line-height: 1 !important;
}

  .formGuiaAtendimento > input:disabled {
    background-color: white;
  }

.formGuiaAtendimento > label {
  font-size: 10px !important;
  margin-bottom: 0px !important;
  font-weight: 700 !important;
}

#ProcedimentosGuia > thead > tr > th {
  font-size: 10px !important;
  margin-bottom: 0px !important;
  font-weight: 700 !important;
}

#ProcedimentosGuia > tbody > tr > td > input {
  height: 17px !important;
  font-size: 12px !important;
  padding: 1px 5px !important;
  line-height: 1 !important;
}

  #ProcedimentosGuia > tbody > tr > td > input:disabled {
    background-color: white;
  }

#ProcedimentosGuia > tbody > tr > td {
  height: 17px !important;
  font-size: 12px !important;
  padding: 1px 5px !important;
  line-height: 1 !important;
}


.RowModalProcDemonstrativo > div > table > tbody > tr > td,
#PartialExtratos > table > tbody > tr > td,
#PartialLotes > table > tbody > tr > td,
#PartialItensLotes > table > tbody > tr > td {
  font-size: x-small;
  padding: 0.0rem !important;
}

  .RowModalProcDemonstrativo > div > table > tbody > tr > td > a,
  #PartialExtratos > table > tbody > tr > td > a,
  #PartialLotes > table > tbody > tr > td > a,
  #PartialItensLotes > table > tbody > tr > td > a {
    font-size: x-small;
  }

#PartialExtratos,
#PartialLotes,
#PartialItensLotes {
  overflow: auto;
  height: calc(100vh - 230px);
}

.Selected {
  transform: scale(1.007);
  background-color: #d6101085 !important;
  transition: all 100ms;
  /*border: 2px solid red;*/
}

.UnSelected {
  transform: scale(1);
  /*background-color: #17a2b8 !important;*/
  transition: all 100ms;
}

.card-tools > button {
  margin: 0 2pt !important;
}

.card-tools > a {
  margin: 0 2pt !important;
}

.card-acoes {
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
  margin: 5px 0;
}

  .card-acoes > a {
    margin: 0 2pt;
  }

.card-title {
  display: flex;
  align-items: center;
}

.stats {
  display: flex;
  align-items: center;
}

.statusrepasse {
  height: 33px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}


.emelaboracao {
  width: 10px;
  height: 10px;
  border-radius: 10px;
  background-color: yellow;
}

.emconferencia {
  width: 10px;
  height: 10px;
  border-radius: 10px;
  background-color:darkorange;
  display: flex;
  align-items: center;
}

.concluido {
  width: 10px;
  height: 10px;
  border-radius: 10px;
  background-color: green;
  display: flex;
  align-items: center;
}
.processado {
  width: 10px;
  height: 10px;
  border-radius: 10px;
  background-color: cyan;
  display: flex;
  align-items: center;
}
.parcProcessado {
  width: 10px;
  height: 10px;
  border-radius: 10px;
  background-color: olive;
  display: flex;
  align-items: center;
}
.glossado {
  width: 10px;
  height: 10px;
  border-radius: 10px;
  background-color: black;
  display: flex;
  align-items: center;
}

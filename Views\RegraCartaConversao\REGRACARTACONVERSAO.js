﻿var RegraCartaConversao = function () {
};

RegraCartaConversao.init = function () {
  $(document).on("click", ".RemoverItem", function () {
    var idRegra = $('#Codigo').val();
    var sequenciaItem = $('.Selected').data("sequenciaitem");
    if (sequenciaItem != undefined && sequenciaItem != null && sequenciaItem.length != 0)
      RegraCartaConversao.RemoveItem(idRegra, sequenciaItem);
  });

  $(document).on("click", ".EditarCondicao", function () {
    var idRegra = $('#Codigo').val();
    var sequenciaItem = $('.Selected').data("sequenciaitem");
    if (!$(this).hasClass("disabled")) {
      RegraCartaConversao.EditCondicao(idRegra, sequenciaItem);
    }
  });

  $(document).on("click", "#AdicionarCondicao", function () {
    var idRegra = $('#Codigo').val();
    if (!$(this).hasClass("disabled")) {
      RegraCartaConversao.InsertCondicao(idRegra, true, false, false, 0)
    }
  });

  $(document).on("click", ".AdicionarCondicaoBefore", function () {
    var idRegra = $('#Codigo').val();
    var sequenciaItem = $('.Selected').data("sequenciaitem");

    if (!$(this).hasClass("disabled")) {
      if ($('.Selected').length != 0) {
        RegraCartaConversao.InsertCondicao(idRegra, false, false, true, sequenciaItem)
      }
    }
  });

  $(document).on("click", ".AdicionarCondicaoAfter", function () {
    var idRegra = $('#Codigo').val();
    var sequenciaItem = $('.Selected').data("sequenciaitem");
    if (!$(this).hasClass("disabled")) {
      if ($('.Selected').length != 0) {
        RegraCartaConversao.InsertCondicao(idRegra, false, true, false, sequenciaItem)
      }
    }
  });

  $(document).on("click", ".Condicao, .OperadorLogico, .Parenteses", function () {

    if (!$(this).hasClass("Selected")) {
      $('.Selected').each(function (index, element) {
        $(element).removeClass("Selected")
        $(element).addClass("UnSelected")
      });

      if ($(this).hasClass("Selected")) {
        $(this).removeClass("Selected")
        $(this).addClass("UnSelected")
      }
      else {
        $(this).addClass("Selected")
        $(this).removeClass("UnSelected")
      }
    }
    else {
      $(this).removeClass("Selected")
      $(this).addClass("UnSelected")
    }
  });

  $(document).on("click", ".Condicao", function () {
    if ($(this).hasClass("Selected")) {
      $('#BotoesEditar').show();
      $('#BotaoAdicionar').hide();
    }
    else {
      $('#BotoesEditar').hide();
      $('#BotaoAdicionar').show();
    }
  });

  $(document).on("click", ".OperadorLogico", function () {
    $('#BotoesEditar').hide();
    $('#BotaoAdicionar').show();
  });

  $(document).on("click", ".Parenteses", function () {
    $('#BotoesEditar').hide();
    $('#BotaoAdicionar').show();
  });

  $(document).on("click", ".addBefore", function () {
    var botaoClicado = $(this);
    var idRegra = $('#Codigo').val();
    var data = {};
    var sequenciaItem = $('.Selected').data("sequenciaitem");

    if ($('.Selected').length != 0) {
      $('.addBefore').each(function (index, element) {
        $(element).removeAttr("name");
        $(element).removeAttr("id");
      });


      if ($(this).hasClass("OpLogicoButtom")) {
        botaoClicado.attr("name", "operadorLogicoEnum");
        botaoClicado.attr("id", "operadorLogicoEnum");
        data = { operadorLogicoEnum: $(this).data("enum") }
      }
      else {
        botaoClicado.attr("name", "parentesesEnum");
        botaoClicado.attr("id", "parentesesEnum");
        data = { parentesesEnum: $(this).data("enum") }
      }
      RegraCartaConversao.InsertOperadoresCondicionais(data, idRegra, false, false, true, sequenciaItem)
    }
  });

  $(document).on("click", ".addAfter", function () {
    var botaoClicado = $(this);
    var idRegra = $('#Codigo').val();
    var data = {};
    var sequenciaItem = $('.Selected').data("sequenciaitem");

    if ($('.Selected').length != 0) {
      $('.addAfter').each(function (index, element) {
        $(element).removeAttr("name");
        $(element).removeAttr("id");
      });


      if ($(this).hasClass("OpLogicoButtom")) {
        botaoClicado.attr("name", "operadorLogicoEnum");
        botaoClicado.attr("id", "operadorLogicoEnum");
        data = { operadorLogicoEnum: $(this).data("enum") }
      }
      else {
        botaoClicado.attr("name", "parentesesEnum");
        botaoClicado.attr("id", "parentesesEnum");
        data = { parentesesEnum: $(this).data("enum") }
      }
      RegraCartaConversao.InsertOperadoresCondicionais(data, idRegra, false, true, false, sequenciaItem)
    }
  });

  $(document).on("keyup", "#Conteudo", function () {
    var campo = $('#RegraCartaConversaoCampoSelect_id').val();
    var acao = $('#RegraCartaConversaoAcaoSelect_id').val();
    var conteudo = $('#Conteudo').val();


    if (campo != 0 && acao != 0 && conteudo != null && conteudo != "") {
      $('#AdicionarCondicao').removeClass("disabled");
      $('.AdicionarCondicaoBefore').removeClass("disabled");
      $('.EditarCondicao').removeClass("disabled");
      $('.AdicionarCondicaoAfter').removeClass("disabled");
    }
    else {
      $('#AdicionarCondicao').addClass("disabled");
      $('.AdicionarCondicaoBefore').addClass("disabled");
      $('.EditarCondicao').addClass("disabled");
      $('.AdicionarCondicaoAfter').addClass("disabled");
    }

  });

  $(document).on("change", "#RegraCartaConversaoCampoSelectLookup, #RegraCartaConversaoAcaoSelectLookup", function () {
    var campo = $('#RegraCartaConversaoCampoSelectLookup').val();
    var acao = $('#RegraCartaConversaoAcaoSelectLookup').val();
    var conteudo = $('#Conteudo').val();


    if (campo != 0 && campo != null
      && acao != 0 && acao != null
      && conteudo != null && conteudo != "") {
      $('#AdicionarCondicao').removeClass("disabled");
      $('.AdicionarCondicaoBefore').removeClass("disabled");
      $('.EditarCondicao').removeClass("disabled");
      $('.AdicionarCondicaoAfter').removeClass("disabled");
    }
    else {
      $('#AdicionarCondicao').addClass("disabled");
      $('.AdicionarCondicaoBefore').addClass("disabled");
      $('.EditarCondicao').addClass("disabled");
      $('.AdicionarCondicaoAfter').addClass("disabled");
    }
  });

  $(document).on("change", "#RegraCartaConversaoAcaoSelectLookup", function () {
    var idacao = $('#RegraCartaConversaoAcaoSelectLookup').val();
    var idcampo = $('#RegraCartaConversaoCampoSelectLookup').val();
    if (idacao == null || idacao == 0 || idacao == undefined) {
      $('#Conteudo').removeClass();
      $('#Conteudo').inputmask('remove')
      $('#Conteudo').val('')
      $('#Conteudo').addClass("form-control");
      $("#Conteudo").prop('disabled', true);
    }
    else {
      RegraCartaConversao.GetTypeCampo(idcampo);
    }
  });

};

RegraCartaConversao.InsertOperadoresCondicionais = function (data, idRegra, insertUltimo, insertAfter, insertBefore, sequenciaItem) {
  var cabecalho = {
    "idRegraConversao": idRegra
    , "insertUltimo": insertUltimo
    , "insertAfter": insertAfter
    , "insertBefore": insertBefore
    , "sequenciaItem": sequenciaItem
  }

  $.ajax({
    type: 'POST',
    headers: cabecalho,
    url: GetURLBaseComplete() + '/RegraCartaConversao/CreateCondicao',
    //processData: false,
    data: data,
    //tradicional: true,
    dataType: 'json',
    success: function (data) {
      if (!data.Erro) {
        $('#BotoesEditar').hide();
        $('#BotaoAdicionar').show();
        RegraCartaConversao.GetPartialRegra(idRegra);
      }
    },
    error: function (err) {
      Alerta("Erro", "Gentileza tentar mais tarde.", "red", 5000);
    }
  });
}

RegraCartaConversao.InsertCondicao = function (idRegra, insertUltimo, insertAfter, insertBefore, sequenciaItem) {
  var cabecalho = {
    "idRegraConversao": idRegra
    , "insertUltimo": insertUltimo
    , "insertAfter": insertAfter
    , "insertBefore": insertBefore
    , "sequenciaItem": sequenciaItem
  }

  var valorConteudo = $('#Conteudo').inputmask('unmaskedvalue');
  $('#Conteudo').inputmask('remove');
  $('#Conteudo').val(valorConteudo)

  $.ajax({
    type: 'POST',
    headers: cabecalho,
    url: GetURLBaseComplete() + '/RegraCartaConversao/CreateCondicao',
    //processData: false,
    data: $("#FormCreate").serialize(),
    //tradicional: true,
    dataType: 'json',
    success: function (data) {
      if (!data.Erro) {
        LimparTodosSelect();
        $('#Conteudo').removeClass();
        $('#Conteudo').inputmask('remove')
        $('#Conteudo').val('')
        $('#Conteudo').addClass("form-control");
        $("#Conteudo").prop('disabled', true);
        RegraCartaConversao.GetPartialRegra(idRegra);
        $('#BotoesEditar').hide();
        $('#BotaoAdicionar').show();
      }
    },
    error: function (err) {
      Alerta("Erro", "Gentileza tentar mais tarde.", "red", 5000);
    }
  });
}

RegraCartaConversao.EditCondicao = function (IdRegra, IdSequencia) {
  var cabecalho = {
    "idRegraConversao": IdRegra
    , "sequenciaItem": IdSequencia
  }


  $.ajax({
    type: 'POST',
    headers: cabecalho,
    url: GetURLBaseComplete() + '/RegraCartaConversao/EditCondicao',
    //processData: false,
    data: $("#FormCreate").serialize(),
    //tradicional: true,
    dataType: 'json',
    success: function (data) {
      if (!data.Erro) {
        RegraCartaConversao.GetPartialRegra(IdRegra);
        $('#BotoesEditar').hide();
        $('#BotaoAdicionar').show();
      }
    },
    error: function (err) {
      Alerta("Erro", "Gentileza tentar mais tarde.", "red", 5000);
    }
  });
}

RegraCartaConversao.GetTypeCampo = function (Idcampo) {
  var model = {
    idCampo: Idcampo
  };

  $.ajax({
    type: 'GET',
    url: GetURLBaseComplete() + '/RegraCartaConversaoCampo/GetTypoCampo',
    data: model,
    dataType: 'json',
    success: function (data) {
      if (!data.Erro) {
        $("#Conteudo").prop('disabled', false);

        if (data.Mensagem == "string") {
          $('#Conteudo').removeClass();
          $('#Conteudo').inputmask('remove')
          $('#Conteudo').val('')
          $('#Conteudo').addClass("form-control");
        }
        if (data.Mensagem == "int") {
          $('#Conteudo').removeClass();
          $('#Conteudo').inputmask('remove')
          $('#Conteudo').val('')
          $('#Conteudo').addClass("form-control numerosOnly");
        }
        if (data.Mensagem == "decimalValor") {
          $('#Conteudo').removeClass();
          $('#Conteudo').inputmask('remove')
          $('#Conteudo').val('')
          $('#Conteudo').addClass("form-control moneyRegras");
        }
        RepasseConvenio.IniciaMascaras();
      }
    },
    error: function (err) {
      Alerta("Erro", "Gentileza tentar mais tarde.", "red", 5000);
    }
  });
}

RegraCartaConversao.RemoveItem = function (IdRegra, IdSequencia) {
  var data = {
    idRegraConversao: IdRegra
    , sequenciaItem: IdSequencia
  }


  $.ajax({
    type: 'POST',
    url: GetURLBaseComplete() + '/RegraCartaConversao/DeleteCondicao',
    data: data,
    dataType: 'json',
    success: function (data) {
      if (!data.Erro) {
        RegraCartaConversao.GetPartialRegra(IdRegra);
        $('#BotoesEditar').hide();
        $('#BotaoAdicionar').show();
      }
    },
    error: function (err) {
      Alerta("Erro", "Gentileza tentar mais tarde.", "red", 5000);
    }
  });
}


RegraCartaConversao.GetPartialRegra = function (IdRegra) {
  var data = {
    idRegra: IdRegra
  };

  $.ajax({
    type: 'GET',
    url: GetURLBaseComplete() + '/RegraCartaConversao/PartialRegra',
    //processData: false,
    data: data,
    //tradicional: true,
    dataType: 'html',
    success: function (data) {
      $('#PartialRegra').html(data);
    },
    error: function (err) {
      Alerta("Erro", "Gentileza tentar mais tarde.", "red", 5000);
    }
  });
}

$(document).ready(function () {
  RegraCartaConversao.init();
});
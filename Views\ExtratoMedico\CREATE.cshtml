﻿@using PagedList.Mvc
@using RepasseConvenio.Models
@using RepasseConvenio.Infrastructure
@using RepasseConvenio.Infrastructure.Controls
@model ExtratoMedicoModel

@{
  ViewBag.Title = "Novo Extrato Médico";
  Layout = "~/Views/Shared/_Layout.cshtml";
}

@Scripts.Render("~/Views/ExtratoMedico/ExtratoMedico.js")

@using (Html.BeginForm("Create", "ExtratoMedico", FormMethod.Post))
{
  @Html.HiddenFor(m => m.CodigoMedico)
  
  <div class="row">
    <section class="col-lg-12 connectedSortable ui-sortable">
      <div class="card">
        <div class="card-header ui-sortable-handle">
          <h3 class="card-title">
            <i class="fas fa-user mr-1"></i>
            Extrato Médico
          </h3>
          <div class="card-tools">
          </div>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-3">
              <div class="form-group">
                @Html.LabelFor(m => m.DataApuracao)
                @Html.LibEditorFor(m => m.DataApuracao, new { @class = "form-control airDatePickerDateTime Data" })
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                @Html.LabelFor(m => m.DataPagamento)
                @Html.LibEditorFor(m => m.DataPagamento, new { @class = "form-control airDatePickerDate Data" })
                @Html.ValidationMessageFor(m => m.DataPagamento, "", new { @class = "text-danger" })
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                @Html.LabelFor(m => m.Valor)
                @Html.LibEditorFor(m => m.Valor, new { @class = "form-control money" })
                @Html.ValidationMessageFor(m => m.Valor, "", new { @class = "text-danger" })
              </div>
            </div>

            <div class="col-md-3">
              <div class="form-group">
                @Html.LabelFor(m => m.ClassificacaoSelect)
                @Html.LibSelect2For(m => m.ClassificacaoSelect, new { @class = "form-control" })
              </div>
            </div>

          </div>
          <div class="row">

            <div class="col-md-2">
              <div class="form-group">
                @Html.LabelFor(m => m.TipoLancamentoEnum)
                @Html.LibDropDown(m => m.TipoLancamentoEnum)
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-group">
                @Html.LabelFor(m => m.TipoContaEnum)
                @Html.LibDropDown(m => m.TipoContaEnum)
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group" id="EmpresaCNPJ" hidden="hidden">
                @Html.LabelFor(m => m.EmpresaMedicoCNPJSelect)
                @Html.LibSelect2For(m => m.EmpresaMedicoCNPJSelect, new { @class = "form-control", IdPai = "CodigoMedico" })
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-12">
              <div class="form-group">
                @Html.LibLabelFor(m => m.Observacao)
                @Html.LibEditorFor(m => m.Observacao, new { @class = "form-control " })
                @Html.ValidationMessageFor(m => m.Observacao, "", new { @class = "text-danger" })
              </div>
            </div>
          </div>

        </div>
        <div class="card-footer">
          <button type="button" value="Voltar" class="btn btn-secondary" onclick="window.history.back();">Cancelar</button>
          <button type="submit" value="Create" class="btn btn-info pull-rigth">Salvar</button>
        </div>
      </div>
    </section>
  </div>
}
<style>
  #TipoContaEnum, #TipoLancamentoEnum {
    height: auto !important;
  }
</style>

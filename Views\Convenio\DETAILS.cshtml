﻿@using RepasseConvenio.Models
@model ConvenioModel

@{
  ViewBag.Title = "Detalhes Convênio";
}

@Scripts.Render("~/Views/Convenio/Convenio.js")

<div class="row">
  <section class="col-lg-12 connectedSortable ui-sortable">
    <div class="card">
      <div class="card-header ui-sortable-handle">
        <h3 class="card-title">
          <i class="fas fa-chart-pie mr-1"></i>
            <b>@Model.CNPJ</b> - Código ANS: <b>@Model.CodANS</b> - <b>@Model.RazaoSocial</b>
        </h3>
        <div class="card-tools">
        </div>
      </div><!-- /.card-header -->
      <div class="card-body">
        <div class="row" id="ProcedimentoPartial">
          @Html.Partial("_ProcedimentoPartial", Model.ListProcedimentos)
        </div>
      </div><!-- /.card-body -->
      <div class="card-footer">
        <button type="button" value="Voltar" class="btn btn-secondary" onclick="window.history.back();">Voltar</button>
      </div>
    </div>
  </section>
</div>
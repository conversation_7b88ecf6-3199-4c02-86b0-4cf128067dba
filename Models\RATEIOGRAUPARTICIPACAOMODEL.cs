﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Web;

namespace RepasseConvenio.Models
{
  public class RateioGrauParticipacaoModel
  {
    public int Codigo { get; set; }
    public int CodigoRateio { get; set; }
    public int CodigoMedico { get; set; }
    public int CodGrauParticipacao { get; set; }
    public decimal Percentual { get; set; }

    [DisplayName("Selecione o Médico")]
    [URLSelect("Select2/GetMedicoSelect")]
    [PlaceHolderAttr("Selecione o Médico")]
    public Select2Model MedicoSelect
    {
      get
      {
        MedicoService MedicoService = new MedicoService();
        return MedicoService.GetById(this.CodigoMedico).ToSelect2Model();
      }
      set
      {
        if (value != null && !String.IsNullOrEmpty(value.id) && !value.id.Equals("0"))
          this.CodigoMedico = int.Parse(value.id);
      }
    }

    [DisplayName("Selecione o Grau Participação")]
    [URLSelect("Select2/GetGrauParticipacaoSelect")]
    [PlaceHolderAttr("Selecione o Grau Participação")]
    public Select2Model GrauParticipacaoSelect
    {
      get
      {
        GrauParticipacaoServices GrauParticipacaoServices = new GrauParticipacaoServices();
        return GrauParticipacaoServices.GetById(this.CodGrauParticipacao).ToSelect2Model();
      }
      set
      {
        if (value != null && !String.IsNullOrEmpty(value.id) && !value.id.Equals("0"))
          this.CodGrauParticipacao = int.Parse(value.id);
      }
    }
  }

  public class RateioGPIndex
  {
    public int Codigo { get; set; }
    public string Procedimento { get; set; }
    public string Hospital { get; set; }
    public string NomeMedico { get; set; }
    public string CRMMedico { get; set; }
    public string GrauParticipacao { get; set; }
    public decimal Percentual { get; set; }
  }

  public static class RateioGrauParticipacaoConversion
  {

    public static R_RateioGrauParticipacao toRateioGPCreate(this RateioGrauParticipacaoModel model)
    {
      R_RateioGrauParticipacao entity = new R_RateioGrauParticipacao();

      entity.RG_Id = model.Codigo;
      entity.RG_IdRateioMedico = model.CodigoRateio;
      entity.RG_IdMedico = model.CodigoMedico;
      entity.RG_IdGrauParticipacao = model.CodGrauParticipacao;
      entity.RG_Percentual = model.Percentual;
      return entity;
    }
    public static R_RateioGrauParticipacao toRateioGPEdit(this RateioGrauParticipacaoModel model)
    {
      R_RateioGrauParticipacao entity = new R_RateioGrauParticipacao();
      entity.RG_Id = model.Codigo;
      entity.RG_IdRateioMedico = model.CodigoRateio;
      entity.RG_IdMedico = model.CodigoMedico;
      entity.RG_IdGrauParticipacao = model.CodGrauParticipacao;
      entity.RG_Percentual = model.Percentual;
      return entity;

    }
  }
}
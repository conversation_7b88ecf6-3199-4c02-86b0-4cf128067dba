﻿@using RepasseConvenio.Models
@using System.Globalization;
@model List<AtendimentoNaoPago>

<div class="col-md-12 table-responsive p-0 ">
  <table class="table table-sm table-striped table-hover text-nowrap">
    <thead>
      <tr>
        <th>
          @Html.CheckBox("CheckAllAtdNaoPg")
        </th>
        <th>
          Número do Lote
        </th>
        <th>
          Número da Guia Atendimento
        </th>
        <th>
          Status
        </th>
        <th>
          Valor Faturado
        </th>
      </tr>
    </thead>
    <tbody>
      @foreach (AtendimentoNaoPago item in Model)
      {
        <tr>
          <td>
            <input class="isSelectedAtdNaoPg" data-codigoitem="@item.Codigo" data-val="true" name="item.isSelected" type="checkbox" value="true">
          </td>
          <td>
            @item.NroLote
          </td>
          <td>
            @item.NroGuiaAtendimento
          </td>
          <td>
            @item.StatusGuia
          </td>
          <td>
            @item.ValorFaturado.ToString("C2", CultureInfo.CreateSpecificCulture("pt-BR"))
          </td>
        </tr>
      }
    </tbody>
  </table>
</div>

﻿@using RepasseConvenio.Models
@using PagedList
@using PagedList.Mvc;
@model ConvenioIndex

@{
  ViewBag.Title = "Lista Convênio";
}

<script src="~/signalr/hubs"></script>
@Scripts.Render("~/Scripts/SignalR.js")
@Scripts.Render("~/Views/Convenio/Convenio.js")
@Styles.Render("~/Views/Convenio/Convenio.css")
@*@Scripts.Render("~/Views/Convenio/Convenio.css")*@

<div class="row">
  <section class="col-lg-12 connectedSortable ui-sortable">
    <div class="card">
      <div class="card-header ui-sortable-handle card-header-sm">
        <h3 class="card-title">
          <i class="fas fa-pagelines"></i>
          Convênio
        </h3>
        <div class="card-tools">
          <button type="button" class="btn btn-block btn-outline-primary" id="IntegrarConvenios"> Integrar Convênios </button>
        </div>
        <div class="card-tools">
        </div>
      </div><!-- /.card-header -->

      <div class="card-body">
        <div class="card-acoes col-6">
          <a class="btn btn-block btn-outline-primary disabled" id="Editar"> Editar </a>
          <a class="btn btn-block btn-outline-primary disabled" id="Detalhes"> Detalhes </a>
          <a class="btn btn-block btn-outline-primary disabled" id="Responsavel"> Responsáveis </a>

        </div>


        <div class="tab-content p-0">
          <div class="box-body">
            <div class="row">
              <div class="col-md-12 table-responsive p-0">
                <table id="TableTela" class="table-striped table table-sm table-hover text-nowrap">
                  <thead>
                    <tr>
                      <th>
                        CNPJ
                      </th>
                      <th>
                        Razão Social
                      </th>
                      <th>
                        Código ANS
                      </th>
                      <th>
                        Autarquia
                      </th>
                      <th>
                        Vencimento Lote de Glosa
                      </th>
                      <th>
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    @foreach (ConvenioModel item in Model.ListaConvenio)
                    {
                      <tr class="CrSelecionavel" data-codigoconvenio="@item.Codigo">
                        <td>
                          <input type="hidden" @item.Codigo />
                          @item.CNPJ
                        </td>
                        <td>
                          @item.RazaoSocial
                        </td>
                        <td>
                          @item.CodANS
                        </td>
                        <td>
                          @if (item.Autarquia == true)
                          {
                            <text>
                              Sim
                            </text>
                          }
                          else
                          {
                            <text>
                              Não
                            </text>
                          }
                        </td>
                        <td>
                          @item.VencLoteGlosa
                        </td>
                      </tr>
                    }
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <div class="box-footer with-border" style="padding: 0 10pt;">
            <div class="row">
              <div class="col-md-8 ">
                @Html.PagedListPager(Model.QuantidadeConvenios, Page => Url.Action("Index", new { Page }))
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>

﻿@using RepasseConvenio.Infrastructure.Controls
@using RepasseConvenio.Models
@model List<GuiaDemonstrativoIndex>

@{
  ViewBag.Title = "Lista Guias";
  ViewBag.DescricaoTela = "Lista Guias";
  ViewBag.ResumoTela = "Lista Guias";
  Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="row">
  <section class="col-lg-12 connectedSortable ui-sortable">
    <div class="card">
      <div class="card-header ui-sortable-handle">
        <h3 class="card-title">
          Demonstrativo
        </h3>
        <div class="card-tools">
        </div>
      </div>
      <div class="card-body">
        <div class="tab-content p-0">
          <div class="box-body">
            <div class="row">
              <div class="col-md-12 table-responsive p-0 ">
                <table class="table table-sm table-striped table-hover text-nowrap">
                  <thead>
                    <tr>
                      <th>
                        @*@Html.CheckBox("CheckAllPlanRecebimento")*@
                      </th>
                      <th>
                        Número G<PERSON>
                      </th>
                      <th>
                        Número Car<PERSON>nh<PERSON>
                      </th>
                      <th>
                        Número Atendimento
                      </th>
                      <th>
                        Data Atendimento
                      </th>
                      <th>
                        Total Faturado
                      </th>
                      <th>
                        Valor Apresentado
                      </th>
                      <th>
                        Total Glosado
                      </th>
                      <th>
                        Total Pago
                      </th>
                      <th>

                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    @foreach (GuiaDemonstrativoIndex item in Model)
                    {
                      <tr>
                        <td>
                          @*<input class="isSelected" data-codigoplanilha="@item.Codigo" data-val="true" data-val-required="O campo isSelected é obrigatório." name="item.isSelected" type="checkbox" value="true">*@
                        </td>
                        <td>
                          @item.NumeroGuia
                        </td>
                        <td>
                          @item.NumeroCarteirinha
                        </td>
                        <td>
                          @item.NumeroAtendimento
                        </td>
                        <td>
                          @item.DataAtendimento.ToString("dd/MM/yyyy")
                        </td>
                        <td>
                          @item.ValorFaturado
                        </td>
                        <td>
                          @item.ValorApresentado
                        </td>
                        <td>
                          @item.ValorGlosado
                        </td>
                        <td>
                          @item.ValorPago
                        </td>
                        <td>
                          @*<a data-url="@Url.Action("Edit", "PlanilhaRecebimento", new { id = item.Codigo })" data-idplan="@item.Codigo" data-idrepasse="@item.CodigoRepasse" class="btn btn-outline-warning btn-circle btn-sm btnEditPlanRecebimento" title="Editar">
                              Visualizar
                            </a>*@
                        </td>
                      </tr>
                    }
                  </tbody>
                </table>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>

<div class="box box-primary">
  <div class="box-footer">
    <button type="button" value="Voltar" class="btn btn-secondary" onclick="location.href='@Url.Action("Index", "Repasse")'">Cancelar</button>
  </div>
  <!-- /.box-footer -->
</div>
<br />



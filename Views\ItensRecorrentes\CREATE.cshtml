﻿@using PagedList.Mvc
@using RepasseConvenio.Models
@using RepasseConvenio.Infrastructure
@using RepasseConvenio.Infrastructure.Controls
@model ItensRecorrentesModel

@{
  ViewBag.Title = "Novo Lançamento Recorrente";
  Layout = "~/Views/Shared/_Layout.cshtml";
}

@Scripts.Render("~/Views/ItensRecorrentes/ItensRecorrentes.js")

@using (Html.BeginForm("Create", "ItensRecorrentes", FormMethod.Post))
{
  @Html.HiddenFor(m => m.CodigoMedico)
  @Html.HiddenFor(m => m.recorrenteAux)
  <div class="row">
    <section class="col-lg-12 connectedSortable ui-sortable">
      <div class="card">
        <div class="card-header ui-sortable-handle">
          <h3 class="card-title">
            <i class="fas fa-book mr-1"></i>
            Lançamento Recorrente
          </h3>
          <div class="card-tools">
          </div>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-2">
              <input type="checkbox" name="Recorrente" class="icheck-blue" id="Recorrente" />
              @Html.LabelFor(m => m.Recorrente)
            </div>


            <div class="col-md-3">
              <div class="form-group">
                @Html.LabelFor(m => m.DataRecorrencia)
                @Html.LibEditorFor(m => m.DataRecorrencia, new { @class = "form-control airDatePickerDate Data" })
                @Html.ValidationMessageFor(m => m.DataRecorrencia, "", new { @class = "text-danger" })
              </div>
            </div>
            <div class="col-md-3 recorrentediv">
              <div class="form-group">
                @Html.LabelFor(m => m.TipoRecorrenciaEnum)
                @Html.LibDropDown(m => m.TipoRecorrenciaEnum)
              </div>
            </div>
            <div class="col-md-3 recorrentediv">
              <div class="form-group">
                @Html.LabelFor(m => m.DataFim)
                @Html.LibEditorFor(m => m.DataFim, new { @class = "form-control airDatePickerDate Data" })
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-2">
              <div class="form-group">
                @Html.LabelFor(m => m.Valor)
                @Html.LibEditorFor(m => m.Valor, new { @class = "form-control money" })
                @Html.ValidationMessageFor(m => m.Valor, "", new { @class = "text-danger" })
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                @Html.LabelFor(m => m.ClassificacaoSelect)
                @Html.LibSelect2For(m => m.ClassificacaoSelect, new { @class = "form-control" })
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-group">
                @Html.LabelFor(m => m.TipoLancamentoEnum)
                @Html.LibDropDown(m => m.TipoLancamentoEnum)
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-group">
                @Html.LabelFor(m => m.TipoContaEnum)
                @Html.LibDropDown(m => m.TipoContaEnum)
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group" id="EmpresaCNPJ" hidden="hidden">
                @Html.LabelFor(m => m.EmpresaMedicoCNPJSelect)
                @Html.LibSelect2For(m => m.EmpresaMedicoCNPJSelect, new { @class = "form-control" })
              </div>
            </div>
          </div>
        </div>
        <div class="card-footer">
          <button type="button" value="Voltar" class="btn btn-secondary" onclick="location.href='@Url.Action("Index","ItensRecorrentes", new {id = Model.CodigoMedico })'">Cancelar</button>
          <button type="submit" value="Create" class="btn btn-info pull-rigth">Salvar</button>
        </div>
      </div>
    </section>
  </div>
}
<style>
  #TipoContaEnum, #TipoLancamentoEnum, #TipoRecorrenciaEnum {
    height: auto !important;
  }
</style>

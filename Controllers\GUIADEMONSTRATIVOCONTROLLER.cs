﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Controls;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;


namespace RepasseConvenio.Controllers
{
  public class GuiaDemonstrativoController : LibController
  {
    public ActionResult Index(int IdDemonstrativo)
    {
      try
      {
        GuiaDemonstrativoService guiaDemonstrativoService = new GuiaDemonstrativoService();
        List<GuiaDemonstrativoIndex> list = guiaDemonstrativoService.GetListaGuiaDemonstrativoIndex(IdDemonstrativo);
        return View(list);
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message, "12000"));
        return RedirectToAction("Index");
      }
    }

  }
}
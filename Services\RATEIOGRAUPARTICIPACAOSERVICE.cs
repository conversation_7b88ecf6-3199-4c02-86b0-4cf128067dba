﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Models;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Web;

namespace RepasseConvenio.Services
{
  public class RateioGrauParticipacaoService : ServiceBase
  {
    public RateioGrauParticipacaoService()
   : base()
    { }
    public RateioGrauParticipacaoService(RepasseEntities Contexto)
       : base(Contexto)
    { }

    public RateioGrauParticipacaoService(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public RateioGrauParticipacaoService(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public List<RateioGrauParticipacaoModel> Get()
    {
      string query = @"SELECT
                          RG_IdGrauParticipacao [CodGrauParticipacao]
                        , RG_Percentual [Percentual]
                        , RG_IdRateioMedico [CodigoRateio]
                        , RG_IdMedico [CodigoMedico]
                        , RG_Id [Codigo]
                     FROM R_RateioGrauParticipaca";

      return Contexto.Database.SqlQuery<RateioGrauParticipacaoModel>(query).ToList();
    }

    public List<RateioGPIndex> GettoIndex(int id)
    {
      string query = @"SELECT
						              RG.RG_Id [Codigo]
						            , RM.RM_CodigoProcedimento + ' - ' + RM.RM_DescricaoProcedimento [Procedimento]
						            , H.H_Nome [Hospital]
						            , M.M_Nome [NomeMedico]
						            , M.M_CRM [CRMMedico]
                        , GP.GP_Codigo + ' - ' + GP.GP_Nome [GrauParticipacao]
                        , RG.RG_Percentual [Percentual]
                     FROM R_RateioGrauParticipacao RG
                       INNER JOIN R_RateioMedico RM ON RG.RG_IdRateioMedico = RM.RM_Id
                       INNER JOIN R_Medico M ON M.M_Id = RG.RG_IdMedico
					             LEFT JOIN R_Hospital H ON H.H_Id = RM.RM_IdHospital
                       INNER JOIN R_GrauParticipacao GP ON GP.GP_Id = RG.RG_IdGrauParticipacao
                      WHERE RG.RG_IdRateioMedico = @id";

      return Contexto.Database.SqlQuery<RateioGPIndex>(query, new SqlParameter("@id", id)).ToList();
    }

    public RateioGrauParticipacaoModel GetbyId(int id)
    {
      string query = @"SELECT
                          RG_IdGrauParticipacao [CodGrauParticipacao]
                        , RG_Percentual [Percentual]
                        , RG_IdRateioMedico [CodigoRateio]
                        , RG_IdMedico [CodigoMedico]
                        , RG_Id [Codigo]
                     FROM R_RateioGrauParticipacao
                      WHERE RG_Id = @id";

      return Contexto.Database.SqlQuery<RateioGrauParticipacaoModel>(query, new SqlParameter("@id", id)).FirstOrDefault();
    }

    public bool Exist(int rateio, int medico, int grau)
    {
      R_RateioGrauParticipacao rateioMedico = Contexto.R_RateioGrauParticipacao.Where(a => a.RG_IdRateioMedico == rateio && a.RG_IdMedico == medico && a.RG_IdGrauParticipacao == grau).FirstOrDefault();

      if (rateioMedico == null)
        return false;
      else
        return true;
    }

    public void Create(RateioGrauParticipacaoModel model)
    {
      try
      {
        R_RateioGrauParticipacao rateio = model.toRateioGPCreate();
        Create(rateio);
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }

    public void Edit(RateioGrauParticipacaoModel model)
    {
      R_RateioGrauParticipacao rateio = model.toRateioGPEdit();
      Edit(rateio);
    }
  }
}
﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Models;
using RepasseConvenio.WSPortalCooperado;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.Entity;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Text;
using System.Transactions;
using System.Web;
using System.Data.OleDb;
using RepasseConvenio.Infrastructure;
using Microsoft.Ajax.Utilities;
using System.Linq.Expressions;
using PagedList;

namespace RepasseConvenio.Services
{
  public class LoteServices : ServiceBase
  {
    public LoteServices()
   : base()
    { }
    public LoteServices(RepasseEntities Contexto)
       : base(Contexto)
    { }

    public LoteServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public LoteServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public int IntegraLotes(LoteRepasse loteRepasse)
    {
      StatusLoteServices statusLoteServices = new StatusLoteServices();
      ConvenioServices convenioServices = new ConvenioServices();
      R_Lote lote = GetByNumeroLote(loteRepasse.NumeroLote.ToString());

      if (lote == null)
      {
        int IdStatusLoteNaoProcessado = statusLoteServices.GetIdByEnum(StatusLoteEnum.NaoProcessado);
        lote = loteRepasse.ModelToEntityCreate(IdStatusLoteNaoProcessado);
        Create(lote);
      }

      return lote.L_Id;
    }

    public R_Lote GetByNumeroLote(string NumeroLote)
    {
      string query = @"SELECT
                        *
                       FROM R_Lote
                       WHERE L_NumeroLote = @NumeroLote";

      return Contexto.Database.SqlQuery<R_Lote>(query, new SqlParameter("@NumeroLote", NumeroLote)).FirstOrDefault();
    }

    public R_StatusLote GetById(int Id)
    {

      string query = @"SELECT
                        *
                       FROM R_StatusLote
                       WHERE SL_Enum = @Id";

      return Contexto.Database.SqlQuery<R_StatusLote>(query, new SqlParameter("Id", Id)).FirstOrDefault();

    }

    public R_Lote GetByLoteId(int Id)
    {

      string query = @"SELECT
                        *
                       FROM R_Lote
                       WHERE L_Id= @Id";

      return Contexto.Database.SqlQuery<R_Lote>(query, new SqlParameter("Id", Id)).FirstOrDefault();

    }

    public string GetNrLoteByLoteId(int Id)
    {
      string query = @"SELECT
                        L_NumeroLote AS value
                       FROM R_Lote
                       WHERE L_Id= @Id";

      QuerySearch search = Contexto.Database.SqlQuery<QuerySearch>(query, new SqlParameter("Id", Id)).FirstOrDefault();
      if (search != null)
        return search.value;
      else
        return "";
    }

    public IPagedList<LoteModel> Get(int pageNumber, string filtro)
    {
      string query = String.Format(@" SELECT 
	                                        L.L_Id AS Codigo,
	                                        L.L_DataCriacao AS DataCriacao,
	                                        L.L_DataVencimento AS DataVencimento,
	                                        L.L_NumeroLote AS NumeroLote,
										                      L.L_DataEnvio As DataEnvio,
										                      L.L_IdStatusLote As IdStatusLote
                                    FROM R_Lote L
                                    ");

      return Contexto.Database.SqlQuery<LoteModel>(query)
             .ToList().OrderBy(a => a.DataVencimento).ToPagedList(pageNumber, PageSize);
    }

    public void AtualizarStatusLote(int IdDemonstrativo)
    {
      DemonstrativoConvenioService demonstrativoConvenioService = new DemonstrativoConvenioService();
      GuiaDemonstrativoService guiaDemonstrativoService = new GuiaDemonstrativoService();
      ItensLoteServices itensLoteServices = new ItensLoteServices();
      StatusItensLoteServices statusItensLoteServices = new StatusItensLoteServices();
      StatusLoteServices statusLoteServices = new StatusLoteServices();

      R_DemonstrativoConvenio demonstrativoConvenio = demonstrativoConvenioService.GetById(IdDemonstrativo);
      List<int> ListaLotes = GetListIdLoteByIdDemonstrativo(demonstrativoConvenio.DC_Id);

      List<R_StatusItensLote> ListaStatusItensLote = statusItensLoteServices.Getall();
      List<R_StatusLote> ListaStatusLote = statusLoteServices.Getall();

      foreach (var IdLote in ListaLotes)
      {
        List<R_ItensLote> ListaItensLote = itensLoteServices.GetListaItensLoteByIdLote(IdLote);
        AtualizaItensLote(ListaItensLote);

        R_Lote lote = GetLote(IdLote);
        int IdStatusItensLoteNaoProcessado = ListaStatusItensLote.Where(a => a.SIL_Enum == (int)StatusItensLoteEnum.NaoProcessado).Select(a => a.SIL_Id).FirstOrDefault();
        int IdStatusItensLoteNaoPago = ListaStatusItensLote.Where(a => a.SIL_Enum == (int)StatusItensLoteEnum.NaoPago).Select(a => a.SIL_Id).FirstOrDefault();
        int IdStatusItensLoteGlosado = ListaStatusItensLote.Where(a => a.SIL_Enum == (int)StatusItensLoteEnum.Glosado).Select(a => a.SIL_Id).FirstOrDefault();
        if (ListaItensLote.Where(a => a.IL_IdStatusItensLote == IdStatusItensLoteNaoProcessado).Count() == ListaItensLote.Count())
        {
          lote.L_IdStatusLote = ListaStatusLote.Where(a => a.SL_Enum == (int)StatusLoteEnum.NaoProcessado).Select(a => a.SL_Id).FirstOrDefault();
          Edit(lote);
        }
        else if (ListaItensLote.Where(a => a.IL_IdStatusItensLote == IdStatusItensLoteNaoProcessado).Count() == 0
              && ListaItensLote.Where(a => a.IL_IdStatusItensLote == IdStatusItensLoteNaoPago).Count() == 0)
        {
          lote.L_IdStatusLote = ListaStatusLote.Where(a => a.SL_Enum == (int)StatusLoteEnum.Processado).Select(a => a.SL_Id).FirstOrDefault();
          Edit(lote);
        }
        else if (ListaItensLote.Where(a => a.IL_IdStatusItensLote == IdStatusItensLoteNaoProcessado).Count() < ListaItensLote.Count()
              || ListaItensLote.Where(a => a.IL_IdStatusItensLote == IdStatusItensLoteNaoPago).Count() < ListaItensLote.Count())
        {
          lote.L_IdStatusLote = ListaStatusLote.Where(a => a.SL_Enum == (int)StatusLoteEnum.ProcessadoParcialmente).Select(a => a.SL_Id).FirstOrDefault();
          Edit(lote);
        }
      }
    }

    public void AtualizaItensLote(List<R_ItensLote> ListaItensLote)
    {
      GuiaDemonstrativoService guiaDemonstrativoService = new GuiaDemonstrativoService();
      StatusItensLoteServices statusItensLoteServices = new StatusItensLoteServices();
      SituacaoItensLoteServices situacaoItensLoteServices = new SituacaoItensLoteServices();
      List<R_StatusItensLote> ListaStatusItensLote = statusItensLoteServices.Getall();
      List<R_SituacaoItensLote> ListaSituacaoItensLote = situacaoItensLoteServices.Getall();

      foreach (var item in ListaItensLote)
      {
        GuiaDemonstrativoAtualizaLote guiaDemonstrativoAtualizaLote = guiaDemonstrativoService.GetGuiaDemonstrativoAtualizaLote(item.IL_IdGuiaAtendimento);

        if (guiaDemonstrativoAtualizaLote != null)
        {
          if (guiaDemonstrativoAtualizaLote.TotalFaturado == guiaDemonstrativoAtualizaLote.TotalPago || guiaDemonstrativoAtualizaLote.TotalPago > guiaDemonstrativoAtualizaLote.TotalFaturado)
          {
            item.IL_IdStatusItensLote = ListaStatusItensLote.Where(a => a.SIL_Enum == (int)StatusItensLoteEnum.Pago).Select(a => a.SIL_Id).FirstOrDefault();
            item.IL_IdSituacaoItensLote = ListaSituacaoItensLote.Where(a => a.SIL_Enum == (int)SituacaoItensLoteEnum.Pago).Select(a => a.SIL_Id).FirstOrDefault();
            Edit(item);
          }
        }

        if (item.IL_IdStatusItensLote == ListaStatusItensLote.Where(a => a.SIL_Enum == (int)StatusItensLoteEnum.Glosado).Select(a => a.SIL_Id).FirstOrDefault()
       || item.IL_IdStatusItensLote == ListaStatusItensLote.Where(a => a.SIL_Enum == (int)StatusItensLoteEnum.Pago).Select(a => a.SIL_Id).FirstOrDefault()
       || item.IL_IdStatusItensLote == ListaStatusItensLote.Where(a => a.SIL_Enum == (int)StatusItensLoteEnum.NaoPago).Select(a => a.SIL_Id).FirstOrDefault()
       || item.IL_IdStatusItensLote == ListaStatusItensLote.Where(a => a.SIL_Enum == (int)StatusItensLoteEnum.NaoProcessado).Select(a => a.SIL_Id).FirstOrDefault())
        {
          if (guiaDemonstrativoAtualizaLote == null)
          {
            item.IL_IdStatusItensLote = ListaStatusItensLote.Where(a => a.SIL_Enum == (int)StatusItensLoteEnum.NaoPago).Select(a => a.SIL_Id).FirstOrDefault();
            item.IL_IdSituacaoItensLote = ListaSituacaoItensLote.Where(a => a.SIL_Enum == (int)SituacaoItensLoteEnum.NaoPago).Select(a => a.SIL_Id).FirstOrDefault();
            Edit(item);
          }
          else if (guiaDemonstrativoAtualizaLote.IdGuia.HasValue)
          {
            if (guiaDemonstrativoAtualizaLote.TotalPago == 0)
            {
              item.IL_IdStatusItensLote = ListaStatusItensLote.Where(a => a.SIL_Enum == (int)StatusItensLoteEnum.NaoPago).Select(a => a.SIL_Id).FirstOrDefault();
              item.IL_IdSituacaoItensLote = ListaSituacaoItensLote.Where(a => a.SIL_Enum == (int)SituacaoItensLoteEnum.NaoPago).Select(a => a.SIL_Id).FirstOrDefault();
              Edit(item);
            }
            else if (guiaDemonstrativoAtualizaLote.TotalFaturado > guiaDemonstrativoAtualizaLote.TotalPago)
            {
              item.IL_IdStatusItensLote = ListaStatusItensLote.Where(a => a.SIL_Enum == (int)StatusItensLoteEnum.Glosado).Select(a => a.SIL_Id).FirstOrDefault();
              item.IL_IdSituacaoItensLote = ListaSituacaoItensLote.Where(a => a.SIL_Enum == (int)SituacaoItensLoteEnum.GlosasGeradas).Select(a => a.SIL_Id).FirstOrDefault();
              Edit(item);
            }
            else if (guiaDemonstrativoAtualizaLote.TotalFaturado == guiaDemonstrativoAtualizaLote.TotalPago || guiaDemonstrativoAtualizaLote.TotalPago > guiaDemonstrativoAtualizaLote.TotalFaturado)
            {
              item.IL_IdSituacaoItensLote = ListaSituacaoItensLote.Where(a => a.SIL_Enum == (int)SituacaoItensLoteEnum.Pago).Select(a => a.SIL_Id).FirstOrDefault();
              item.IL_IdStatusItensLote = ListaStatusItensLote.Where(a => a.SIL_Enum == (int)StatusItensLoteEnum.Pago).Select(a => a.SIL_Id).FirstOrDefault();
              Edit(item);
            }
          }
        }
      }

    }

    public void AtualizarLoteParcialmente(int IdDemonstrativo, List<int> ListIdGuiaAtendimento)
    {
      ItensLoteServices itensLoteServices = new ItensLoteServices();
      StatusItensLoteServices statusItensLoteServices = new StatusItensLoteServices();
      StatusLoteServices statusLoteServices = new StatusLoteServices();
      RepasseService repasseService = new RepasseService();

      List<int> ListaLotes = GetListIdGuiaAtendimentoByGuiaDemonstrativo(ListIdGuiaAtendimento);
      List<R_StatusItensLote> ListaStatusItensLote = statusItensLoteServices.Getall();
      List<R_StatusLote> ListaStatusLote = statusLoteServices.Getall();

      foreach (var IdLote in ListaLotes)
      {
        List<R_ItensLote> ListaItensLote = itensLoteServices.GetListaItensLoteByIdLote(IdLote);

        List<RepassesStatus> ListaRepassesStatus = repasseService.GetRepassesStatus(ListIdGuiaAtendimento);

        AtualizaItensLote(ListaItensLote);

        foreach (var item in ListaItensLote)
        {
          int IdStatusItemPago = statusItensLoteServices.GetIdByEnum(StatusItensLoteEnum.Pago);

          if (item.IL_IdStatusItensLote != IdStatusItemPago)
          {
            if (ListaRepassesStatus.Where(a => a.EnumStatusRepasse == EnumStatusRepasse.ProcessadoGlosado).Count() == 1)
            {
              int IdStatusItemGlosado = statusItensLoteServices.GetIdByEnum(StatusItensLoteEnum.Reapresentacao);
              string UpdateItensLote = @"UPDATE R_ItensLote SET IL_IdStatusItensLote = @IdStatusItemGlosado WHERE IL_IdGuiaAtendimento IN (@IdGuiaAtendimento)";
              Contexto.Database.ExecuteSqlCommand(UpdateItensLote, new SqlParameter("@IdStatusItemGlosado", IdStatusItemGlosado)
                                                                 , new SqlParameter("@IdGuiaAtendimento", item.IL_IdGuiaAtendimento));
            }
            else if (ListaRepassesStatus.Where(a => a.EnumStatusRepasse == EnumStatusRepasse.ProcessadoGlosado).Count() == 2)
            {
              int IdStatusItemGlosado = statusItensLoteServices.GetIdByEnum(StatusItensLoteEnum.Contestacao);
              string UpdateItensLote = @"UPDATE R_ItensLote SET IL_IdStatusItensLote = @IdStatusItemGlosado WHERE IL_IdGuiaAtendimento IN (@IdGuiaAtendimento)";
              UpdateItensLote = UpdateItensLote.Replace("#ids#", string.Join(",", ListIdGuiaAtendimento));
              Contexto.Database.ExecuteSqlCommand(UpdateItensLote, new SqlParameter("@IdStatusItemGlosado", IdStatusItemGlosado)
                                                                 , new SqlParameter("@IdGuiaAtendimento", item.IL_IdGuiaAtendimento));
            }
            else if (ListaRepassesStatus.Where(a => a.EnumStatusRepasse == EnumStatusRepasse.ProcessadoGlosado).Count() > 2)
            {
              int IdStatusItemGlosado = statusItensLoteServices.GetIdByEnum(StatusItensLoteEnum.Treplica);
              string UpdateItensLote = @"UPDATE R_ItensLote SET IL_IdStatusItensLote = @IdStatusItemGlosado WHERE IL_IdGuiaAtendimento IN (@IdGuiaAtendimento)";
              UpdateItensLote = UpdateItensLote.Replace("#ids#", string.Join(",", ListIdGuiaAtendimento));
              Contexto.Database.ExecuteSqlCommand(UpdateItensLote, new SqlParameter("@IdStatusItemGlosado", IdStatusItemGlosado)
                                                                 , new SqlParameter("@IdGuiaAtendimento", item.IL_IdGuiaAtendimento));
            }
          }

        }

        R_Lote lote = GetLote(IdLote);
        int IdStatusItensLoteNaoProcessado = ListaStatusItensLote.Where(a => a.SIL_Enum == (int)StatusItensLoteEnum.NaoProcessado).Select(a => a.SIL_Id).FirstOrDefault();
        int IdStatusItensLoteNaoPago = ListaStatusItensLote.Where(a => a.SIL_Enum == (int)StatusItensLoteEnum.NaoPago).Select(a => a.SIL_Id).FirstOrDefault();
        int IdStatusItensLoteGlosado = ListaStatusItensLote.Where(a => a.SIL_Enum == (int)StatusItensLoteEnum.Glosado).Select(a => a.SIL_Id).FirstOrDefault();
        if (ListaItensLote.Where(a => a.IL_IdStatusItensLote == IdStatusItensLoteNaoProcessado).Count() == ListaItensLote.Count())
        {
          lote.L_IdStatusLote = ListaStatusLote.Where(a => a.SL_Enum == (int)StatusLoteEnum.NaoProcessado).Select(a => a.SL_Id).FirstOrDefault();
          Edit(lote);
        }
        else if (ListaItensLote.Where(a => a.IL_IdStatusItensLote == IdStatusItensLoteNaoProcessado).Count() == 0
              && ListaItensLote.Where(a => a.IL_IdStatusItensLote == IdStatusItensLoteNaoPago).Count() == 0)
        {
          lote.L_IdStatusLote = ListaStatusLote.Where(a => a.SL_Enum == (int)StatusLoteEnum.Processado).Select(a => a.SL_Id).FirstOrDefault();
          Edit(lote);
        }
        else if (ListaItensLote.Where(a => a.IL_IdStatusItensLote == IdStatusItensLoteNaoProcessado).Count() < ListaItensLote.Count()
              || ListaItensLote.Where(a => a.IL_IdStatusItensLote == IdStatusItensLoteNaoPago).Count() < ListaItensLote.Count())
        {
          lote.L_IdStatusLote = ListaStatusLote.Where(a => a.SL_Enum == (int)StatusLoteEnum.ProcessadoParcialmente).Select(a => a.SL_Id).FirstOrDefault();
          Edit(lote);
        }
      }
    }

    public List<int> GetListIdGuiaAtendimentoByGuiaDemonstrativo(List<int> ListIdGuiaAtendimento)
    {
      string query = @"SELECT 
                        DISTINCT IL_IdLote
                       FROM R_ItensLote
                       WHERE IL_IdGuiaAtendimento IN (#ids#)";

      query = query.Replace("#ids#", string.Join(",", ListIdGuiaAtendimento.ToArray()));

      return Contexto.Database.SqlQuery<int>(query).ToList();
    }

    public int GetIdLoteByIdGuia(int IdGuiaRandom)
    {
      string query = @"SELECT
                        TOP(1)IL_IdLote
                       FROM R_ItensLote where IL_IdGuiaAtendimento = @IdGuiaRandom";

      return Contexto.Database.SqlQuery<int>(query, new SqlParameter("@IdGuiaRandom", IdGuiaRandom)).FirstOrDefault();
    }

    public List<int> GetListIdLoteByIdDemonstrativo(int IdDemonstrativo)
    {
      string query = @"SELECT 
                        DISTINCT L.L_Id
                       FROM R_GuiaDemonstrativo GD
                       INNER JOIN R_ItensLote IL ON IL.IL_IdGuiaAtendimento = GD.GD_IdGuia AND GD.GD_IdDemonstrativoConvenio = @IdDemonstrativo
                       INNER JOIN R_Lote L ON L.L_Id = IL.IL_IdLote";

      return Contexto.Database.SqlQuery<int>(query, new SqlParameter("@IdDemonstrativo", IdDemonstrativo)).ToList();
    }

    public R_Lote GetLote(int IdLote)
    {
      string query = @"SELECT
                        *
                       FROM R_Lote
                       WHERE L_Id = @IdLote";

      return Contexto.Database.SqlQuery<R_Lote>(query, new SqlParameter("@IdLote", IdLote)).FirstOrDefault();
    }

    public List<LoteRepassaeModal> GetAllLoteByRepasse(int IdRepasse)
    {
      string query = @"SELECT 
                        DISTINCT L.L_Id [Codigo],
                        L.L_NumeroLote [NumeroLote],
                        L.L_DataCriacao [DataCriacao],
                        L.L_DataEnvio [DataEnvio],
                        L.L_DataVencimento [DataVencimento],
                        SL.SL_Descricao [DescricaoStatus]
                       FROM R_GuiaDemonstrativo GD
                       INNER JOIN R_DemonstrativoConvenio DC ON DC.DC_Id = GD.GD_IdDemonstrativoConvenio AND DC.DC_IdRepasse = @IdRepasse
                       INNER JOIN R_ItensLote IL ON IL.IL_IdGuiaAtendimento = GD.GD_IdGuia
                       INNER JOIN R_Lote L ON IL.IL_IdLote = L.L_Id
                       INNER JOIN R_StatusLote SL ON L.L_IdStatusLote = SL.SL_Id";

      return Contexto.Database.SqlQuery<LoteRepassaeModal>(query, new SqlParameter("@IdRepasse", IdRepasse)).ToList();
    }
  }
}
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Linq.Expressions;
using System.Text;
using System.Web.Mvc.Html;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using RepasseConvenio.Domain.Infrastructure.Helpers;
using System.Web.Routing;

namespace RepasseConvenio.Infrastructure.Controls
{
  public class ButtonConfiguration
  {
    public string Id { get; set; }
    public string UrlAction { get; set; }
    public bool hasText { get; set; }
    public string Texto { get; set; }
    public bool hasIcon { get; set; }
    public string iconClass { get; set; }
    public object attributes { get; set; }
  }

  public class LibButton
  {
    public HtmlHelper HtmlHelper { get; set; }
    public ButtonConfiguration ButtonConfiguration { get; set; }

    public LibButton(HtmlHelper htmlHelper, ButtonConfiguration ButtonConfiguration)
    {
    }

    public string RenderHtml()
    {
      return "";
    }

  }
}
﻿@using RepasseConvenio.Models
@model List<ExtratoMedicoRegistroPagamento>

<div class="card-acoes">
  @*<a class="btn btn-block btn-outline-primary disabled" id="CartaConversao"> Carta Conversão </a>*@
</div>

<div class="col-md-12 table-responsive p-0">

  <table class="table table-sm table-striped table-hover text-nowrap">
    <thead>
      <tr>
        <th>
          Número da Guia
        </th>
        <th>
          Valor
        </th>
        <th>
          Data do Processamento
        </th>
        <th>
          Tipo de Lançamento
        </th>
      </tr>
    </thead>
    <tbody>
      @foreach (ExtratoMedicoRegistroPagamento item in Model)
      {
        <tr>
          <td>
            @if (string.IsNullOrEmpty(item.NroGuiaAtendimento))
            {
              <text>-</text>
            }
            else
            {
              <text>@(item.NroGuiaAtendimento)</text>
            }
          </td>
          <td>
            @item.Valor
          </td>
          <td>
            @item.DataProcessamentoAux
          </td>
          <td>
            @item.TipoLancamento
          </td>
        </tr>
      }
    </tbody>
  </table>
</div>

﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Net;
using System.Net.Mail;
using System.Threading;
using System.IO;
using System.Configuration;

namespace RepasseConvenio.Infrastructure.Helpers
{
  public static class EMailHelper
  {
    private static string SMTPServer { get; set; }
    private static string User { get; set; }
    private static string Password { get; set; }
    private static string FromMail { get; set; }
    private static int Porta { get; set; }
    private static bool EnableSSL { get; set; }
    private static string body = @"";

    private static string bodyImagem = @"";
    public class EstruturaEmail
    {
      public string ToMail { get; set; }
      public string Subject { get; set; }
      public string Message { get; set; }
    }

    public static void Initialize(string smtpServer, string user, string password, string fromMail, int porta, bool enableSSL)
    {
    }

    public static void Send(string[] toMail, string subject, string message)
    {
    }

    public static void SendWithCopia(string[] toMail, string[] toCCMail, string subject, string message)
    {
    }

    public static void SendImagem(string[] toMail, string subject, string message)
    {
    }

    public static void SendWithCopiaImagem(string[] toMail, string[] toCCMail, string subject, string message)
    {
    }

    public static void SendWithAnexo(string[] toMail, string subject, string message, Stream file, string fileName)
    {
    }

  }
}
﻿using System;
using System.Text.RegularExpressions;
using RepasseConvenio.Infrastructure;
using PortalCooperado.Domain.Infrastructure;

namespace RepasseConvenio.Domain.Infrastructure.Helpers
{
  public class ValidadorHelper
  {
    public static void ValidaCPF(string CPF)
    {
    }

    public static bool ValidaCNPJ(string CNPJ)
    {
      return false;
    }

    public static bool ValidaCaracterEspeciais(string text)
    {
        return true;
    }

    public static string ReplaceCaracterEspeciais(string text)
    {
      return text;
    }

  }
}

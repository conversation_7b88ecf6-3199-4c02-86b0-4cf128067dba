﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using Microsoft.AspNet.SignalR.Messaging;
using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Controls;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace RepasseConvenio.Controllers
{
  [Authorize]
  public class ItensRecorrentesController : LibController
  {

    private ItensRecorrentesServices ItensRecorrentesServices
    {
      get
      {
        if (_ItensRecorrentesServices == null)
          _ItensRecorrentesServices = new ItensRecorrentesServices(ContextoUsuario.UserLogged);

        return _ItensRecorrentesServices;
      }
    }
    private ItensRecorrentesServices _ItensRecorrentesServices;

    public ActionResult Index(int id, int? Page)
    {
      try
      {
        MedicoService medicoService = new MedicoService();
        ViewBag.NomeMedico = medicoService.GetNomeMedicoById(id).ToUpper();
        int numpag = Page ?? 1;
        List<ItensRecorrentesIndex> model = ItensRecorrentesServices.Get(id, numpag);

        ViewBag.CodigoMedico = id;
        return View(model);
      }
      catch (Exception)
      {
        throw;
      }
    }
    [Security("7C22170A-89D5-4099-8EC9-BA4FCBAAC782")]
    public ActionResult Create(int id)
    {
      try
      {
        ItensRecorrentesModel ItensRecorrentesModel = new ItensRecorrentesModel();

        ItensRecorrentesModel.CodigoMedico = id;
        ItensRecorrentesModel.Recorrente = false;

        return View(ItensRecorrentesModel);
      }
      catch (Exception)
      {
        throw;
      }
    }

    [Security("7C22170A-89D5-4099-8EC9-BA4FCBAAC782")]
    [HttpPost]
    public ActionResult Create(ItensRecorrentesModel itens)
    {
      try
      {
        if (itens.TipoContaEnum.Equals(EnumTipoConta.PF))
        {
          R_Medico medico = new MedicoService().GetById(itens.CodigoMedico);

          if (medico == null)
            throw new Exception("Não foi possivel encontrar o Médico.");

          itens.CPFCNPJ = medico.M_CPF;
        }

        if (itens.recorrenteAux == 1)
          itens.Recorrente = true;
        else
        {
          itens.Recorrente = false;
          itens.DataFim = itens.DataRecorrencia;
        }

        ItensRecorrentesServices.Create(itens);
        MessageListToast.Add(new RepasseConvenio.Infrastructure.Controls.Message(MessageType.Success, "Item Recorrente criado com sucesso."));

        return RedirectToAction("Index", new { id = itens.CodigoMedico });
      }
      catch (CustomException ex)
      {
        MessageListToast.Add(new RepasseConvenio.Infrastructure.Controls.Message(MessageType.Error, ex.Message));
        return View(itens);
      }
      catch (Exception)
      {
        MessageListToast.Add(new RepasseConvenio.Infrastructure.Controls.Message(MessageType.Error, "Favor verificar os campos preenchidos."));
        return View(itens);
      }
    }


    [Security("2493815A-816F-4F7A-A7B7-3D8695ABF864")]
    public ActionResult Edit(int? id)
    {
      try
      {
        if (id == null)
          throw new Exception("Nenhum Item Recorrente foi selecionado");

        ItensRecorrentesModel itens = ItensRecorrentesServices.GetById(id.Value);

        if (itens.Recorrente)
          itens.recorrenteAux = 1;
        else
          itens.recorrenteAux = 0;

        if (itens == null)
          throw new Exception("Não foi possivel encontrar este item de recorrencia");

        return View(itens);

      }
      catch (Exception)
      {
        throw;
      }
    }


    [Security("2493815A-816F-4F7A-A7B7-3D8695ABF864")]
    [HttpPost]
    public ActionResult Edit(ItensRecorrentesModel itens)
    {
      try
      {
        if (itens.recorrenteAux == 1)
          itens.Recorrente = true;
        else
        {
          itens.Recorrente = false;
          itens.DataFim = itens.DataRecorrencia;
          itens.TipoRecorrenciaEnum = null;
        }

        ItensRecorrentesServices.Edit(itens);

        MessageListToast.Add(new RepasseConvenio.Infrastructure.Controls.Message(MessageType.Success, "Item Recorrente criado com sucesso."));
        return RedirectToAction("Index", new { id = itens.CodigoMedico });

        //MessageListToast.Add(new RepasseConvenio.Infrastructure.Controls.Message(MessageType.Error, "Favor verificar os campos preenchidos."));
        //return View(itens);
      }
      catch (Exception)
      {
        throw;
      }
    }

    [Security("2848063C-316C-4486-8E6D-AC9E8BC15A4D")]
    public ActionResult Delete(int id, int CodigoMedico)
    {
      try
      {
        ItensRecorrentesServices.Delete(id);
        MessageListToast.Add(new RepasseConvenio.Infrastructure.Controls.Message(MessageType.Success, "Item Recorrente Removido com sucesso."));
        return RedirectToAction("Index", new { id = CodigoMedico });
      }
      catch (CustomException ex)
      {
        MessageListToast.Add(new RepasseConvenio.Infrastructure.Controls.Message(MessageType.Error, ex.Message));
        return RedirectToAction("Index", new { id = CodigoMedico });

      }
      catch (Exception ex)
      {
        MessageListToast.Add(new RepasseConvenio.Infrastructure.Controls.Message(MessageType.Error, ex.Message));
        return RedirectToAction("Index", new { id = CodigoMedico });
      }
    }


    [HttpGet]
    public PartialViewResult _GridItensRecorrentes(int id, int? Page)
    {
      int numpag = Page ?? 1;
      List<ItensRecorrentesIndex> model = ItensRecorrentesServices.Get(id, numpag);
      ViewBag.CodigoMedico = id;
      return PartialView("_GridItensRecorrentes", model);
    }
  }
}
﻿@using RepasseConvenio.Models
@model List<NotaFiscalRepasseModel>

@{
  ViewBag.Title = "Repasse";
  string action = ViewContext.RouteData.Values["action"].ToString();
}

  <div class="col-md-12 table-responsive p-0 ">
    <table class="table table-sm table-striped table-hover text-nowrap">
      <thead>
        <tr>
          <th>
            Número
          </th>
          <th>
            Serie
          </th>
          <th>
            Data
          </th>
          <th>
            Valor Total
          </th>
          <th>
            Valor Liquido
          </th>
          <th>
            Impostos
          </th>
          <th>

          </th>
        </tr>
      </thead>
      <tbody>
        @foreach (NotaFiscalRepasseModel item in Model)
        {
          <tr>
            <td>
              @item.NFM_Numero
            </td>
            <td>
              @item.NFM_Serie
            </td>
            <td>
              @item.NFM_DataEmissao.ToString("dd/MM/yyyy")
            </td>
            <td>
              @item.NFM_ValorTotal
            </td>
            <td>
              @item.NFM_ValorLiquido
            </td>
            <td>
              @item.NFM_Impostos
            </td>
            <td>
              @if (!action.Equals("Details"))
              {
                <a data-url="@Url.Action("Edit", "NotaFiscalRepasse", new { id = item.NFM_Codigo })" data-idnota="@item.NFM_Codigo" data-idrepasse="@item.NFM_CodigoRepasse" class="btn btn-outline-warning btn-circle btn-sm btnEditNotaFiscal" title="Editar">
                  Editar
                </a>
                <a data-idnota="@item.NFM_Codigo" class="btn btn-outline-danger btn-circle btn-sm btnRemoverNotaFiscal" title="Editar">
                  Excluir
                </a>
              }

            </td>
          </tr>
        }
      </tbody>
    </table>
  </div>

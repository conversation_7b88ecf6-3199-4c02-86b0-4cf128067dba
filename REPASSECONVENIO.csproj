﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.Default.props" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.Default.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{A96FC298-7623-46AA-B3AC-D2C251E1DA33}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RepasseConvenio</RootNamespace>
    <AssemblyName>RepasseConvenio</AssemblyName>
    <TargetFrameworkVersion>v4.8.1</TargetFrameworkVersion>
    <MvcBuildViews>false</MvcBuildViews>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress>
    </Use64BitIISExpress>
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TypeScriptToolsVersion>3.8</TypeScriptToolsVersion>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="itextsharp, Version=5.5.13.1, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\iTextSharp.5.5.13.1\lib\itextsharp.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.SignalR.Core, Version=2.4.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.SignalR.Core.2.4.1\lib\net45\Microsoft.AspNet.SignalR.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.SignalR.SystemWeb, Version=2.4.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.SignalR.SystemWeb.2.4.1\lib\net45\Microsoft.AspNet.SignalR.SystemWeb.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=3.6.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.3.6.0\lib\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Owin, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.2.1.0\lib\net45\Microsoft.Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Host.SystemWeb, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Host.SystemWeb.2.1.0\lib\net45\Microsoft.Owin.Host.SystemWeb.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.2.1.0\lib\net45\Microsoft.Owin.Security.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.Common, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dlls\Microsoft.ReportViewer.Common.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.WebForms, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dlls\Microsoft.ReportViewer.WebForms.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=********, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.12.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Owin, Version=1.0.0.0, Culture=neutral, PublicKeyToken=f0ebd12fd5e55cc5, processorArchitecture=MSIL">
      <HintPath>..\packages\Owin.1.0\lib\net40\Owin.dll</HintPath>
    </Reference>
    <Reference Include="PagedList, Version=1.17.0.0, Culture=neutral, PublicKeyToken=abbb863e9397c5e1, processorArchitecture=MSIL">
      <HintPath>..\packages\PagedList.1.17.0.0\lib\net40\PagedList.dll</HintPath>
    </Reference>
    <Reference Include="PagedList.Mvc, Version=4.5.0.0, Culture=neutral, PublicKeyToken=abbb863e9397c5e1, processorArchitecture=MSIL">
      <HintPath>..\packages\PagedList.Mvc.4.5.0.0\lib\net40\PagedList.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Linq.Dynamic.Core, Version=1.2.2.0, Culture=neutral, PublicKeyToken=0f07ec44de6ac832, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Linq.Dynamic.Core.1.2.2\lib\net46\System.Linq.Dynamic.Core.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Abstractions" />
    <Reference Include="System.Web.Routing" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http">
    </Reference>
    <Reference Include="System.Net.Http.WebRequest">
    </Reference>
    <Reference Include="System.Web.Helpers, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=5.2.7.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.7\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Optimization">
      <HintPath>..\packages\Microsoft.AspNet.Web.Optimization.1.1.3\lib\net40\System.Web.Optimization.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.7\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="WebGrease">
      <Private>True</Private>
      <HintPath>..\packages\WebGrease.1.6.0\lib\WebGrease.dll</HintPath>
    </Reference>
    <Reference Include="Antlr3.Runtime">
      <Private>True</Private>
      <HintPath>..\packages\Antlr.3.5.0.2\lib\Antlr3.Runtime.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_Start\BundleConfig.cs" />
    <Compile Include="App_Start\FilterConfig.cs" />
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="Controllers\AccountController.cs" />
    <Compile Include="Controllers\AnexoProtocoloGlosaController.cs" />
    <Compile Include="Controllers\BancoUnicooperController.cs" />
    <Compile Include="Controllers\ClassificacaoRepasseController.cs" />
    <Compile Include="Controllers\DepositoRepasseController.cs" />
    <Compile Include="Controllers\GuiaNotaFiscalController.cs" />
    <Compile Include="Controllers\ProtocoloGlosaController.cs" />
    <Compile Include="Controllers\ItensLoteGlosaController.cs" />
    <Compile Include="Controllers\ItensRecorrentesController.cs" />
    <Compile Include="Controllers\GuiaDemonstrativoController.cs" />
    <Compile Include="Controllers\EmpresaMedicoController.cs" />
    <Compile Include="Controllers\DemonstrativoConvenioController.cs" />
    <Compile Include="Controllers\JustificativaGlosaController.cs" />
    <Compile Include="Controllers\LoteController.cs" />
    <Compile Include="Controllers\LoteGlosaController.cs" />
    <Compile Include="Controllers\RegistroImpostoController.cs" />
    <Compile Include="Controllers\ResponsavelConvenioController.cs" />
    <Compile Include="Controllers\TabelaProgressivaIRPFController.cs" />
    <Compile Include="Controllers\ValoresTabelaProgressivaINSSController.cs" />
    <Compile Include="Controllers\TabelaProgressivaINSSController.cs" />
    <Compile Include="Controllers\RegistroPagamentoController.cs" />
    <Compile Include="Controllers\RegraCartaConversaoCampoController.cs" />
    <Compile Include="Controllers\RegraCartaConversaoAcaoController.cs" />
    <Compile Include="Controllers\RegraCartaConversaoController.cs" />
    <Compile Include="Controllers\ExtratoMedicoController.cs" />
    <Compile Include="Controllers\ProcedimentoController.cs" />
    <Compile Include="Controllers\ConvenioController.cs" />
    <Compile Include="Controllers\RateioGrauParticipacaoController.cs" />
    <Compile Include="Controllers\RateioFixoController.cs" />
    <Compile Include="Controllers\RateioMedicoController.cs" />
    <Compile Include="Controllers\MedicoController.cs" />
    <Compile Include="Controllers\NotaFiscalRepasseController.cs" />
    <Compile Include="Controllers\PlanilhaRecebimentoController.cs" />
    <Compile Include="Controllers\UsuarioController.cs" />
    <Compile Include="Controllers\HomeController.cs" />
    <Compile Include="Controllers\Select2Controller.cs" />
    <Compile Include="Controllers\RepasseController.cs" />
    <Compile Include="Controllers\ValoresTabelaProgressivaIRPFController.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Infrastructure\Atributos\MultiButtonAttribute.cs" />
    <Compile Include="Infrastructure\Controls\CustomControls.cs" />
    <Compile Include="Infrastructure\Controls\LibButton.cs" />
    <Compile Include="Infrastructure\Controls\LibDropDown.cs" />
    <Compile Include="Infrastructure\Controls\LibEditor.cs" />
    <Compile Include="Infrastructure\Controls\LibLabel.cs" />
    <Compile Include="Infrastructure\Controls\LibAlertMessage.cs" />
    <Compile Include="Infrastructure\Controls\LibMessageToast.cs" />
    <Compile Include="Infrastructure\Controls\LibPagination.cs" />
    <Compile Include="Infrastructure\Controls\Libselect2.cs" />
    <Compile Include="Infrastructure\Controls\PagedControlOptions.cs" />
    <Compile Include="Infrastructure\DataMapperPaging.cs" />
    <Compile Include="Infrastructure\Exception\LibException.cs" />
    <Compile Include="Infrastructure\Helpers\DirectoryHelper.cs" />
    <Compile Include="Infrastructure\Helpers\AnexoHelper.cs" />
    <Compile Include="Infrastructure\Helpers\AtributtesHelper.cs" />
    <Compile Include="Infrastructure\Helpers\CommonHelper.cs" />
    <Compile Include="Infrastructure\Helpers\EmailHelper.cs" />
    <Compile Include="Infrastructure\Helpers\EnumHelper.cs" />
    <Compile Include="Infrastructure\Helpers\FileHelper.cs" />
    <Compile Include="Infrastructure\Helpers\SegurancaHelper.cs" />
    <Compile Include="Infrastructure\Helpers\ValidadorHelper.cs" />
    <Compile Include="Infrastructure\Hubs\SendSignal.cs" />
    <Compile Include="Infrastructure\Hubs\SignalHub.cs" />
    <Compile Include="Infrastructure\Mensagem\CustomMensagens.cs" />
    <Compile Include="Infrastructure\Mensagem\MessageColors.cs" />
    <Compile Include="Infrastructure\Mensagem\MessageTipo.cs" />
    <Compile Include="Models\AnexoProtocoloGlosaModel.cs" />
    <Compile Include="Models\BancoUnicooperModel.cs" />
    <Compile Include="Models\ClassificacaoRepasseModel.cs" />
    <Compile Include="Models\ControleMensalModel.cs" />
    <Compile Include="Models\DepositoRepasseModel.cs" />
    <Compile Include="Models\GuiaNotaFiscalModel.cs" />
    <Compile Include="Models\ItensLoteGlosaModel.cs" />
    <Compile Include="Models\ItensRecorrentesModel.cs" />
    <Compile Include="Models\ItensLoteModel.cs" />
    <Compile Include="Models\JustificativaGlosaModel.cs" />
    <Compile Include="Models\LogAuxModel.cs" />
    <Compile Include="Models\LogExtratoMedicoModel.cs" />
    <Compile Include="Models\ProtocoloGlosaModel.cs" />
    <Compile Include="Models\LoteGlosaModel.cs" />
    <Compile Include="Models\LoteModel.cs" />
    <Compile Include="Models\MotivoGlosaModel.cs" />
    <Compile Include="Models\ProcGuiaDemonstrativoModel.cs" />
    <Compile Include="Models\GuiaDemonstrativoModel.cs" />
    <Compile Include="Models\EmpresaMedicoModel.cs" />
    <Compile Include="Models\ExtratoMedicoModel.cs" />
    <Compile Include="Models\DemonstrativoModel.cs" />
    <Compile Include="Models\RegistroImpostoModel.cs" />
    <Compile Include="Models\ResponsavelConvenioModel.cs" />
    <Compile Include="Models\TabelaProgressivaIRPFModel.cs" />
    <Compile Include="Models\ValoresTabelaProgressivaINSSModel.cs" />
    <Compile Include="Models\TabelaProgressivaINSSModel.cs" />
    <Compile Include="Models\ProcedimentoModel.cs" />
    <Compile Include="Models\ConvenioModel.cs" />
    <Compile Include="Models\CommonModels.cs" />
    <Compile Include="Models\EnumCommon.cs" />
    <Compile Include="Models\GrauParticipacaoModel.cs" />
    <Compile Include="Models\GuiaAtendimentoModel.cs" />
    <Compile Include="Models\PartGuiaAtendimentoModel.cs" />
    <Compile Include="Models\ProcGuiaAtendimentoModel.cs" />
    <Compile Include="Models\RateioGrauParticipacaoModel.cs" />
    <Compile Include="Models\RateioFixoModel.cs" />
    <Compile Include="Models\RateioMedicoModel.cs" />
    <Compile Include="Models\MedicoModel.cs" />
    <Compile Include="Models\BancoModel.cs" />
    <Compile Include="Models\NotaFiscaRepasseModel.cs" />
    <Compile Include="Models\PlanilhaRecebimentoModel.cs" />
    <Compile Include="Models\RegistroPagamentoModel.cs" />
    <Compile Include="Models\RegraCartaConversaoModel.cs" />
    <Compile Include="Models\UsuarioModel.cs" />
    <Compile Include="Models\R_RepasseModel.cs" />
    <Compile Include="Infrastructure\ContextoUsuario.cs" />
    <Compile Include="Infrastructure\LibController.cs" />
    <Compile Include="Models\Select2Model.cs" />
    <Compile Include="Models\ValoresTabelaProgressivaIRPFModel.cs" />
    <Compile Include="Models\WSModel.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="Reports\RelatorioGlosa.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>RelatorioGlosa.xsd</DependentUpon>
    </Compile>
    <Compile Include="Services\BancoUnicooperService.cs" />
    <Compile Include="Services\ClassificacaoRepasseService.cs" />
    <Compile Include="Services\ControleMensalService.cs" />
    <Compile Include="Services\CodDocumentoService.cs" />
    <Compile Include="Services\DepositoRepasseService.cs" />
    <Compile Include="Services\GuiaNotaFiscalService.cs" />
    <Compile Include="Services\ParametrosService.cs" />
    <Compile Include="Services\ItensLoteGlosaServices.cs" />
    <Compile Include="Services\JustificativaGlosaService.cs" />
    <Compile Include="Services\LogGuiaAtendimentoService.cs" />
    <Compile Include="Services\ItensRecorrentesService.cs" />
    <Compile Include="Services\ItensLoteServices.cs" />
    <Compile Include="Services\LogExtratoMedicoServices.cs" />
    <Compile Include="Services\LogLoteGlosaService.cs" />
    <Compile Include="Services\AnexoProtocoloGlosaService.cs" />
    <Compile Include="Services\ProtocoloGlosaServices.cs" />
    <Compile Include="Services\MotivoGlosaJustificativaServices.cs" />
    <Compile Include="Services\LoteGlosaServices.cs" />
    <Compile Include="Services\LoteServices.cs" />
    <Compile Include="Services\GuiaAtendimentoService.cs" />
    <Compile Include="Services\MotivoGlosaService.cs" />
    <Compile Include="Services\PartGuiaAtendimentoService.cs" />
    <Compile Include="Services\ProcGuiaAtendimentoService.cs" />
    <Compile Include="Services\ProcGuiaDemonstrativoService.cs" />
    <Compile Include="Services\GuiaDemonstrativoService.cs" />
    <Compile Include="Services\ExtratoMedicoService.cs" />
    <Compile Include="Services\GrauParticipacaoService.cs" />
    <Compile Include="Services\EmpresaMedicoService.cs" />
    <Compile Include="Services\DemonstrativoConvenioService.cs" />
    <Compile Include="Services\ProcedimentoServices.cs" />
    <Compile Include="Services\HospitalService.cs" />
    <Compile Include="Services\ConvenioServices.cs" />
    <Compile Include="Services\RateioGrauParticipacaoService.cs" />
    <Compile Include="Services\RateioFixoService.cs" />
    <Compile Include="Services\RateioMedicoService.cs" />
    <Compile Include="Services\PlanilhaRecebimentoService.cs" />
    <Compile Include="Services\NotaFiscalRepasseService.cs" />
    <Compile Include="Services\MedicoService.cs" />
    <Compile Include="Services\RegistroImpostoServices.cs" />
    <Compile Include="Services\RegistroPagamentoService.cs" />
    <Compile Include="Services\RegraCartaConversaoServices.cs" />
    <Compile Include="Services\RegraCartaConversaoAcaoServices.cs" />
    <Compile Include="Services\RelacaoResponsavelConvenioServices.cs" />
    <Compile Include="Services\RepasseService.cs" />
    <Compile Include="Services\AccountService.cs" />
    <Compile Include="Services\ResponsavelConvenioServices.cs" />
    <Compile Include="Services\Select2Services.cs" />
    <Compile Include="Services\ServiceBase.cs" />
    <Compile Include="Services\BancoServices.cs" />
    <Compile Include="Services\StatusDemonstrativoConvenioServices.cs" />
    <Compile Include="Services\StatusGlosaServices.cs" />
    <Compile Include="Services\SituacaoItensLoteServices.cs" />
    <Compile Include="Services\TipoImportacaoGuiaDemonstrativoServices.cs" />
    <Compile Include="Services\StatusGuiaNotaFiscalServices.cs" />
    <Compile Include="Services\StausProcGuiaDemonstrativoServices.cs" />
    <Compile Include="Services\StatusItensLoteServices.cs" />
    <Compile Include="Services\StatusGuiaDemonstrativoConvenioServices.cs" />
    <Compile Include="Services\StatusLoteServices.cs" />
    <Compile Include="Services\StatusControleMensalServices.cs" />
    <Compile Include="Services\StatusRegistroPagamentoServices.cs" />
    <Compile Include="Services\StatusRepasseServices.cs" />
    <Compile Include="Services\TabelaProgressivaIRPFServices.cs" />
    <Compile Include="Services\TipoLogLoteGlosaService.cs" />
    <Compile Include="Services\TipoLoteGlosaServices.cs" />
    <Compile Include="Services\TipoRegistroImpostoServices.cs" />
    <Compile Include="Services\TipoUsuarioServices.cs" />
    <Compile Include="Services\RegraCartaConversaoCampoServices.cs" />
    <Compile Include="Services\ValoresTabelaProgressivaINSSServices.cs" />
    <Compile Include="Services\ValoresTabelaProgressivaIRPFServices.cs" />
    <Compile Include="Services\WSRepasseServices.cs" />
    <Compile Include="Services\TabelaProgressivaINSSServices.cs" />
    <Compile Include="Services\UsuarioServices.cs" />
    <Compile Include="Startup.cs" />
    <Compile Include="Web References\WSPortalCooperado\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.map</DependentUpon>
    </Compile>
    <Compile Include="WSRepasse.asmx.cs">
      <DependentUpon>WSRepasse.asmx</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Content\adminlte.css" />
    <Content Include="Content\AirDatePicker\AirDatePicker.css" />
    <Content Include="Content\bootstrap-datetimepicker.css" />
    <Content Include="Content\bootstrap-datetimepicker.min.css" />
    <Content Include="Content\bootstrap-grid.css" />
    <Content Include="Content\bootstrap-grid.min.css" />
    <Content Include="Content\bootstrap-reboot.css" />
    <Content Include="Content\bootstrap-reboot.min.css" />
    <Content Include="Content\bootstrap.css" />
    <Content Include="Content\bootstrap.min.css" />
    <Content Include="Content\font-awesome.css" />
    <Content Include="Content\font-awesome.min.css" />
    <Content Include="Content\fontawesome\all.css" />
    <Content Include="Content\fontawesome\all.min.css" />
    <Content Include="Content\fontawesome\brands.css" />
    <Content Include="Content\fontawesome\brands.min.css" />
    <Content Include="Content\fontawesome\fontawesome.css" />
    <Content Include="Content\fontawesome\fontawesome.min.css" />
    <Content Include="Content\fontawesome\regular.css" />
    <Content Include="Content\fontawesome\regular.min.css" />
    <Content Include="Content\fontawesome\solid.css" />
    <Content Include="Content\fontawesome\solid.min.css" />
    <Content Include="Content\fontawesome\svg-with-js.css" />
    <Content Include="Content\fontawesome\svg-with-js.min.css" />
    <Content Include="Content\fontawesome\v4-shims.css" />
    <Content Include="Content\fontawesome\v4-shims.min.css" />
    <Content Include="Content\img\AdminLTELogo.png" />
    <Content Include="Content\img\avatar.png" />
    <Content Include="Content\iziToast.min.css" />
    <Content Include="Content\PagedList.css" />
    <Content Include="Content\select2.min.css" />
    <Content Include="Content\sweetalert.css" />
    <Content Include="Content\webfonts\fa-brands-400.svg" />
    <Content Include="Content\webfonts\fa-regular-400.svg" />
    <Content Include="Content\webfonts\fa-solid-900.svg" />
    <Content Include="favicon.ico" />
    <Content Include="Fonts\fontawesome-webfont.svg" />
    <Content Include="Fonts\fonts\MWFONT.svg" />
    <Content Include="Fonts\MWFONT.svg" />
    <Content Include="Global.asax" />
    <Content Include="Content\Site-1.0.0.css" />
    <Content Include="Scripts\adminlte.js" />
    <Content Include="Scripts\AirDatePicker\AirDatePicker-Pt-Br.js" />
    <Content Include="Scripts\AirDatePicker\AirDatePicker.js" />
    <Content Include="Scripts\pt-br.js" />
    <Content Include="Scripts\bootstrap-datetimepicker.js" />
    <Content Include="Scripts\bootstrap-datetimepicker.min.js" />
    <Content Include="Scripts\bootstrap.bundle.js" />
    <Content Include="Scripts\bootstrap.bundle.min.js" />
    <Content Include="Scripts\bootstrap.js" />
    <Content Include="Scripts\bootstrap.min.js" />
    <Content Include="Scripts\Controls-1.0.1.js" />
    <Content Include="Scripts\esm\popper-utils.js" />
    <Content Include="Scripts\esm\popper-utils.min.js" />
    <Content Include="Scripts\esm\popper.js" />
    <Content Include="Scripts\esm\popper.min.js" />
    <Content Include="Scripts\index.js.flow" />
    <Content Include="Scripts\esm\popper.min.js.map" />
    <Content Include="Scripts\esm\popper.js.map" />
    <Content Include="Scripts\esm\popper-utils.min.js.map" />
    <Content Include="Scripts\esm\popper-utils.js.map" />
    <Content Include="Scripts\bootstrap.min.js.map" />
    <Content Include="Scripts\bootstrap.js.map" />
    <Content Include="Scripts\bootstrap.bundle.min.js.map" />
    <Content Include="Scripts\bootstrap.bundle.js.map" />
    <Content Include="Content\bootstrap.min.css.map" />
    <Content Include="Content\bootstrap.css.map" />
    <Content Include="Content\bootstrap-reboot.min.css.map" />
    <Content Include="Content\bootstrap-reboot.css.map" />
    <Content Include="Content\bootstrap-grid.min.css.map" />
    <Content Include="Content\bootstrap-grid.css.map" />
    <Content Include="Views\BancoUnicooper\BancoUnicooper.js" />
    <Content Include="Views\BancoUnicooper\TabelaBanco.css" />
    <Content Include="Views\ClassificacaoRepasse\ClassificacaoRepasse.js" />
    <Content Include="Views\ClassificacaoRepasse\ClassificacaoRepasse.css" />
    <Content Include="Views\Convenio\Convenio.css" />
    <Content Include="Views\ExtratoMedico\ExtratoMedico.css" />
    <Content Include="Views\ItensRecorrentes\ItensRecorrentes.js" />
    <Content Include="Views\JustificativaGlosa\JustificativaGlosa.css" />
    <Content Include="Views\JustificativaGlosa\JustificativaGlosa.js" />
    <Content Include="Views\LoteGlosa\LoteGlosa.css" />
    <Content Include="Views\LoteGlosa\LoteGlosa.js" />
    <Content Include="Views\Lote\Lote.js" />
    <Content Include="Views\Lote\Lote.css" />
    <Content Include="Views\Medico\Medico.css" />
    <Content Include="Views\ProtocoloGlosa\ProtocoloGlosa.css" />
    <Content Include="Views\ProtocoloGlosa\ProtocoloGlosa.js" />
    <Content Include="Views\RegistroImposto\RegistroImposto.js" />
    <Content Include="Views\ResponsavelConvenio\ResponsavelConvenio.js" />
    <Content Include="Views\TabelaProgressivaINSS\TabelaINSS.css" />
    <Content Include="Views\TabelaProgressivaINSS\TabelaProgressivaINSS.js" />
    <Content Include="Views\TabelaProgressivaIRPF\TabelaIRPF.css" />
    <Content Include="Views\TabelaProgressivaIRPF\TabelaIRPF.js" />
    <Content Include="WSRepasse.asmx" />
    <Content Include="Content\webfonts\fa-brands-400.eot" />
    <Content Include="Content\webfonts\fa-brands-400.ttf" />
    <Content Include="Content\webfonts\fa-brands-400.woff" />
    <Content Include="Content\webfonts\fa-brands-400.woff2" />
    <Content Include="Content\webfonts\fa-regular-400.eot" />
    <Content Include="Content\webfonts\fa-regular-400.ttf" />
    <Content Include="Content\webfonts\fa-regular-400.woff" />
    <Content Include="Content\webfonts\fa-regular-400.woff2" />
    <Content Include="Content\webfonts\fa-solid-900.eot" />
    <Content Include="Content\webfonts\fa-solid-900.ttf" />
    <Content Include="Content\webfonts\fa-solid-900.woff" />
    <Content Include="Content\webfonts\fa-solid-900.woff2" />
    <Content Include="Fonts\fontawesome-webfont.eot" />
    <Content Include="Fonts\fontawesome-webfont.ttf" />
    <Content Include="Fonts\fontawesome-webfont.woff" />
    <Content Include="Fonts\fontawesome-webfont.woff2" />
    <Content Include="Fonts\FontAwesome.otf" />
    <Content Include="Fonts\MWFONT.eot" />
    <Content Include="Fonts\MWFONT.ttf" />
    <Content Include="Fonts\MWFONT.woff" />
    <Content Include="Fonts\fonts\MWFONT.eot" />
    <Content Include="Fonts\fonts\MWFONT.ttf" />
    <Content Include="Fonts\fonts\MWFONT.woff" />
    <Content Include="AppSettings.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Content Include="Reports\RelatorioGlosa.xsc">
      <DependentUpon>RelatorioGlosa.xsd</DependentUpon>
    </Content>
    <None Include="Reports\RelatorioGlosa.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>RelatorioGlosa.Designer.cs</LastGenOutput>
    </None>
    <Content Include="Reports\RelatorioGlosa.xss">
      <DependentUpon>RelatorioGlosa.xsd</DependentUpon>
    </Content>
    <None Include="Scripts\jquery-3.5.1.intellisense.js" />
    <Content Include="Scripts\inputmask\bindings\inputmask.binding.js" />
    <Content Include="Scripts\inputmask\bindings\inputmask.binding.min.js" />
    <Content Include="Scripts\inputmask\dependencyLibs\inputmask.dependencyLib.jqlite.js" />
    <Content Include="Scripts\inputmask\dependencyLibs\inputmask.dependencyLib.jqlite.min.js" />
    <Content Include="Scripts\inputmask\dependencyLibs\inputmask.dependencyLib.jquery.js" />
    <Content Include="Scripts\inputmask\dependencyLibs\inputmask.dependencyLib.jquery.min.js" />
    <Content Include="Scripts\inputmask\dependencyLibs\inputmask.dependencyLib.js" />
    <Content Include="Scripts\inputmask\dependencyLibs\inputmask.dependencyLib.min.js" />
    <Content Include="Scripts\inputmask\global\document.js" />
    <Content Include="Scripts\inputmask\global\document.min.js" />
    <Content Include="Scripts\inputmask\global\window.js" />
    <Content Include="Scripts\inputmask\global\window.min.js" />
    <Content Include="Scripts\inputmask\inputmask.date.extensions.js" />
    <Content Include="Scripts\inputmask\inputmask.date.extensions.min.js" />
    <Content Include="Scripts\inputmask\inputmask.extensions.js" />
    <Content Include="Scripts\inputmask\inputmask.extensions.min.js" />
    <Content Include="Scripts\inputmask\inputmask.js" />
    <Content Include="Scripts\inputmask\inputmask.min.js" />
    <Content Include="Scripts\inputmask\inputmask.numeric.extensions.js" />
    <Content Include="Scripts\inputmask\inputmask.numeric.extensions.min.js" />
    <Content Include="Scripts\inputmask\inputmask.phone.extensions.js" />
    <Content Include="Scripts\inputmask\inputmask.phone.extensions.min.js" />
    <Content Include="Scripts\inputmask\inputmask.regex.extensions.js" />
    <Content Include="Scripts\inputmask\inputmask.regex.extensions.min.js" />
    <Content Include="Scripts\inputmask\jquery.inputmask.js" />
    <Content Include="Scripts\inputmask\jquery.inputmask.min.js" />
    <Content Include="Scripts\inputmask\phone-codes\phone-be.js" />
    <Content Include="Scripts\inputmask\phone-codes\phone-be.min.js" />
    <Content Include="Scripts\inputmask\phone-codes\phone-nl.js" />
    <Content Include="Scripts\inputmask\phone-codes\phone-nl.min.js" />
    <Content Include="Scripts\inputmask\phone-codes\phone-ru.js" />
    <Content Include="Scripts\inputmask\phone-codes\phone-ru.min.js" />
    <Content Include="Scripts\inputmask\phone-codes\phone-uk.js" />
    <Content Include="Scripts\inputmask\phone-codes\phone-uk.min.js" />
    <Content Include="Scripts\inputmask\phone-codes\phone.js" />
    <Content Include="Scripts\inputmask\phone-codes\phone.min.js" />
    <Content Include="Scripts\jquery-3.5.1.js" />
    <Content Include="Scripts\jquery-3.5.1.min.js" />
    <Content Include="Scripts\jquery-3.5.1.slim.js" />
    <Content Include="Scripts\jquery-3.5.1.slim.min.js" />
    <Content Include="Scripts\jquery-3.5.1.slim.min.map" />
    <Content Include="Scripts\jquery-3.5.1.min.map" />
    <None Include="Scripts\jquery.validate-vsdoc.js" />
    <Content Include="Scripts\jquery.inputmask.bundle.js" />
    <Content Include="Scripts\jquery.inputmask.bundle.min.js" />
    <Content Include="Scripts\jquery.signalR-2.4.1.js" />
    <Content Include="Scripts\jquery.signalR-2.4.1.min.js" />
    <Content Include="Scripts\jquery.validate.js" />
    <Content Include="Scripts\jquery.validate.min.js" />
    <Content Include="Scripts\jquery.validate.unobtrusive.js" />
    <Content Include="Scripts\jquery.validate.unobtrusive.min.js" />
    <Content Include="Scripts\modernizr-2.8.3.js" />
    <Content Include="Scripts\moment-with-locales.js" />
    <Content Include="Scripts\moment-with-locales.min.js" />
    <Content Include="Scripts\moment.js" />
    <Content Include="Scripts\moment.min.js" />
    <Content Include="Scripts\Pace.js" />
    <Content Include="Scripts\popper-utils.js" />
    <Content Include="Scripts\popper-utils.min.js" />
    <Content Include="Scripts\popper.js" />
    <Content Include="Scripts\popper.min.js" />
    <Content Include="Scripts\select2.min.js" />
    <Content Include="Scripts\Select2Controls-1.0.0.js" />
    <Content Include="Scripts\SignalR.js" />
    <Content Include="Scripts\Site-1.0.2.js" />
    <Content Include="Scripts\src\index.js" />
    <Content Include="Scripts\src\methods\defaults.js" />
    <Content Include="Scripts\src\methods\destroy.js" />
    <Content Include="Scripts\src\methods\disableEventListeners.js" />
    <Content Include="Scripts\src\methods\enableEventListeners.js" />
    <Content Include="Scripts\src\methods\placements.js" />
    <Content Include="Scripts\src\methods\update.js" />
    <Content Include="Scripts\src\modifiers\applyStyle.js" />
    <Content Include="Scripts\src\modifiers\arrow.js" />
    <Content Include="Scripts\src\modifiers\computeStyle.js" />
    <Content Include="Scripts\src\modifiers\flip.js" />
    <Content Include="Scripts\src\modifiers\hide.js" />
    <Content Include="Scripts\src\modifiers\index.js" />
    <Content Include="Scripts\src\modifiers\inner.js" />
    <Content Include="Scripts\src\modifiers\keepTogether.js" />
    <Content Include="Scripts\src\modifiers\offset.js" />
    <Content Include="Scripts\src\modifiers\preventOverflow.js" />
    <Content Include="Scripts\src\modifiers\shift.js" />
    <Content Include="Scripts\src\utils\clockwise.js" />
    <Content Include="Scripts\src\utils\computeAutoPlacement.js" />
    <Content Include="Scripts\src\utils\debounce.js" />
    <Content Include="Scripts\src\utils\find.js" />
    <Content Include="Scripts\src\utils\findCommonOffsetParent.js" />
    <Content Include="Scripts\src\utils\findIndex.js" />
    <Content Include="Scripts\src\utils\getBordersSize.js" />
    <Content Include="Scripts\src\utils\getBoundaries.js" />
    <Content Include="Scripts\src\utils\getBoundingClientRect.js" />
    <Content Include="Scripts\src\utils\getClientRect.js" />
    <Content Include="Scripts\src\utils\getFixedPositionOffsetParent.js" />
    <Content Include="Scripts\src\utils\getOffsetParent.js" />
    <Content Include="Scripts\src\utils\getOffsetRect.js" />
    <Content Include="Scripts\src\utils\getOffsetRectRelativeToArbitraryNode.js" />
    <Content Include="Scripts\src\utils\getOppositePlacement.js" />
    <Content Include="Scripts\src\utils\getOppositeVariation.js" />
    <Content Include="Scripts\src\utils\getOuterSizes.js" />
    <Content Include="Scripts\src\utils\getParentNode.js" />
    <Content Include="Scripts\src\utils\getPopperOffsets.js" />
    <Content Include="Scripts\src\utils\getReferenceNode.js" />
    <Content Include="Scripts\src\utils\getReferenceOffsets.js" />
    <Content Include="Scripts\src\utils\getRoot.js" />
    <Content Include="Scripts\src\utils\getRoundedOffsets.js" />
    <Content Include="Scripts\src\utils\getScroll.js" />
    <Content Include="Scripts\src\utils\getScrollParent.js" />
    <Content Include="Scripts\src\utils\getStyleComputedProperty.js" />
    <Content Include="Scripts\src\utils\getSupportedPropertyName.js" />
    <Content Include="Scripts\src\utils\getViewportOffsetRectRelativeToArtbitraryNode.js" />
    <Content Include="Scripts\src\utils\getWindow.js" />
    <Content Include="Scripts\src\utils\getWindowSizes.js" />
    <Content Include="Scripts\src\utils\includeScroll.js" />
    <Content Include="Scripts\src\utils\index.js" />
    <Content Include="Scripts\src\utils\isBrowser.js" />
    <Content Include="Scripts\src\utils\isFixed.js" />
    <Content Include="Scripts\src\utils\isFunction.js" />
    <Content Include="Scripts\src\utils\isIE.js" />
    <Content Include="Scripts\src\utils\isModifierEnabled.js" />
    <Content Include="Scripts\src\utils\isModifierRequired.js" />
    <Content Include="Scripts\src\utils\isNumeric.js" />
    <Content Include="Scripts\src\utils\isOffsetContainer.js" />
    <Content Include="Scripts\src\utils\removeEventListeners.js" />
    <Content Include="Scripts\src\utils\runModifiers.js" />
    <Content Include="Scripts\src\utils\setAttributes.js" />
    <Content Include="Scripts\src\utils\setStyles.js" />
    <Content Include="Scripts\src\utils\setupEventListeners.js" />
    <Content Include="Scripts\sweetalert.min.js" />
    <Content Include="Scripts\umd\popper-utils.js" />
    <Content Include="Scripts\umd\popper-utils.min.js" />
    <Content Include="Scripts\umd\popper.js" />
    <Content Include="Scripts\umd\popper.min.js" />
    <Content Include="Views\Account\Account.css" />
    <Content Include="Views\Account\Account.js" />
    <Content Include="Views\Convenio\Convenio.js" />
    <Content Include="Views\ExtratoMedico\ExtratoMedico.js" />
    <Content Include="Views\Medico\Medico.js" />
    <Content Include="Views\RegistroPagamento\RegistroPagamento.js" />
    <Content Include="Views\RegraCartaConversao\RegraCartaConversao.css" />
    <Content Include="Views\RegraCartaConversao\RegraCartaConversao.js" />
    <Content Include="Views\Repasse\Repasse.css" />
    <Content Include="Views\RateioFixo\Index.cshtml" />
    <Content Include="Views\RateioGrauParticipacao\Index.cshtml" />
    <Content Include="Views\Shared\ModalRetornoUsuario.cshtml" />
    <Content Include="Views\Convenio\Index.cshtml" />
    <Content Include="Views\Procedimento\Index.cshtml" />
    <Content Include="Views\Convenio\Details.cshtml" />
    <Content Include="Views\Convenio\_ProcedimentoPartial.cshtml" />
    <Content Include="Views\RegraCartaConversao\_PartialRegra.cshtml" />
    <Content Include="Views\ExtratoMedico\Create.cshtml" />
    <Content Include="Views\ExtratoMedico\Edit.cshtml" />
    <Content Include="Views\ExtratoMedico\Index.cshtml" />
    <Content Include="Views\RegraCartaConversao\Edit.cshtml" />
    <Content Include="Views\EmpresaMedico\Index.cshtml" />
    <Content Include="Views\RegistroPagamento\Index.cshtml" />
    <Content Include="Views\RegistroPagamento\_PartialGrid.cshtml" />
    <Content Include="Views\ExtratoMedico\_GridExtratos.cshtml" />
    <Content Include="Views\Repasse\Details.cshtml" />
    <Content Include="Web References\WSPortalCooperado\ConvenioRepasse.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\WSPortalCooperado\EmpresaMedicoRepasse.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Views\Medico\Detalhes.cshtml" />
    <Content Include="Views\TabelaProgressivaINSS\Index.cshtml" />
    <Content Include="Views\TabelaProgressivaINSS\Edit.cshtml" />
    <Content Include="Views\TabelaProgressivaINSS\Create.cshtml" />
    <Content Include="Views\TabelaProgressivaINSS\_GridValores.cshtml" />
    <Content Include="Views\TabelaProgressivaINSS\_ModalValores.cshtml" />
    <Content Include="Views\TabelaProgressivaIRPF\Create.cshtml" />
    <Content Include="Views\TabelaProgressivaIRPF\Edit.cshtml" />
    <Content Include="Views\TabelaProgressivaIRPF\Index.cshtml" />
    <Content Include="Views\TabelaProgressivaIRPF\_GridValores.cshtml" />
    <Content Include="Views\TabelaProgressivaIRPF\_ModalValoresIR.cshtml" />
    <Content Include="Views\Repasse\_GridDemonstrativoConvenio.cshtml" />
    <Content Include="Views\GuiaDemonstrativo\Index.cshtml" />
    <Content Include="Views\Lote\Index.cshtml" />
    <Content Include="Views\Lote\Detalhes.cshtml" />
    <Content Include="Views\RegraCartaConversao\Index.cshtml" />
    <Content Include="Views\Repasse\_ModalGuiasDemonstrativo.cshtml" />
    <Content Include="Views\Repasse\_GridGuiasDemonstrativo.cshtml" />
    <Content Include="Views\Repasse\_GridGuiaAtendimento.cshtml" />
    <Content Include="Views\Repasse\_ModalGuiaAtendimento.cshtml" />
    <Content Include="Views\Repasse\_ModalExtratos.cshtml" />
    <Content Include="Views\Repasse\_GridExtratos.cshtml" />
    <Content Include="Views\Convenio\Edit.cshtml" />
    <Content Include="Views\Repasse\_ModalProcedimentosDemonstrativo.cshtml" />
    <Content Include="Views\Repasse\_GridProcedimentosDemonstrativo.cshtml" />
    <Content Include="Views\Repasse\_ModalLotes.cshtml" />
    <Content Include="Views\Repasse\_GridLotesRepasse.cshtml" />
    <Content Include="Views\Repasse\_ModalItensLote.cshtml" />
    <Content Include="Views\Repasse\_GridItensLote.cshtml" />
    <Content Include="Views\RegistroPagamento\_ModalExtratos.cshtml" />
    <Content Include="Views\RegistroPagamento\_GridDetalhesExtratos.cshtml" />
    <Content Include="Views\ItensRecorrentes\_GridItensRecorrentes.cshtml" />
    <Content Include="Views\ItensRecorrentes\Create.cshtml" />
    <Content Include="Views\ItensRecorrentes\Edit.cshtml" />
    <Content Include="Views\ItensRecorrentes\Index.cshtml" />
    <Content Include="Views\RegistroImposto\Index.cshtml" />
    <Content Include="Views\RegistroImposto\_PartialGrid.cshtml" />
    <Content Include="Views\RegistroImposto\_ModalExtratos.cshtml" />
    <Content Include="Views\RegistroImposto\_GridDetalhesExtratos.cshtml" />
    <Content Include="Views\ResponsavelConvenio\Index.cshtml" />
    <Content Include="Views\ResponsavelConvenio\_Grid.cshtml" />
    <Content Include="Views\ResponsavelConvenio\_ModalNovo.cshtml" />
    <Content Include="Views\ResponsavelConvenio\Edit.cshtml" />
    <Content Include="Views\JustificativaGlosa\Index.cshtml" />
    <Content Include="Views\JustificativaGlosa\Create.cshtml" />
    <Content Include="Views\JustificativaGlosa\Edit.cshtml" />
    <Content Include="Views\Repasse\_ModalAtdNaoPago.cshtml" />
    <Content Include="Views\Repasse\_GridAtdNaoPago.cshtml" />
    <Content Include="Views\LoteGlosa\Detalhes.cshtml" />
    <Content Include="Views\LoteGlosa\Index.cshtml" />
    <Content Include="Views\LoteGlosa\_ModalAnaliseLote.cshtml" />
    <Content Include="Views\LoteGlosa\_ModalTransfRespon.cshtml" />
    <Content Include="Views\LoteGlosa\_GridItensLote.cshtml" />
    <Content Include="Views\LoteGlosa\_GridTransfRespon.cshtml" />
    <Content Include="Views\LoteGlosa\_GridLogs.cshtml" />
    <Content Include="Views\LoteGlosa\_ModalLogs.cshtml" />
    <Content Include="Views\Repasse\_ModalProcessarLoteGlosa.cshtml" />
    <Content Include="Views\Repasse\_GridProcessamentoGlosa.cshtml" />
    <Content Include="Views\LoteGlosa\_ModalAnaliseDetalhadaGlosa.cshtml" />
    <Content Include="Views\LoteGlosa\_GridGuiaAtendimento.cshtml" />
    <Content Include="Views\ExtratoMedico\_ModalLogs.cshtml" />
    <Content Include="Views\ExtratoMedico\_GridLogs.cshtml" />
    <Content Include="Views\ExtratoMedico\_GridLogsDetalhes.cshtml" />
    <Content Include="Views\ExtratoMedico\_ModalLogsDetalhes.cshtml" />
    <Content Include="Views\ProtocoloGlosa\Index.cshtml" />
    <Content Include="Views\ProtocoloGlosa\Create.cshtml" />
    <Content Include="Views\ProtocoloGlosa\Detalhes.cshtml" />
    <Content Include="Views\BancoUnicooper\Create.cshtml" />
    <Content Include="Views\BancoUnicooper\Index.cshtml" />
    <Content Include="Views\BancoUnicooper\Edit.cshtml" />
    <Content Include="Views\Repasse\_ModalGuiaNotaFiscal.cshtml" />
    <Content Include="Views\Repasse\_PartialGuiaNotaFiscal.cshtml" />
    <Content Include="Views\Repasse\_PartialGuiaNotaFiscalEdit.cshtml" />
    <Content Include="Views\Repasse\_ModalDepositoRepasse.cshtml" />
    <Content Include="Views\Repasse\_PartialDepositoRepasse.cshtml" />
    <Content Include="Views\Repasse\_GridDepositoRepasse.cshtml" />
    <Content Include="Views\Repasse\_ModalInserteDepositoRepasse.cshtml" />
    <Content Include="Views\ClassificacaoRepasse\Index.cshtml" />
    <Content Include="Views\ClassificacaoRepasse\Edit.cshtml" />
    <Content Include="Views\ClassificacaoRepasse\Create.cshtml" />
    <None Include="Web References\WSPortalCooperado\IntegraRepasse.disco" />
    <Content Include="Web.config" />
    <Content Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Views\Web.config" />
    <Content Include="Views\_ViewStart.cshtml" />
    <Content Include="Views\Shared\Error.cshtml" />
    <Content Include="Views\Shared\_Layout.cshtml" />
    <Content Include="Views\Home\About.cshtml" />
    <Content Include="Views\Home\Contact.cshtml" />
    <Content Include="Views\Home\Index.cshtml" />
    <Content Include="Scripts\umd\popper.min.js.map" />
    <Content Include="Scripts\umd\popper.js.map" />
    <Content Include="Scripts\umd\popper.js.flow" />
    <Content Include="Scripts\umd\popper-utils.min.js.map" />
    <Content Include="Scripts\umd\popper-utils.js.map" />
    <Content Include="Scripts\README.md" />
    <Content Include="Scripts\popper.min.js.map" />
    <Content Include="Scripts\popper.js.map" />
    <Content Include="Scripts\popper-utils.min.js.map" />
    <Content Include="Scripts\popper-utils.js.map" />
    <Content Include="Views\Account\Dados.cshtml" />
    <Content Include="Views\Account\ForgotPassword.cshtml" />
    <Content Include="Views\Account\Login.cshtml" />
    <Content Include="Views\Repasse\Index.cshtml" />
    <Content Include="Views\Repasse\Create.cshtml" />
    <Content Include="Views\Usuario\Create.cshtml" />
    <Content Include="Views\Usuario\Edit.cshtml" />
    <Content Include="Views\Usuario\Index.cshtml" />
    <Content Include="Views\Repasse\Edit.cshtml" />
    <Content Include="Views\Repasse\_GridPlanRecebimento.cshtml" />
    <Content Include="Views\Repasse\_GridNotaFiscalRepasse.cshtml" />
    <Content Include="Views\Repasse\_PartialNotaFiscal.cshtml" />
    <Content Include="Views\Repasse\_PartialPlanRecebimento.cshtml" />
    <Content Include="Views\Medico\Index.cshtml" />
    <Content Include="Views\RateioMedico\Index.cshtml" />
    <Content Include="Views\RateioMedico\Create.cshtml" />
    <Content Include="Views\Repasse\_ModalNotaFiscal.cshtml" />
    <Content Include="Views\Repasse\_ModalPlanRecebimento.cshtml" />
    <Content Include="Views\RateioMedico\Edit.cshtml" />
    <Content Include="Views\Repasse\_ModalBuscaLote.cshtml" />
    <Content Include="Views\RateioFixo\Create.cshtml" />
    <Content Include="Views\RateioFixo\Edit.cshtml" />
    <Content Include="Views\RateioGrauParticipacao\Create.cshtml" />
    <Content Include="Views\RateioGrauParticipacao\Edit.cshtml" />
    <Content Include="Views\Shared\_LayoutLogin.cshtml" />
    <Content Include="Views\Repasse\_GridLotes.cshtml" />
    <None Include="Web References\WSPortalCooperado\IntegraRepasse.wsdl" />
    <Content Include="Web References\WSPortalCooperado\MedicoRepasse.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\WSPortalCooperado\ProcedimentosConvenioRepasse.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <None Include="Web References\WSPortalCooperado\Reference.map">
      <Generator>MSDiscoCodeGenerator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <Content Include="Web References\WSPortalCooperado\RepasseRetornoWS.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\WSPortalCooperado\ResultPesquisaLote.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\WSPortalCooperado\RetornoGetGuiaLoteRepasse.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
    <Content Include="Web References\WSPortalCooperado\RetornoMedicoIntegraGuia.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
    <Folder Include="Views\ItensLoteGlosa\" />
    <Folder Include="Views\ValoresTabelaProgressivaIRPFController\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <TypeScriptCompile Include="Scripts\index.d.ts" />
    <Content Include="Views\Repasse\Repasse.js" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{4A0DDDB5-7A95-4FBF-97CC-616D07737A77}" />
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Connected Services\" />
  </ItemGroup>
  <ItemGroup>
    <WebReferences Include="Web References\" />
  </ItemGroup>
  <ItemGroup>
    <WebReferenceUrl Include="http://localhost/PortalCooperado/integrarepasse.asmx">
      <UrlBehavior>Dynamic</UrlBehavior>
      <RelPath>Web References\WSPortalCooperado\</RelPath>
      <UpdateFromURL>http://localhost/PortalCooperado/integrarepasse.asmx</UpdateFromURL>
      <ServiceLocationURL>
      </ServiceLocationURL>
      <CachedDynamicPropName>
      </CachedDynamicPropName>
      <CachedAppSettingsObjectName>Settings</CachedAppSettingsObjectName>
      <CachedSettingsPropName>RepasseConvenio_WSPortalCooperado_IntegraRepasse</CachedSettingsPropName>
    </WebReferenceUrl>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Reports\RelatorioLoteGlosa.rdlc" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.targets" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.targets')" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <Target Name="MvcBuildViews" AfterTargets="AfterBuild" Condition="'$(MvcBuildViews)'=='true'">
    <AspNetCompiler VirtualPath="temp" PhysicalPath="$(WebProjectOutputDir)" />
  </Target>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>59668</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost/RepasseConvenio</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.3.6.0\build\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.3.6.0\build\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>Este projeto faz referência a pacotes do NuGet que não estão presentes neste computador. Use a Restauração de Pacotes do NuGet para baixá-los.  Para obter mais informações, consulte http://go.microsoft.com/fwlink/?LinkID=322105. O arquivo ausente é {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.3.6.0\build\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.3.6.0\build\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target> -->
</Project>
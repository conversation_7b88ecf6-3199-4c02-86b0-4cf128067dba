﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Web;

namespace RepasseConvenio.Models
{
  public class BancoUnicooperModel
  {
    public int Codigo { get; set; }
    public int IdBanco { get; set; }

    [DisplayName("Selecione o banco")]
    [URLSelect("Select2/GetBancoSelect")]
    [PlaceHolderAttr("Selecione o Banco")]
    public Select2Model BancoSelect
    {
      get
      {
        BancoServices BancoService = new BancoServices();
        return BancoService.GetById(this.IdBanco).ToSelect2Model();
      }
      set
      {
        if (value != null && !string.IsNullOrEmpty(value.id) && !value.id.Equals("0"))
          this.IdBanco = int.Parse(value.id);

      }
    }
    [DisplayName("Descrição")]
    public string Descricao  { get; set; }
    [DisplayName("Agência")]
    public string  Agencia { get; set; }
    [DisplayName("Nome")]
    public string  BancoNome { get; set; }
    [DisplayName("Conta")]
    public string conta { get; set; }
    [DisplayName("Dígito")]
    public string Digito { get; set; }

  }


  public class BancoUnicooperIndex
  {
     public int Codigo { get; set; }
    public int IdBanco { get; set; }

    [DisplayName("Selecione o Banco")]
    [URLSelect("Select2/GetBancoSelect")]
    [PlaceHolderAttr("Selecione o Banco")]
    public Select2Model BancoSelect
    {
      get
      {
        BancoServices BancoService = new BancoServices();
        return BancoService.GetById(this.IdBanco).ToSelect2Model();
      //  return BancoService.GetById(this.IdBanco.GetValueOrDefault()).ToSelect2Model();
      }
      set
      {
        if (value != null && !string.IsNullOrEmpty(value.id) && !value.id.Equals("0"))
          this.IdBanco = int.Parse(value.id);
       
      }
    }

    public string Descricao  { get; set; }
    public string  Agencia { get; set; }
    public string conta { get; set; }
    public string Digito { get; set; }

  }

  public static class Complemento
  {
    public static LookupModel ToLookupModel(this R_BancosUnicooper entity)
    {
      if (entity == null)
        return null;

      return new LookupModel()
      {
        Codigo = entity.BU_Id.ToString(),
        Description = entity.BU_Descricao

      };
    }
  }


  public static class BancoUnicooperModelConversion
  {
    public static R_BancosUnicooper toCreateBancounicooper(this BancoUnicooperModel model)
                                                                
                                                                
    {
      return new R_BancosUnicooper()
      {
        BU_IdBanco = model.IdBanco,
        BU_Descricao = model.Descricao,
        BU_Agencia = model.Agencia,
        BU_Conta = model.conta,
        BU_Digito = model.Digito
       
      };
    }

    public static R_BancosUnicooper toEditBancoUnicooper(this BancoUnicooperModel model)
    {
      R_BancosUnicooper entity = new R_BancosUnicooper();
      entity.BU_Id = model.Codigo;
      entity.BU_IdBanco = model.IdBanco;
      entity.BU_Descricao = model.Descricao;
      entity.BU_Agencia = model.Agencia;
      entity.BU_Conta = model.conta;
      entity.BU_Digito = model.Digito;


      return entity;
    }
  }
}
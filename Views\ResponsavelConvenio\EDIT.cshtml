﻿@using PagedList.Mvc
@using RepasseConvenio.Models
@using RepasseConvenio.Infrastructure
@using RepasseConvenio.Infrastructure.Controls
@model ResponsavelConvenioModel

@{
  ViewBag.Title = "Editar";
  Layout = "~/Views/Shared/_Layout.cshtml";
}

@Scripts.Render("~/Views/ResponsavelConvenio/ResponsavelConvenio.js")

@using (Html.BeginForm("Edit", "ResponsavelConvenio", FormMethod.Post))
{
  @Html.HiddenFor(m => m.Codigo)
  @Html.HiddenFor(m => m.CodigoConvenio)
  @Html.HiddenFor(m => m.CodigoRelacao)

  <div class="row">
    <section class="col-lg-12 connectedSortable ui-sortable">
      <div class="card">
        <div class="card-header ui-sortable-handle">
          <h3 class="card-title">
            <i class="fas fa-user mr-1"></i>
            Responsável do Convênio
          </h3>
          <div class="card-tools">
          </div>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                @Html.LabelFor(m => m.NomeResponsavel)
                @Html.LibEditorFor(m => m.NomeResponsavel, new { @class = "form-control" })
                @Html.ValidationMessageFor(m => m.NomeResponsavel, "", new { @class = "text-danger" })
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                @Html.LabelFor(m => m.CPF)
                @Html.LibEditorFor(m => m.CPF, new { @class = "form-control CPF" })
                @Html.ValidationMessageFor(m => m.CPF, "", new { @class = "text-danger" })
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                @Html.LabelFor(m => m.Email)
                @Html.LibEditorFor(m => m.Email, new { @class = "form-control EMAIL" })
                @Html.ValidationMessageFor(m => m.Email, "", new { @class = "text-danger" })
              </div>
            </div>
          </div>
        </div>
        <div class="card-footer">
          <a type="button" value="Voltar" class="btn btn-secondary" href="@Url.Action("Index", "ResponsavelConvenio", new { CodigoConvenio = Model.CodigoConvenio })">Cancelar</a>
          <button type="submit" value="Editar" class="btn btn-primary">Salvar</button>
        </div>
      </div>
    </section>
  </div>
}
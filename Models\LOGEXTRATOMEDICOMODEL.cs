﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Script.Serialization;

namespace RepasseConvenio.Models
{
  public class LogExtratoMedicoModel
  {
  }

  public class LogExtratoMedicoModal
  {
    public int Codigo { get; set; }
    public string Descricao { get; set; }

    public DateTime DataCriacao { get; set; }

    public string Usuario { get; set; }
  }


  public static class LogExtratoMedicoConversions
  {
    public static R_LogExtratoMedico Create(this R_LogExtratoMedico logExtratoMedico
                                           , int IdExtrato
                                           , int IdUsuario
                                           , string Descricao)
    {
      JavaScriptSerializer javaScriptSerializer = new JavaScriptSerializer();
      return new R_LogExtratoMedico()
      {
        LEM_Data = DateTime.Now,
        LEM_Descricao = Descricao,
        LEM_IdExtratoMedico = IdExtrato,
        LEM_IdUsuario = IdUsuario,
      };
    }

    public static R_LogExtratoMedico Edit(this R_LogExtratoMedico logExtratoMedico
                                       , List<LogAux> ListLogAux
                                       , int IdExtrato
                                       , int IdUsuario
                                       , string Descricao)
    {
      JavaScriptSerializer javaScriptSerializer = new JavaScriptSerializer();
      return new R_LogExtratoMedico()
      {
        LEM_Data = DateTime.Now,
        LEM_Descricao = Descricao,
        LEM_IdExtratoMedico = IdExtrato,
        LEM_IdUsuario = IdUsuario,
        LEM_Log = javaScriptSerializer.Serialize(ListLogAux)
      };
    }
  }
}
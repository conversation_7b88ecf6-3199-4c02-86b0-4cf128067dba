﻿@using RepasseConvenio.Models
@model ProcessaGlosaRepasse

<div class="row">
  <div class="col-md-4">
    <div class="form-group">
      @Html.EditorFor(model => model.DataVencimentoLoteGlosa, new { htmlAttributes = new { @class = "form-control Data airDatePickerDate" } })
      @Html.ValidationMessageFor(model => model.DataVencimentoLoteGlosa, "", new { @class = "text-danger" })
    </div>
  </div>
  <div class="col-md-8">
    <div class="form-group">
      <p id="ShowDataVencimento" style="display: none; color: red;">A data de vencimento do lote será dia: </p>
    </div>
  </div>
</div>

<div class="row">

</div>
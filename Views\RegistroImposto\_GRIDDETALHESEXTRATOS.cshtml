﻿@using RepasseConvenio.Models
@model List<ExtratoMedicoRegistroImposto>

<div class="col-md-12 table-responsive p-0">
  <table class="table table-sm table-striped table-hover text-nowrap">
    <thead>
      <tr>
        <th>
          <PERSON><PERSON><PERSON><PERSON> Gui<PERSON>
        </th>
        <th>
          Valor
        </th>
        <th>
          Data do Processamento
        </th>
        <th>
          Tipo de Imposto
        </th>
      </tr>
    </thead>
    <tbody>
      @foreach (ExtratoMedicoRegistroImposto item in Model)
      {
        <tr>
          <td>
            @if (string.IsNullOrEmpty(item.NroGuiaAtendimento))
            {
              <text>-</text>
            }
            else
            {
              <text>@(item.NroGuiaAtendimento)</text>
            }
          </td>
          <td>
            @item.Valor
          </td>
          <td>
            @item.DataProcessamentoAux
          </td>
          <td>
            @RepasseConvenio.Infrastructure.Helpers.EnumHelper.Description(item.TipoImposto)
          </td>
        </tr>
      }
    </tbody>
  </table>
</div>

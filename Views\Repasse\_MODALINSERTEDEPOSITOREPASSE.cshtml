﻿


<div class="modal fade" id="ModalIserteDepositoRepasse" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog" style="max-width: 50%; ">
      <div class="modal-content">
        <div class="modal-header">
          <h4 class="modal-title">Depósito Repasse</h4>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">×</span>
          </button>

        </div>
        <div class="modal-body" style="height: 160px; overflow-x: auto;">
          <div class="row">

            <div class="col-md-4">
              <div class="form-group" data-select2-id="10">
                <label for="BancoInsertSelect">Selecione o Banco</label>
                <input id="BancoInsertSelect_id" class="inserir" name="BancoInsertSelect.id" type="hidden" value="">
                <input id="BancoInsertSelect_text" name="BancoInsertSelect.text" type="hidden" value="">
                <select class="form-control "  id="BancoInsertSelectLookup" name="BancoInsertSelectLookup"> </select>
                <script>
                  MakeDropDown('BancoInsertSelect', GetURLBaseComplete() + '/DepositoRepasse/GetBancoDepositoRepasseSelect', 'Selecione o Banco');
                </script>
              </div>
            </div>

            <div class="col-md-4  ">
              <label>Data do depósito</label>
              <input type="Text" class=" form-control airDatePickerDate Data " readonly="readonly" id="DataInserteDeposito" name="DataDeposito" ; value="" />
            </div>
            <div class="col-md-4  ">
              <label>Valor do depósito</label>
              <input type="Text" class=" form-control money disabled " readonly="readonly" id="ValorInserteDeposito" name="ValorDeposito" ; value="" />
            </div>
          </div>

          <div class="row">

            <div class="col-md-4  ">
              <label>Número do documento</label>
              <input type="Text" class=" form-control numeros" readonly="readonly" id="NumeroInserteDcumento" name="NumeroDcumento" ; value="" />
            </div>
            <div class="col-md-4  ">
              <label>Valor Utilizado</label>
              <input type="Text" class=" form-control money " readonly="readonly" id="ValorInserteUtilizado" name="ValorUtilizado" ; value="" />
            </div>

          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-primary btnInserteSaveBanco ">Salvar</button>
          <button type="button" class="btn btn-outline-dark pull-left" data-dismiss="modal">Cancelar</button>
        </div>

      </div>
    </div>
  </div>












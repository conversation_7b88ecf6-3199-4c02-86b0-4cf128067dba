﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using PagedList;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RepasseConvenio.Services
{
  public class StatusGuiaDemonstrativoConvenioServices : ServiceBase
  {
    public StatusGuiaDemonstrativoConvenioServices(RepasseEntities Contexto)
        : base(Contexto)
    { }
    public StatusGuiaDemonstrativoConvenioServices()
       : base()
    { }

    public StatusGuiaDemonstrativoConvenioServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public StatusGuiaDemonstrativoConvenioServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public int GetIdByEnum(StatusGuiaDemonstrativoEnum statusGuiaDemonstrativoEnum)
    {
      return Contexto.R_StatusGuiaDemonstrativoConvenio.Where(a => a.SGDC_Enum == (int)statusGuiaDemonstrativoEnum).Select(a => a.SGDC_Id).FirstOrDefault();
    }
    public R_StatusGuiaDemonstrativoConvenio GetByEnum(StatusGuiaDemonstrativoEnum statusGuiaDemonstrativoEnum)
    {
      return Contexto.R_StatusGuiaDemonstrativoConvenio.Where(a => a.SGDC_Enum == (int)statusGuiaDemonstrativoEnum).FirstOrDefault();
    }

    public R_StatusGuiaDemonstrativoConvenio GetById(int Id)
    {
      return Contexto.R_StatusGuiaDemonstrativoConvenio.Where(a => a.SGDC_Id == Id).FirstOrDefault();
    }

    public List<R_StatusGuiaDemonstrativoConvenio> GetAll()
    {
      string query = @"SELECT * FROM R_StatusGuiaDemonstrativoConvenio";
      return Contexto.Database.SqlQuery<R_StatusGuiaDemonstrativoConvenio>(query).ToList();
    }
  }
}


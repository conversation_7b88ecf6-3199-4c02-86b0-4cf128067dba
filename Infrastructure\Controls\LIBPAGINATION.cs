﻿using System.Linq;
using System.Web.Mvc;
using System.Text;
using System.Web.Mvc.Html;
using System.Web.Routing;
using System.Collections;
using System.Collections.Generic;
using PagedList;
using System;

namespace RepasseConvenio.Infrastructure.Controls
{
  public class LibPagination<T>
  {
    public HtmlHelper HtmlHelper { get; set; }

    public PagedList<T> Model { get; set; }

    public LibPagination(HtmlHelper htmlHelper, PagedList<T> model)
    {
    }

    public string RenderHtml(string classe)
    {
      return "";
    }
  }

  public class PagedList<T>
  {
    public PagedList()
    {
    }

    public bool HasData
    {
      get { return this.List.Count > 0; }
    }

    public int PageSize { get; set; }

    public int Page { get; set; }

    public int Count { get; set; }

    public IList<T> List { get; set; }

    public string Action { get; set; }

    public string Controller { get; set; }

    public string SenhaCertificado { get; set; }

    [AllowHtml]
    public string TextoAuxiliar { get; set; }

    public Dictionary<string, object> CurrentFilter { get; set; }
  }

  [Serializable]
  public class StaticPagedList<T> : BasePagedList<T>
  {
    public StaticPagedList(IEnumerable<T> subset, IPagedList metaData)
      : this(subset, metaData.PageNumber, metaData.PageSize, metaData.TotalItemCount)
    {
    }

    public StaticPagedList(IEnumerable<T> subset, int pageNumber, int pageSize, int totalItemCount)
      : base(pageNumber, pageSize, totalItemCount)
    {
    }
  }

  public static class ExtensionsPagedList
  {
    public static IPagedList<T> ToPagedListCustom<T>(this IEnumerable<T> superset, int pageNumber, int pageSize, int pageCount)
    {
      return new StaticPagedList<T>(superset, pageNumber, pageSize, pageCount);
    }
  }
}
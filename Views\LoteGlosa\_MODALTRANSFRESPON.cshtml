﻿@using RepasseConvenio.Models

<div class="modal fade" id="ModalTransfRespon" data-keyboard="false" data-backdrop="static">
  <div class="modal-dialog" style="max-width: 75%;">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">Transferir Responsável</h4>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">×</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-md-12 container">
            <div id="PartialTransfRespon" style="display: flex;">
              @Html.Partial("_GridTransfRespon", new LoteGlosaTransResponGrid())
            </div>
          </div>
        </div>
      </div>
      <div class="card-footer" style=" display: flex; justify-content: flex-end;">
        <button type="button" value="Voltar" class="btn btn-success" id="Transferir">Transferir</button>
      </div>
    </div>
  </div>
</div>
﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Models;
using RepasseConvenio.Services;
using System;
using System.Web.Mvc;

namespace RepasseConvenio.Controllers
{
  public class ValoresTabelaProgressivaIRPFController : Controller
  {

    // GET: ValoresTabelaProgressivaIRPFController
    private ValoresTabelaProgressivaIRPFServices ValoresTabelaProgressivaIRPFServices
    {
      get
      {
        if (_ValoresTabelaProgressivaIRPFServices == null)
          _ValoresTabelaProgressivaIRPFServices = new ValoresTabelaProgressivaIRPFServices(ContextoUsuario.UserLogged);

        return _ValoresTabelaProgressivaIRPFServices;
      }
    }

    private ValoresTabelaProgressivaIRPFServices _ValoresTabelaProgressivaIRPFServices;

    public JsonResult Create(ValoresTabelaProgressivaIRPFModel model)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        ValoresTabelaProgressivaIRPFServices.Create(model);
        retornoAjax.Titulo = "Sucesso";
        retornoAjax.Mensagem = "Valor Tabela INSS inserido com sucesso.";
        retornoAjax.TipoMensagem = "success";
        retornoAjax.Erro = false;
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
      catch (CustomException ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
      catch (Exception ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
    }


    [HttpPost]
    public ActionResult Edit(ValoresTabelaProgressivaIRPFModel model)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        ValoresTabelaProgressivaIRPFServices.Edit(model);
        retornoAjax.Titulo = "Sucesso";
        retornoAjax.Mensagem = "Valor Tabela IRPF atualizado com sucesso.";
        retornoAjax.TipoMensagem = "success";
        retornoAjax.Erro = false;
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
      catch (CustomException ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
      catch (Exception ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
    }


    [HttpPost]
    public ActionResult Delete(int id)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        ValoresTabelaProgressivaIRPFServices.Delete(id);
        retornoAjax.Titulo = "Sucesso";
        retornoAjax.Mensagem = "Valor Tabela IRPF removido com sucesso.";
        retornoAjax.TipoMensagem = "success";
        retornoAjax.Erro = false;
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
      catch (CustomException ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
      catch (Exception ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
    }



    public ActionResult Index()
    {
      return View();
    }
  }
}
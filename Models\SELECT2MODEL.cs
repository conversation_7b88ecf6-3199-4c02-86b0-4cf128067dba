﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using RepasseConvenio.Models;
using RepasseConvenio.Services;

namespace RepasseConvenio.Models
{
  public class Select2Model
  {
    public string id { get; set; }
    public string text { get; set; }
  }

  public static class Selec2Conversions
  {
    public static Select2Model ToSelect2Model(this R_Usuario statusUsuario)
    {
      if (statusUsuario == null)
        return null;

      return new Select2Model()
      {
        id = statusUsuario.U_Id.ToString(),
        text = statusUsuario.U_Nome
      };
    }

    public static Select2Model ToSelect2Model(this R_Convenio conv)
    {
      if (conv == null)
        return null;

      return new Select2Model()
      {
        id = conv.C_Id.ToString(),
        text = conv.C_RazaoSocial
      };
    }

    public static Select2Model ToSelect2Model(this R_MotivoGlosa motivo)
    {
      if (motivo == null)
        return null;

      return new Select2Model()
      {
        id = motivo.MG_Id.ToString(),
        text = string.Format("{0} - {1}", motivo.MG_Id, motivo.MG_Descricao)
      };
    }

    public static Select2Model ToSelect2Model(this R_StatusRepasse status)
    {
      if (status == null)
        return null;

      return new Select2Model()
      {
        id = status.SR_Id.ToString(),
        text = status.SR_Descricao
      };
    }

    public static Select2Model ToSelect2Model(this R_Banco banco)
    {
      if (banco == null)
        return null;

      return new Select2Model()
      {
        id = banco.B_Id.ToString(),
        text = banco.B_Codigo + "-" + banco.B_Nome
      };
    }

    public static Select2Model ToSelect2Model(this R_BancosUnicooper banco)
    {
      if (banco == null)
        return null;

      return new Select2Model()
      {
        id = banco.BU_Id.ToString(),
        text = banco.BU_Descricao 
      };
    }

    public static Select2Model ToSelect2Model(this R_DepositoRepasse banco)
    {
      if (banco == null)
        return null;

      return new Select2Model()
      {
        id = banco.DR_Id.ToString(),
        text = new BancoUnicooperService().GetById(banco.DR_IdBancosUnicooper).BU_Descricao
      };
    }

    public static Select2Model ToSelect2Model(this R_Hospital Hospital)
    {
      if (Hospital == null)
        return null;

      return new Select2Model()
      {
        id = Hospital.H_Id.ToString(),
        text = Hospital.H_Nome
      };
    }

    public static Select2Model ToSelect2Model(this R_Procedimentos procedimentos)
    {
      if (procedimentos == null)
        return null;

      return new Select2Model()
      {
        id = procedimentos.P_Codigo,
        text = procedimentos.P_Codigo + " - " + procedimentos.P_Descricao
      };
    }

    public static Select2Model ToSelect2Model(this R_GrauParticipacao grauParticipacao)
    {
      if (grauParticipacao == null)
        return null;

      return new Select2Model()
      {
        id = grauParticipacao.GP_Id.ToString(),
        text = grauParticipacao.GP_Nome
      };
    }

    public static Select2Model ToSelect2Model(this R_Medico medico)
    {
      if (medico == null)
        return null;

      return new Select2Model()
      {
        id = medico.M_Id.ToString(),
        text = medico.M_Nome
      };
    }

    public static Select2Model ToSelect2Model(this R_JustificativaGlosa justificativa)
    {
      if (justificativa == null)
        return null;

      return new Select2Model()
      {
        id = justificativa.JG_Id.ToString(),
        text = string.Format("{0} - {1}", justificativa.JG_Codigo, justificativa.JG_Descricao)
      };
    }

    public static Select2Model ToSelect2Model(this R_RegraCartaConversaoCampo regraCartaConversaoCampo)
    {
      if (regraCartaConversaoCampo == null)
        return null;

      return new Select2Model()
      {
        id = regraCartaConversaoCampo.RCCC_Id.ToString(),
        text = regraCartaConversaoCampo.RCCC_Campo
      };
    }

    public static Select2Model ToSelect2Model(this R_RegraCartaConversaoAcao regraCartaConversaoAcao)
    {
      if (regraCartaConversaoAcao == null)
        return null;

      return new Select2Model()
      {
        id = regraCartaConversaoAcao.RCCA_Id.ToString(),
        text = regraCartaConversaoAcao.RCCA_Acao
      };
    }

    public static Select2Model ToSelect2Model(this R_ClassificacaoRepasse classificacao)
    {
      if (classificacao == null)
        return null;

      return new Select2Model()
      {
        id = classificacao.CR_Id.ToString(),
        text = classificacao.CR_Descricao
      };
    }
    public static Select2Model ToSelect2Model(this R_EmpresaMedico empresa)
    {
      if (empresa == null)
        return null;

      return new Select2Model()
      {
        id = empresa.EM_CNPJ,
        text = empresa.EM_RazaoSocial
      };
    }

    public static Select2Model ToSelect2Model(this R_TipoRegistroImposto tipoImposto)
    {
      if (tipoImposto == null)
        return null;

      return new Select2Model()
      {
        id = tipoImposto.TRI_Id.ToString(),
        text = tipoImposto.TRI_Descricao
      };
    }

    public static Select2Model ToSelect2ModelWithId(this R_EmpresaMedico empresa)
    {
      if (empresa == null)
        return null;

      return new Select2Model()
      {
        id = empresa.EM_Id.ToString(),
        text = empresa.EM_RazaoSocial
      };
    }

    public static Select2Model ToSelect2Model(this R_ResponsavelConvenio model)
    {
      if (model == null)
        return null;

      return new Select2Model()
      {
        id = model.RC_Id.ToString(),
        text = model.RC_Nome
      };
    }

    public static Select2Model ToSelect2Model(this R_StatusGlosa status)
    {
      if (status == null)
        return null;

      return new Select2Model()
      {
        id = status.SG_Id.ToString(),
        text = status.SG_Descricao
      };
    }

    public static Select2Model ToSelect2Model(this R_Repasse status)
    {
      if (status == null)
        return null;

      return new Select2Model()
      {
        id = status.R_Id.ToString(),
        text = status.R_Numero
      };
    }
    
  }
}

﻿
/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Controls;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.Web.Mvc;

namespace RepasseConvenio.Controllers
{
  [Authorize]
  [Security("367EA0DE-0A43-41FD-AC0B-A8C701CAFA55")]
  public class LoteGlosaController : LibController
  {
    [HttpGet]
    public ActionResult Index()
    {
      LoteGlosaServices loteGlosaServices = new LoteGlosaServices(ContextoUsuario.UserLogged);
      List<LoteGlosaIndex> ListLoteGlosaIndex = loteGlosaServices.GetLoteGlosaIndex();
      LoteGlosaFiltroModel lote = new LoteGlosaFiltroModel();
      lote.ListLoteGlosa = ListLoteGlosaIndex;
      return View(lote);
    }
    [Security("367EA0DE-0A43-41FD-AC0B-A8C701CAFA55")]
    [HttpPost]
    public ActionResult Index(LoteGlosaFiltroModel lote)
    {
      try
      {
        LoteGlosaServices loteGlosaServices = new LoteGlosaServices(ContextoUsuario.UserLogged);
        lote.ListLoteGlosa = loteGlosaServices.GetLoteGlosaIndex(lote);

        if (lote.ListLoteGlosa.Count > 0)
          MessageListToast.Add(new Message(MessageType.Success, "Busca realizada com sucesso."));
        else
          MessageListToast.Add(new Message(MessageType.Warning, "Nenhum lote foi encontrado."));

        return View("Index", lote);
      }
      catch (CustomException ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return RedirectToAction("Index");
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, "Tivemos uma falha na sua solicitação."));
        return RedirectToAction("Index");
      }
    }

    [HttpGet]
    public ActionResult Detalhes(int IdLoteGlosa)
    {
      try
      {
        ItensLoteGlosaServices itensLoteGlosaServices = new ItensLoteGlosaServices(ContextoUsuario.UserLogged);
        List<ItensLoteGlosaIndex> ListItensLoteGlosaIndex = itensLoteGlosaServices.GetItensLoteGlosaIndex(IdLoteGlosa);

        LoteGlosaServices loteGlosaServices = new LoteGlosaServices();
        StatusGlosaEnum statusGlosaEnum = loteGlosaServices.GetStatusGlosaEnumById(IdLoteGlosa);
        ViewBag.IdLote = IdLoteGlosa;
        ViewBag.StatusLote = statusGlosaEnum;
        return View(ListItensLoteGlosaIndex);
      }
      catch (CustomException ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message, "12000"));
        return RedirectToAction("Index");
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, "Ocorreu um erro na sua solicitação, entre em contato com o administrador ou tente novamente mais tarde", "12000"));
        return RedirectToAction("Index");
      }
    }

    [HttpGet]
    public PartialViewResult _GridItens(int id)
    {
      ItensLoteGlosaServices itensLoteGlosaServices = new ItensLoteGlosaServices(ContextoUsuario.UserLogged);
      List<ItensLoteGlosaIndex> ListItensLoteGlosaIndex = itensLoteGlosaServices.GetItensLoteGlosaIndex(id);

      return PartialView("_GridItensLote", ListItensLoteGlosaIndex);
    }
    [Security("DD04C0A9-C0EA-40D8-8D66-44CBDC0BC97C")]
    [HttpGet]
    public PartialViewResult _GetLogs(int id)
    {
      List<LogLoteGlosa> Logs = new LoteGlosaServices().GetLogs(id);
      return PartialView("_GridLogs", Logs);
    }

    [HttpPost]
    public JsonResult ProcessarGlosa(int IdRepasse, DateTime DataCreditoFatura)
    {
      try
      {
        LoteGlosaServices loteGlosaServices = new LoteGlosaServices(ContextoUsuario.UserLogged);
        loteGlosaServices.ProcessarGlosa(IdRepasse, DataCreditoFatura);
        return Json(new { Erro = false, status = "success", message = "Glosa Processada" });
      }
      catch (CustomException ex)
      {
        return Json(new { Erro = true, status = "error", message = ex.Message });
      }
      catch (Exception ex)
      {
        return Json(new { Erro = true, status = "error", message = "Ocorreu um erro na sua solicitação, entre em contato com o administrador ou tente novamente mais tarde" });
      }
    }

    [HttpPost]
    public JsonResult AssumirLote(int IdLote)
    {
      try
      {
        LoteGlosaServices loteGlosaServices = new LoteGlosaServices(ContextoUsuario.UserLogged);
        loteGlosaServices.AssumirLote(IdLote);
        return Json(new { Erro = false, status = "success", message = "Glosa Processada" });
      }
      catch (CustomException ex)
      {
        return Json(new { Erro = true, status = "error", message = ex.Message });
      }
      catch (Exception ex)
      {
        return Json(new { Erro = true, status = "error", message = "Ocorreu um erro na sua solicitação, entre em contato com o administrador ou tente novamente mais tarde" });
      }
    }
    [Security("F8E91C35-5729-4956-B66D-1DB66B75F818")]
    [HttpPost]
    public JsonResult TransferirResponsavel(int IdLote, int IdResponsavel)
    {
      try
      {
        LoteGlosaServices loteGlosaServices = new LoteGlosaServices(ContextoUsuario.UserLogged);
        loteGlosaServices.TransferirResponsavel(IdLote, IdResponsavel);
        return Json(new { Erro = false, status = "success", message = "Transferido Responsavel." });
      }
      catch (CustomException ex)
      {
        return Json(new { Erro = true, status = "error", message = ex.Message });
      }
      catch (Exception ex)
      {
        return Json(new { Erro = true, status = "error", message = "Ocorreu um erro na sua solicitação, entre em contato com o administrador ou tente novamente mais tarde" });
      }
    }
    [Security("6BCB19A4-DFF6-4CD0-A7E0-D35DDFE52811")]
    [HttpPost]
    public JsonResult HaveResponsavel(int IdLote)
    {
      try
      {
        LoteGlosaServices loteGlosaServices = new LoteGlosaServices();
        bool haveResponsavel = loteGlosaServices.HaveResponsavel(IdLote);
        return Json(new { Erro = false, status = "success", HaveResponsavel = haveResponsavel });
      }
      catch (CustomException ex)
      {
        return Json(new { Erro = true, status = "error", message = ex.Message });
      }
      catch (Exception ex)
      {
        return Json(new { Erro = true, status = "error", message = "Ocorreu um erro na sua solicitação, entre em contato com o administrador ou tente novamente mais tarde" });
      }
    }
    [Security("3DEB6D84-C9F6-48F8-9559-6465DD2C03FC")]
    [HttpPost]
    public JsonResult SalvarJustificativa(List<int> IdsItens, int IdLoteGlosa, string Comentario, int CodJustificativa)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        if (IdsItens == null || IdsItens.Count == 0)
          return Json(new { Erro = true, status = "error", message = "Nenhum item selecionado." });

        if (CodJustificativa == 0)
          return Json(new { Erro = true, status = "error", message = "Nenhuma justificativa selecionada." });

        new LoteGlosaServices(ContextoUsuario.UserLogged).JustificaProcsGlosa(IdsItens, CodJustificativa, Comentario, IdLoteGlosa);

        retornoAjax.Erro = false;
        retornoAjax.Mensagem = "Itens justificados/comentados com sucesso.";
        retornoAjax.Titulo = "Sucesso";
        return Json(retornoAjax);
      }
      catch (CustomException ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Titulo = "Erro";
        return Json(retornoAjax);
      }
      catch (Exception ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.Mensagem = "Favor verificar os campos preenchidos.";
        retornoAjax.Titulo = "Erro";
        return Json(retornoAjax);
      }
    }
    [Security("891C19EB-729B-40FB-885E-56AB6BC1865A")]
    [HttpPost]
    public JsonResult EnviarConvenio(int codigoGlosa)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        new LoteGlosaServices().EnviarConvenio(codigoGlosa);

        retornoAjax.Erro = false;
        retornoAjax.Mensagem = "Lote enviado pro convênio.";
        retornoAjax.Titulo = "Sucesso";
        return Json(retornoAjax);
      }
      catch (CustomException ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Titulo = "Erro";
        return Json(retornoAjax);
      }
      catch (Exception ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.Mensagem = "Favor verificar os campos preenchidos.";
        retornoAjax.Titulo = "Erro";
        return Json(retornoAjax);
      }
    }

    [Security("13E8FC42-26BA-4A91-8D13-ED316967BC5B")]
    [HttpPost]
    public JsonResult AceitarGlosa(List<int> IdsItens, int IdLoteGlosa, string Comentario, int CodJustificativa)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        if (IdsItens == null || IdsItens.Count == 0)
          return Json(new { Erro = true, status = "error", message = "Nenhum item selecionado." });

        if (CodJustificativa == 0)
          return Json(new { Erro = true, status = "error", message = "Nenhuma justificativa selecionada." });

        new LoteGlosaServices(ContextoUsuario.UserLogged).AceitarGlosa(IdsItens, IdLoteGlosa, Comentario, CodJustificativa);

        retornoAjax.Erro = false;
        retornoAjax.Mensagem = "Glosa Aceita.";
        retornoAjax.Titulo = "Sucesso";
        return Json(retornoAjax);
      }
      catch (CustomException ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Titulo = "Erro";
        return Json(retornoAjax);
      }
      catch (Exception ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.Mensagem = "Favor verificar os campos preenchidos.";
        retornoAjax.Titulo = "Erro";
        return Json(retornoAjax);
      }
    }

    [HttpPost]
    public JsonResult GetDataVencimento(int IdConvenio, DateTime DataCreditoFatura)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        DateTime DataVencimentoLote = new ConvenioServices(ContextoUsuario.UserLogged).GetDiasVencimentoGlosa(IdConvenio, DataCreditoFatura);
        retornoAjax.Erro = false;
        retornoAjax.Mensagem = "Glosa Aceita.";
        retornoAjax.Titulo = "Sucesso";
        return Json(new { RetornoAjax = retornoAjax, DataVencimento = DataVencimentoLote.ToString("dd/MM/yyyy") });
      }
      catch (CustomException ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Titulo = "Erro";
        return Json(new { RetornoAjax = retornoAjax });
      }
      catch (Exception ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.Mensagem = "Favor verificar os campos preenchidos.";
        retornoAjax.Titulo = "Erro";
        return Json(new { RetornoAjax = retornoAjax });
      }
    }
    [Security("E759FF58-BD79-4661-A18A-C5FEAD47C121")]
    public PartialViewResult GetGridGuiaAtenimento(int CodigoGuiaAtendimento)
    {
      GuiaAtendimentoService guiaAtendimentoService = new GuiaAtendimentoService();
      GuiaAtendimentoGrid guiaAtendimentoGrid = guiaAtendimentoService.GetGuiaAtendimentoGrid(CodigoGuiaAtendimento);
      return PartialView("_GridGuiaAtendimento", guiaAtendimentoGrid);
    }

    public FileResult GerarXML(int CodigoLoteGlosa)
    {
      LoteGlosaServices loteGlosaServices = new LoteGlosaServices();
      string codigoLote = loteGlosaServices.GetCodigoLote(CodigoLoteGlosa);
      byte[] file = loteGlosaServices.GerarXML(CodigoLoteGlosa);
      return File(file, "application/xml", "Lote_" + codigoLote + ".xml");
    }

    public FileResult GerarRelatorio(int CodigoLoteGlosa)
    {
      LoteGlosaServices loteGlosaServices = new LoteGlosaServices();
      string codigoLote = loteGlosaServices.GetCodigoLote(CodigoLoteGlosa);
      byte[] file = loteGlosaServices.GerarRelatorio(CodigoLoteGlosa);
      return File(file, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "Lote_" + codigoLote + ".xlsx");
    }
  }
}
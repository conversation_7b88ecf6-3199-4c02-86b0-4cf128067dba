﻿@using RepasseConvenio.Models
@model List<DemonstrativoConvenioGrid>

@{
  ViewBag.Title = "Repasse";
}

<div class="col-md-12 table-responsive p-0 ">
  <table class="table table-sm table-striped table-hover text-nowrap">
    <thead>
      <tr>
        <th>
          @Html.CheckBox("CheckAllPlanRecebimento")
        </th>
        <th>
          Nome
        </th>
        <th>
          Vlr Apresentado
        </th>
        <th>
          Vlr Faturado
        </th>
        <th>
          Vlr Glosado
        </th>
        <th>
          Vlr Pago
        </th>
        <th>

        </th>
      </tr>
    </thead>
    <tbody>
      @foreach (DemonstrativoConvenioGrid item in Model)
      {
        <tr>
          <td>
            <input class="isSelected" data-codigoplanilha="@item.Codigo" data-val="true" data-val-required="O campo isSelected é obrigatório." name="item.isSelected" type="checkbox" value="true">
          </td>
          <td>
            @item.NomePlanilha
          </td>
          <td>
            @item.ValorApresentado
          </td>
          <td>
            @item.TotalFaturado
          </td>
          <td>
            @item.TotalGlosado
          </td>
          <td>
            @item.TotalPago
          </td>
          <td>
            <a class="btn btn-outline-warning btn-circle btn-sm VisualizarGuiaDemonstrativo" data-codigodemonstrativo="@item.Codigo" title="Visualizar">
              Visualizar
            </a>
            @*<a data-idplan="@item.Codigo" class="btn btn-outline-danger btn-circle btn-sm btnRemoverPlanRecebimento" title="Excluir">
                Excluir
              </a>*@
          </td>
        </tr>
      }
    </tbody>
  </table>
</div>

﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Models;
using RepasseConvenio.WSPortalCooperado;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.Entity;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Text;
using System.Transactions;
using System.Web;
using System.Data.OleDb;
using RepasseConvenio.Infrastructure;
using Microsoft.Ajax.Utilities;

namespace RepasseConvenio.Services
{
  public class ProcGuiaDemonstrativoService : ServiceBase
  {
    public ProcGuiaDemonstrativoService()
   : base()
    { }
    public ProcGuiaDemonstrativoService(RepasseEntities Contexto)
       : base(Contexto)
    { }

    public ProcGuiaDemonstrativoService(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public ProcGuiaDemonstrativoService(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public void Create(GuiaDemonstrativoIntegra guiaDemonstrativoIntegra, int IdGuiaDemonstrativo, int IdGuiaAtendimento)
    {
      ProcGuiaAtendimentoService procGuiaAtendimentoService = new ProcGuiaAtendimentoService(Contexto);
      ProcGuiaDemonstrativoIntegra procGuiaDemonstrativoIntegra = procGuiaAtendimentoService.GetValorFaturado(IdGuiaAtendimento, guiaDemonstrativoIntegra.CodigoProcedimento);
      R_ProcGuiaDemonstrativo procGuiaDemonstrativo = guiaDemonstrativoIntegra.ModeltoEntityCreate(IdGuiaDemonstrativo, procGuiaDemonstrativoIntegra);

      StausProcGuiaDemonstrativoServices stausProcGuiaDemonstrativoServices = new StausProcGuiaDemonstrativoServices();
      List<R_StausProcGuiaDemonstrativo> ListaStausProcGuiaDemonstrativo = stausProcGuiaDemonstrativoServices.Getall();

      if (procGuiaDemonstrativo.PGD_TotalPago == 0)
        procGuiaDemonstrativo.PGD_IdStatusProcGuiaDemonstrativo = ListaStausProcGuiaDemonstrativo.Where(a => a.SPGD_Enum == (int)StatusProcGuiaDemonstrativoEnum.NaoPago).Select(a => a.SPGD_Id).FirstOrDefault();
      else if (procGuiaDemonstrativo.PGD_TotalFaturado > procGuiaDemonstrativo.PGD_TotalPago)
        procGuiaDemonstrativo.PGD_IdStatusProcGuiaDemonstrativo = ListaStausProcGuiaDemonstrativo.Where(a => a.SPGD_Enum == (int)StatusProcGuiaDemonstrativoEnum.Glosado).Select(a => a.SPGD_Id).FirstOrDefault();
      else if (procGuiaDemonstrativo.PGD_TotalFaturado == procGuiaDemonstrativo.PGD_TotalPago || procGuiaDemonstrativo.PGD_TotalPago > procGuiaDemonstrativo.PGD_TotalFaturado)
        procGuiaDemonstrativo.PGD_IdStatusProcGuiaDemonstrativo = ListaStausProcGuiaDemonstrativo.Where(a => a.SPGD_Enum == (int)StatusProcGuiaDemonstrativoEnum.Pago).Select(a => a.SPGD_Id).FirstOrDefault();

      Create(procGuiaDemonstrativo);
    }

    public void Delete(int IdGuiaDemonstrativo)
    {
      string query = @"DELETE FROM R_ProcGuiaDemonstrativo WHERE PGD_IdGuiaDemonstrativo = @IdGuiaDemonstrativo";
      Contexto.Database.ExecuteSqlCommand(query, new SqlParameter("@IdGuiaDemonstrativo", IdGuiaDemonstrativo));
    }
    public List<ProcGuiaDemonstrativoModal> GetListaProcGuiaDemonstrativoModal(int IdDemonstrativo)
    {
      string query = @"SELECT 
                        GD.GD_Id [CodigoGuiaDemonstrativo],
                        GD.GD_IdGuia [CodigoGuia],
                        DC.DC_IdRepasse [IdRepasse],
                        GD.GD_NumeroGuia [NumeroGuia],
                        GD.GD_NumeroCarteirinha [NumeroCarteirinha],
                        GD.GD_NumeroAtendimento [NumeroAtendimento],
                        PGD.PGD_TotalApresentado [ValorApresentado],
                        PGD.PGD_TotalGlosado [ValorGlosado],
                        PGD.PGD_TotalPago [ValorPago],
                        PGD.PGD_TotalFaturado [ValorFaturado],
                        PGD.PGD_CodigoProcedimento + ' - ' + PGD.PGD_DescricaoProcedimento [Procedimento],
                        GD.GD_DataAtendimento [DataAtendimento]
                       FROM R_ProcGuiaDemonstrativo PGD
                       INNER JOIN R_GuiaDemonstrativo GD ON GD.GD_Id = PGD.PGD_IdGuiaDemonstrativo AND GD.GD_IdDemonstrativoConvenio = @IdDemonstrativo 
                       INNER JOIN R_DemonstrativoConvenio DC ON DC.DC_Id = GD.GD_IdDemonstrativoConvenio
                       ORDER BY GD.GD_DataAtendimento DESC";

      return Contexto.Database.SqlQuery<ProcGuiaDemonstrativoModal>(query, new SqlParameter("@IdDemonstrativo", IdDemonstrativo)).ToList();
    }

    public List<R_ProcGuiaDemonstrativo> GetListaProcGuiaDemonstrativo(int IdGuiaDemonstrativo)
    {
      string query = @"SELECT 
                         *
                       FROM R_ProcGuiaDemonstrativo PGD
                       INNER JOIN R_GuiaDemonstrativo GD ON GD.GD_Id = PGD.PGD_IdGuiaDemonstrativo AND GD.GD_Id = @IdGuiaDemonstrativo";

      return Contexto.Database.SqlQuery<R_ProcGuiaDemonstrativo>(query, new SqlParameter("@IdGuiaDemonstrativo", IdGuiaDemonstrativo)).ToList();
    }


  }
}
﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System.Web;
using System.Web.Optimization;

namespace RepasseConvenio
{
  public class BundleConfig
  {
    // Para obter mais informações sobre o agrupamento, visite https://go.microsoft.com/fwlink/?LinkId=301862
    public static void RegisterBundles(BundleCollection bundles)
    {
      bundles.Add(new ScriptBundle("~/bundles/jquery").Include(
                  "~/Scripts/jquery-{version}.js"));

      bundles.Add(new ScriptBundle("~/bundles/jqueryval").Include(
                 "~/Scripts/jquery.validate.js",
                  "~/Scripts/jquery.validate.unobtrusive.js"));

      // Use a versão em desenvolvimento do Modernizr para desenvolver e aprender. Em seguida, quando estiver
      // pronto para a produção, utilize a ferramenta de build em https://modernizr.com para escolher somente os testes que precisa.
      bundles.Add(new ScriptBundle("~/bundles/modernizr").Include(
                  "~/Scripts/modernizr-*"));

      bundles.Add(new ScriptBundle("~/bundles/bootstrap").Include(
                "~/Scripts/bootstrap.js",
                "~/Scripts/respond.js",
                "~/Scripts/popper.js"));

      bundles.Add(new ScriptBundle("~/bundles/signal").Include(
                "~/Scripts/jquery.signalR-2.4.1.js"));

      bundles.Add(new ScriptBundle("~/bundles/inputmask").Include(
                  "~/Scripts/inputmask/inputmask.js",
                  "~/Scripts/inputmask/jquery.inputmask.js",
                  "~/Scripts/inputmask/inputmask.extensions.js",
                  "~/Scripts/inputmask/inputmask.date.extensions.js",
                  "~/Scripts/inputmask/inputmask.numeric.extensions.js"));

      bundles.Add(new Bundle("~/bundles/plugins").Include(
               "~/Scripts/jquery.sparkline.min.js",
               "~/Scripts/icheck.min.js",
               "~/Scripts/sweetalert.min.js",
               "~/Scripts/select2.min.js",
               "~/Scripts/moment.js",
               "~/Scripts/app.min.js",
               "~/Scripts/pt-br.js",
               //"~/Scripts/bootstrap-datetimepicker.min.js",
               "~/plugins/CKeditor/ckeditor.js",
               "~/Scripts/magicsuggest.js",
               "~/Scripts/iziToas.min.js",
               "~/Scripts/adminlte.js",
               "~/Scripts/Pace.js",
               "~/Scripts/AirDatePicker/AirDatePicker.js",
               "~/Scripts/AirDatePicker/AirDatePicker-Pt-Br.js",
               "~/Scripts/bootstrap-toggle.min.js",
               "~/Scripts/Site-{version}.js",
               "~/Scripts/Controls-{version}.js",
              "~/Scripts/Select2Controls-{version}.js"

        ));

      bundles.Add(new StyleBundle("~/Content/css").Include(
                "~/Content/PagedList.css",
                "~/Content/bootstrap.css",
               "~/Content/font-awesome.min.css",
               "~/Content/ionicons.min.css",
               "~/Content/icomoon.css",
               "~/Content/iziToast.min.css",
               "~/Content/iCheck/blue.css",
               "~/Content/adminlte.css",
               //"~/Content/bootstrap-datetimepicker.min.css",
               "~/Content/bootstrap-toggle.min.css",
               "~/Content/select2.min.css",
               "~/Content/sweetalert.css",
               "~/Content/fontawesome/all.css",
               "~/Content/AirDatePicker/AirDatePicker.css",
                "~/Content/site-{version}.css"));
    }
  }
}

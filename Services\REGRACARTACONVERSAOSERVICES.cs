﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using PagedList;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.Script.Serialization;
using System.Linq.Dynamic.Core;
using Org.BouncyCastle.Bcpg.OpenPgp;

namespace RepasseConvenio.Services
{
  public class RegraCartaConversaoServices : ServiceBase
  {
    public RegraCartaConversaoServices(RepasseEntities Contexto)
        : base(Contexto)
    { }
    public RegraCartaConversaoServices()
       : base()
    { }

    public RegraCartaConversaoServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public RegraCartaConversaoServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public RegraCartaConversaoModel Create(RegraCartaConversaoModel regraCartaConversaoModel)
    {
      R_RegraCartaConversao regraCartaConversao = regraCartaConversaoModel.RegraCartaConversaoCreateModel();
      Create(regraCartaConversao);
      regraCartaConversaoModel.Codigo = regraCartaConversao.RCC_Id;
      return regraCartaConversaoModel;
    }

    public void CreateCondicao(RegraCartaConversaoCondicaoModel regraCartaConversaoCondicaoModel
                                                                , int IdRegraCartaConversao
                                                                , bool insertUltimo
                                                                , bool insertAfter
                                                                , bool insertBefore
                                                                , int sequenciaItem)
    {
      JavaScriptSerializer javaScriptSerializer = new JavaScriptSerializer();
      List<RegraCartaConversaoCondicaoModel> ListaRegraCartaConversaoCondicaoModel = new List<RegraCartaConversaoCondicaoModel>();
      R_RegraCartaConversao regraCartaConversao = GetById(IdRegraCartaConversao);
      if (String.IsNullOrEmpty(regraCartaConversao.RCC_Html) || regraCartaConversao.RCC_Html == "[]")
      {
        regraCartaConversaoCondicaoModel.Sequencia = 1;
        ListaRegraCartaConversaoCondicaoModel.Add(regraCartaConversaoCondicaoModel);
        ListaRegraCartaConversaoCondicaoModel = ReeordenarLista(ListaRegraCartaConversaoCondicaoModel);
        regraCartaConversao.EntityToCondicaoEdit(ListaRegraCartaConversaoCondicaoModel);
        Edit(regraCartaConversao);
      }
      else
      {
        ListaRegraCartaConversaoCondicaoModel = javaScriptSerializer.Deserialize<List<RegraCartaConversaoCondicaoModel>>(regraCartaConversao.RCC_Html);

        if (insertBefore)
        {
          foreach (RegraCartaConversaoCondicaoModel regraCartaConversaoCondicaoModel1 in ListaRegraCartaConversaoCondicaoModel)
          {
            if (regraCartaConversaoCondicaoModel1.Sequencia >= sequenciaItem)
            {
              regraCartaConversaoCondicaoModel1.Sequencia = ++regraCartaConversaoCondicaoModel1.Sequencia;
            }
          }
        }

        if (insertAfter)
        {
          foreach (RegraCartaConversaoCondicaoModel regraCartaConversaoCondicaoModel1 in ListaRegraCartaConversaoCondicaoModel)
          {
            if (regraCartaConversaoCondicaoModel1.Sequencia > sequenciaItem)
            {
              regraCartaConversaoCondicaoModel1.Sequencia = ++regraCartaConversaoCondicaoModel1.Sequencia;
            }
          }
          sequenciaItem++;
        }

        if (insertUltimo)
        {
          sequenciaItem = ListaRegraCartaConversaoCondicaoModel.Max(a => a.Sequencia) + 1;
        }

        regraCartaConversaoCondicaoModel.Sequencia = sequenciaItem;
        ListaRegraCartaConversaoCondicaoModel.Add(regraCartaConversaoCondicaoModel);
        ListaRegraCartaConversaoCondicaoModel = ReeordenarLista(ListaRegraCartaConversaoCondicaoModel);
        regraCartaConversao.EntityToCondicaoEdit(ListaRegraCartaConversaoCondicaoModel);
        Edit(regraCartaConversao);
      }
    }

    public void EditCondicao(RegraCartaConversaoCondicaoModel regraCartaConversaoCondicaoModel, int idRegraCartaConversao, int IdSequencia)
    {
      JavaScriptSerializer javaScriptSerializer = new JavaScriptSerializer();

      R_RegraCartaConversao regraCartaConversao = GetById(idRegraCartaConversao);
      List<RegraCartaConversaoCondicaoModel> ListaRegraCartaConversaoCondicaoModel = javaScriptSerializer.Deserialize<List<RegraCartaConversaoCondicaoModel>>(regraCartaConversao.RCC_Html);
      RegraCartaConversaoCondicaoModel newRegra = ListaRegraCartaConversaoCondicaoModel.Where(a => a.Sequencia == IdSequencia).FirstOrDefault();
      ListaRegraCartaConversaoCondicaoModel.Remove(newRegra);
      newRegra.operadorLogicoEnum = regraCartaConversaoCondicaoModel.operadorLogicoEnum;
      newRegra.parentesesEnum = regraCartaConversaoCondicaoModel.parentesesEnum;
      newRegra.RegraCartaConversaoAcaoSelect = regraCartaConversaoCondicaoModel.RegraCartaConversaoAcaoSelect;
      newRegra.RegraCartaConversaoCampoSelect = regraCartaConversaoCondicaoModel.RegraCartaConversaoCampoSelect;
      newRegra.Sequencia = IdSequencia;
      newRegra.CodigoRegraCartaConversaoAcao = regraCartaConversaoCondicaoModel.CodigoRegraCartaConversaoAcao;
      newRegra.CodigoRegraCartaConversaoCampo = regraCartaConversaoCondicaoModel.CodigoRegraCartaConversaoCampo;
      newRegra.Conteudo = regraCartaConversaoCondicaoModel.Conteudo;
      ListaRegraCartaConversaoCondicaoModel.Add(newRegra);
      ListaRegraCartaConversaoCondicaoModel = ReeordenarLista(ListaRegraCartaConversaoCondicaoModel);
      regraCartaConversao.RCC_Html = javaScriptSerializer.Serialize(ListaRegraCartaConversaoCondicaoModel);
      Edit(regraCartaConversao);
    }

    public void Delete(int IdRegra, int IdSequenciaItem)
    {
      JavaScriptSerializer javaScriptSerializer = new JavaScriptSerializer();

      R_RegraCartaConversao regraCartaConversao = GetById(IdRegra);
      List<RegraCartaConversaoCondicaoModel> ListaRegraCartaConversaoCondicaoModel = javaScriptSerializer.Deserialize<List<RegraCartaConversaoCondicaoModel>>(regraCartaConversao.RCC_Html);
      RegraCartaConversaoCondicaoModel newRegra = ListaRegraCartaConversaoCondicaoModel.Where(a => a.Sequencia == IdSequenciaItem).FirstOrDefault();
      ListaRegraCartaConversaoCondicaoModel.Remove(newRegra);
      ListaRegraCartaConversaoCondicaoModel = ReeordenarLista(ListaRegraCartaConversaoCondicaoModel);
      regraCartaConversao.RCC_Html = javaScriptSerializer.Serialize(ListaRegraCartaConversaoCondicaoModel);
      Edit(regraCartaConversao);
    }

    public R_RegraCartaConversao GetById(int IdRegraCartaConversao)
    {
      string query = @"SELECT 
                        *
                       FROM R_RegraCartaConversao
                       WHERE RCC_Id = @IdRegraCartaConversao";

      return Contexto.Database.SqlQuery<R_RegraCartaConversao>(query, new SqlParameter("@IdRegraCartaConversao", IdRegraCartaConversao)).FirstOrDefault();
    }
    public R_RegraCartaConversao GetByIdEmpresaMedico(int IdEmpresaMedico)
    {
      string query = @"SELECT 
                        *
                       FROM R_RegraCartaConversao
                       WHERE RCC_IdEmpresaMedico = @IdEmpresaMedico";

      return Contexto.Database.SqlQuery<R_RegraCartaConversao>(query, new SqlParameter("@IdEmpresaMedico", IdEmpresaMedico)).FirstOrDefault();
    }

    public R_RegraCartaConversao GetByIdMedico(int IdMedico)
    {
      string query = @"SELECT 
                        *
                       FROM R_RegraCartaConversao
                       WHERE RCC_IdMedico = @IdMedico";

      return Contexto.Database.SqlQuery<R_RegraCartaConversao>(query, new SqlParameter("@IdMedico", IdMedico)).FirstOrDefault();
    }

    public IPagedList<RegraCartaConversaoIndex> GetListRegraCartaConversaoIndex(int IdMedico, int Pagina)
    {
      string query = @"SELECT 
                        RCC_Id [Codigo],
                        RCC_Descricao [Descricao],
                        RCC_Prioridade [Prioridade],
                        EM.EM_RazaoSocial [Empresa],
                        RCC.RCC_Valida [Valida]
                       FROM R_RegraCartaConversao RCC
                       LEFT JOIN R_EmpresaMedico EM ON EM.EM_Id = RCC.RCC_IdEmpresaMedico
                       WHERE RCC_IdMedico = @IdMedico
                       ORDER BY isnull(RCC_Prioridade, 9999999)";

      return Contexto.Database.SqlQuery<RegraCartaConversaoIndex>(query, new SqlParameter("@IdMedico", IdMedico)).ToList().ToPagedList(Pagina, PageSize);
    }

    public RegraCartaConversaoModel GetRegraCartaConversaoModelById(int IdRegraCartaConversao)
    {
      string query = @"SELECT 
                        *
                       FROM R_RegraCartaConversao
                       WHERE RCC_Id = @IdRegraCartaConversao";

      R_RegraCartaConversao regraCartaConversao = Contexto.Database.SqlQuery<R_RegraCartaConversao>(query, new SqlParameter("@IdRegraCartaConversao", IdRegraCartaConversao)).FirstOrDefault();

      return regraCartaConversao.EntityToModel();
    }

    public List<RegraCartaConversaoCondicaoModel> GetListRegraCartaConversaoCondicaoModel(int idRegra)
    {
      string query = @"SELECT
                        RCC_Html
                       FROM R_RegraCartaConversao
                       WHERE RCC_Id = @idRegra";

      string Html = Contexto.Database.SqlQuery<string>(query, new SqlParameter("@idRegra", idRegra)).FirstOrDefault();

      JavaScriptSerializer javaScriptSerializer = new JavaScriptSerializer();
      return javaScriptSerializer.Deserialize<List<RegraCartaConversaoCondicaoModel>>(Html).OrderBy(a => a.Sequencia).ToList();
    }

    public List<RegraCartaConversaoCondicaoModel> ReeordenarLista(List<RegraCartaConversaoCondicaoModel> Lista)
    {
      Lista = Lista.OrderBy(a => a.Sequencia).ToList();
      int i = 1;
      foreach (RegraCartaConversaoCondicaoModel regraCartaConversaoCondicaoModel in Lista)
      {
        regraCartaConversaoCondicaoModel.Sequencia = i;
        i++;
      }
      return Lista;
    }

    public string GerarSqlCondicoes(List<RegraCartaConversaoCondicaoModel> Lista)
    {
      string Tipo = string.Empty;
      string operador = string.Empty;
      string campoCondicao = string.Empty;
      string tipoCampo = string.Empty;
      List<string> condicao = new List<string>();
      Lista = Lista.OrderBy(a => a.Sequencia).ToList();

      foreach (RegraCartaConversaoCondicaoModel Model in Lista)
      {
        if (Model.CodigoRegraCartaConversaoAcao != null
          && Model.CodigoRegraCartaConversaoAcao != 0)
          Tipo = "Condicao";

        if (Model.operadorLogicoEnum != 0)
          Tipo = "Operador Logico";

        if (Model.parentesesEnum != 0)
          Tipo = "Parenteses";


        RegraCartaConversaoAcaoServices regraCartaConversaoAcaoServices = new RegraCartaConversaoAcaoServices();
        RegraCartaConversaoCampoServices regraCartaConversaoCampoServices = new RegraCartaConversaoCampoServices();
        List<R_RegraCartaConversaoAcao> ListaRegraAcao = regraCartaConversaoAcaoServices.GetAll();
        List<R_RegraCartaConversaoCampo> ListaRegraCampo = regraCartaConversaoCampoServices.GetAll();
        R_RegraCartaConversaoAcao regraCartaConversaoAcao = new R_RegraCartaConversaoAcao();

        switch (Tipo)
        {
          case "Condicao":
            operador = ListaRegraAcao.Where(a => a.RCCA_Id == Model.CodigoRegraCartaConversaoAcao).Select(a => a.RCCA_OperadorSql).FirstOrDefault();
            campoCondicao = ListaRegraCampo.Where(a => a.RCCC_Id == Model.CodigoRegraCartaConversaoCampo).Select(a => a.RCCC_CampoSql).FirstOrDefault();
            tipoCampo = ListaRegraCampo.Where(a => a.RCCC_Id == Model.CodigoRegraCartaConversaoCampo).Select(a => a.RCCC_TipoCampo).FirstOrDefault();
            switch (operador.Trim())
            {
              case "like":
                condicao.Add(string.Format("{0}.Contains(\"{1}\")", campoCondicao, Model.Conteudo));
                break;
              case "=":
                if (tipoCampo == "string")
                  condicao.Add(string.Format("{0} == \"{1}\"", campoCondicao, Model.Conteudo));
                else if (tipoCampo == "int" || tipoCampo == "decimalValor")
                  condicao.Add(string.Format("{0} == {1}", campoCondicao, Model.Conteudo));
                break;
              case ">":
                condicao.Add(string.Format("{0} > {1}", campoCondicao, Model.Conteudo));
                break;
              case "<":
                condicao.Add(string.Format("{0} < {1}", campoCondicao, Model.Conteudo));
                break;
              case ">=":
                condicao.Add(string.Format("{0} >= {1}", campoCondicao, Model.Conteudo));
                break;
              case "<=":
                condicao.Add(string.Format("{0} <= {1}", campoCondicao, Model.Conteudo));
                break;
              case "start":
                condicao.Add(string.Format("{0}.StartsWith(\"{1}\")", campoCondicao, Model.Conteudo));
                break;
              case "ends":
                condicao.Add(string.Format("{0}.EndsWith(\"{1}\")", campoCondicao, Model.Conteudo));
                break;
              default:
                break;
            }
            break;
          case "Operador Logico":
            switch (Model.operadorLogicoEnum)
            {
              case OperadorLogicoEnum.E:
                condicao.Add("AND");
                break;
              case OperadorLogicoEnum.OU:
                condicao.Add("OR");
                break;
              default:
                break;
            }
            break;
          case "Parenteses":
            switch (Model.parentesesEnum)
            {
              case ParentesesEnum.ParentesesEsquerdo:
                condicao.Add("(");
                break;
              case ParentesesEnum.ParentesesDireito:
                condicao.Add(")");
                break;
              default:
                break;
            }
            break;
          default:
            break;
        }


      }
      return string.Join(" ", condicao.ToArray());
    }

    public void SalvarValidarRegras(RegraCartaConversaoModel regraCartaConversaoModel)
    {
      if (!regraCartaConversaoModel.Prioridade.HasValue)
        throw new CustomException("Para ativar a regra é necessário que ela tenha uma prioridade.");

      R_RegraCartaConversao regraCartaConversao = GetById(regraCartaConversaoModel.Codigo);
      regraCartaConversao.ModelToEntitySalvar(regraCartaConversaoModel);
      regraCartaConversao.RCC_Valida = true;

      List<RegraCartaConversaoCondicaoModel> ListaRegraCartaConversaoCondicaoModel = new JavaScriptSerializer().Deserialize<List<RegraCartaConversaoCondicaoModel>>(regraCartaConversao.RCC_Html);

      try
      {
        regraCartaConversao.RCC_QuerySql = GerarSqlCondicoes(ListaRegraCartaConversaoCondicaoModel);
        new List<RegraCarta>().AsQueryable().Where(regraCartaConversao.RCC_QuerySql).FirstOrDefault();
        Edit(regraCartaConversao);
      }
      catch (Exception ex)
      {
        regraCartaConversao.RCC_Valida = false;
        Edit(regraCartaConversao);
        throw new CustomException("Gentileza verificar as condições");
      }
    }

    public void Salvar(RegraCartaConversaoModel regraCartaConversaoModel)
    {
      R_RegraCartaConversao regraCartaConversao = GetById(regraCartaConversaoModel.Codigo);
      regraCartaConversao.ModelToEntitySalvar(regraCartaConversaoModel);

      if (!regraCartaConversaoModel.Prioridade.HasValue)
        regraCartaConversao.RCC_Valida = false;

      Edit(regraCartaConversao);
    }

    public RetornoValidacaoRegra ProcesarRegras(int idMedico, RegraCarta regraCarta)
    {
      RetornoValidacaoRegra retornoValidacaoRegra = new RetornoValidacaoRegra();

      List<RegraCarta> ListaRegraCarta = new List<RegraCarta>();
      ListaRegraCarta.Add(regraCarta);

      MedicoService medicoService = new MedicoService();
      MedicoRegra medicoRegra = medicoService.GetMedicoRegra(idMedico);

      List<EmpresaMedicoRegra> ListaEmpresaMedicoRegra = GetRegrasCartaConversaoByMedico(idMedico);

      foreach (EmpresaMedicoRegra empresaMedicoRegra in ListaEmpresaMedicoRegra)
      {
        regraCarta = ListaRegraCarta.AsQueryable().Where(empresaMedicoRegra.Query.ToLower()).FirstOrDefault();
        if (regraCarta != null && !string.IsNullOrEmpty(empresaMedicoRegra.CNPJ))
          return retornoValidacaoRegra.RetornoValidacaoRegraCreate(empresaMedicoRegra.CNPJ, TipoPessoaEnum.PJ);
        else if(regraCarta != null)
          return retornoValidacaoRegra.RetornoValidacaoRegraCreate(medicoRegra.CPF, TipoPessoaEnum.PF);
      }

      return retornoValidacaoRegra.RetornoValidacaoRegraCreate(medicoRegra.CPF, TipoPessoaEnum.PF);
    }

    public List<EmpresaMedicoRegra> GetRegrasCartaConversaoByMedico(int IdMedico)
    {
      string query = @"SELECT
                        RCC.RCC_Prioridade [Prioridade],
                        RCC.RCC_QuerySql [Query],
                        ISNULL(EM.EM_Id, 0) [IdEmpresa],
                        EM.EM_CNPJ [CNPJ]
                       FROM R_RegraCartaConversao RCC
                       INNER JOIN R_Medico M ON M.M_Id = RCC.RCC_IdMedico AND RCC.RCC_IdMedico = @IdMedico AND RCC.RCC_Valida = 1
                       LEFT JOIN R_EmpresaMedico EM ON EM.EM_Id = RCC.RCC_IdEmpresaMedico
                       ORDER BY ISNULL(RCC_Prioridade, 9999999)";

      return Contexto.Database.SqlQuery<EmpresaMedicoRegra>(query, new SqlParameter("@IdMedico", IdMedico)).ToList();
    }
  }
}


﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using PagedList;
using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Controls;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace RepasseConvenio.Controllers
{
  [Authorize]
  [Security("6371E783-98CD-40CA-A411-BAD6A493D908")]
  public class RegistroImpostoController : LibController
  {
    private RegistroImpostoServices RegistroImpostoServices
    {
      get
      {
        if (_RegistroImpostoServices == null)
          _RegistroImpostoServices = new RegistroImpostoServices(ContextoUsuario.UserLogged);

        return _RegistroImpostoServices;
      }
    }
    private RegistroImpostoServices _RegistroImpostoServices;

    [Security("54585077-9207-4F8E-B382-4013E0887750")]
    public ActionResult Index(int page = 1, string search = "", string data = "")
    {
      try
      {
        RegistroImpostoIndex registro = new RegistroImpostoIndex();
        registro.ListRegistro = RegistroImpostoServices.Get(page, search, data);

        return View(registro);
      }
      catch (Exception)
      {
        throw;
      }
    }

    [HttpPost]
    [Security("54585077-9207-4F8E-B382-4013E0887750")]
    public ActionResult Index(RegistroImpostoIndex registro)
    {
      try
      {
        if ((!registro.DataAte.HasValue && !registro.DataDe.HasValue) && (registro.MedicoSelect == null || !registro.CodigoMedico.HasValue))
          throw new CustomException("É necessário preencher os campos de Data/Medico.");

        if (registro.DataDe.HasValue && registro.DataAte.HasValue)
        {
          if (registro.DataAte.Value > registro.DataDe.Value.AddDays(30))
            registro.DataAte = registro.DataDe.Value.AddDays(30);
        }

        List<ExtratoMedicoRegistroImpostoModel> ListextratoMedicoRegistros = RegistroImpostoServices.GetExtratoImposto(registro.DataDe, registro.DataAte, registro.CodigoMedico, registro.CodigoTipoImposto);

        if (ListextratoMedicoRegistros.Count > 0)
        {
          RegistroImpostoServices.GerarRegistroPagamento(ListextratoMedicoRegistros);
          MessageListToast.Add(new Message(MessageType.Success, "Registro de impostos processado com sucesso."));
        }
        else
        {
          MessageListToast.Add(new Message(MessageType.Success, "Nenhum registro de imposto nestes filtros está pendente de processamento."));
        }

        return RedirectToAction("Index");
      }
      catch (CustomException ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return RedirectToAction("Index");
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, "Tivemos uma falha na sua solicitação."));
        return RedirectToAction("Index");
      }
    }
    [Security("83D89656-27B4-4E24-9CE0-699C7913472A")]
    public JsonResult RealizarPagamento(DateTime DataPagamento, List<int> ListIdRegistro)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        if (ListIdRegistro.Count == 0)
          throw new CustomException("Nenhum registro de imposto foi selecionado.");

        RegistroImpostoServices.RealizarPagamento(DataPagamento, ListIdRegistro);

        retornoAjax.Titulo = "Sucesso";
        retornoAjax.Mensagem = "Pagamento de imposto realizado com sucesso.";
        retornoAjax.Erro = false;
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
      catch (CustomException ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
      catch (Exception ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
    }
    [Security("B3DCAF77-8FA7-45D1-B5FF-44522EF9BE85")]
    public JsonResult Delete(List<int> idsRegistro)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {

        if (idsRegistro == null || idsRegistro.Count == 0)
          throw new CustomException("Nenhum registro de imposto foi selecionado.");

        RegistroImpostoServices.DeleteRegistros(idsRegistro);

        retornoAjax.Titulo = "Sucesso";
        retornoAjax.Mensagem = "Registros de Impostos deletados com sucesso.";
        retornoAjax.TipoMensagem = "success";
        retornoAjax.Erro = false;
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
      catch (Exception ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
    }

    [Security("95E19D77-2D56-4177-B0A0-AC5DAB88F575")]
    [HttpGet]
    public PartialViewResult GetPartialGrid(int? Page, string search, string data)
    {
      try
      {
        Page = Page ?? 1;
        IPagedList<RegistroImpostoModel> Lista = RegistroImpostoServices.Get(Page.Value, search, data);
        return PartialView("_PartialGrid", Lista);
      }
      catch (Exception)
      {
        throw;
      }
    }
    [Security("391C6532-FF8C-4617-B401-C6EEB9034768")]
    [HttpGet]
    public PartialViewResult GetExtratos(int id)
    {
      try
      {
        List<ExtratoMedicoRegistroImposto> Lista = RegistroImpostoServices.GetExtratos(id);

        if (Lista == null)
          Lista = new List<ExtratoMedicoRegistroImposto>();

        return PartialView("_GridDetalhesExtratos", Lista);
      }
      catch (Exception)
      {
        throw;
      }
    }
  }
}
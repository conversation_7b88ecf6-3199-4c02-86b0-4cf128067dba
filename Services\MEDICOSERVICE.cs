﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using Microsoft.AspNet.SignalR;
using Microsoft.SqlServer.Server;
using PagedList;
using RepasseConvenio.Infrastructure.Hubs;
using RepasseConvenio.Models;
using RepasseConvenio.WSPortalCooperado;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Data.SqlClient;
using System.EnterpriseServices.Internal;
using System.Linq;
using System.Web;
using System.Web.UI;

namespace RepasseConvenio.Services
{
  public class MedicoService : ServiceBase
  {
    public MedicoService()
   : base()
    { }
    public MedicoService(RepasseEntities Contexto)
       : base(Contexto)
    { }

    public MedicoService(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public MedicoService(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public IPagedList<MedicoModel> Get(int pagina)
    {
      string query = @"SELECT
                          M_Nome [Nome]
                        , M_CPF [CPF]
                        , M_CRM [CRM]
                        , M_Email [Email]
                        , M_Id [Codigo]
                        ,M_ValorProLabore [ValorProLabore]
                        ,M_Diretor [Diretor]
                        ,M_Ativo [Ativo]
                     FROM R_Medico";

      return Contexto.Database.SqlQuery<MedicoModel>(query).ToList().OrderBy(a => a.Nome).ToPagedList(pagina, PageSize);
    }

    public IPagedList<MedicoModel> MedicoSearch(MedicoIndex medicoIndex, int pagina)
    {
      string query = @"SELECT
                          M_Nome [Nome]
                        , M_CPF [CPF]
                        , M_CRM [CRM]
                        , M_Email [Email]
                        , M_Id [Codigo]
                        ,M_ValorProLabore [ValorProLabore]
                        ,M_Diretor [Diretor]
                        ,M_Ativo [Ativo]
                     FROM R_Medico
                     [Condicao]";

      List<SqlParameter> ListaSqlParameter = new List<SqlParameter>();
      //SqlParameterCollection a = new SqlParameterCollection();

      List<String> ListaCondicao = new List<string>();
      string condicao = string.Empty;

      if (!string.IsNullOrEmpty(medicoIndex.Nome))
      {
        ListaSqlParameter.Add(new SqlParameter("@Nome", "%" + medicoIndex.Nome + "%"));
        ListaCondicao.Add("M_Nome like @Nome");
      }

      if (!string.IsNullOrEmpty(medicoIndex.CPF))
      {
        ListaSqlParameter.Add(new SqlParameter("@CPF", "%" + medicoIndex.CPF + "%"));
        ListaCondicao.Add("M_CPF like @CPF");
      }

      if (!string.IsNullOrEmpty(medicoIndex.CRM))
      {
        ListaSqlParameter.Add(new SqlParameter("@CRM", "%" + medicoIndex.CRM + "%"));
        ListaCondicao.Add("M_CRM like @CRM");
      }

      if (!string.IsNullOrEmpty(medicoIndex.Email))
      {
        ListaSqlParameter.Add(new SqlParameter("@Email", "%" + medicoIndex.Email + "%"));
        ListaCondicao.Add("M_Email like @Email");
      }

      if (ListaCondicao.Count != 0)
      {
        condicao = "Where " + string.Join(" OR ", ListaCondicao.ToArray());
        query = query.Replace("[Condicao]", condicao);
      }
      else
        query = query.Replace("[Condicao]", "");

      if (ListaCondicao.Count != 0)
        return Contexto.Database.SqlQuery<MedicoModel>(query, ListaSqlParameter.ToArray()).ToList().OrderBy(a => a.Nome).ToPagedList(pagina, PageSize);
      else
        return Contexto.Database.SqlQuery<MedicoModel>(query).OrderBy(a => a.Nome).ToList().ToPagedList(pagina, PageSize);
    }

    public R_Medico GetById(int id)
    {
      return Contexto.R_Medico.Where(a => a.M_Id == id).FirstOrDefault();
    }

    public bool GetStatusMedico(int IdMedico)
    {
      string query = @"SELECT
                        M_Ativo
                       FROM R_Medico
                       WHERE M_Id = @IdMedico";

      return Contexto.Database.SqlQuery<bool>(query, new SqlParameter("@IdMedico", IdMedico)).FirstOrDefault();
    }
    public List<R_Medico> GetDiretores()
    {
      return Contexto.R_Medico.Where(a => a.M_Diretor == true).ToList();
    }
    public List<R_Medico> GetByTerm(string term)
    {
      if (string.IsNullOrEmpty(term))
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_Medico");

        return Contexto.Database.SqlQuery<R_Medico>(query).ToList();

      }
      else
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_Medico
                                       WHERE M_Nome LIKE @termo OR M_CPF LIKE @termo ");

        return Contexto.Database.SqlQuery<R_Medico>(query, new SqlParameter("@termo", string.Format("%{0}%", term))).ToList();

      }
    }
    public R_Medico GetByCPF(string cpf)
    {
      return Contexto.R_Medico.Where(a => a.M_CPF == cpf).FirstOrDefault();
    }
    public int GetIdByIdMedicoExterno(string IdMedicoExterno)
    {
      string query = @"SELECT
                        M_Id
                       FROM R_Medico
                       WHERE M_IdMedicoExterno = @IdMedicoExterno";

      return Contexto.Database.SqlQuery<int>(query, new SqlParameter("@IdMedicoExterno", IdMedicoExterno)).FirstOrDefault();
    }
    public bool IfExistByCPF(string cpf)
    {
      string query = @"select count(1) from R_Medico where M_CPF = @cpf";
      int quantidade = Contexto.Database.SqlQuery<int>(query, new SqlParameter("@cpf", cpf)).FirstOrDefault();
      if (quantidade == 0)
        return false;
      else
        return true;
    }
    public void CreateMedicoIntegra(MedicoRepasse medicoRepasse)
    {
      R_Medico medico = new R_Medico();
      medico = medico.MedicoRepasseToEntityCreate(medicoRepasse);
      Create(medico);
    }
    public void EditMedicoDetalhes(MedicoModel medicoModel)
    {
      R_Medico medicoEntiry = GetById(medicoModel.Codigo);
      medicoEntiry.M_Diretor = medicoModel.Diretor;
      medicoEntiry.M_ValorProLabore = medicoModel.ValorProLabore;
      medicoEntiry.M_Ativo = medicoModel.Ativo;
      Edit(medicoEntiry);
    }
    public void IntegraMedico(string connectionId)
    {
      SendSignal sendSignal = new SendSignal();
      sendSignal.openModalRetornoUsuario(connectionId, "Integrando Médicos");
      sendSignal.creatPStrong(connectionId, "Procurando Médicos...");

      IntegraRepasse integraRepasse = new IntegraRepasse();
      EmpresaMedicoService empresaMedicoService = new EmpresaMedicoService();
      List<MedicoRepasse> ListaMedicos = integraRepasse.GetMedicos("0788867A-C083-41F4-AD83-53260DAFE030").ToList();
      List<MedicoRepasse> ListaAtualizaEmpresaMedigo = new List<MedicoRepasse>();
      sendSignal.creatPSucess(connectionId, string.Format("{0} Médicos Encontrados", ListaMedicos.Count()));
      sendSignal.creatPSucess(connectionId, "Sincronizando... Aguarde");
      int i = 0;
      int z = 0;
      int quantidadePular = ListaMedicos.Count() / 100;
      int quantidade = 0;
      foreach (MedicoRepasse medicoRepasse in ListaMedicos)
      {
        if (i == quantidade)
        {
          quantidade += quantidadePular;
          sendSignal.setPorcentagemProgress(connectionId, (z + 1).ToString());
          z++;
        }

        bool ifExistByCPF = IfExistByCPF(medicoRepasse.CPF);
        if (!ifExistByCPF)
        {
          CreateMedicoIntegra(medicoRepasse);
          ListaAtualizaEmpresaMedigo.Add(medicoRepasse);
        }
        i++;
      }

      int QuantidadeTotalMedicos = ListaMedicos.Count();
      int MedicoAtual = 0;
      foreach (MedicoRepasse medicoRepasse in ListaMedicos)
      {
        MedicoAtual++;
        int IdMedico = GetIdByIdMedicoExterno(medicoRepasse.Codigo.ToString());
        empresaMedicoService.IntegraEmpresaMedico(connectionId, IdMedico, medicoRepasse.Codigo, medicoRepasse.Nome, MedicoAtual, QuantidadeTotalMedicos);
        sendSignal.concluirProgress(connectionId, 100, "Médicos Integrados com Sucesso.");
      }
    }
    public MedicoRegra GetMedicoRegra(int IdMedico)
    {
      string query = @"SELECT
                        M_CPF [CPF]
                       FROM R_Medico 
                       WHERE M_Id = @IdMedico";

      return Contexto.Database.SqlQuery<MedicoRegra>(query, new SqlParameter("@IdMedico", IdMedico)).FirstOrDefault();
    }
    public MedicoModel DetalhesMedico(int IdMedico)
    {
      string query = @"SELECT
                          M_Nome [Nome]
                        , M_CPF [CPF]
                        , M_CRM [CRM]
                        , M_Email [Email]
                        , M_Id [Codigo]
                        , M_AgenciaMedico [AgenciaMedico]
                        , M_BancoMedico [BancoMedico]
                        , M_ContaMedico [ContaMedico]
                        ,M_ValorProLabore [ValorProLabore]
                        ,M_Diretor [Diretor]
                        ,M_Ativo [Ativo]
                     FROM R_Medico
                      WHERE M_Id = @IdMedico";

      return Contexto.Database.SqlQuery<MedicoModel>(query, new SqlParameter("@IdMedico", IdMedico)).FirstOrDefault();
    }
    public int GetIdMedicoByIdEmpresa(int IdEmpresaMedico)
    {
      string query = @"SELECT
                        DISTINCT M.M_Id
                       FROM R_Medico M
                       INNER JOIN R_EmpresaMedico EM ON EM.EM_IdMedico = M.M_Id AND EM.EM_Id = @IdEmpresaMedico";

      return Contexto.Database.SqlQuery<int>(query, new SqlParameter("@IdEmpresaMedico", IdEmpresaMedico)).FirstOrDefault();
    }

    public int GetIdMedicoByCPF(string CPF)
    {
      string query = @"SELECT
                        M_Id
                       FROM R_Medico
                       WHERE M_CPF = @CPF";

      return Contexto.Database.SqlQuery<int>(query, new SqlParameter("@CPF", CPF)).FirstOrDefault();
    }

    public string GetCPFMedico(int IdMedico)
    {
      string query = @"SELECT
                        M_CPF
                       FROM R_Medico
                       WHERE M_Id = @IdMedico";

      return Contexto.Database.SqlQuery<string>(query, new SqlParameter("@IdMedico", IdMedico)).FirstOrDefault();
    }


    public string GetNomeMedicoById(int Id)
    {
      string query = @"SELECT
                        M_Nome
                       FROM R_Medico
                       WHERE M_Id = @Id";

      return Contexto.Database.SqlQuery<string>(query, new SqlParameter("@Id", Id)).FirstOrDefault();
    }
  }
}
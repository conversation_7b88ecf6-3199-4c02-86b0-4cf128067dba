﻿@using RepasseConvenio.Infrastructure.Controls
@using RepasseConvenio.Models
@model PlanilhaRecebimentoModel

@{
  ViewBag.Title = "Novo Repasse";
  ViewBag.DescricaoTela = "Repasse";
  ViewBag.ResumoTela = "Novo Repasse";
}

@using (Html.BeginForm("Create", "PlanilhaRecebimento", FormMethod.Post, new { id = "PostPlanRecebimento", @style = "" }))
{
  @Html.AntiForgeryToken()
  @Html.HiddenFor(model => model.PRM_Codigo)
  @Html.HiddenFor(model => model.PRM_CodigoRepasse)
  @Html.HiddenFor(model => model.PRM_DataCriacao)
  <div class="box box-primary">
    <div class="box-body">
      @Html.ValidationSummary(true, "", new { @class = "text-danger" })

      <div class="row">
        <div class="col-md-4">
          <div class="form-group">
            @Html.LabelFor(model => model.PRM_Nome)
            @Html.EditorFor(model => model.PRM_Nome, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.PRM_Nome, "", new { @class = "text-danger" })
          </div>
        </div>

        <div class="col-md-2">
          <div class="checkbox form-group">
            @Html.LabelFor(model => model.PRM_NumeroGuia)
            @Html.EditorFor(model => model.PRM_NumeroGuia, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.PRM_NumeroGuia, "", new { @class = "text-danger" })
          </div>
        </div>

        <div class="col-md-2">
          <div class="form-group">
            @Html.LabelFor(model => model.PRM_TotalFaturadoText)
            @Html.EditorFor(model => model.PRM_TotalFaturadoText, new { htmlAttributes = new { @class = "form-control money" } })
            @Html.ValidationMessageFor(model => model.PRM_TotalFaturadoText, "", new { @class = "text-danger" })
          </div>
        </div>
        <div class="col-md-2">
          <div class="form-group">
            @Html.LabelFor(model => model.PRM_TotalGlosadoText)
            @Html.EditorFor(model => model.PRM_TotalGlosadoText, new { htmlAttributes = new { @class = "form-control money" } })
            @Html.ValidationMessageFor(model => model.PRM_TotalGlosadoText, "", new { @class = "text-danger" })
          </div>
        </div>
        <div class="col-md-2">
          <div class="form-group">
            @Html.LabelFor(model => model.PRM_TotalPagoText)
            @Html.EditorFor(model => model.PRM_TotalPagoText, new { htmlAttributes = new { @class = "form-control money" } })
            @Html.ValidationMessageFor(model => model.PRM_TotalPagoText, "", new { @class = "text-danger" })
          </div>
        </div>
      </div>
    </div>

    <div class="box-footer">
      <button type="button" class="btn btn-outline-dark pull-left" data-dismiss="modal">Cancelar</button>

      @if (Model.PRM_Codigo == 0)
      {
        <button type="button" value="Create" class="btn btn-info pull-right" id="btnCreatePlanRecebimento">Salvar</button>
      }
      else
      {
        <button type="button" value="Create" class="btn btn-info pull-right" id="btnEditPlanRecebimento">Salvar</button>
      }

    </div>
    <!-- /.box-footer -->

  </div>
}

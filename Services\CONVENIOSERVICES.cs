﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using PagedList;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RepasseConvenio.WSPortalCooperado;
using RepasseConvenio.Infrastructure.Hubs;
using System.Web.Script.Serialization;

namespace RepasseConvenio.Services
{
  public class ConvenioServices : ServiceBase
  {
    public ConvenioServices(RepasseEntities Contexto)
        : base(Contexto)
    { }
    public ConvenioServices()
       : base()
    { }

    public ConvenioServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public ConvenioServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public ConvenioIndex Get(int page)
    {
      ConvenioIndex convenioIndex = new ConvenioIndex();
      int QuantidadeConvenios = Contexto.Database.SqlQuery<int>(@"SELECT COUNT(1) FROM R_Convenio").FirstOrDefault();
      List<int> ListaQuantidadeConvenios = new List<int>();

      for (int i = 0; i < QuantidadeConvenios; i++)
      {
        ListaQuantidadeConvenios.Add(i);
      }

      convenioIndex.QuantidadeConvenios = ListaQuantidadeConvenios.ToPagedList(page, PageSize);

      string query = string.Format(@"SELECT 
                                          C_Id [Codigo]
                                        , C_RazaoSocial [RazaoSocial]
                                        , C_CNPJ [CNPJ]
                                        , C_CodANS [CodANS]
                                        , C_IdConvenioExterno [CodConvenio]
										                    , C_Autarquia [Autarquia]
										                    , C_VencLoteGlosa [VencLoteGlosa]
                                       FROM 
                                          R_Convenio
                                       ORDER BY C_RazaoSocial
                                       OFFSET (@pag - 1) * @PageSize ROWS
                                       FETCH NEXT @PageSize ROWS ONLY");

      convenioIndex.ListaConvenio = Contexto.Database.SqlQuery<ConvenioModel>(query, new SqlParameter("@pag", page)
                                                                                   , new SqlParameter("@PageSize", PageSize)).ToList();
      return convenioIndex;
    }

    public ConvenioModel GetByIdConvenio(int id)
    {
      string query = string.Format(@"SELECT 
                                          C_Id [Codigo]
                                        , C_RazaoSocial [RazaoSocial]
                                        , C_CNPJ [CNPJ]
                                        , C_CodANS [CodANS]
                                        , C_IdConvenioExterno [CodConvenioExterno]
										                    , C_Autarquia [Autarquia]
										                    , C_VencLoteGlosa [VencLoteGlosa]
                                       FROM 
                                          R_Convenio
                                       WHERE C_Id = @id");

      return Contexto.Database.SqlQuery<ConvenioModel>(query, new SqlParameter("@id", id)).FirstOrDefault();
    }
    public string GetRazaoSocialById(int Id)
    {
      return Contexto.R_Convenio.Where(a => a.C_Id == Id).Select(a => a.C_RazaoSocial).FirstOrDefault();
    }
    public R_Convenio GetById(int Id)
    {
      return Contexto.R_Convenio.Where(a => a.C_Id == Id).FirstOrDefault();
    }
    public R_Convenio GetByCNPJ(string cnpj)
    {
      return Contexto.R_Convenio.Where(a => a.C_CNPJ.Equals(cnpj)).FirstOrDefault();
    }
    public int GetIdByCNPJ(string cnpj)
    {
      return Contexto.R_Convenio.Where(a => a.C_CNPJ.Equals(cnpj)).Select(a => a.C_Id).FirstOrDefault();
    }
    public string GetCNPJById(int Id)
    {
      return Contexto.R_Convenio.Where(a => a.C_Id.Equals(Id)).Select(a => a.C_CNPJ).FirstOrDefault();
    }
    public List<R_Convenio> GetByTerm(string term)
    {
      if (string.IsNullOrEmpty(term))
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_Convenio");

        return Contexto.Database.SqlQuery<R_Convenio>(query).ToList();

      }
      else
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_Convenio
                                       WHERE C_RazaoSocial LIKE @termo");

        return Contexto.Database.SqlQuery<R_Convenio>(query, new SqlParameter("@termo", string.Format("%{0}%", term))).ToList();

      }
    }
    public List<R_Convenio> GetAll()
    {
      string query = @"SELECT
                        *
                       FROM R_Convenio";

      return Contexto.Database.SqlQuery<R_Convenio>(query).ToList();
    }

    public bool IfExistByCodigoExterno(string codigoExterno)
    {
      string query = @"SELECT Count(1) FROM R_Convenio WHERE C_IdConvenioExterno = @codigoExterno";
      int quantidade = Contexto.Database.SqlQuery<int>(query, new SqlParameter("@codigoExterno", codigoExterno)).FirstOrDefault();
      if (quantidade == 0)
        return false;
      else
        return true;
    }

    public int GetIdConvenio(int idConvenioExterno)
    {
      string query = @"SELECT 
                        C_Id
                       FROM R_Convenio
                       where C_IdConvenioExterno = @idConvenioExterno";

      return Contexto.Database.SqlQuery<int>(query, new SqlParameter("@idConvenioExterno", idConvenioExterno)).FirstOrDefault();
    }

    public void EditConvenio(ConvenioModel model)
    {
      R_Convenio convenio = model.ToConvenioEdit();
      Edit(convenio);
      UpdateRegrasCartaConversao();
    }

    public void CreateConvenioIntegra(ConvenioRepasse convenioRepasse)
    {
      R_Convenio convenio = new R_Convenio();
      convenio = convenio.ConvenioRepasseToEntityCreate(convenioRepasse);
      Create(convenio);
    }

    public void IntegraConvenio(string connectionId)
    {
      SendSignal sendSignal = new SendSignal();
      sendSignal.openModalRetornoUsuario(connectionId, "Integrando Convênios");
      sendSignal.creatPStrong(connectionId, "Procurando Convênios...");

      IntegraRepasse integraRepasse = new IntegraRepasse();
      List<ConvenioRepasse> ListaConvenio = integraRepasse.GetConvenios("5FD334DA-B1B1-4F65-9222-BE261A4154FE").ToList();
      List<ConvenioRepasse> ListaToUpdateProcedimentos = new List<ConvenioRepasse>();
      sendSignal.creatPSucess(connectionId, string.Format("{0} Convênios Encontrados", ListaConvenio.Count()));
      sendSignal.creatPSucess(connectionId, "Sincronizando... Aguarde");
      int i = 0;
      int z = 0;
      int quantidadePular = ListaConvenio.Count() / 100;
      int quantidade = 0;
      foreach (ConvenioRepasse convenioRepasse in ListaConvenio)
      {
        if (i == quantidade)
        {
          quantidade += quantidadePular;
          sendSignal.setPorcentagemProgress(connectionId, (z + 1).ToString());
          z++;
        }


        bool ifExistByCodigoExterno = IfExistByCodigoExterno(convenioRepasse.Codigo.ToString());
        if (!ifExistByCodigoExterno)
        {
          CreateConvenioIntegra(convenioRepasse);
          ListaToUpdateProcedimentos.Add(convenioRepasse);
        }
        i++;
      }
      int QuantidadeTotalConvenios = ListaToUpdateProcedimentos.Count();
      int ConvenioAtual = 0;
      foreach (ConvenioRepasse convenioRepasse in ListaToUpdateProcedimentos)
      {
        ConvenioAtual++;
        int idConvenio = GetIdConvenio(convenioRepasse.Codigo);
        ProcedimentoServices procedimentoServices = new ProcedimentoServices();
        procedimentoServices.IntegraProcedimentos(connectionId, convenioRepasse.Codigo, idConvenio, convenioRepasse.RazaoSocial, ConvenioAtual, QuantidadeTotalConvenios);
      }
      sendSignal.concluirProgress(connectionId, 100, "Convênois Integrados com Sucesso.");
    }

    public void UpdateRegrasCartaConversao()
    {
      List<string> RazoesSociaisAutarquia = Contexto.R_Convenio.Where(a => a.C_Autarquia).Select(a => a.C_RazaoSocial).ToList();

      if (RazoesSociaisAutarquia.Count > 0)
      {
        int IdAcao = new RegraCartaConversaoAcaoServices().GetIdByAcao("Igual a", "string");
        R_RegraCartaConversaoCampo Campo = new RegraCartaConversaoCampoServices().GetByCampo("Convênio", "string");

        string QueryCarta = string.Empty;
        List<RegraCartaConversaoCondicaoModel> CondicoesCarta = new List<RegraCartaConversaoCondicaoModel>();

        for (int i = 0; i < RazoesSociaisAutarquia.Count; i++)
        {
          if (i == 0)
            QueryCarta = string.Format("{0} == \"{1}\"", Campo.RCCC_CampoSql, RazoesSociaisAutarquia[0]);
          else
            QueryCarta += string.Format(" OR {0} == \"{1}\"", Campo.RCCC_CampoSql, RazoesSociaisAutarquia[i]);

          RegraCartaConversaoCondicaoModel Condicao = new RegraCartaConversaoCondicaoModel()
          {
            CodigoRegraCartaConversaoAcao = IdAcao,
            CodigoRegraCartaConversaoCampo = Campo.RCCC_Id,
            Conteudo = RazoesSociaisAutarquia[i],
            operadorLogicoEnum = 0,
            parentesesEnum = 0,
            Sequencia = i + i
          };

          CondicoesCarta.Add(Condicao);

          if (i + 1 < RazoesSociaisAutarquia.Count)
          {
            Condicao = new RegraCartaConversaoCondicaoModel()
            {
              operadorLogicoEnum = (OperadorLogicoEnum)2,
              parentesesEnum = 0,
              Sequencia = i + i + 1
            };

            CondicoesCarta.Add(Condicao);
          }
        }

        string RegrasHTML = new JavaScriptSerializer().Serialize(CondicoesCarta);

        Contexto.Database.ExecuteSqlCommand(@"UPDATE 
                                                R_RegraCartaConversao 
                                              SET RCC_QuerySql = @Query, RCC_Html = @Html, RCC_Descricao = 'Convênios Autarquia', RCC_Valida = 1 
                                              WHERE RCC_Prioridade = 0", new SqlParameter("@Query", QueryCarta), new SqlParameter("@Html", RegrasHTML));

        Contexto.Database.ExecuteSqlCommand(@"
          INSERT INTO 
          [R_RegraCartaConversao] (
	          [RCC_IdEmpresaMedico]
	          ,[RCC_QuerySql]
	          ,[RCC_Html]
	          ,[RCC_Valida]
	          ,[RCC_IdMedico]
	          ,[RCC_Prioridade]
	          ,[RCC_Descricao]
          ) 
          SELECT
	          NULL,
	          @Query,
	          @Html,
	          1,	
            M_Id,
	          0,
	          'Convênios Autarquia'
          FROM R_Medico
          WHERE M_Id NOT IN (SELECT RCC_IdMedico FROM R_RegraCartaConversao WHERE RCC_Prioridade = 0)
        ", new SqlParameter("@Query", QueryCarta), new SqlParameter("@Html", RegrasHTML));
      }
    }

    public DateTime GetDiasVencimentoGlosa(int IdConvenio, DateTime dateTime)
    {
      string query = @"SELECT
                        C_VencLoteGlosa
                       FROM R_Convenio
                       WHERE C_Id = @IdConvenio";

      int? QuantidadeDiasVencimento = Contexto.Database.SqlQuery<int?>(query, new SqlParameter("@IdConvenio", IdConvenio)).FirstOrDefault();

      if (QuantidadeDiasVencimento == null)
        throw new CustomException("Paramêtro de vencimento de glosa não definido, gentileza cadastrar o parâmetro para o convênio.");

      return dateTime.AddDays(QuantidadeDiasVencimento.Value);
    }
  }
}

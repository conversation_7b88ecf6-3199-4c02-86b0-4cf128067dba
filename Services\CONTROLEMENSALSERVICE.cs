﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using Microsoft.AspNet.SignalR;
using RepasseConvenio.Infrastructure.Hubs;
using RepasseConvenio.Models;
using RepasseConvenio.WSPortalCooperado;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Web;

namespace RepasseConvenio.Services
{
  public class ControleMensalService : ServiceBase
  {
    public ControleMensalService()
   : base()
    { }
    public ControleMensalService(RepasseEntities Contexto)
       : base(Contexto)
    { }

    public ControleMensalService(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public ControleMensalService(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public void Create()
    {
      DateTime DataInicio = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
      DateTime DataFim = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.DaysInMonth(DateTime.Now.Year, DateTime.Now.Month));

      if (!IfExist(DataInicio, DataFim))
      {
        StatusControleMensalServices statusControleMensalServices = new StatusControleMensalServices();
        int IdStatusControleMensal = statusControleMensalServices.GetIdByEnum(StatusControlheMensalEnum.NaoProcessado);
        R_ControleMensal controleMensal = new R_ControleMensal();
        controleMensal = controleMensal.Create(DataInicio, DataFim, IdStatusControleMensal);
        Create(controleMensal);
      }
    }

    public bool IfExist(DateTime DataInicio, DateTime Datafim)
    {
      string query = @"SELECT
                        COUNT(1)
                       FROM R_ControleMensal
                       WHERE CM_DataInicio = @DataInicio AND CM_DataFim = @Datafim";

      int quantidade = Contexto.Database.SqlQuery<int>(query, new SqlParameter("@DataInicio", DataInicio)
                                           , new SqlParameter("@DataFim", Datafim)).FirstOrDefault();

      if (quantidade == 0)
        return false;
      else
        return true;
    }

    public R_ControleMensal GetByMonth(int year, int month)
    {
      DateTime DataInicio = new DateTime(year, month, 1);
      DateTime DataFim = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.DaysInMonth(year, month));

      string query = @"SELECT
                        *
                       FROM R_ControleMensal
                       WHERE CM_DataInicio = @DataInicio AND CM_DataFim = @Datafim";

      return Contexto.Database.SqlQuery<R_ControleMensal>(query, new SqlParameter("@DataInicio", DataInicio)
                                                         , new SqlParameter("@DataFim", DataFim)).FirstOrDefault();


    }

    public void AtualizarControleMensal(DateTime DataPagamento)
    {
      ExtratoMedicoServices extratoMedicoServices = new ExtratoMedicoServices();
      bool IfExistExtratoDiretorPendenteMes = extratoMedicoServices.IfExistExtratoDiretorPendenteMes(DataPagamento);
      if(!IfExistExtratoDiretorPendenteMes)
      {
        StatusControleMensalServices statusControleMensalServices = new StatusControleMensalServices();
        int IdProcessado = statusControleMensalServices.GetIdByEnum(StatusControlheMensalEnum.Processado);
        R_ControleMensal controleMensal = GetByMonth(DataPagamento.Year, DataPagamento.Month);
        controleMensal.CM_IdStatusControlheMensal = IdProcessado;
        Edit(controleMensal);
      }
    }
  }
}
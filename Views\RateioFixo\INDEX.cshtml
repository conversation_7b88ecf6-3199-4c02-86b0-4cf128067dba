﻿@using RepasseConvenio.Models
@model List<RateioFixoIndex>

@{
  ViewBag.Title = "Lista Rateio Fixo";
}
<div class="row">
  <section class="col-lg-12 connectedSortable ui-sortable">
    <div class="card">
      <div class="card-header ui-sortable-handle">
        <h3 class="card-title">
          <i class="fas fa-user mr-1"></i>
          Rateio Fixo
        </h3>
        <div class="card-tools">
        </div>
      </div><!-- /.card-header -->
      <div class="card-body">
        <div class="tab-content p-0">
          <div class="box-body">
            <button type="button" value="Create" class="btn btn-primary pull-rigth" onclick="location.href = '@Url.Action("Create", "RateioFixo", new {codRateio = @ViewBag.CodigoRateio})'">Novo</button>

            <div class="row">
              <div class="col-md-12">
                <table class="table table-hover table-striped">
                  <thead>
                    <tr>
                      <th>
                        Procedimento
                      </th>
                      <th>
                        Hospital
                      </th>
                      <th>
                        Nome Médico
                      </th>
                      <th>
                        CRM
                      </th>
                      <th>
                        Função
                      </th>
                      <th>
                        Percentual
                      </th>
                      <th>
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    @foreach (RateioFixoIndex item in Model)
                    {
                      <tr>
                        <td>
                          @item.Procedimento
                        </td>
                        <td>
                          @item.Hospital
                        </td>
                        <td>
                          @item.NomeMedico
                        </td>
                        <td>
                          @item.CRMMedico
                        </td>
                        <td>
                          @item.Funcao
                        </td>
                        <td>
                          @item.Percentual%
                        </td>
                        <td>
                          <a href="@Url.Action("Edit", "RateioFixo", new { CodFixo = item.Codigo })" class="btn btn-warning" title="Rateio Fixo">
                            Editar Rateio Fixo
                          </a>
                        </td>
                      </tr>
                    }
                  </tbody>
                </table>
              </div>
            </div>
            <div class="row">
              <div class="col-md-3">
                <a class="btn" href="@Url.Action("Index", "RateioMedico", new { cod = 0, codRateio = @ViewBag.CodigoRateio})">Voltar</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>
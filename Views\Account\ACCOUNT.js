﻿var Login = function () {
};

Login.init = function () {
  var hash = location.hash;  /*Query string, usando o valor que ja esta na url.*/
  hash = hash.replace('#', '');
  if (hash == "loginCandidato")
    $('.nav-tabs a[href="#loginCandidato"]').tab('show');

  //$(document).on("click", ".open-modal-Entrar", function () {
  //  swal({
  //    title: "Você é funcionário Mip?",
  //    text: "",
  //    type: "info",
  //    showCancelButton: true,
  //    cancelButtonText: "N\u00e3o",
  //    confirmButtonColor: "#DD6B55",
  //    confirmButtonText: "Sim",
  //    closeOnConfirm: true
  //  }, function (isConfirm) {
  //    if (isConfirm) {
  //      var url = GetURLBaseComplete() + '/Login/LoginAux#' + "loginColaborador";
  //      location.href = url;
  //      return false;
  //    } else {
  //      var url = GetURLBaseComplete() + '/Login/LoginAux#' + "loginCandidato";
  //      location.href = url;
  //      return false;
  //    }

  //  });
  //}); /*Exibe um modal do tipo info*/

  $(document).on("click", "#Logar", function () {
    var url = GetURLBaseComplete() + '/Login/LoginAux#' + "loginCandidato";
    location.href = url;
    return false;
  });

  $(document).on("click", ".open-modal-Entrar", function () {
    var url = GetURLBaseComplete() + '/Login/LoginAux#' + "loginColaborador";
    location.href = url;
    return false;
  }); /*Exibe um modal do tipo info*/

  $(document).on("click", ".open-modal-RegistrarIdeias", function () {
    swal({
      title: "Você é funcionário MIP?",
      text: "",
      type: "info",
      showCancelButton: true,
      cancelButtonText: "N\u00e3o",
      confirmButtonColor: "#DD6B55",
      confirmButtonText: "Sim",
      closeOnConfirm: true
    }, function (isConfirm) {
      if (isConfirm) {
        var url = GetURLBaseComplete() + '/Login/LoginAux#' + "loginColaborador";
        location.href = url;
      } else {
        var url = GetURLBaseComplete() + '/Login/Register';
        location.href = url;
        //location.href = "http://localhost/Atendimento/Login/Register";
        return false;
      }
    });
  });

  $(document).on("click", ".ProximaAba", function () {/*Redireciona para outra aba atravez do id*/
    $('.nav-tabs a[href="#Doc"]').tab('show');
  });

  $(document).on("click", ".EtapaAnterior", function () {
    $('.nav-tabs a[href="#Dados"]').tab('show');
  });

  $(document).on("click", ".VoltarAba", function () {
    $('.nav-tabs a[href="#Promocao"]').tab('show');
  });

  $(document).on("click", ".IrAbaDesafios", function () {
    $('.nav-tabs a[href="#Desafios"]').tab('show');
  });

  $(document).on("click", "#upload1", function () {
    $("input[id=ArquivoInput]").trigger('click');
  });

  $("#filePerfil").on('change', function (a) {

    if (typeof (FileReader) != "undefined") {
      var fileName = a.target.value.split('\\').pop();
      var extensaoArray = fileName.split('.')
      var extensao = extensaoArray[extensaoArray.length - 1];
      var reader = new FileReader();

      if (extensao.toLowerCase() == 'png') {
        reader.onload = function (e) {

          var formData = new FormData();
          var file = document.getElementById("filePerfil").files[0];
          formData.append("FileUpload", file);

          $.ajax({
            url: GetURLBaseComplete() + "/Login/ChangeImage",
            dataType: "json",
            type: "POST",
            data: formData,
            contentType: false,
            processData: false,
            success: function (data) {
              if (!data.Error) {
                $(".fileuser").attr('src', e.target.result);
                Alerta("Sucesso", data.Mensagem, "success", 3000);
              } else {
                Alerta("Erro", data.Mensagem, "error", 3000);
              }
            }
          });
        }
        reader.readAsDataURL($(this)[0].files[0]);
      } else {
        $("#ArquivoInput").val("");
        alert("Favor inserir uma imagem PNG");
      }
    } else {
      $("#ArquivoInput").val("");
      alert("Este navegador nao suporta FileReader.");
    }
  });

  $("#ArquivoInput").on('change', function (a) {

    if (typeof (FileReader) != "undefined") {
      var fileName = a.target.value.split('\\').pop();
      var extensaoArray = fileName.split('.')
      var extensao = extensaoArray[extensaoArray.length - 1];
      var reader = new FileReader();

      if (extensao.toLowerCase() == 'jpg' || extensao.toLowerCase() == 'jpeg' || extensao.toLowerCase() == 'png') {
        reader.onload = function (e) {
          $("#img-file-upload1").attr('src', e.target.result);
          $(".excluir-imagem").removeAttr("disabled");
        }
        reader.readAsDataURL($(this)[0].files[0]);
      } else {
        $("#img-file-upload1").attr('src', GetURLBaseComplete() + "/Content/Images/picture.png");
        $("#ArquivoInput").val("");
        alert("Favor inserir uma imagem");
      }
    } else {
      $("#ArquivoInput").val("");
      alert("Este navegador nao suporta FileReader.");
    }
  });

  $(document).on("click", ".excluir-imagem", function () {
    $("#img-file-upload1").attr('src', GetURLBaseComplete() + "/Content/Images/picture.png");
    $(".excluir-imagem").attr("disabled", "disabled");
    $("#ArquivoInput").val("");

  });

  $(document).on("click", ".fileuser", function () {
    $("#filePerfil").trigger("click");
  });

  $(document).on("change", ".inputfile", function () {
    var input = this;
    var files = input.files;
    for (var i = 0; i < files.length; i++) {
      var file = files[i];
      $("#ArquivosNomes").append('<p id="ArquivosNomes_' + input.id + '">' + file.name + "  " +
      '<a href="#" id="btnRemover_' + input.id + '" class="btn btn-alert btn-circle btn-sm excluir" title="Remover">' + "<i class='fa fa-trash'>" + '</i>' + '</a>' + '</p>');
    }
  });

  $(document).on('click', '#adicionaFile', function () {
    var inputs = $(".inputsfiles");
    var qtdInputs = inputs.length;

    $('<input class="inputsfiles inputfile" style="display:none" id="ArquivoInput_' + qtdInputs + '_" name="ArquivoInput[' + qtdInputs + ']" type="file" value="">').insertAfter("#adicionaFile");

    $("#ArquivoInput_" + qtdInputs + "_").trigger("click");
  });


  $(document).on("click", ".excluir", function () {
    var LinhaP = this.attributes["id"].value;
    Login.RenameFiles(LinhaP);
  });

}

Login.RenameFiles = function (linhaP) {
  linhaP = linhaP.split("_")[2];
  $('#ArquivosNomes_ArquivoInput_' + linhaP + '_').remove();

  var divlinhas = $("#ArquivosNomes p").length;

  for (var i = linhaP; i <= divlinhas; i++) {
    var linhaaux = parseInt(i) + 1;
    $("#ArquivosNomes_" + "ArquivoInput_" + linhaaux + "_").attr('id', "ArquivosNomes_" + "ArquivoInput_" + i + "_");
    $("#btnRemover_ArquivoInput_" + linhaaux + "_").attr('id', "btnRemover_ArquivoInput_" + i + "_");
  }
}

$(document).ready(function () {
  Login.init();
});

﻿//------------------------------------------------------------------------------
// <auto-generated>
//     O código foi gerado por uma ferramenta.
//     Versão de Tempo de Execução:4.0.30319.42000
//
//     As alterações ao arquivo poderão causar comportamento incorreto e serão perdidas se
//     o código for gerado novamente.
// </auto-generated>
//------------------------------------------------------------------------------

// 
// Este código-fonte foi gerado automaticamente por Microsoft.VSDesigner, Versão 4.0.30319.42000.
// 
#pragma warning disable 1591

namespace RepasseConvenio.WSPortalCooperado {
    using System;
    using System.Web.Services;
    using System.Diagnostics;
    using System.Web.Services.Protocols;
    using System.Xml.Serialization;
    using System.ComponentModel;
    
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Web.Services.WebServiceBindingAttribute(Name="IntegraRepasseSoap", Namespace="http://tempuri.org/")]
    public partial class IntegraRepasse : System.Web.Services.Protocols.SoapHttpClientProtocol {
        
        private System.Threading.SendOrPostCallback BuscaBasicaLotesOperationCompleted;
        
        private System.Threading.SendOrPostCallback BuscaAvancadaLotesOperationCompleted;
        
        private System.Threading.SendOrPostCallback IntegracaoGuiaRepasseOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetMedicosOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetConveniosOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetProcedimentosConveniosOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetEmpresasMedicoOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetGuiaLoteRepasseOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetLoteRepasseOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetMedicoIntegraGuiaOperationCompleted;
        
        private bool useDefaultCredentialsSetExplicitly;
        
        /// <remarks/>
        public IntegraRepasse() {
            this.Url = "http://localhost/PortalCooperado/integrarepasse.asmx";
            if ((this.IsLocalFileSystemWebService(this.Url) == true)) {
                this.UseDefaultCredentials = true;
                this.useDefaultCredentialsSetExplicitly = false;
            }
            else {
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        public new string Url {
            get {
                return base.Url;
            }
            set {
                if ((((this.IsLocalFileSystemWebService(base.Url) == true) 
                            && (this.useDefaultCredentialsSetExplicitly == false)) 
                            && (this.IsLocalFileSystemWebService(value) == false))) {
                    base.UseDefaultCredentials = false;
                }
                base.Url = value;
            }
        }
        
        public new bool UseDefaultCredentials {
            get {
                return base.UseDefaultCredentials;
            }
            set {
                base.UseDefaultCredentials = value;
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        /// <remarks/>
        public event BuscaBasicaLotesCompletedEventHandler BuscaBasicaLotesCompleted;
        
        /// <remarks/>
        public event BuscaAvancadaLotesCompletedEventHandler BuscaAvancadaLotesCompleted;
        
        /// <remarks/>
        public event IntegracaoGuiaRepasseCompletedEventHandler IntegracaoGuiaRepasseCompleted;
        
        /// <remarks/>
        public event GetMedicosCompletedEventHandler GetMedicosCompleted;
        
        /// <remarks/>
        public event GetConveniosCompletedEventHandler GetConveniosCompleted;
        
        /// <remarks/>
        public event GetProcedimentosConveniosCompletedEventHandler GetProcedimentosConveniosCompleted;
        
        /// <remarks/>
        public event GetEmpresasMedicoCompletedEventHandler GetEmpresasMedicoCompleted;
        
        /// <remarks/>
        public event GetGuiaLoteRepasseCompletedEventHandler GetGuiaLoteRepasseCompleted;
        
        /// <remarks/>
        public event GetLoteRepasseCompletedEventHandler GetLoteRepasseCompleted;
        
        /// <remarks/>
        public event GetMedicoIntegraGuiaCompletedEventHandler GetMedicoIntegraGuiaCompleted;
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/BuscaBasicaLotes", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public ResultPesquisaLote BuscaBasicaLotes(BuscaLoteRepasse model) {
            object[] results = this.Invoke("BuscaBasicaLotes", new object[] {
                        model});
            return ((ResultPesquisaLote)(results[0]));
        }
        
        /// <remarks/>
        public void BuscaBasicaLotesAsync(BuscaLoteRepasse model) {
            this.BuscaBasicaLotesAsync(model, null);
        }
        
        /// <remarks/>
        public void BuscaBasicaLotesAsync(BuscaLoteRepasse model, object userState) {
            if ((this.BuscaBasicaLotesOperationCompleted == null)) {
                this.BuscaBasicaLotesOperationCompleted = new System.Threading.SendOrPostCallback(this.OnBuscaBasicaLotesOperationCompleted);
            }
            this.InvokeAsync("BuscaBasicaLotes", new object[] {
                        model}, this.BuscaBasicaLotesOperationCompleted, userState);
        }
        
        private void OnBuscaBasicaLotesOperationCompleted(object arg) {
            if ((this.BuscaBasicaLotesCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.BuscaBasicaLotesCompleted(this, new BuscaBasicaLotesCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/BuscaAvancadaLotes", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public ResultPesquisaLote BuscaAvancadaLotes(BuscaAvancadaRepasse model) {
            object[] results = this.Invoke("BuscaAvancadaLotes", new object[] {
                        model});
            return ((ResultPesquisaLote)(results[0]));
        }
        
        /// <remarks/>
        public void BuscaAvancadaLotesAsync(BuscaAvancadaRepasse model) {
            this.BuscaAvancadaLotesAsync(model, null);
        }
        
        /// <remarks/>
        public void BuscaAvancadaLotesAsync(BuscaAvancadaRepasse model, object userState) {
            if ((this.BuscaAvancadaLotesOperationCompleted == null)) {
                this.BuscaAvancadaLotesOperationCompleted = new System.Threading.SendOrPostCallback(this.OnBuscaAvancadaLotesOperationCompleted);
            }
            this.InvokeAsync("BuscaAvancadaLotes", new object[] {
                        model}, this.BuscaAvancadaLotesOperationCompleted, userState);
        }
        
        private void OnBuscaAvancadaLotesOperationCompleted(object arg) {
            if ((this.BuscaAvancadaLotesCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.BuscaAvancadaLotesCompleted(this, new BuscaAvancadaLotesCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IntegracaoGuiaRepasse", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public RepasseRetornoWS IntegracaoGuiaRepasse(IntegracoesGuiasRepasseWS model) {
            object[] results = this.Invoke("IntegracaoGuiaRepasse", new object[] {
                        model});
            return ((RepasseRetornoWS)(results[0]));
        }
        
        /// <remarks/>
        public void IntegracaoGuiaRepasseAsync(IntegracoesGuiasRepasseWS model) {
            this.IntegracaoGuiaRepasseAsync(model, null);
        }
        
        /// <remarks/>
        public void IntegracaoGuiaRepasseAsync(IntegracoesGuiasRepasseWS model, object userState) {
            if ((this.IntegracaoGuiaRepasseOperationCompleted == null)) {
                this.IntegracaoGuiaRepasseOperationCompleted = new System.Threading.SendOrPostCallback(this.OnIntegracaoGuiaRepasseOperationCompleted);
            }
            this.InvokeAsync("IntegracaoGuiaRepasse", new object[] {
                        model}, this.IntegracaoGuiaRepasseOperationCompleted, userState);
        }
        
        private void OnIntegracaoGuiaRepasseOperationCompleted(object arg) {
            if ((this.IntegracaoGuiaRepasseCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.IntegracaoGuiaRepasseCompleted(this, new IntegracaoGuiaRepasseCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetMedicos", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public MedicoRepasse[] GetMedicos(string password) {
            object[] results = this.Invoke("GetMedicos", new object[] {
                        password});
            return ((MedicoRepasse[])(results[0]));
        }
        
        /// <remarks/>
        public void GetMedicosAsync(string password) {
            this.GetMedicosAsync(password, null);
        }
        
        /// <remarks/>
        public void GetMedicosAsync(string password, object userState) {
            if ((this.GetMedicosOperationCompleted == null)) {
                this.GetMedicosOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetMedicosOperationCompleted);
            }
            this.InvokeAsync("GetMedicos", new object[] {
                        password}, this.GetMedicosOperationCompleted, userState);
        }
        
        private void OnGetMedicosOperationCompleted(object arg) {
            if ((this.GetMedicosCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetMedicosCompleted(this, new GetMedicosCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetConvenios", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public ConvenioRepasse[] GetConvenios(string password) {
            object[] results = this.Invoke("GetConvenios", new object[] {
                        password});
            return ((ConvenioRepasse[])(results[0]));
        }
        
        /// <remarks/>
        public void GetConveniosAsync(string password) {
            this.GetConveniosAsync(password, null);
        }
        
        /// <remarks/>
        public void GetConveniosAsync(string password, object userState) {
            if ((this.GetConveniosOperationCompleted == null)) {
                this.GetConveniosOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetConveniosOperationCompleted);
            }
            this.InvokeAsync("GetConvenios", new object[] {
                        password}, this.GetConveniosOperationCompleted, userState);
        }
        
        private void OnGetConveniosOperationCompleted(object arg) {
            if ((this.GetConveniosCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetConveniosCompleted(this, new GetConveniosCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetProcedimentosConvenios", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public ProcedimentosConvenioRepasse[] GetProcedimentosConvenios(string password, int idConvenio) {
            object[] results = this.Invoke("GetProcedimentosConvenios", new object[] {
                        password,
                        idConvenio});
            return ((ProcedimentosConvenioRepasse[])(results[0]));
        }
        
        /// <remarks/>
        public void GetProcedimentosConveniosAsync(string password, int idConvenio) {
            this.GetProcedimentosConveniosAsync(password, idConvenio, null);
        }
        
        /// <remarks/>
        public void GetProcedimentosConveniosAsync(string password, int idConvenio, object userState) {
            if ((this.GetProcedimentosConveniosOperationCompleted == null)) {
                this.GetProcedimentosConveniosOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetProcedimentosConveniosOperationCompleted);
            }
            this.InvokeAsync("GetProcedimentosConvenios", new object[] {
                        password,
                        idConvenio}, this.GetProcedimentosConveniosOperationCompleted, userState);
        }
        
        private void OnGetProcedimentosConveniosOperationCompleted(object arg) {
            if ((this.GetProcedimentosConveniosCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetProcedimentosConveniosCompleted(this, new GetProcedimentosConveniosCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetEmpresasMedico", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public EmpresaMedicoRepasse[] GetEmpresasMedico(string password, int IdMedico) {
            object[] results = this.Invoke("GetEmpresasMedico", new object[] {
                        password,
                        IdMedico});
            return ((EmpresaMedicoRepasse[])(results[0]));
        }
        
        /// <remarks/>
        public void GetEmpresasMedicoAsync(string password, int IdMedico) {
            this.GetEmpresasMedicoAsync(password, IdMedico, null);
        }
        
        /// <remarks/>
        public void GetEmpresasMedicoAsync(string password, int IdMedico, object userState) {
            if ((this.GetEmpresasMedicoOperationCompleted == null)) {
                this.GetEmpresasMedicoOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetEmpresasMedicoOperationCompleted);
            }
            this.InvokeAsync("GetEmpresasMedico", new object[] {
                        password,
                        IdMedico}, this.GetEmpresasMedicoOperationCompleted, userState);
        }
        
        private void OnGetEmpresasMedicoOperationCompleted(object arg) {
            if ((this.GetEmpresasMedicoCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetEmpresasMedicoCompleted(this, new GetEmpresasMedicoCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetGuiaLoteRepasse", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public RetornoGetGuiaLoteRepasse GetGuiaLoteRepasse(IntegraGuiaRepasse integraGuiaRepasse) {
            object[] results = this.Invoke("GetGuiaLoteRepasse", new object[] {
                        integraGuiaRepasse});
            return ((RetornoGetGuiaLoteRepasse)(results[0]));
        }
        
        /// <remarks/>
        public void GetGuiaLoteRepasseAsync(IntegraGuiaRepasse integraGuiaRepasse) {
            this.GetGuiaLoteRepasseAsync(integraGuiaRepasse, null);
        }
        
        /// <remarks/>
        public void GetGuiaLoteRepasseAsync(IntegraGuiaRepasse integraGuiaRepasse, object userState) {
            if ((this.GetGuiaLoteRepasseOperationCompleted == null)) {
                this.GetGuiaLoteRepasseOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetGuiaLoteRepasseOperationCompleted);
            }
            this.InvokeAsync("GetGuiaLoteRepasse", new object[] {
                        integraGuiaRepasse}, this.GetGuiaLoteRepasseOperationCompleted, userState);
        }
        
        private void OnGetGuiaLoteRepasseOperationCompleted(object arg) {
            if ((this.GetGuiaLoteRepasseCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetGuiaLoteRepasseCompleted(this, new GetGuiaLoteRepasseCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetLoteRepasse", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public RetornoGetGuiaLoteRepasse GetLoteRepasse(IntegraLoteRepasse integraLoteRepasse) {
            object[] results = this.Invoke("GetLoteRepasse", new object[] {
                        integraLoteRepasse});
            return ((RetornoGetGuiaLoteRepasse)(results[0]));
        }
        
        /// <remarks/>
        public void GetLoteRepasseAsync(IntegraLoteRepasse integraLoteRepasse) {
            this.GetLoteRepasseAsync(integraLoteRepasse, null);
        }
        
        /// <remarks/>
        public void GetLoteRepasseAsync(IntegraLoteRepasse integraLoteRepasse, object userState) {
            if ((this.GetLoteRepasseOperationCompleted == null)) {
                this.GetLoteRepasseOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetLoteRepasseOperationCompleted);
            }
            this.InvokeAsync("GetLoteRepasse", new object[] {
                        integraLoteRepasse}, this.GetLoteRepasseOperationCompleted, userState);
        }
        
        private void OnGetLoteRepasseOperationCompleted(object arg) {
            if ((this.GetLoteRepasseCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetLoteRepasseCompleted(this, new GetLoteRepasseCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetMedicoIntegraGuia", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public RetornoMedicoIntegraGuia GetMedicoIntegraGuia(string CPFMedico, string password) {
            object[] results = this.Invoke("GetMedicoIntegraGuia", new object[] {
                        CPFMedico,
                        password});
            return ((RetornoMedicoIntegraGuia)(results[0]));
        }
        
        /// <remarks/>
        public void GetMedicoIntegraGuiaAsync(string CPFMedico, string password) {
            this.GetMedicoIntegraGuiaAsync(CPFMedico, password, null);
        }
        
        /// <remarks/>
        public void GetMedicoIntegraGuiaAsync(string CPFMedico, string password, object userState) {
            if ((this.GetMedicoIntegraGuiaOperationCompleted == null)) {
                this.GetMedicoIntegraGuiaOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetMedicoIntegraGuiaOperationCompleted);
            }
            this.InvokeAsync("GetMedicoIntegraGuia", new object[] {
                        CPFMedico,
                        password}, this.GetMedicoIntegraGuiaOperationCompleted, userState);
        }
        
        private void OnGetMedicoIntegraGuiaOperationCompleted(object arg) {
            if ((this.GetMedicoIntegraGuiaCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetMedicoIntegraGuiaCompleted(this, new GetMedicoIntegraGuiaCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        public new void CancelAsync(object userState) {
            base.CancelAsync(userState);
        }
        
        private bool IsLocalFileSystemWebService(string url) {
            if (((url == null) 
                        || (url == string.Empty))) {
                return false;
            }
            System.Uri wsUri = new System.Uri(url);
            if (((wsUri.Port >= 1024) 
                        && (string.Compare(wsUri.Host, "localHost", System.StringComparison.OrdinalIgnoreCase) == 0))) {
                return true;
            }
            return false;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class BuscaLoteRepasse {
        
        private System.Nullable<int> numeroLoteField;
        
        private System.Nullable<System.DateTime> dataDeField;
        
        private System.Nullable<System.DateTime> dataAteField;
        
        private string cNPJConvenioField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<int> NumeroLote {
            get {
                return this.numeroLoteField;
            }
            set {
                this.numeroLoteField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<System.DateTime> DataDe {
            get {
                return this.dataDeField;
            }
            set {
                this.dataDeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<System.DateTime> DataAte {
            get {
                return this.dataAteField;
            }
            set {
                this.dataAteField = value;
            }
        }
        
        /// <remarks/>
        public string CNPJConvenio {
            get {
                return this.cNPJConvenioField;
            }
            set {
                this.cNPJConvenioField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class MedicoIntegraGuia {
        
        private int codigoField;
        
        private string cRMField;
        
        private string cPFField;
        
        private string nomeField;
        
        private string emailField;
        
        private string agenciaMedicoField;
        
        private string bancoMedicoField;
        
        private string contaMedicoField;
        
        private StatusCooperado statusCooperadoField;
        
        /// <remarks/>
        public int Codigo {
            get {
                return this.codigoField;
            }
            set {
                this.codigoField = value;
            }
        }
        
        /// <remarks/>
        public string CRM {
            get {
                return this.cRMField;
            }
            set {
                this.cRMField = value;
            }
        }
        
        /// <remarks/>
        public string CPF {
            get {
                return this.cPFField;
            }
            set {
                this.cPFField = value;
            }
        }
        
        /// <remarks/>
        public string Nome {
            get {
                return this.nomeField;
            }
            set {
                this.nomeField = value;
            }
        }
        
        /// <remarks/>
        public string Email {
            get {
                return this.emailField;
            }
            set {
                this.emailField = value;
            }
        }
        
        /// <remarks/>
        public string AgenciaMedico {
            get {
                return this.agenciaMedicoField;
            }
            set {
                this.agenciaMedicoField = value;
            }
        }
        
        /// <remarks/>
        public string BancoMedico {
            get {
                return this.bancoMedicoField;
            }
            set {
                this.bancoMedicoField = value;
            }
        }
        
        /// <remarks/>
        public string ContaMedico {
            get {
                return this.contaMedicoField;
            }
            set {
                this.contaMedicoField = value;
            }
        }
        
        /// <remarks/>
        public StatusCooperado statusCooperado {
            get {
                return this.statusCooperadoField;
            }
            set {
                this.statusCooperadoField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public enum StatusCooperado {
        
        /// <remarks/>
        Ativo,
        
        /// <remarks/>
        Suspenso,
        
        /// <remarks/>
        Desligado,
        
        /// <remarks/>
        Emcadastro,
        
        /// <remarks/>
        Eliminado,
        
        /// <remarks/>
        Inativo,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class RetornoMedicoIntegraGuia {
        
        private MedicoIntegraGuia medicoIntegraGuiaField;
        
        private bool erroField;
        
        private bool erroPersonalizadoField;
        
        private string mensagemField;
        
        /// <remarks/>
        public MedicoIntegraGuia medicoIntegraGuia {
            get {
                return this.medicoIntegraGuiaField;
            }
            set {
                this.medicoIntegraGuiaField = value;
            }
        }
        
        /// <remarks/>
        public bool Erro {
            get {
                return this.erroField;
            }
            set {
                this.erroField = value;
            }
        }
        
        /// <remarks/>
        public bool ErroPersonalizado {
            get {
                return this.erroPersonalizadoField;
            }
            set {
                this.erroPersonalizadoField = value;
            }
        }
        
        /// <remarks/>
        public string Mensagem {
            get {
                return this.mensagemField;
            }
            set {
                this.mensagemField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class IntegraLoteRepasse {
        
        private string cNPJConvenioField;
        
        private int nroLoteField;
        
        /// <remarks/>
        public string CNPJConvenio {
            get {
                return this.cNPJConvenioField;
            }
            set {
                this.cNPJConvenioField = value;
            }
        }
        
        /// <remarks/>
        public int NroLote {
            get {
                return this.nroLoteField;
            }
            set {
                this.nroLoteField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class LoteRepasse {
        
        private int numeroLoteField;
        
        private System.DateTime dataCriacaoField;
        
        private System.Nullable<System.DateTime> dataEnvioField;
        
        private System.Nullable<System.DateTime> dataVencimentoField;
        
        private string cNPJConvenioField;
        
        private GuiaRepasseWS[] listaGuiasRepasseField;
        
        /// <remarks/>
        public int NumeroLote {
            get {
                return this.numeroLoteField;
            }
            set {
                this.numeroLoteField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime DataCriacao {
            get {
                return this.dataCriacaoField;
            }
            set {
                this.dataCriacaoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<System.DateTime> DataEnvio {
            get {
                return this.dataEnvioField;
            }
            set {
                this.dataEnvioField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<System.DateTime> DataVencimento {
            get {
                return this.dataVencimentoField;
            }
            set {
                this.dataVencimentoField = value;
            }
        }
        
        /// <remarks/>
        public string CNPJConvenio {
            get {
                return this.cNPJConvenioField;
            }
            set {
                this.cNPJConvenioField = value;
            }
        }
        
        /// <remarks/>
        public GuiaRepasseWS[] ListaGuiasRepasse {
            get {
                return this.listaGuiasRepasseField;
            }
            set {
                this.listaGuiasRepasseField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class GuiaRepasseWS {
        
        private string cPFCooperadoField;
        
        private string cNPJConvenioField;
        
        private string codGuiaPrestadorField;
        
        private System.Nullable<System.DateTime> dtInicioFaturamentoField;
        
        private System.Nullable<System.DateTime> dtFimFaturamentoField;
        
        private System.Nullable<System.DateTime> dtEmissaoField;
        
        private string codGuiaPrincipalField;
        
        private System.Nullable<System.DateTime> dtAutorizacaoGuiaField;
        
        private string codSenhaGuiaField;
        
        private System.Nullable<System.DateTime> dtValidadeSenhaField;
        
        private string codGuiaOperadoraField;
        
        private string codCarteiraPacienteField;
        
        private System.Nullable<System.DateTime> dtValidadeCarteiraField;
        
        private string nomePacienteField;
        
        private string codCartaoNacSaudeField;
        
        private System.Nullable<int> identRNField;
        
        private string nomeSolicitanteField;
        
        private string conselhoSolicitanteField;
        
        private string codNumConselhoField;
        
        private string codUFField;
        
        private string codCBOField;
        
        private string caraterAtendimentoField;
        
        private System.Nullable<System.DateTime> dtSolicitacaoField;
        
        private string descIndClinicaField;
        
        private string codigoProcSolicitadoField;
        
        private System.Nullable<int> qtdeSolicitadoField;
        
        private System.Nullable<int> qtdeAutorizadaField;
        
        private string codPrestContratadoField;
        
        private string nomePrestadorField;
        
        private string codNCNESField;
        
        private string descTipoAtendimentoField;
        
        private string descIndAcidenteField;
        
        private string descTipoConsultaField;
        
        private string motEnceramentoField;
        
        private string obsJustificativaField;
        
        private System.Nullable<decimal> vlrTotalField;
        
        private System.Nullable<decimal> vlrGlosaField;
        
        private string descTipoSaidaGuiaConsultaField;
        
        private string nroUnicooperField;
        
        private int tipoGuiaField;
        
        private string complementoField;
        
        private string descPlanoField;
        
        private System.Nullable<System.DateTime> dataInternacaoField;
        
        private System.Nullable<System.DateTime> dataAltaField;
        
        private string solicitacaoInternacaoField;
        
        private System.Nullable<System.DateTime> dataEntregaField;
        
        private string atendimentoHospitalField;
        
        private string descTipoAcomodacaoField;
        
        private System.Nullable<int> origemGuiaField;
        
        private System.Nullable<System.DateTime> dataAtendimentoField;
        
        private string cNPJFaturamentoField;
        
        private decimal valorFaturadoField;
        
        private string codRegistroField;
        
        private ParticipacaoGuiaRepasseWS participacaoGuiaField;
        
        private ProcedimentoRepasseWS[] procedimentosField;
        
        /// <remarks/>
        public string CPFCooperado {
            get {
                return this.cPFCooperadoField;
            }
            set {
                this.cPFCooperadoField = value;
            }
        }
        
        /// <remarks/>
        public string CNPJConvenio {
            get {
                return this.cNPJConvenioField;
            }
            set {
                this.cNPJConvenioField = value;
            }
        }
        
        /// <remarks/>
        public string CodGuiaPrestador {
            get {
                return this.codGuiaPrestadorField;
            }
            set {
                this.codGuiaPrestadorField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<System.DateTime> DtInicioFaturamento {
            get {
                return this.dtInicioFaturamentoField;
            }
            set {
                this.dtInicioFaturamentoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<System.DateTime> DtFimFaturamento {
            get {
                return this.dtFimFaturamentoField;
            }
            set {
                this.dtFimFaturamentoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<System.DateTime> DtEmissao {
            get {
                return this.dtEmissaoField;
            }
            set {
                this.dtEmissaoField = value;
            }
        }
        
        /// <remarks/>
        public string CodGuiaPrincipal {
            get {
                return this.codGuiaPrincipalField;
            }
            set {
                this.codGuiaPrincipalField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<System.DateTime> DtAutorizacaoGuia {
            get {
                return this.dtAutorizacaoGuiaField;
            }
            set {
                this.dtAutorizacaoGuiaField = value;
            }
        }
        
        /// <remarks/>
        public string CodSenhaGuia {
            get {
                return this.codSenhaGuiaField;
            }
            set {
                this.codSenhaGuiaField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<System.DateTime> DtValidadeSenha {
            get {
                return this.dtValidadeSenhaField;
            }
            set {
                this.dtValidadeSenhaField = value;
            }
        }
        
        /// <remarks/>
        public string CodGuiaOperadora {
            get {
                return this.codGuiaOperadoraField;
            }
            set {
                this.codGuiaOperadoraField = value;
            }
        }
        
        /// <remarks/>
        public string CodCarteiraPaciente {
            get {
                return this.codCarteiraPacienteField;
            }
            set {
                this.codCarteiraPacienteField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<System.DateTime> DtValidadeCarteira {
            get {
                return this.dtValidadeCarteiraField;
            }
            set {
                this.dtValidadeCarteiraField = value;
            }
        }
        
        /// <remarks/>
        public string NomePaciente {
            get {
                return this.nomePacienteField;
            }
            set {
                this.nomePacienteField = value;
            }
        }
        
        /// <remarks/>
        public string CodCartaoNacSaude {
            get {
                return this.codCartaoNacSaudeField;
            }
            set {
                this.codCartaoNacSaudeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<int> IdentRN {
            get {
                return this.identRNField;
            }
            set {
                this.identRNField = value;
            }
        }
        
        /// <remarks/>
        public string NomeSolicitante {
            get {
                return this.nomeSolicitanteField;
            }
            set {
                this.nomeSolicitanteField = value;
            }
        }
        
        /// <remarks/>
        public string ConselhoSolicitante {
            get {
                return this.conselhoSolicitanteField;
            }
            set {
                this.conselhoSolicitanteField = value;
            }
        }
        
        /// <remarks/>
        public string CodNumConselho {
            get {
                return this.codNumConselhoField;
            }
            set {
                this.codNumConselhoField = value;
            }
        }
        
        /// <remarks/>
        public string CodUF {
            get {
                return this.codUFField;
            }
            set {
                this.codUFField = value;
            }
        }
        
        /// <remarks/>
        public string CodCBO {
            get {
                return this.codCBOField;
            }
            set {
                this.codCBOField = value;
            }
        }
        
        /// <remarks/>
        public string CaraterAtendimento {
            get {
                return this.caraterAtendimentoField;
            }
            set {
                this.caraterAtendimentoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<System.DateTime> DtSolicitacao {
            get {
                return this.dtSolicitacaoField;
            }
            set {
                this.dtSolicitacaoField = value;
            }
        }
        
        /// <remarks/>
        public string DescIndClinica {
            get {
                return this.descIndClinicaField;
            }
            set {
                this.descIndClinicaField = value;
            }
        }
        
        /// <remarks/>
        public string CodigoProcSolicitado {
            get {
                return this.codigoProcSolicitadoField;
            }
            set {
                this.codigoProcSolicitadoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<int> QtdeSolicitado {
            get {
                return this.qtdeSolicitadoField;
            }
            set {
                this.qtdeSolicitadoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<int> QtdeAutorizada {
            get {
                return this.qtdeAutorizadaField;
            }
            set {
                this.qtdeAutorizadaField = value;
            }
        }
        
        /// <remarks/>
        public string CodPrestContratado {
            get {
                return this.codPrestContratadoField;
            }
            set {
                this.codPrestContratadoField = value;
            }
        }
        
        /// <remarks/>
        public string NomePrestador {
            get {
                return this.nomePrestadorField;
            }
            set {
                this.nomePrestadorField = value;
            }
        }
        
        /// <remarks/>
        public string CodNCNES {
            get {
                return this.codNCNESField;
            }
            set {
                this.codNCNESField = value;
            }
        }
        
        /// <remarks/>
        public string DescTipoAtendimento {
            get {
                return this.descTipoAtendimentoField;
            }
            set {
                this.descTipoAtendimentoField = value;
            }
        }
        
        /// <remarks/>
        public string DescIndAcidente {
            get {
                return this.descIndAcidenteField;
            }
            set {
                this.descIndAcidenteField = value;
            }
        }
        
        /// <remarks/>
        public string DescTipoConsulta {
            get {
                return this.descTipoConsultaField;
            }
            set {
                this.descTipoConsultaField = value;
            }
        }
        
        /// <remarks/>
        public string MotEnceramento {
            get {
                return this.motEnceramentoField;
            }
            set {
                this.motEnceramentoField = value;
            }
        }
        
        /// <remarks/>
        public string ObsJustificativa {
            get {
                return this.obsJustificativaField;
            }
            set {
                this.obsJustificativaField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<decimal> VlrTotal {
            get {
                return this.vlrTotalField;
            }
            set {
                this.vlrTotalField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<decimal> VlrGlosa {
            get {
                return this.vlrGlosaField;
            }
            set {
                this.vlrGlosaField = value;
            }
        }
        
        /// <remarks/>
        public string DescTipoSaidaGuiaConsulta {
            get {
                return this.descTipoSaidaGuiaConsultaField;
            }
            set {
                this.descTipoSaidaGuiaConsultaField = value;
            }
        }
        
        /// <remarks/>
        public string NroUnicooper {
            get {
                return this.nroUnicooperField;
            }
            set {
                this.nroUnicooperField = value;
            }
        }
        
        /// <remarks/>
        public int TipoGuia {
            get {
                return this.tipoGuiaField;
            }
            set {
                this.tipoGuiaField = value;
            }
        }
        
        /// <remarks/>
        public string Complemento {
            get {
                return this.complementoField;
            }
            set {
                this.complementoField = value;
            }
        }
        
        /// <remarks/>
        public string DescPlano {
            get {
                return this.descPlanoField;
            }
            set {
                this.descPlanoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<System.DateTime> DataInternacao {
            get {
                return this.dataInternacaoField;
            }
            set {
                this.dataInternacaoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<System.DateTime> DataAlta {
            get {
                return this.dataAltaField;
            }
            set {
                this.dataAltaField = value;
            }
        }
        
        /// <remarks/>
        public string SolicitacaoInternacao {
            get {
                return this.solicitacaoInternacaoField;
            }
            set {
                this.solicitacaoInternacaoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<System.DateTime> DataEntrega {
            get {
                return this.dataEntregaField;
            }
            set {
                this.dataEntregaField = value;
            }
        }
        
        /// <remarks/>
        public string AtendimentoHospital {
            get {
                return this.atendimentoHospitalField;
            }
            set {
                this.atendimentoHospitalField = value;
            }
        }
        
        /// <remarks/>
        public string DescTipoAcomodacao {
            get {
                return this.descTipoAcomodacaoField;
            }
            set {
                this.descTipoAcomodacaoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<int> OrigemGuia {
            get {
                return this.origemGuiaField;
            }
            set {
                this.origemGuiaField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<System.DateTime> DataAtendimento {
            get {
                return this.dataAtendimentoField;
            }
            set {
                this.dataAtendimentoField = value;
            }
        }
        
        /// <remarks/>
        public string CNPJFaturamento {
            get {
                return this.cNPJFaturamentoField;
            }
            set {
                this.cNPJFaturamentoField = value;
            }
        }
        
        /// <remarks/>
        public decimal ValorFaturado {
            get {
                return this.valorFaturadoField;
            }
            set {
                this.valorFaturadoField = value;
            }
        }
        
        /// <remarks/>
        public string CodRegistro {
            get {
                return this.codRegistroField;
            }
            set {
                this.codRegistroField = value;
            }
        }
        
        /// <remarks/>
        public ParticipacaoGuiaRepasseWS ParticipacaoGuia {
            get {
                return this.participacaoGuiaField;
            }
            set {
                this.participacaoGuiaField = value;
            }
        }
        
        /// <remarks/>
        public ProcedimentoRepasseWS[] Procedimentos {
            get {
                return this.procedimentosField;
            }
            set {
                this.procedimentosField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class ParticipacaoGuiaRepasseWS {
        
        private string codigoGuiaAtendimentoField;
        
        private System.Nullable<int> seqProcedimentoField;
        
        private string codGrauPartField;
        
        private string codCBOField;
        
        private System.Nullable<System.DateTime> dtRealizacaoProcSerieField;
        
        private MedicoRepasse medicoRepasseField;
        
        /// <remarks/>
        public string CodigoGuiaAtendimento {
            get {
                return this.codigoGuiaAtendimentoField;
            }
            set {
                this.codigoGuiaAtendimentoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<int> SeqProcedimento {
            get {
                return this.seqProcedimentoField;
            }
            set {
                this.seqProcedimentoField = value;
            }
        }
        
        /// <remarks/>
        public string CodGrauPart {
            get {
                return this.codGrauPartField;
            }
            set {
                this.codGrauPartField = value;
            }
        }
        
        /// <remarks/>
        public string CodCBO {
            get {
                return this.codCBOField;
            }
            set {
                this.codCBOField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<System.DateTime> DtRealizacaoProcSerie {
            get {
                return this.dtRealizacaoProcSerieField;
            }
            set {
                this.dtRealizacaoProcSerieField = value;
            }
        }
        
        /// <remarks/>
        public MedicoRepasse MedicoRepasse {
            get {
                return this.medicoRepasseField;
            }
            set {
                this.medicoRepasseField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class MedicoRepasse {
        
        private int codigoField;
        
        private string cRMField;
        
        private string cPFField;
        
        private string nomeField;
        
        private string emailField;
        
        private string agenciaMedicoField;
        
        private string bancoMedicoField;
        
        private string contaMedicoField;
        
        private StatusCooperado statusCooperadoField;
        
        /// <remarks/>
        public int Codigo {
            get {
                return this.codigoField;
            }
            set {
                this.codigoField = value;
            }
        }
        
        /// <remarks/>
        public string CRM {
            get {
                return this.cRMField;
            }
            set {
                this.cRMField = value;
            }
        }
        
        /// <remarks/>
        public string CPF {
            get {
                return this.cPFField;
            }
            set {
                this.cPFField = value;
            }
        }
        
        /// <remarks/>
        public string Nome {
            get {
                return this.nomeField;
            }
            set {
                this.nomeField = value;
            }
        }
        
        /// <remarks/>
        public string Email {
            get {
                return this.emailField;
            }
            set {
                this.emailField = value;
            }
        }
        
        /// <remarks/>
        public string AgenciaMedico {
            get {
                return this.agenciaMedicoField;
            }
            set {
                this.agenciaMedicoField = value;
            }
        }
        
        /// <remarks/>
        public string BancoMedico {
            get {
                return this.bancoMedicoField;
            }
            set {
                this.bancoMedicoField = value;
            }
        }
        
        /// <remarks/>
        public string ContaMedico {
            get {
                return this.contaMedicoField;
            }
            set {
                this.contaMedicoField = value;
            }
        }
        
        /// <remarks/>
        public StatusCooperado statusCooperado {
            get {
                return this.statusCooperadoField;
            }
            set {
                this.statusCooperadoField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class ProcedimentoRepasseWS {
        
        private decimal taxaAdmField;
        
        private string codigoGuiaAtendimentoField;
        
        private int seqProcedimentoField;
        
        private System.Nullable<System.DateTime> dtRealizacaoField;
        
        private string hrIniRealizacaoField;
        
        private string hrFimRealizacaoField;
        
        private string codProcedimentoField;
        
        private string descProcedimentoField;
        
        private System.Nullable<int> qtdeRealizadaField;
        
        private string codViaAcessoField;
        
        private string codTecUtilizadaField;
        
        private System.Nullable<decimal> percFatorField;
        
        private System.Nullable<decimal> vlrUnitField;
        
        private System.Nullable<decimal> vlrTotalField;
        
        private string descTipoProcedimentoField;
        
        private string codTabANSField;
        
        private System.Nullable<decimal> percFatorFaturamentoField;
        
        private bool urgenciaField;
        
        private decimal valorFaturadoField;
        
        /// <remarks/>
        public decimal TaxaAdm {
            get {
                return this.taxaAdmField;
            }
            set {
                this.taxaAdmField = value;
            }
        }
        
        /// <remarks/>
        public string CodigoGuiaAtendimento {
            get {
                return this.codigoGuiaAtendimentoField;
            }
            set {
                this.codigoGuiaAtendimentoField = value;
            }
        }
        
        /// <remarks/>
        public int SeqProcedimento {
            get {
                return this.seqProcedimentoField;
            }
            set {
                this.seqProcedimentoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<System.DateTime> DtRealizacao {
            get {
                return this.dtRealizacaoField;
            }
            set {
                this.dtRealizacaoField = value;
            }
        }
        
        /// <remarks/>
        public string HrIniRealizacao {
            get {
                return this.hrIniRealizacaoField;
            }
            set {
                this.hrIniRealizacaoField = value;
            }
        }
        
        /// <remarks/>
        public string HrFimRealizacao {
            get {
                return this.hrFimRealizacaoField;
            }
            set {
                this.hrFimRealizacaoField = value;
            }
        }
        
        /// <remarks/>
        public string CodProcedimento {
            get {
                return this.codProcedimentoField;
            }
            set {
                this.codProcedimentoField = value;
            }
        }
        
        /// <remarks/>
        public string DescProcedimento {
            get {
                return this.descProcedimentoField;
            }
            set {
                this.descProcedimentoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<int> QtdeRealizada {
            get {
                return this.qtdeRealizadaField;
            }
            set {
                this.qtdeRealizadaField = value;
            }
        }
        
        /// <remarks/>
        public string CodViaAcesso {
            get {
                return this.codViaAcessoField;
            }
            set {
                this.codViaAcessoField = value;
            }
        }
        
        /// <remarks/>
        public string CodTecUtilizada {
            get {
                return this.codTecUtilizadaField;
            }
            set {
                this.codTecUtilizadaField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<decimal> PercFator {
            get {
                return this.percFatorField;
            }
            set {
                this.percFatorField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<decimal> VlrUnit {
            get {
                return this.vlrUnitField;
            }
            set {
                this.vlrUnitField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<decimal> VlrTotal {
            get {
                return this.vlrTotalField;
            }
            set {
                this.vlrTotalField = value;
            }
        }
        
        /// <remarks/>
        public string DescTipoProcedimento {
            get {
                return this.descTipoProcedimentoField;
            }
            set {
                this.descTipoProcedimentoField = value;
            }
        }
        
        /// <remarks/>
        public string CodTabANS {
            get {
                return this.codTabANSField;
            }
            set {
                this.codTabANSField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<decimal> PercFatorFaturamento {
            get {
                return this.percFatorFaturamentoField;
            }
            set {
                this.percFatorFaturamentoField = value;
            }
        }
        
        /// <remarks/>
        public bool Urgencia {
            get {
                return this.urgenciaField;
            }
            set {
                this.urgenciaField = value;
            }
        }
        
        /// <remarks/>
        public decimal ValorFaturado {
            get {
                return this.valorFaturadoField;
            }
            set {
                this.valorFaturadoField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class RetornoGetGuiaLoteRepasse {
        
        private LoteRepasse loteRepasseField;
        
        private bool erroField;
        
        private bool erroPersonalizadoField;
        
        private string mensagemField;
        
        /// <remarks/>
        public LoteRepasse loteRepasse {
            get {
                return this.loteRepasseField;
            }
            set {
                this.loteRepasseField = value;
            }
        }
        
        /// <remarks/>
        public bool Erro {
            get {
                return this.erroField;
            }
            set {
                this.erroField = value;
            }
        }
        
        /// <remarks/>
        public bool ErroPersonalizado {
            get {
                return this.erroPersonalizadoField;
            }
            set {
                this.erroPersonalizadoField = value;
            }
        }
        
        /// <remarks/>
        public string Mensagem {
            get {
                return this.mensagemField;
            }
            set {
                this.mensagemField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class IntegraGuiaRepasse {
        
        private string cNPJConvenioField;
        
        private string nroUnicooperField;
        
        /// <remarks/>
        public string CNPJConvenio {
            get {
                return this.cNPJConvenioField;
            }
            set {
                this.cNPJConvenioField = value;
            }
        }
        
        /// <remarks/>
        public string NroUnicooper {
            get {
                return this.nroUnicooperField;
            }
            set {
                this.nroUnicooperField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class EmpresaMedicoRepasse {
        
        private string cNPJField;
        
        private string razaoSocialField;
        
        private string agenciaEmpresaField;
        
        private string bancoEmpresaField;
        
        private string contaEmpresaField;
        
        /// <remarks/>
        public string CNPJ {
            get {
                return this.cNPJField;
            }
            set {
                this.cNPJField = value;
            }
        }
        
        /// <remarks/>
        public string RazaoSocial {
            get {
                return this.razaoSocialField;
            }
            set {
                this.razaoSocialField = value;
            }
        }
        
        /// <remarks/>
        public string AgenciaEmpresa {
            get {
                return this.agenciaEmpresaField;
            }
            set {
                this.agenciaEmpresaField = value;
            }
        }
        
        /// <remarks/>
        public string BancoEmpresa {
            get {
                return this.bancoEmpresaField;
            }
            set {
                this.bancoEmpresaField = value;
            }
        }
        
        /// <remarks/>
        public string ContaEmpresa {
            get {
                return this.contaEmpresaField;
            }
            set {
                this.contaEmpresaField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class ProcedimentosConvenioRepasse {
        
        private string codigoField;
        
        private string descricaoField;
        
        /// <remarks/>
        public string Codigo {
            get {
                return this.codigoField;
            }
            set {
                this.codigoField = value;
            }
        }
        
        /// <remarks/>
        public string Descricao {
            get {
                return this.descricaoField;
            }
            set {
                this.descricaoField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class ConvenioRepasse {
        
        private int codigoField;
        
        private string razaoSocialField;
        
        private string cNPJConvenioField;
        
        private string codANSField;
        
        /// <remarks/>
        public int Codigo {
            get {
                return this.codigoField;
            }
            set {
                this.codigoField = value;
            }
        }
        
        /// <remarks/>
        public string RazaoSocial {
            get {
                return this.razaoSocialField;
            }
            set {
                this.razaoSocialField = value;
            }
        }
        
        /// <remarks/>
        public string CNPJConvenio {
            get {
                return this.cNPJConvenioField;
            }
            set {
                this.cNPJConvenioField = value;
            }
        }
        
        /// <remarks/>
        public string CodANS {
            get {
                return this.codANSField;
            }
            set {
                this.codANSField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class IntegracoesGuiasRepasseWS {
        
        private bool marcarIntegracaoField;
        
        private string[] nroGuiasField;
        
        /// <remarks/>
        public bool MarcarIntegracao {
            get {
                return this.marcarIntegracaoField;
            }
            set {
                this.marcarIntegracaoField = value;
            }
        }
        
        /// <remarks/>
        public string[] NroGuias {
            get {
                return this.nroGuiasField;
            }
            set {
                this.nroGuiasField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class BuscaAvancadaRepasse {
        
        private int[] numerosLotesField;
        
        private string cNPJConvenioField;
        
        /// <remarks/>
        public int[] NumerosLotes {
            get {
                return this.numerosLotesField;
            }
            set {
                this.numerosLotesField = value;
            }
        }
        
        /// <remarks/>
        public string CNPJConvenio {
            get {
                return this.cNPJConvenioField;
            }
            set {
                this.cNPJConvenioField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class LoteBasicoWS {
        
        private int numeroLoteField;
        
        private System.DateTime dataEmissaoField;
        
        private decimal valorField;
        
        private GuiaRepasseWS[] listaGuiasRepasseField;
        
        /// <remarks/>
        public int NumeroLote {
            get {
                return this.numeroLoteField;
            }
            set {
                this.numeroLoteField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime DataEmissao {
            get {
                return this.dataEmissaoField;
            }
            set {
                this.dataEmissaoField = value;
            }
        }
        
        /// <remarks/>
        public decimal Valor {
            get {
                return this.valorField;
            }
            set {
                this.valorField = value;
            }
        }
        
        /// <remarks/>
        public GuiaRepasseWS[] ListaGuiasRepasse {
            get {
                return this.listaGuiasRepasseField;
            }
            set {
                this.listaGuiasRepasseField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ResultPesquisaLote))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class RepasseRetornoWS {
        
        private bool erroField;
        
        private string mensagemField;
        
        /// <remarks/>
        public bool Erro {
            get {
                return this.erroField;
            }
            set {
                this.erroField = value;
            }
        }
        
        /// <remarks/>
        public string Mensagem {
            get {
                return this.mensagemField;
            }
            set {
                this.mensagemField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class ResultPesquisaLote : RepasseRetornoWS {
        
        private LoteBasicoWS[] listaLotesBasicField;
        
        /// <remarks/>
        public LoteBasicoWS[] ListaLotesBasic {
            get {
                return this.listaLotesBasicField;
            }
            set {
                this.listaLotesBasicField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void BuscaBasicaLotesCompletedEventHandler(object sender, BuscaBasicaLotesCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class BuscaBasicaLotesCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal BuscaBasicaLotesCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public ResultPesquisaLote Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((ResultPesquisaLote)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void BuscaAvancadaLotesCompletedEventHandler(object sender, BuscaAvancadaLotesCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class BuscaAvancadaLotesCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal BuscaAvancadaLotesCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public ResultPesquisaLote Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((ResultPesquisaLote)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void IntegracaoGuiaRepasseCompletedEventHandler(object sender, IntegracaoGuiaRepasseCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class IntegracaoGuiaRepasseCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal IntegracaoGuiaRepasseCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public RepasseRetornoWS Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((RepasseRetornoWS)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void GetMedicosCompletedEventHandler(object sender, GetMedicosCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetMedicosCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetMedicosCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public MedicoRepasse[] Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((MedicoRepasse[])(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void GetConveniosCompletedEventHandler(object sender, GetConveniosCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetConveniosCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetConveniosCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public ConvenioRepasse[] Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((ConvenioRepasse[])(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void GetProcedimentosConveniosCompletedEventHandler(object sender, GetProcedimentosConveniosCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetProcedimentosConveniosCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetProcedimentosConveniosCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public ProcedimentosConvenioRepasse[] Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((ProcedimentosConvenioRepasse[])(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void GetEmpresasMedicoCompletedEventHandler(object sender, GetEmpresasMedicoCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetEmpresasMedicoCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetEmpresasMedicoCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public EmpresaMedicoRepasse[] Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((EmpresaMedicoRepasse[])(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void GetGuiaLoteRepasseCompletedEventHandler(object sender, GetGuiaLoteRepasseCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetGuiaLoteRepasseCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetGuiaLoteRepasseCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public RetornoGetGuiaLoteRepasse Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((RetornoGetGuiaLoteRepasse)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void GetLoteRepasseCompletedEventHandler(object sender, GetLoteRepasseCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetLoteRepasseCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetLoteRepasseCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public RetornoGetGuiaLoteRepasse Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((RetornoGetGuiaLoteRepasse)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void GetMedicoIntegraGuiaCompletedEventHandler(object sender, GetMedicoIntegraGuiaCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetMedicoIntegraGuiaCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetMedicoIntegraGuiaCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public RetornoMedicoIntegraGuia Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((RetornoMedicoIntegraGuia)(this.results[0]));
            }
        }
    }
}

#pragma warning restore 1591
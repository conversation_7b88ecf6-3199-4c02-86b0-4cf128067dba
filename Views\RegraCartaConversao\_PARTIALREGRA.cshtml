﻿@using RepasseConvenio.Models
@model List<RegraCartaConversaoCondicaoModel>

@foreach (RegraCartaConversaoCondicaoModel regraCartaConversaoCondicaoModel in Model)
{
  if (!string.IsNullOrEmpty(regraCartaConversaoCondicaoModel.Conteudo))
  {
    <span class="info-box-icon bg-info Condicao UnSelected all" data-sequenciaitem="@regraCartaConversaoCondicaoModel.Sequencia">
      <span class="info-box-text">@regraCartaConversaoCondicaoModel.RegraCartaConversaoCampoSelect.text</span>
      <span class="info-box-text">@regraCartaConversaoCondicaoModel.RegraCartaConversaoAcaoSelect.text</span>
      <span class="info-box-text">@regraCartaConversaoCondicaoModel.Conteudo</span>
    </span>
  }
  else if (regraCartaConversaoCondicaoModel.operadorLogicoEnum != 0)
  {
    <span class="info-box-icon bg-info OperadorLogico UnSelected all" data-sequenciaitem="@regraCartaConversaoCondicaoModel.Sequencia" data-enum="@Convert.ToInt32(regraCartaConversaoCondicaoModel.operadorLogicoEnum)"><span class="info-box-text">@RepasseConvenio.Infrastructure.Helpers.EnumHelper.Description(regraCartaConversaoCondicaoModel.operadorLogicoEnum)</span></span>
  }
  else if (regraCartaConversaoCondicaoModel.parentesesEnum != 0)
  {
    <span class="info-box-icon bg-info Parenteses UnSelected all" data-sequenciaitem="@regraCartaConversaoCondicaoModel.Sequencia" data-enum="@Convert.ToInt32(regraCartaConversaoCondicaoModel.parentesesEnum)"><span class="info-box-text">@RepasseConvenio.Infrastructure.Helpers.EnumHelper.Description(regraCartaConversaoCondicaoModel.parentesesEnum)</span></span>
  }
}


﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.WSPortalCooperado;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Web;

namespace RepasseConvenio.Models
{
  public class PartGuiaAtendimentoModel
  {
    public int Codigo { get; set; }
    public int CodigoGuiaAtendimento { get; set; }
    public int? SeqProcedimento { get; set; }
    public string CodGrauPart { get; set; }
    public int CodigoMedico { get; set; }
    public string CodCBO { get; set; }
    public DateTime? DtRealizacaoProcSerie { get; set; }

  }
  public static class PartGuiaAtendimentoModelConversions
  {
    public static R_PartGuiaAtendimento ToPartGuiaAtendimentoCreate(this PartGuiaAtendimentoModel model)
    {
      R_PartGuiaAtendimento entity = new R_PartGuiaAtendimento();

      entity.PGA_Id = model.Codigo;
      entity.PGA_IdGuiaAtendimento = model.CodigoGuiaAtendimento;
      entity.PGA_SeqProcedimento = model.SeqProcedimento;
      entity.PGA_CodGrauPart = model.CodGrauPart;
      entity.PGA_IdMedico = model.CodigoMedico;
      entity.PGA_CodCBO = model.CodCBO;
      entity.PGA_DtRealizacaoProcSerie = model.DtRealizacaoProcSerie;

      return entity;
    }

    public static R_PartGuiaAtendimento ToPartGuiaAtendimentoCreate(this ParticipacaoGuiaRepasseWS model
                                                                   , int IdGuiaAtendimento
                                                                   , int IdMedico)
    {
      R_PartGuiaAtendimento entity = new R_PartGuiaAtendimento();
      entity.PGA_IdGuiaAtendimento = IdGuiaAtendimento;
      entity.PGA_SeqProcedimento = model.SeqProcedimento;
      entity.PGA_CodGrauPart = model.CodGrauPart;
      entity.PGA_IdMedico = IdMedico;
      entity.PGA_CodCBO = model.CodCBO;
      entity.PGA_DtRealizacaoProcSerie = model.DtRealizacaoProcSerie;

      return entity;
    }

    public static R_PartGuiaAtendimento ToPartGuiaAtendimentoEdit(this PartGuiaAtendimentoModel model, R_PartGuiaAtendimento entity)
    {
      entity.PGA_IdGuiaAtendimento = model.CodigoGuiaAtendimento;
      entity.PGA_SeqProcedimento = model.SeqProcedimento;
      entity.PGA_CodGrauPart = model.CodGrauPart;
      entity.PGA_IdMedico = model.CodigoMedico;
      entity.PGA_CodCBO = model.CodCBO;
      entity.PGA_DtRealizacaoProcSerie = model.DtRealizacaoProcSerie;

      return entity;
    }

    public static PartGuiaAtendimentoModel ToPartGuiaAtendimentoModel(this R_PartGuiaAtendimento entity)
    {
      PartGuiaAtendimentoModel model = new PartGuiaAtendimentoModel();
      
      model.Codigo = entity.PGA_Id;
      model.CodigoGuiaAtendimento = entity.PGA_IdGuiaAtendimento;
      model.SeqProcedimento = entity.PGA_SeqProcedimento;
      model.CodGrauPart = entity.PGA_CodGrauPart;
      model.CodigoMedico = entity.PGA_IdMedico;
      model.CodCBO = entity.PGA_CodCBO;
      model.DtRealizacaoProcSerie = entity.PGA_DtRealizacaoProcSerie;

      return model;
    }
  }
}
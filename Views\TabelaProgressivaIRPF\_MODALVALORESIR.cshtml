﻿@using RepasseConvenio.Infrastructure
  @using RepasseConvenio.Infrastructure.Controls
  @using RepasseConvenio.Models
  @model ValoresTabelaProgressivaIRPFModel
  <div class="modal fade" id="modalvaloresir" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog" style="max-width: 50%;">
      <div class="modal-content">
        <div class="modal-header">
          <h4 class="modal-title">Novo Valor Tabela IRPF</h4>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">×</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-md-12 container">
              @using (Html.BeginForm("CreateIR", "ValoresTabelaProgressivaIRPF", FormMethod.Post, new { id = "FormValoresIR", @style = "" }))
              {
                @Html.AntiForgeryToken()
                @Html.HiddenFor(model => model.Codigo)
                @Html.HiddenFor(model => model.CodigoTabela)
                <div class="box box-primary">
                  <div class="box-body">
                    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
                    <div class="row">
                      <div class="col-md-4">
                        <div class="form-group">
                          @Html.LabelFor(m => m.ValorInicialText)
                          @Html.LibEditorFor(m => m.ValorInicialText, new { @class = "form-control money" })
                          @Html.ValidationMessageFor(m => m.ValorInicialText, "", new { @class = "text-danger" })
                        </div>
                      </div>
                      <div class="col-md-4">
                        <div class="form-group">
                          @Html.LabelFor(m => m.ValorFinalText)
                          @Html.LibEditorFor(m => m.ValorFinalText, new { @class = "form-control money" })
                          @Html.ValidationMessageFor(m => m.ValorFinalText, "", new { @class = "text-danger" })
                        </div>
                      </div>
                      <div class="col-md-4">
                        <div class="form-group">
                          @Html.LabelFor(m => m.AliquotaProgressiva)
                          @Html.LibEditorFor(m => m.AliquotaProgressiva, new { @class = "form-control perAcresc" })
                          @Html.ValidationMessageFor(m => m.AliquotaProgressiva, "", new { @class = "text-danger" })
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="box-footer">
                    <button type="button" class="btn btn-outline-dark pull-left" data-dismiss="modal">Cancelar</button>

                    @if (Model.Codigo == 0)
                    {
                      <button type="button" value="Create" class="btn btn-info pull-rigth" id="btnCreateIR">Salvar</button>
                    }
                    else
                    {
                      <button type="button" value="Create" class="btn btn-info pull-rigth" id="btnEditIR">Salvar</button>
                    }

                  </div>
                </div>
              }
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>






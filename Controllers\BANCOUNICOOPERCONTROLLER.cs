﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Controls;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace RepasseConvenio.Controllers
{

  [Security("23E205BC-6A0E-44EE-AAA2-919C8FF782C3")]
  public class BancoUnicooperController : LibController
  {

    private BancoUnicooperService BancoUnicooperService
    {
      get
      {
        if (_BancoUnicooperService == null)
          _BancoUnicooperService = new BancoUnicooperService(ContextoUsuario.UserLogged);

        return _BancoUnicooperService;
      }
    }

    private BancoUnicooperService _BancoUnicooperService;




    // GET: BancoUnicooper
    [Security("1ADD7DA2-AF76-4148-905A-80C257434BE5")]
    public ActionResult Index(int? page, string filtro)
    {
      try
      {
        int pageNumber = page ?? 1;
        ViewBag.page = pageNumber;
        ViewBag.filtro = filtro;

        var list = BancoUnicooperService.Get(pageNumber, filtro);
        return View(list);
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return View();
      }
    }

    [HttpGet]
    [Security("CBC72FD3-BDEB-4D7F-A457-2B63FE9B25BF")]
    public ActionResult Create()
    {
      BancoUnicooperModel bancoUnicooperModel = new BancoUnicooperModel();
      return View(bancoUnicooperModel);
    }

    [HttpPost]
    [Security("CBC72FD3-BDEB-4D7F-A457-2B63FE9B25BF")]
    public ActionResult Create(BancoUnicooperModel bancoUnicooperModel)
    {

      try
      {
        BancoUnicooperService.Create(bancoUnicooperModel);
        return RedirectToAction("Index");
      }

      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return View();
      }



    }

    [HttpGet]
    [Security("DE3D14E9-355C-4B76-A98E-E203F50C68F1")]
    public ActionResult Edit(int codigo)
    {
      BancoUnicooperModel model = BancoUnicooperService.GetEdit(codigo);
      return View(model);
    }


    [HttpPost]
    [Security("DE3D14E9-355C-4B76-A98E-E203F50C68F1")]
    public ActionResult Edit(BancoUnicooperModel model)
    {
      try
      {
        BancoUnicooperService.Edit(model);
        return RedirectToAction("Index");
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return View(model);
      }

    }

    [Security("3CF6F17C-3185-4794-88B9-8A640BBE5CCB")]
    public ActionResult Delete(int codigo)
    {
      try
      {
        BancoUnicooperService.Delete(codigo);

        MessageListToast.Add(new Message(MessageType.Success, LibMessageToast.MessageDeleteSucess));
        return RedirectToAction("Index", "BancoUnicooper");
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return RedirectToAction("Index", "BancoUnicooper");
      }
    }

    [HttpGet]
    public ActionResult GetBancoSelect(string term)
    {
      BancoServices BancoServices = new BancoServices();
      List<Select2Model> Lista = new List<Select2Model>();
      Lista = BancoServices.GetByTerm(term).Select(list => list.ToSelect2Model()).ToList();
      return Json(new { items = Lista }, JsonRequestBehavior.AllowGet);
    }



  }
}
﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using PagedList;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RepasseConvenio.Services
{
  public class SituacaoItensLoteServices : ServiceBase
  {
    public SituacaoItensLoteServices(RepasseEntities Contexto)
        : base(Contexto)
    { }
    public SituacaoItensLoteServices()
       : base()
    { }

    public SituacaoItensLoteServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public SituacaoItensLoteServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public int GetIdByEnum(SituacaoItensLoteEnum situacaoItensLoteEnum)
    {
      return Contexto.R_SituacaoItensLote.Where(a => a.SIL_Enum == (int)situacaoItensLoteEnum).Select(a => a.SIL_Id).FirstOrDefault();
    }
    public R_SituacaoItensLote GetByEnum(SituacaoItensLoteEnum situacaoItensLoteEnum)
    {
      return Contexto.R_SituacaoItensLote.Where(a => a.SIL_Enum == (int)situacaoItensLoteEnum).FirstOrDefault();
    }

    public R_SituacaoItensLote GetById(int Id)
    {
      return Contexto.R_SituacaoItensLote.Where(a => a.SIL_Id == Id).FirstOrDefault();
    }

    public List<R_SituacaoItensLote> Getall()
    {
      string query = @"select * from R_SituacaoItensLote";
      return Contexto.Database.SqlQuery<R_SituacaoItensLote>(query).ToList();
    }

  }
}


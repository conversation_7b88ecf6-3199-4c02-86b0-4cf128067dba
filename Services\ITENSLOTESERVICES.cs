﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Models;
using RepasseConvenio.WSPortalCooperado;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.Entity;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Text;
using System.Transactions;
using System.Web;
using System.Data.OleDb;
using RepasseConvenio.Infrastructure;
using Microsoft.Ajax.Utilities;
using System.Linq.Expressions;

namespace RepasseConvenio.Services
{
  public class ItensLoteServices : ServiceBase
  {
    public ItensLoteServices()
   : base()
    { }
    public ItensLoteServices(RepasseEntities Contexto)
       : base(Contexto)
    { }

    public ItensLoteServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public ItensLoteServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public void IntegraItensLotes(int IdLote, int IdGuiaAtendimento, string NumeroGuia)
    {
      StatusItensLoteServices statusItensLoteServices = new StatusItensLoteServices();
      SituacaoItensLoteServices situacaoItensLoteServices = new SituacaoItensLoteServices();
      int IdStatusNaoProcessado = statusItensLoteServices.GetIdByEnum(StatusItensLoteEnum.NaoProcessado);
      int IdSituacaoNaoProcessado = situacaoItensLoteServices.GetIdByEnum(SituacaoItensLoteEnum.NaoProcessada);
      R_ItensLote itensLote = new R_ItensLote();
      itensLote = itensLote.Create(IdLote, IdGuiaAtendimento, NumeroGuia, IdStatusNaoProcessado, IdSituacaoNaoProcessado);
      Create(itensLote);
    }

    public List<ItensLoteModel> GetById(int Id)
    {

      string query = @"SELECT    
                        I.IL_Id As Codigo,
                        I.IL_IdGuiaAtendimento As IdGuiaAtendimento,
                        I.IL_IdLote As IdLote,
                        SIL.SIL_Descricao As StatusLote	,
                        I.IL_NumeroGuia As NumeroGuia,
                        G.GA_DataAtendimento As DataAtendimento,
                        G.GA_VlrTotal As ValorTotal,
                        G.GA_VlrGlosa As ValorGlosado,
                        G.GA_ValorFaturado As ValorFaturado,
                        SIIL.SIL_Descricao [SituacaoLote]
                       FROM R_ItensLote I 
                       INNER JOIN R_GuiaAtendimento G ON I.IL_IdGuiaAtendimento = G.GA_Id
                       INNER JOIN R_StatusItensLote SIL ON SIL.SIL_Id = I.IL_IdStatusItensLote
                       INNER JOIN R_SituacaoItensLote SIIL ON SIIL.SIL_Id = I.IL_IdSituacaoItensLote
                       WHERE IL_IdLote = @Id";

      return Contexto.Database.SqlQuery<ItensLoteModel>(query, new SqlParameter("@Id", Id)).ToList();

    }
    public R_StatusItensLote GetByStatusId(int Id)
    {
      string query = @"SELECT
                        *
                       FROM R_StatusItensLote
                       WHERE SIL_Enum = @Id";

      return Contexto.Database.SqlQuery<R_StatusItensLote>(query, new SqlParameter("Id", Id)).FirstOrDefault();
    }

    public R_ItensLote GetItensLote(int IdGuiaAtendimento)
    {
      string query = @"select * from R_ItensLote where IL_IdGuiaAtendimento = @IdGuiaAtendimento";

      return Contexto.Database.SqlQuery<R_ItensLote>(query, new SqlParameter("@IdGuiaAtendimento", IdGuiaAtendimento)).FirstOrDefault();
    }

    public List<R_ItensLote> GetListaItensLoteByIdLote(int IdLote)
    {
      string query = @"SELECT
                        *
                       FROM R_ItensLote where IL_IdLote = @IdLote";

      return Contexto.Database.SqlQuery<R_ItensLote>(query, new SqlParameter("@IdLote", IdLote)).ToList();
    }

    public List<ItensLoteModal> GetAllByIdLote(int CodigoLote)
    {
      string query = @"SELECT
                        IL.IL_Id [Codigo],
                        IL.IL_IdLote [CodigoLote],
                        IL.IL_IdGuiaAtendimento [CodigoGuiaAtendimento],
                        IL.IL_NumeroGuia [NumeroGuia],
                        SIL.SIL_Descricao [DescricaoStatus]
                       FROM R_ItensLote IL
                       INNER JOIN R_StatusItensLote SIL ON SIL.SIL_Id = IL.IL_IdStatusItensLote AND IL.IL_IdLote = @CodigoLote";

      return Contexto.Database.SqlQuery<ItensLoteModal>(query, new SqlParameter("@CodigoLote", CodigoLote)).ToList();
    }

    public List<AtendimentoNaoPago> GetAtdNaoPagoByRepasse(int IdRepasse)
    {
      string query = @"SELECT
	                        IL.IL_Id [Codigo],
	                        L.L_NumeroLote [NroLote],
	                        IL.IL_NumeroGuia [NroGuiaAtendimento],
	                        SIL.SIL_Descricao [StatusGuia],
	                        GA.GA_ValorFaturado [ValorFaturado]
                        FROM R_ItensLote IL
                        INNER JOIN R_StatusItensLote SIL ON SIL.SIL_Id = IL.IL_IdStatusItensLote 
                        INNER JOIN R_GuiaAtendimento GA ON GA.GA_Id = IL.IL_IdGuiaAtendimento 
                        INNER JOIN R_Lote L ON L.L_Id = IL.IL_IdLote 
                        WHERE 
                        SIL.SIL_Enum IN (@NaoProcessado, @NaoPago)
                        AND IL.IL_IdLote IN (
	                        SELECT 
	                        DISTINCT L.L_Id
	                        FROM R_GuiaDemonstrativo GD
	                        INNER JOIN R_DemonstrativoConvenio DC ON DC.DC_Id = GD.GD_IdDemonstrativoConvenio AND DC.DC_IdRepasse = @IdRepasse
	                        INNER JOIN R_ItensLote IL ON IL.IL_IdGuiaAtendimento = GD.GD_IdGuia
	                        INNER JOIN R_Lote L ON IL.IL_IdLote = L.L_Id
                        )";

      return Contexto.Database.SqlQuery<AtendimentoNaoPago>(query,
        new SqlParameter("@IdRepasse", IdRepasse),
        new SqlParameter("@NaoProcessado", (int)StatusItensLoteEnum.NaoProcessado),
        new SqlParameter("@NaoPago", (int)StatusItensLoteEnum.NaoPago)
        ).ToList();
    }

    public void UpdateStatus(List<int> IdGuiasAtendimento, SituacaoItensLoteEnum situacaoItensLoteEnum)
    {
      SituacaoItensLoteServices situacaoItensLoteServices = new SituacaoItensLoteServices();
      int IdSituacao = situacaoItensLoteServices.GetIdByEnum(situacaoItensLoteEnum);

      string query = @"UPDATE R_ItensLote SET IL_IdSituacaoItensLote = @IdSituacao WHERE IL_IdGuiaAtendimento IN (#Ids#)";
      query = query.Replace("#Ids#", string.Join(",", IdGuiasAtendimento.ToArray()));
      Contexto.Database.ExecuteSqlCommand(query, new SqlParameter("@IdSituacao", IdSituacao));
    }


  }
}
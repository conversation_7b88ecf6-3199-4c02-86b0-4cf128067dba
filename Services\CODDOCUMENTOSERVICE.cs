﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using PagedList;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;
using RepasseConvenio.WSPortalCooperado;
using System.Threading;

namespace RepasseConvenio.Services
{
  public class CodDocumentoService : ServiceBase
  {
    public CodDocumentoService(RepasseEntities Contexto)
        : base(Contexto)
    { }
    public CodDocumentoService()
       : base()
    {
      if (semaphore == null)
        semaphore = new Semaphore(1, 1);
    }

    public CodDocumentoService(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public CodDocumentoService(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public string GetSequencialDocumento()
    {
      try
      {
        if (semaphore.WaitOne())
        {
          R_CodDocumento documento = Contexto.R_CodDocumento.AsNoTracking().FirstOrDefault();
          Contexto.R_CodDocumento.AsNoTracking().FirstOrDefault();

          if (documento == null)
          {
            documento = new R_CodDocumento();
            documento.CD_NumeroDocumento = 1;
            documento.CD_CodDocumento = base.GerarCodigo(documento.CD_NumeroDocumento, PrefixoDocumento);
            Create(documento);
          }

          string CodigoDocumento = documento.CD_CodDocumento;

          documento.CD_NumeroDocumento += 1;
          documento.CD_CodDocumento = base.GerarCodigo(documento.CD_NumeroDocumento, PrefixoDocumento);
          Edit(documento);
          return CodigoDocumento;
        }
        else
          throw new Exception("Falha ao gerar codigo documento");
      }
      catch (Exception ex)
      {
        throw ex;
      }
      finally
      {
        semaphore.Release();
      }
    }


  }
}


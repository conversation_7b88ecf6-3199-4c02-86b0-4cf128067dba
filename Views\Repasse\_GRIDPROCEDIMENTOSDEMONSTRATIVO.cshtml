﻿@using RepasseConvenio.Models
@using System.Globalization;
@model List<ProcGuiaDemonstrativoModal>

<div class="row RowModalProcDemonstrativo">
  <div class="col-md-12 table-responsive p-0 " style="overflow: auto; height: calc(100vh - 170px)">
    <table class="table table-sm table-striped table-hover text-nowrap">
      <thead>
        <tr >
          <th style="width: 50pt;">
          </th>
          <th style="width: 54pt;">
            Guia
          </th>
          <th style="width: 77pt;">
            Carteirinha
          </th>
          <th style="width: 115pt;">
            Nro. Atendimento
          </th>
          <th style="width: 55pt;">
            Data
          </th>
          <th style="width: 310pt;">
            Procedimento
          </th>
          <th>
            Faturado
          </th>
          <th>
            Apresentado
          </th>
          <th>
            Glosado
          </th>
          <th>
            Pago
          </th>

        </tr>
      </thead>
      <tbody>
        @foreach (ProcGuiaDemonstrativoModal item in Model)
        {
        <tr>
          <td>
            <a class="btn btn-outline-warning btn-circle btn-sm VisualizarGuiaAtendimento" data-codigoguiaatendimento="@item.CodigoGuia" title="Visualizar">
              Visualizar
            </a>
          </td>
          <td>
            @item.NumeroGuia
          </td>
          <td>
            @item.NumeroCarteirinha
          </td>
          <td>
            @item.NumeroAtendimento
          </td>
          <td>
            @item.DataAtendimento.ToString("dd/MM/yyyy")
          </td>
          <td>
            @item.Procedimento
          </td>
          <td>
            @item.ValorFaturado.ToString("C2", CultureInfo.CreateSpecificCulture("pt-BR"))
          </td>
          <td>
            @item.ValorApresentado.ToString("C2", CultureInfo.CreateSpecificCulture("pt-BR"))
          </td>
          <td>
            @item.ValorGlosado.ToString("C2", CultureInfo.CreateSpecificCulture("pt-BR"))
          </td>
          <td>
            @item.ValorPago.ToString("C2", CultureInfo.CreateSpecificCulture("pt-BR"))
          </td>

        </tr>
        }
      </tbody>
    </table>
  </div>

</div>

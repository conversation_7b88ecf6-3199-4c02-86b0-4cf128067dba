﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using PagedList;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;
using System.Globalization;

namespace RepasseConvenio.Services
{
  public class WSRepasseServices : ServiceBase
  {
    public WSRepasseServices(RepasseEntities Contexto)
        : base(Contexto)
    { }
    public WSRepasseServices()
       : base()
    { }

    public WSRepasseServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public WSRepasseServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public string ApuracaoProlaboreDiretores()
    {
      StringBuilder sb = new StringBuilder();
      sb.AppendLine(string.Format("[{0}] - Iniciando serviço de Apurar Prolabore Diretores", DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss")));

      try
      {
        MedicoService medicoService = new MedicoService(Contexto);
        ExtratoMedicoServices extratoMedicoServices = new ExtratoMedicoServices(Contexto);
        ValoresTabelaProgressivaINSSServices valoresTabelaProgressivaINSSServices = new ValoresTabelaProgressivaINSSServices(Contexto);
        List<R_Medico> ListMedicos = medicoService.GetDiretores();
        int IdClassificacaoPROLABORE = new ClassificacaoRepasseServices(Contexto).GetIdByEnum(EnumClassificacaoRepasse.PROLABORE);
        int IdClassificacaoINSS = new ClassificacaoRepasseServices(Contexto).GetIdByEnum(EnumClassificacaoRepasse.INSS);
        int IdClassificacaoIRPF = new ClassificacaoRepasseServices(Contexto).GetIdByEnum(EnumClassificacaoRepasse.IRPF);
        foreach (R_Medico medico in ListMedicos)
        {
          R_ExtratoMedico extratoMed = medico.R_ExtratoMedico.Where(a => a.EX_IdClassificacao == IdClassificacaoPROLABORE &&
                                         (a.EX_DataApuracao.Month == DateTime.Now.Month ||
                                           (a.EX_DataPagamento.HasValue && a.EX_DataPagamento.Value.Month == DateTime.Now.Month)
                                         )
                                      ).FirstOrDefault();

          if (extratoMed == null && medico.M_ValorProLabore.HasValue)
          {
            #region EXECUÇÃO APURAÇÃO POR MÉDICO

            R_ValoresTabelaProgressivaINSS valorINSS = valoresTabelaProgressivaINSSServices.GetValorTabelaProgressivaINSS(DateTime.Now, medico.M_ValorProLabore.Value);
            R_ValoresTabelaProgressivaIRPF valorIRPF = valoresTabelaProgressivaINSSServices.GetValorTabelaProgressivaIRPF(DateTime.Now, medico.M_ValorProLabore.Value);

            CodDocumentoService codDocumentoService = new CodDocumentoService();
            string CodigoDocumento = codDocumentoService.GetSequencialDocumento();

            using (var scope = new TransactionScope(TransactionScopeOption.RequiresNew))
            {
              string numMes = DateTime.Now.Month.ToString().PadLeft(2, '0');
              string descMes = new CultureInfo("pt-BR").DateTimeFormat.GetMonthName(DateTime.Now.Month);

              #region Extrato Crédito
              R_ExtratoMedico extratoMedico = new R_ExtratoMedico();
              extratoMedico.EX_DataApuracao = DateTime.Now;
              //extratoMedico.EX_DataPagamento = DateTime.Now;
              //extratoMedico.EX_CodigoRepasse = ;
              extratoMedico.EX_TipoLancamento = EnumTipoLancamento.C.ToString();
              extratoMedico.EX_IdClassificacao = IdClassificacaoPROLABORE;
              extratoMedico.EX_Valor = medico.M_ValorProLabore.Value;
              //extratoMedico.EX_IdConvenio = ;
              extratoMedico.EX_TipoConta = TipoPessoaEnum.PF.ToString();
              extratoMedico.EX_CPFCNPJDeposito = medico.M_CPF;
              extratoMedico.EX_IdMedico = medico.M_Id;
              extratoMedico.Observacao = $"Extrato crédito criado referente ao mês {numMes} - {descMes}";
              extratoMedico.EX_CodigoDocumento = CodigoDocumento;
              extratoMedico.EX_DataCriacao = DateTime.Now;
              extratoMedico.EX_IdUsuarioCriacao = 4;
              //extratoMedico.EX_IdRegistroPagamento = ;
              Create(extratoMedico);
              #endregion

              #region Extrato Débito INSS
              if (valorINSS != null && valorINSS.V_AliquotaProgressiva > 0)
              {
                R_ExtratoMedico extratoDebitoINSS = new R_ExtratoMedico();
                extratoDebitoINSS.EX_DataCriacao = DateTime.Now;
                extratoDebitoINSS.EX_DataApuracao = DateTime.Now;
                //extratoDebitoINSS.EX_DataPagamento = DateTime.Now;
                //extratoDebitoINSS.EX_CodigoRepasse = ;
                extratoDebitoINSS.EX_TipoLancamento = EnumTipoLancamento.D.ToString();
                extratoDebitoINSS.EX_IdClassificacao = IdClassificacaoINSS;
                extratoDebitoINSS.EX_Valor = medico.M_ValorProLabore.Value * (valorINSS.V_AliquotaProgressiva / 100);
                //extratoDebitoINSS.EX_IdConvenio = ;
                extratoDebitoINSS.EX_TipoConta = TipoPessoaEnum.PF.ToString();
                extratoDebitoINSS.EX_CPFCNPJDeposito = medico.M_CPF;
                extratoDebitoINSS.EX_IdMedico = medico.M_Id;
                extratoDebitoINSS.Observacao = $"Extrato débito criado referente ao mês {numMes} - {descMes}";
                extratoDebitoINSS.EX_CodigoDocumento = CodigoDocumento;
                extratoDebitoINSS.EX_IdUsuarioCriacao = 4;
                //extratoDebitoINSS.EX_IdRegistroPagamento = ;
                Create(extratoDebitoINSS);
              }
              #endregion

              #region Extrato Débito IRPF
              if (valorIRPF != null && valorIRPF.V_AliquotaProgressiva > 0)
              {
                R_ExtratoMedico extratoDebitoIRPF = new R_ExtratoMedico();
                extratoDebitoIRPF.EX_DataCriacao = DateTime.Now;
                extratoDebitoIRPF.EX_DataApuracao = DateTime.Now;
                //extratoDebitoIRPF.EX_DataPagamento = DateTime.Now;
                //extratoDebitoIRPF.EX_CodigoRepasse = ;
                extratoDebitoIRPF.EX_TipoLancamento = EnumTipoLancamento.D.ToString();
                extratoDebitoIRPF.EX_IdClassificacao = IdClassificacaoIRPF;
                extratoDebitoIRPF.EX_Valor = medico.M_ValorProLabore.Value * (valorIRPF.V_AliquotaProgressiva / 100);
                //extratoDebitoIRPF.EX_IdConvenio = ;
                extratoDebitoIRPF.EX_TipoConta = TipoPessoaEnum.PF.ToString();
                extratoDebitoIRPF.EX_CPFCNPJDeposito = medico.M_CPF;
                extratoDebitoIRPF.EX_IdMedico = medico.M_Id;
                extratoDebitoIRPF.Observacao = $"Extrato débito criado referente ao mês {numMes} - {descMes}";
                extratoDebitoIRPF.EX_CodigoDocumento = CodigoDocumento;
                extratoDebitoIRPF.EX_IdUsuarioCriacao = 4;
                //extratoDebitoIRPF.EX_IdRegistroPagamento = ;
                Create(extratoDebitoIRPF);
              }
              #endregion

              #region Gerando Controle Mensal
              ControleMensalService controleMensalService = new ControleMensalService();
              controleMensalService.Create();
              #endregion

              scope.Complete();
            }
            #endregion
          }
        }

        sb.AppendLine(string.Format("[{0}] - Serviço de Apurar Prolabore Diretores executado com sucesso.", DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss")));
        sb.AppendLine("==============================");
        return sb.ToString();
      }
      catch (Exception ex)
      {
        string Mensagem = ex.InnerException != null ? ex.InnerException.Message : ex.Message;
        sb.AppendLine(string.Format("[{0}] - Erro durante processamento do serviço Apurar Prolabore Diretores: {1}", DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss"), Mensagem));
        sb.AppendLine("==============================");
        return sb.ToString();
      }
    }

    public string EnviaLotesVencendo()
    {
      StringBuilder sb = new StringBuilder();
      sb.AppendLine(string.Format("[{0}] - Iniciando serviço de Enviar Lotes Glosa Vencendo", DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss")));

      try
      {
        string Query = @"SELECT 
	                        LG.LG_Codigo [Codigo]
	                        , C.C_RazaoSocial [NomeConvenio]
	                        , SG.SG_Descricao [Status]
	                        , TLG.TLG_Descricao [Tipo]
	                        , LG.LG_DataCriacao [DataCriacao]
	                        , LG.LG_DataVencimentoAnalise [DataVencimento]
                        FROM R_LoteGlosa LG
                        INNER JOIN R_Convenio C ON C.C_Id=LG.LG_IdConvenio
                        INNER JOIN R_StatusGlosa SG ON SG.SG_Id=LG.LG_IdStatusGlosa
                        INNER JOIN R_TipoLoteGLosa TLG ON TLG.TLG_Id=LG.LG_IdTipoLoteGlosa
                        WHERE 
	                        LG.LG_DataVencimentoAnalise < DATEADD(DD, 5, GETDATE()) AND SG.SG_Enum = 1";

        List<LoteGlosaVencendo> loteGlosaVencendo = Contexto.Database.SqlQuery<LoteGlosaVencendo>(Query).ToList();

        string mensagemEmail = @"Prezado cooperado, segue abaixo a lista de lotes de glosas perto da data de vencimento: <br/><br/>";

        foreach (LoteGlosaVencendo lote in loteGlosaVencendo)
        {
          mensagemEmail += string.Format("Lote {0} ({1}) - Vencimento: {2} - Tipo de Lote: {3} - Gerado: {4} <br/>"
            , lote.Codigo, lote.NomeConvenio, lote.DataVencimento.ToString("dd/MM/yyyy"), lote.Tipo, lote.DataCriacao.ToString("dd/MM/yyyy"));
        }

        EMailHelper.Send(new string[] { "<EMAIL>" }, "Lotes de Glosas Vencendo", mensagemEmail);

        sb.AppendLine(string.Format("[{0}] - Serviço de Enviar Lotes Glosa Vencendo executado com sucesso. Total de lotes encontrados: {1}", DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss"), loteGlosaVencendo.Count));
        sb.AppendLine("==============================");
        return sb.ToString();
      }
      catch (Exception ex)
      {
        string Mensagem = ex.InnerException != null ? ex.InnerException.Message : ex.Message;
        sb.AppendLine(string.Format("[{0}] - Erro durante processamento do serviço Enviar Lotes Glosa Vencendo: {1}", DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss"), Mensagem));
        sb.AppendLine("==============================");
        return sb.ToString();
      }
    }

    public string ItensRecorrentes()
    {
      StringBuilder sb = new StringBuilder();
      sb.AppendLine(string.Format("[{0}] - Iniciando serviço de Itens Recorrentes", DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss")));

      try
      {
        List<R_ItensRecorrentes> Listitens = Contexto.R_ItensRecorrentes.Where(a => a.IR_Recorrente && (a.IR_DataFim.HasValue)).ToList();

        foreach (var item in Listitens)
        {
          if (item.IR_TipoRecorrencia.Value == (int)TipoRecorrenciaEnum.Anual)
          {
            if (item.DateLastExcute.HasValue)
            {
              if (item.DateLastExcute.Value.Date <= DateTime.Now.Date.AddYears(-1))
              {
                R_ExtratoMedico extrato = new R_ExtratoMedico();

                extrato.EX_CPFCNPJDeposito = item.IR_CPFCNPJ;
                extrato.EX_IdClassificacao = item.IR_IdClassificacao;
                extrato.EX_IdMedico = item.IR_IdMedico;
                extrato.EX_TipoConta = item.IR_TipoConta;
                extrato.EX_TipoLancamento = item.IR_TipoLancamento;
                extrato.EX_Valor = item.IR_Valor;
                extrato.EX_DataApuracao = item.IR_DataRecorrencia;

                new ExtratoMedicoServices().Create(extrato);

                item.DateLastExcute = DateTime.Now;
                Edit(item);
              }
            }
            else
            {
              R_ExtratoMedico extrato = new R_ExtratoMedico();

              extrato.EX_CPFCNPJDeposito = item.IR_CPFCNPJ;
              extrato.EX_IdClassificacao = item.IR_IdClassificacao;
              extrato.EX_IdMedico = item.IR_IdMedico;
              extrato.EX_TipoConta = item.IR_TipoConta;
              extrato.EX_TipoLancamento = item.IR_TipoLancamento;
              extrato.EX_Valor = item.IR_Valor;
              extrato.EX_DataApuracao = item.IR_DataRecorrencia;

              new ExtratoMedicoServices().Create(extrato);

              item.DateLastExcute = DateTime.Now;
              Edit(item);
            }
          }
          else if (item.IR_TipoRecorrencia.Value == (int)TipoRecorrenciaEnum.Diario)
          {
            if (item.DateLastExcute.HasValue)
            {
              if (item.DateLastExcute.Value.Date <= DateTime.Now.Date.AddDays(-1))
              {
                R_ExtratoMedico extrato = new R_ExtratoMedico();

                extrato.EX_CPFCNPJDeposito = item.IR_CPFCNPJ;
                extrato.EX_IdClassificacao = item.IR_IdClassificacao;
                extrato.EX_IdMedico = item.IR_IdMedico;
                extrato.EX_TipoConta = item.IR_TipoConta;
                extrato.EX_TipoLancamento = item.IR_TipoLancamento;
                extrato.EX_Valor = item.IR_Valor;
                extrato.EX_DataApuracao = item.IR_DataRecorrencia;

                new ExtratoMedicoServices().Create(extrato);

                item.DateLastExcute = DateTime.Now;
                Edit(item);
              }
            }
            else
            {
              R_ExtratoMedico extrato = new R_ExtratoMedico();

              extrato.EX_CPFCNPJDeposito = item.IR_CPFCNPJ;
              extrato.EX_IdClassificacao = item.IR_IdClassificacao;
              extrato.EX_IdMedico = item.IR_IdMedico;
              extrato.EX_TipoConta = item.IR_TipoConta;
              extrato.EX_TipoLancamento = item.IR_TipoLancamento;
              extrato.EX_Valor = item.IR_Valor;
              extrato.EX_DataApuracao = item.IR_DataRecorrencia;


              new ExtratoMedicoServices().Create(extrato);

              item.DateLastExcute = DateTime.Now;
              Edit(item);
            }
          }
          else if (item.IR_TipoRecorrencia.Value == (int)TipoRecorrenciaEnum.Mensal)
          {
            if (item.DateLastExcute.HasValue)
            {
              if (item.DateLastExcute.Value.Date <= DateTime.Now.Date.AddMonths(-1))
              {
                R_ExtratoMedico extrato = new R_ExtratoMedico();

                extrato.EX_CPFCNPJDeposito = item.IR_CPFCNPJ;
                extrato.EX_IdClassificacao = item.IR_IdClassificacao;
                extrato.EX_IdMedico = item.IR_IdMedico;
                extrato.EX_TipoConta = item.IR_TipoConta;
                extrato.EX_TipoLancamento = item.IR_TipoLancamento;
                extrato.EX_Valor = item.IR_Valor;
                extrato.EX_DataApuracao = item.IR_DataRecorrencia;

                new ExtratoMedicoServices().Create(extrato);

                item.DateLastExcute = DateTime.Now;
                Edit(item);
              }
            }
            else
            {
              R_ExtratoMedico extrato = new R_ExtratoMedico();

              extrato.EX_CPFCNPJDeposito = item.IR_CPFCNPJ;
              extrato.EX_IdClassificacao = item.IR_IdClassificacao;
              extrato.EX_IdMedico = item.IR_IdMedico;
              extrato.EX_TipoConta = item.IR_TipoConta;
              extrato.EX_TipoLancamento = item.IR_TipoLancamento;
              extrato.EX_Valor = item.IR_Valor;
              extrato.EX_DataApuracao = item.IR_DataRecorrencia;

              new ExtratoMedicoServices().Create(extrato);

              item.DateLastExcute = DateTime.Now;
              Edit(item);
            }
          }
          else
          {
            if (item.DateLastExcute.HasValue)
            {
              if (item.DateLastExcute.Value.Date <= DateTime.Now.Date.AddDays(-7))
              {
                R_ExtratoMedico extrato = new R_ExtratoMedico();

                extrato.EX_CPFCNPJDeposito = item.IR_CPFCNPJ;
                extrato.EX_IdClassificacao = item.IR_IdClassificacao;
                extrato.EX_IdMedico = item.IR_IdMedico;
                extrato.EX_TipoConta = item.IR_TipoConta;
                extrato.EX_TipoLancamento = item.IR_TipoLancamento;
                extrato.EX_Valor = item.IR_Valor;
                extrato.EX_DataApuracao = item.IR_DataRecorrencia;

                new ExtratoMedicoServices().Create(extrato);

                item.DateLastExcute = DateTime.Now;
                Edit(item);
              }
            }
            else
            {
              R_ExtratoMedico extrato = new R_ExtratoMedico();

              extrato.EX_CPFCNPJDeposito = item.IR_CPFCNPJ;
              extrato.EX_IdClassificacao = item.IR_IdClassificacao;
              extrato.EX_IdMedico = item.IR_IdMedico;
              extrato.EX_TipoConta = item.IR_TipoConta;
              extrato.EX_TipoLancamento = item.IR_TipoLancamento;
              extrato.EX_Valor = item.IR_Valor;
              extrato.EX_DataApuracao = item.IR_DataRecorrencia;

              new ExtratoMedicoServices().Create(extrato);

              item.DateLastExcute = DateTime.Now;
              Edit(item);
            }
          }
        }

        sb.AppendLine(string.Format("[{0}] - Serviço de Itens Recorrentes executado com sucesso.", DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss")));
        sb.AppendLine("==============================");
        return sb.ToString();
      }
      catch (Exception ex)
      {
        string Mensagem = ex.InnerException != null ? ex.InnerException.Message : ex.Message;
        sb.AppendLine(string.Format("[{0}] - Erro durante processamento do serviço Itens Recorrentes: {1}", DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss"), Mensagem));
        sb.AppendLine("==============================");
        return sb.ToString();
      }
    }

    public string ValidaLotesGlosaVencidas()
    {
      StringBuilder sb = new StringBuilder();
      sb.AppendLine(string.Format("[{0}] - Iniciando serviço de Validar Lotes Glosa Vencidos", DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss")));

      try
      {
        string Query = @"UPDATE R_LoteGlosa SET LG_IdStatusGlosa = (SELECT SG_Id FROM R_StatusGlosa WHERE SG_Enum = 4) WHERE CONVERT(DATE, LG_DataVencimentoAnalise) < CONVERT(DATE, GETDATE())";
        Contexto.Database.ExecuteSqlCommand(Query);

        sb.AppendLine(string.Format("[{0}] - Serviço de Validar Lotes Glosa Vencidos executado com sucesso.", DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss")));
        sb.AppendLine("==============================");
        return sb.ToString();
      }
      catch (Exception ex)
      {
        string Mensagem = ex.InnerException != null ? ex.InnerException.Message : ex.Message;
        sb.AppendLine(string.Format("[{0}] - Erro durante processamento do serviço Validar Lotes Glosa Vencidos: {1}", DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss"), Mensagem));
        sb.AppendLine("==============================");
        return sb.ToString();
      }
    }

    public string LembreteVencientoGlosa()
    {
      StringBuilder sb = new StringBuilder();
      sb.AppendLine("==============================");
      sb.AppendLine(string.Format("[{0}] - Iniciando serviço de enviar e-mail de lembrete de vencimento de glosa", DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss")));
      try
      {
        ParametrosService parametrosService = new ParametrosService();
        R_Parametros parametros = parametrosService.GetByEnum(ParametrosEnum.LembreteVencientoGlosa);

        LoteGlosaServices loteGlosaServices = new LoteGlosaServices();
        List<LotesGlosaVencendoCabecalho> ListaLoteGlosas = loteGlosaServices.GetLotesGlosaVencendo(DateTime.Now.AddDays(Convert.ToInt32(parametros.P_Valor)));

        foreach (var item in ListaLoteGlosas)
        {
          string mensagem = loteGlosaServices.GetTableLoteGlosaVencendo(item.ListaLotesGlosaVencendo);
          EMailHelper.Send(new[] { item.EmailResponsavel }, string.Format("Lotes a {0} dia(s) de vencer.", parametros.P_Valor), mensagem);
          string update = @"update R_LoteGlosa set LG_EnviadoEmail = 1 where LG_Id in (#Ids#)";
          update = update.Replace("#Ids#", string.Join(",", item.ListaLotesGlosaVencendo.Select(a => a.Codigo).ToArray()));
          Contexto.Database.ExecuteSqlCommand(update);
        }

        if (ListaLoteGlosas.Count() != 0)
          sb.AppendLine(string.Format(@"[{0}] - Serviço de Lembrete Vencimento Glosa Executado Com Suceso, 
                                    enviado e-mails para {1} e-mails de um total de {2} vencidos", DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss"), ListaLoteGlosas.Count(), ListaLoteGlosas.Sum(a => a.ListaLotesGlosaVencendo.Count())));
        
        else
          sb.AppendLine(string.Format("[{0}] - Nenhum lote com vencimento próximo", DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss")));

        sb.AppendLine("==============================");
        return sb.ToString();
      }
      catch (Exception ex)
      {
        string Mensagem = ex.InnerException != null ? ex.InnerException.Message : ex.Message;
        sb.AppendLine(string.Format("[{0}] - Erro durante processamento do serviço Lembrete Vencimento Glosa: {1}", DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss"), Mensagem));
        sb.AppendLine("==============================");
        return sb.ToString();
      }
    }
  }
}


﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Controls;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.Data.Entity.Validation;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.Mvc;
using System.Web.Security;

namespace RepasseConvenio.Controllers
{
  [AllowAnonymous]
  public class AccountController : LibController
  {
    public ActionResult Login()
    {
      if (Session.Count > 0 || Response.Cookies.Count >= 0)
      {
        Session["UsuarioLogado"] = null;
        Session.Clear();
        FormsAuthentication.SignOut();
        Response.Cookies.Remove("User");
      }

      return View();
    }

    [HttpPost]
    [AllowAnonymous]
    public ActionResult Login(LoginModel login)
    {
      try
      {
        if (ModelState.IsValid)
        {
          UsuarioServices usuariosServices = new UsuarioServices();
          R_Usuario user = usuariosServices.GetByCPF(login.UserName);
          if (user == null)
            throw new CustomException("Usuário ou senha inválido");


          if (CryptoHelper.Decrypt(user.U_Password) != login.Password)
            throw new CustomException("Usuário ou senha inválido");

          FormsAuthentication.SetAuthCookie(CryptoHelper.Encrypt(user.U_CPF), false);

          HttpCookie cookie = new HttpCookie("User");
          cookie.Value = CryptoHelper.Encrypt(user.U_CPF);
          cookie.Expires = DateTime.Now.AddDays(5);
          Response.Cookies.Add(cookie);

          return RedirectToAction("Index", "Repasse");
        }

        MessageListToast.Add(new Message(MessageType.Error, "Favor verificar se os campos de CPF e Senha estão preenchidos."));
        return View(login);
      }
      catch (DbEntityValidationException ex)
      {
        StringBuilder sb = new StringBuilder();
        foreach (DbEntityValidationResult eve in ex.EntityValidationErrors)
        {
          sb.AppendLine(String.Format(@"{0} - Entity {1} tem os seguintes erros {2}"
                          , DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss")
                          , eve.Entry.Entity.GetType().Name
                          , eve.Entry.State));

          foreach (var ve in eve.ValidationErrors)
          {
            sb.AppendLine(String.Format(@"{0} - Propriedade {1} - Erro {2}"
                , DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss")
                , ve.PropertyName
               , ve.ErrorMessage)
            );
          }
        }

        MessageListToast.Add(new Message(MessageType.Error, sb.ToString()));
        return View(login);
      }
      catch (CustomException ex)
      {
        MessageListToast.Add(new Message(MessageType.Warning, ex.Message));
        return View(login);
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return View(login);
      }
    }

    [AllowAnonymous]
    public ActionResult LogOut()
    {
      try
      {
        Session["UsuarioLogado"] = null;
        Session.Clear();
        FormsAuthentication.SignOut();
        Response.Cookies.Remove("User");

        MessageListToast.Add(new Message(MessageType.Success, "Logout realizado com sucesso"));
        return RedirectToAction("Login", "Account");
      }
      catch (CustomException Ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, Ex.Message));
        return View();
      }
      catch (Exception Ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, Ex.Message));
        return View();
      }
    }

    public ActionResult ForgotPassword()
    {
      try
      {
        return View();
      }
      catch (Exception)
      {
        throw;
      }
    }

    [HttpPost]
    public ActionResult ForgotPassword(ForgotPasswordModel model)
    {
      try
      {
        return View();
      }
      catch (Exception)
      {
        throw;
      }
    }
  }
}
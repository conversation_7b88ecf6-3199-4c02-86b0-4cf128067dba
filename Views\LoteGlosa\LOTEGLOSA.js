﻿var LoteGlosa;
var ListItensGlosa = [];
var ListCodigoGuiaAtendimento = [];

LoteGlosa = function () {
};

LoteGlosa.init = function () {
  $(document).on("click", ".TrSelecionavel", function () {
    var IdLoteGlosa = $(this).data("codigoloteglosa");
    var UrlDownloadXML = GetURLBaseComplete() + "/LoteGlosa/GerarXML?CodigoLoteGlosa=" + IdLoteGlosa;
    var UrlDownloadRelatorio = GetURLBaseComplete() + "/LoteGlosa/GerarRelatorio?CodigoLoteGlosa=" + IdLoteGlosa;

    if (!$(this).hasClass("Selected")) {
      $('.Selected').each(function (index, element) {
        $(element).removeClass("Selected")
        $(element).addClass("UnSelected")
      });

      if ($(this).hasClass("Selected")) {
        $(this).removeClass("Selected")
        $(this).addClass("UnSelected")
        $('#TransferirResponsavel').hide();
      }
      else {
        var model = {
          IdLote: IdLoteGlosa
        }

        $.ajax({
          url: GetURLBaseComplete() + '/LoteGlosa/HaveResponsavel',
          dataType: "json",
          type: "POST",
          data: model,
          success: function (data) {
            if (!data.Erro) {
              if (data.HaveResponsavel) {
                $('#TransferirResponsavel').show();
              }
              else {
                $('#TransferirResponsavel').hide();
              }
            } else {
              AlertToReload("Importação", data.message, "error");
            }
          }
        });

        var statusLoteGlosa = $(this).data("statusloteglosa");
        if (statusLoteGlosa == 6 || statusLoteGlosa == 1) {
          $('#EnviarConvenioIndex').show();
          $('#GerarXML').show();
          $('#GerarRelatorio').show();
        }
        else {
          $('#EnviarConvenioIndex').hide();
          $('#GerarXML').hide();
          $('#GerarRelatorio').hide();
        }

        $(this).addClass("Selected")
        $(this).removeClass("UnSelected")
      }
    }
    else {
      $(this).removeClass("Selected")
      $(this).addClass("UnSelected")
      $('#TransferirResponsavel').hide();
      $('#EnviarConvenioIndex').hide();
      $('#GerarXML').hide();
      $('#GerarRelatorio').hide();
    }

    var countSelected = 0;
    $('.Selected').each(function (index, element) {
      if ($(element).hasClass("Selected")) {
        $('#DetalhesLoteGlosa').data("codigoloteglosa", IdLoteGlosa);
        $('#EnviarConvenioIndex').data("codigoloteglosa", IdLoteGlosa);
        $('#GerarXML').attr("href", UrlDownloadXML);
        $('#GerarRelatorio').attr("href", UrlDownloadRelatorio);
        $('#CodigoLoteGlosa').val(IdLoteGlosa);
        countSelected++;
      }
    });

    if (countSelected == 0) {
      $('#GerarXML').removeAttr("href");
      $('#GerarXML').attr("disabled", true);
      $('#GerarXML').addClass("disabled");

      $('#GerarRelatorio').removeAttr("href");
      $('#GerarRelatorio').attr("disabled", true);
      $('#GerarRelatorio').addClass("disabled");

      $('#DetalhesLoteGlosa').removeData("codigoloteglosa");
      $('#DetalhesLoteGlosa').attr("disabled", true);
      $('#DetalhesLoteGlosa').addClass("disabled");

      $('#TransferirResponsavel').removeData("codigoloteglosa");
      $('#TransferirResponsavel').attr("disabled", true);
      $('#TransferirResponsavel').addClass("disabled");

      $('#EnviarConvenioIndex').removeData("codigoloteglosa");
      $('#EnviarConvenioIndex').attr("disabled", true);
      $('#EnviarConvenioIndex').addClass("disabled");

      $('#LogsLoteGlosa').attr("disabled", true);
      $('#LogsLoteGlosa').addClass("disabled");

      $('#CodigoLoteGlosa').val("");
    }
    else {
      $('#GerarXML').attr("disabled", false);
      $('#GerarXML').removeClass("disabled");

      $('#GerarRelatorio').attr("disabled", false);
      $('#GerarRelatorio').removeClass("disabled");

      $('#DetalhesLoteGlosa').attr("disabled", false);
      $('#DetalhesLoteGlosa').removeClass("disabled");

      $('#TransferirResponsavel').attr("disabled", false);
      $('#TransferirResponsavel').removeClass("disabled");

      $('#EnviarConvenioIndex').attr("disabled", false);
      $('#EnviarConvenioIndex').removeClass("disabled");

      $('#LogsLoteGlosa').attr("disabled", false);
      $('#LogsLoteGlosa').removeClass("disabled");
    }
  });

  $(document).on("click", ".SalvarJustificativaLote, .AceitarGlosaLote", function () {
    var codigoJustificativa = $("#JustificativaSelect_id").val();
    var comentario = $("#Comentario").val();
    var CodigoLoteGlosa = $("#CodigoLote").val();

    if ((!codigoJustificativa || codigoJustificativa == "0")) {
      AlertaToast("Aviso", "Selecione uma justificativa", "warning", 5000, "#FCFFD3");
      return;
    }

    let data = {
      IdsItens: ListItensGlosa,
      IdLoteGlosa: CodigoLoteGlosa,
      CodJustificativa: codigoJustificativa,
      Comentario: comentario
    };
    if ($(this).hasClass("SalvarJustificativaLote"))
      LoteGlosa.SalvarJustificativa(data);
    else if ($(this).hasClass("AceitarGlosaLote"))
      LoteGlosa.AceitarGlosa(data);
  });

  $(document).on("click", ".SalvarJustificativaDetalhada, .AceitarGlosaDetalhada", function () {
    var codigoJustificativa = $("#JustificativaAnaliseDetalhadaSelect_id").val();
    var comentario = $("#ComentarioAnaliseDetalhada").val();
    var CodigoLoteGlosa = $("#CodigoLote").val();

    if ((!codigoJustificativa || codigoJustificativa == "0")) {
      AlertaToast("Aviso", "Selecione uma justificativa", "warning", 5000, "#FCFFD3");
      return;
    }

    let data = {
      IdsItens: ListItensGlosa,
      IdLoteGlosa: CodigoLoteGlosa,
      CodJustificativa: codigoJustificativa,
      Comentario: comentario
    };
    if ($(this).hasClass("SalvarJustificativaDetalhada"))
      LoteGlosa.SalvarJustificativa(data);
    else if ($(this).hasClass("AceitarGlosaDetalhada"))
      LoteGlosa.AceitarGlosa(data);
  });

  $(document).on("change", ".isSelected", function () {
    if ($('.isSelected:checked').length == 0) {
      $('#AceitarJustificarGlosa').addClass("disabled");
      $('#AceitarJustificarGlosa').attr("disabled", true);
      $('#AnaliseEmLote').addClass("disabled");
      $('#AnaliseEmLote').attr("disabled", true);
    }
    else {
      $('#AceitarJustificarGlosa').removeClass("disabled");
      $('#AceitarJustificarGlosa').attr("disabled", false);
      $('#AceitarJustificarGlosa').html("Análise Detalhada da Guia");
      $('#AnaliseEmLote').removeClass("disabled");
      $('#AnaliseEmLote').attr("disabled", false);
      $('#AnaliseEmLote').html("Análise Detalhada da Guia");
    }

    if (!$(this).prop("checked")) {
      $("#CheckAllItensGlosa").prop("checked", false);
    }
    if ($('.isSelected').length == $('.isSelected:checked').length)
      $("#CheckAllItensGlosa").prop("checked", true);

    ListItensGlosa = [];
    ListCodigoGuiaAtendimento = [];

    $('.isSelected').each(function (index, element) {
      if ($(element).is(':checked')) {
        ListItensGlosa.push($(element).data('codigoproc'));
        ListCodigoGuiaAtendimento.push($(element).data('codigoguiaatendimento'));
      }
    });

    if (ListItensGlosa.length <= 1) {
      $('#AceitarJustificarGlosa').html("Análise Detalhada da Guia")
      $('#AnaliseEmLote').html("Análise Detalhada da Guia")
      $('#AceitarJustificarGlosa').attr("id", "AceitarJustificarGlosa");
      $('#AnaliseEmLote').attr("id", "AceitarJustificarGlosa");
    }
    if (ListItensGlosa.length > 1) {
      $('#AceitarJustificarGlosa').html("Análise em lote")
      $('#AnaliseEmLote').html("Análise em lote")
      $('#AceitarJustificarGlosa').attr("id", "AnaliseEmLote");
      $('#AnaliseEmLote').attr("id", "AnaliseEmLote");
    }
  });

  $(document).on("click", "#CheckAllItensGlosa", function () {
    if ($(this).prop('checked')) {
      $('#AceitarJustificarGlosa').removeClass("disabled");
      $('#AceitarJustificarGlosa').attr("disabled", false);
      $('#AnaliseEmLote').removeClass("disabled");
      $('#AnaliseEmLote').attr("disabled", false);
    }
    else {
      $('#AceitarJustificarGlosa').addClass("disabled");
      $('#AceitarJustificarGlosa').attr("disabled", true);
      $('#AnaliseEmLote').addClass("disabled");
      $('#AnaliseEmLote').attr("disabled", true);
    }

    $(".isSelected").prop('checked', $(this).prop('checked'));

    ListItensGlosa = [];
    ListCodigoGuiaAtendimento = [];

    $('.isSelected').each(function (index, element) {
      if ($(element).is(':checked')) {
        ListItensGlosa.push($(element).data('codigoproc'));
        ListCodigoGuiaAtendimento.push($(element).data('codigoguiaatendimento'));
      }
    });

    if (ListItensGlosa.length <= 1) {
      $('#AceitarJustificarGlosa').html("Análise Detalhada da Guia")
      $('#AnaliseEmLote').html("Análise Detalhada da Guia")
      $('#AceitarJustificarGlosa').attr("id", "AceitarJustificarGlosa");
      $('#AnaliseEmLote').attr("id", "AceitarJustificarGlosa");
    }
    if (ListItensGlosa.length > 1) {
      $('#AceitarJustificarGlosa').html("Análise em lote")
      $('#AnaliseEmLote').html("Análise em lote")
      $('#AceitarJustificarGlosa').attr("id", "AnaliseEmLote");
      $('#AnaliseEmLote').attr("id", "AnaliseEmLote");
    }
  });

  $(document).on("click", "#AnaliseEmLote", function () {
    $('#ModalAnaliseLote').modal("show");
  });

  $(document).on("click", "#AceitarJustificarGlosa", function () {
    var codigoGuia = ListCodigoGuiaAtendimento[0];
    LoteGlosa.GetGridGuiasAtendimento(codigoGuia);
  });

  $(document).on("click", "#DetalhesLoteGlosa", function () {
    var CodigoLoteGlosa = $(this).data("codigoloteglosa");
    var urlDetalhesLote = GetURLBaseComplete() + "/LoteGlosa/Detalhes?IdLoteGlosa=" + CodigoLoteGlosa;

    var model = {
      IdLote: CodigoLoteGlosa
    }

    $.ajax({
      url: GetURLBaseComplete() + '/LoteGlosa/HaveResponsavel',
      dataType: "json",
      type: "POST",
      data: model,
      success: function (data) {
        if (!data.Erro) {
          if (!data.HaveResponsavel) {
            LoteGlosa.AlertaAssumirLote("Deseja assumir o lote?", "Lote não contem ninguém responsável, clique em SIM para assumir o lote.", "warning", "Sim", "Não", urlDetalhesLote, CodigoLoteGlosa);
          }
          else
            window.location = urlDetalhesLote;
        } else {
          AlertToReload("Importação", data.message, "error");
        }
      }
    });
  });

  $(document).on("click", "#LogsLoteGlosa", function () {
    var CodigoLoteGlosa = $('#CodigoLoteGlosa').val();

    $.ajax({
      url: GetURLBaseComplete() + "/LoteGlosa/_GetLogs/" + CodigoLoteGlosa,
      dataType: "html",
      type: "GET",
      success: function (data) {
        $("#ModalLogs .modal-body").html(data);
        $("#ModalLogs").modal("show");
      }
    });
  });

  $(document).on("click", "#TransferirResponsavel", function () {
    $("#ModalTransfRespon").modal("show");
  });

  $(document).on("click", "#Transferir", function () {
    var IdResponsavel = $('#ResponsavelSelect_id').val();
    var CodigoGlosa = $('#CodigoLoteGlosa').val();

    var model = {
      IdResponsavel: IdResponsavel,
      IdLote: CodigoGlosa
    }

    $.ajax({
      url: GetURLBaseComplete() + '/LoteGlosa/TransferirResponsavel',
      dataType: "json",
      type: "POST",
      data: model,
      success: function (data) {
        if (!data.Erro) {
          $("#ModalTransfRespon").modal("hide");
          AlertToReload("Sucesso", data.message, "success");
        } else {
          AlertToReload("Erro", data.message, "error");
        }
      }
    });
  });

  $(document).on("click", "#EnviarConvenio", function () {
    var CodigoGlosa = $('#CodigoLote').val();

    var model = {
      codigoGlosa: CodigoGlosa
    };

    $.ajax({
      url: GetURLBaseComplete() + '/LoteGlosa/EnviarConvenio',
      dataType: "json",
      type: "POST",
      data: model,
      success: function (data) {
        if (!data.Erro) {
          AlertToReload("Sucesso", data.Mensagem, "success");
        } else {
          AlertToReload("Erro", data.Mensagem, "error");
        }
      }
    });
  });

  $(document).on("click", "#EnviarConvenioIndex", function () {
    var CodigoGlosa = $(this).data("codigoloteglosa");
    var url = GetURLBaseComplete() + "/ProtocoloGlosa/Index?IdLoteGosa=" + CodigoGlosa;
    window.location = url;
  });
}

LoteGlosa.AceitarGlosa = function (data) {
  $.ajax({
    url: GetURLBaseComplete() + '/LoteGlosa/AceitarGlosa',
    dataType: "json",
    type: "POST",
    data: data,
    success: function (data) {
      if (!data.Erro) {
        Alerta(data.Titulo, data.Mensagem, "success", "Fechar");
        LoteGlosa.GridItens();
        $('#ModalAnaliseDetalhadaGlosa').modal("hide");
        $('#ModalAnaliseLote').modal("hide");
        $("#ComentarioAnaliseDetalhada").val("");
        $("#JustificativaAnaliseDetalhadaSelect_id").val("");
        $("#JustificativaAnaliseDetalhadaSelect_text").val("");
        $("#JustificativaAnaliseDetalhadaSelectLookup").val(null).trigger('change');
      }
      else
        Alerta(data.Titulo, data.Mensagem, "error", "Fechar");
    }
  });
}

LoteGlosa.SalvarJustificativa = function (data) {
  $.ajax({
    url: GetURLBaseComplete() + '/LoteGlosa/SalvarJustificativa',
    dataType: "json",
    type: "POST",
    data: data,
    success: function (data) {
      if (!data.Erro) {
        Alerta(data.Titulo, data.Mensagem, "success", "Fechar");
        LoteGlosa.GridItens();
        $('#ModalAnaliseDetalhadaGlosa').modal("hide");
        $('#ModalAnaliseLote').modal("hide");
        $("#Comentario").val("");
        $("#JustificativaSelect_id").val("");
        $("#JustificativaSelect_text").val("");
        $("#JustificativaSelectLookup").val(null).trigger('change');
      }
      else
        Alerta(data.Titulo, data.Mensagem, "error", "Fechar");
    }
  });
}

LoteGlosa.GridItens = function () {
  let idLote = $("#CodigoLote").val();

  $.ajax({
    url: GetURLBaseComplete() + '/LoteGlosa/_GridItens/' + idLote,
    dataType: "html",
    type: "GET",
    success: function (data) {
      $("#GridItens").html(data)
    }
  });
}

LoteGlosa.AlertaAssumirLote = function (title, message, type, textBtnConfirm, textBtnCancel, url, CodigoLoteGlosa) {
  var model = {
    IdLote: CodigoLoteGlosa
  };

  Swal.fire({
    title: title,
    html: message,
    icon: type,
    showCancelButton: true,
    confirmButtonText: textBtnConfirm,
    cancelButtonText: textBtnCancel,
    showLoaderOnConfirm: true,
    dangerMode: true,
  })
    .then((will) => {
      if (will.isConfirmed) {
        $.ajax({
          url: GetURLBaseComplete() + '/LoteGlosa/AssumirLote',
          dataType: "json",
          type: "POST",
          data: model,
          success: function (data) {
            if (!data.Erro) {
              window.location = url;
            } else {
              AlertToReload("Importação", data.message, "error");
            }
          }
        });
      }
    });
}

LoteGlosa.GetGridGuiasAtendimento = function (IdGuiaAtendimento) {
  var model = {
    CodigoGuiaAtendimento: IdGuiaAtendimento
  }
  $.ajax({
    type: 'GET',
    url: GetURLBaseComplete() + '/LoteGlosa/GetGridGuiaAtenimento',
    dataType: 'html',
    data: model,
    success: function (data) {
      $("#PartialGuiasAtendimento").html(data);
      RepasseConvenio.IniciaMascaras();
      $('#ModalAnaliseDetalhadaGlosa').modal("show");
    },
  });
}

$(document).ready(function () {
  LoteGlosa.init();
});

﻿@using RepasseConvenio.Models

<div class="modal fade" id="ModalDepositoRepasse" data-keyboard="false" data-backdrop="static"">
    <div class="modal-dialog" style="max-width: 50%; ">
      <div class="modal-content">
        <div class="modal-header">
          <h4 class="modal-title">Depósito Repasse</h4>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">×</span>
          </button>

        </div>
        <div class="modal-body" style="height: 160px; overflow-x: auto;">
          <div class="row">

            <div class="col-md-4">
              <div class="form-group" data-select2-id="10">
                <label for="BancoSelect">Selecione o Banco</label>
                <input id="BancoSelect_id" name="BancoSelect.id" type="hidden" value="">
                <input id="BancoSelect_text" name="BancoSelect.text" type="hidden" value="">
                <select class="form-control " id="BancoSelectLookup" name="BancoSelectLookup"> </select>
                <script>
                  MakeDropDown('BancoSelect', GetURLBaseComplete() + '/Repasse/GetBancoUnicooperSelect', 'Selecione o Banco');
                </script>
              </div>
            </div>

            <div class="col-md-4  ">
              <label>Data do depósito</label>
              <input type="Text" class=" form-control airDatePickerDate Data " id="DataDeposito" name="DataDeposito" ; value="" />
            </div>
            <div class="col-md-4  ">
              <label>Valor do depósito</label>
              <input type="Text" class=" form-control money " id="ValorDeposito" name="ValorDeposito" ; value="" />
            </div>
          </div>

          <div class="row">

            <div class="col-md-4  ">
              <label>Número do documento</label>
              <input type="Text" class=" form-control numeros" id="NumeroDcumento" name="NumeroDcumento" ; value="" />
            </div>
            <div class="col-md-4  ">
              <label>Valor Utilizado</label>
              <input type="Text" class=" form-control money " id="ValorUtilizado" name="ValorUtilizado" ; value="" />
            </div>

          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-primary btnSaveBanco ">Salvar</button>
          <button type="button" class="btn btn-outline-dark pull-left" data-dismiss="modal">Cancelar</button>
        </div>

        @*<div class="box-footer" style="margin-top: 15px;">
      <button type="button" class="btn btn-outline-dark pull-left" data-dismiss="modal">Cancelar</button>
    </div>*@
      </div>
    </div>
  </div>








@*Adicionar CampoAba
<div class="modal fade" id="ModalDepositoRepasse">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">Selecionar Campo</h4>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">×</span>
        </button>
      </div>
      <div class="modal-body">
        <input id="AbaTelaLookup_id" name="AbaTelaLookup_id" type="hidden" value="" />
        <input id="IdPainelAba" name="IdPainelAba" type="hidden" value="" />

        <div class="col-md-12 container">
          <div class="row">
            <div class="col-md-8">
              <div class="form-group">
                <label for="">Campos</label>
                <input id="DepositoRepasse_id" name="DepositoRepasse_id" type="hidden" value="">
                <div class="input-group">
                  <input placeholder="Campos" data-toggle="tooltip" data-placement="top" title="" controlpai="AbaTelaLookup" autocomplete="off" class="form-control ui-autocomplete-input" id="DepositoRepasse" name="DepositoRepasse_Description" type="text" value="" data-original-title="Informe o campo">
                  <div class="input-group-addon" id="btnVerTudoDepositoRepasse">
                    <i class="fa fa-caret-down"></i>
                  </div>
                </div>
                <spam id="loadingDepositoRepasse"></spam>
                <span class="field-validation-valid text-danger" data-valmsg-for="DepositoRepasse.Description" data-valmsg-replace="true"></span>
                <script type="text/javascript">
                  $(document).ready(function () {
                    MakeLookup('DepositoRepasse', GetURLBaseComplete() + '/Repasse/GetLookupModelTeste', 'AbaTelaLookup', false);
                  });
                </script>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-sec pull-left" data-dismiss="modal">Cancelar</button>
        <button type="button" class="btn btn-outline-success" id="savePainelAbaCampo" data-url-create="@Url.Action("Create", "PainelAbaCampo")" data-url-edit="@Url.Action("EditPainelAbaCmapo", "PainelAbaCampo")">Salvar</button>
      </div>
    </div>
     /.modal-content 
  </div>
   /.modal-dialog 
</div>*@













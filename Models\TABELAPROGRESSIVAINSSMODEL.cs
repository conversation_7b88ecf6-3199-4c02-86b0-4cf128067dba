﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace RepasseConvenio.Models
{
  public class TabelaProgressivaINSSModel
  {
    public TabelaProgressivaINSSModel()
    {
      ValoresTabelaINSS = new List<ValoresTabelaProgressivaINSSModel>();
    }
    public int Codigo { get; set; }

    [Required]
    [DisplayName("Início da Vigência")]
    public DateTime DtInicioVigencia { get; set; }

    [DisplayName("Fim da Vigência")]
    public DateTime? DtFimVigencia { get; set; }

    [Required]
    [DisplayName("Descrição")]
    public string Descricao { get; set; }
    public List<ValoresTabelaProgressivaINSSModel> ValoresTabelaINSS { get; set; }

  }
  public static class TabelaProgressivaINSSModelConversion
  {
    public static R_TabelaProgressivaINSS ToEntityEdit(this R_TabelaProgressivaINSS entity, TabelaProgressivaINSSModel model)
    {
      entity.T_Descricao = model.Descricao;
      entity.T_DtInicioVigencia = model.DtInicioVigencia;
      entity.T_DtFimVigencia = model.DtFimVigencia;

      return entity;
    }
    public static R_TabelaProgressivaINSS ToEntityCreate(this TabelaProgressivaINSSModel model)
    {
      R_TabelaProgressivaINSS entity = new R_TabelaProgressivaINSS();
      entity.T_Descricao = model.Descricao;
      entity.T_DtInicioVigencia = model.DtInicioVigencia;
      entity.T_DtFimVigencia = model.DtFimVigencia;


      return entity;
    }
    public static TabelaProgressivaINSSModel OffEntityToModel(this R_TabelaProgressivaINSS entity)
    {
    
            TabelaProgressivaINSSModel model = new TabelaProgressivaINSSModel();
      model.Codigo = entity.T_Id;
      model.Descricao = entity.T_Descricao;
      model.DtInicioVigencia = entity.T_DtInicioVigencia;
      model.DtFimVigencia = entity.T_DtFimVigencia;
      model.Descricao = entity.T_Descricao;
      //model.ValorInicial = entity.T_ValorInicial;
      //model.ValorFinal = entity.T_ValorFinal;
      //model.AliquotaProgressiva = entity.T_AliquotaProgressiva;

      return model;
    }
  }
}
﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using PagedList;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RepasseConvenio.Services
{
  public class TipoRegistroImpostoServices : ServiceBase
  {
    public TipoRegistroImpostoServices(RepasseEntities Contexto)
        : base(Contexto)
    { }
    public TipoRegistroImpostoServices()
       : base()
    { }

    public TipoRegistroImpostoServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public TipoRegistroImpostoServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public int GetIdByEnum(EnumTipoRegistroImposto tipoImpostoEnum)
    {
      return Contexto.R_TipoRegistroImposto.Where(a => a.TRI_Enum == (int)tipoImpostoEnum).Select(a => a.TRI_Id).FirstOrDefault();
    }

    public R_TipoRegistroImposto GetByEnum(EnumTipoRegistroImposto tipoImpostoEnum)
    {
      return Contexto.R_TipoRegistroImposto.Where(a => a.TRI_Enum == (int)tipoImpostoEnum).FirstOrDefault();
    }

    public R_TipoRegistroImposto GetById(int Id)
    {
      return Contexto.R_TipoRegistroImposto.Where(a => a.TRI_Id == Id).FirstOrDefault();
    }

    public List<R_TipoRegistroImposto> GetAll()
    {
      string query = @"SELECT * FROM R_TipoRegistroImposto";
      return Contexto.Database.SqlQuery<R_TipoRegistroImposto>(query).ToList();
    }

    public List<R_TipoRegistroImposto> GetByTerm(string term)
    {
      if (string.IsNullOrEmpty(term))
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_TipoRegistroImposto");

        return Contexto.Database.SqlQuery<R_TipoRegistroImposto>(query).ToList();

      }
      else
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_TipoRegistroImposto
                                       WHERE TRI_Descricao LIKE @termo");

        return Contexto.Database.SqlQuery<R_TipoRegistroImposto>(query, new SqlParameter("@termo", string.Format("%{0}%", term))).ToList();
      }
    }
  }
}


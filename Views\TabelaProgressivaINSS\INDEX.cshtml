﻿@using RepasseConvenio.Infrastructure
@using RepasseConvenio.Models
@using PagedList
@using PagedList.Mvc;
@model IPagedList<TabelaProgressivaINSSModel>

@{
  ViewBag.Title = "Tabela Progressiva INSS";
}

@Styles.Render("~/Views/TabelaProgressivaINSS/TabelaINSS.css")
@Scripts.Render("~/Views/TabelaProgressivaINSS/TabelaProgressivaINSS.js?ia=ba")
<div class="row">
  <section class="col-lg-12 connectedSortable ui-sortable">

    <div class="card">
      <div class="card-header ui-sortable-handle">
        <div class="row" style="justify-content:space-between;">
          <h3 class="card-title">
            <i class="fas fa-book mr-1"></i>
            Tabela Progressiva
          </h3>
          <div class="card-tools">
            @if (ContextoUsuario.UserLogged.IsInPermissao(Guid.Parse("7CBD0133-A2FA-4989-8AC2-1463209F0A12")))
            {
              <button id="CreateGRD" onclick="window.location.href='@Url.Action("Create", "TabelaProgressivaINSS")'" style="padding: 0.1rem 0.1rem;" type="button" class="btn btn-sm btn-block btn-outline-primary">
                <i class="fas fa-plus"></i> Novo
              </button>
            }
          </div>
        </div>
      </div>

      <div class="card-body">

        <div class="card-acoes col-4">

          @if (ContextoUsuario.UserLogged.IsInPermissao(Guid.Parse("41F3AF4A-7EF0-4385-B26B-FC7CA19B0203")))
          {

            <a class="btn btn-block btn-outline-primary disabled" id="InssEdit"> Editar </a>
          }
          @if (ContextoUsuario.UserLogged.IsInPermissao(Guid.Parse("EAFF320A-02A6-49C8-9A85-DD049B063E6D")))
          {

            <a class="btn btn-block btn-outline-primary disabled" id="InssDelete"> Excluir </a>
          }
        </div>
        <div class="tab-content p-0">
          <div class="box-body">
            <div class="row">
              <div class="col-md-12 table-responsive p-0">
                <table id="TableTela" class="table-striped table table-sm table-hover text-nowrap">
                  <thead>
                    <tr>
                      <th>
                        Descrição
                      </th>
                      <th>
                        Data De
                      </th>
                      <th>
                        Data Até
                      </th>
                      <th>
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    @foreach (TabelaProgressivaINSSModel item in Model)
                    {
                      <tr class="TrSelecionavel" data-codigomedico="@item.Codigo">
                        <td>
                          <input type="hidden" @item.Codigo />
                          @item.Descricao
                        </td>
                        <td>
                          @item.DtInicioVigencia.ToString("dd/MM/yyyy")
                        </td>
                        <td>
                          @if (item.DtFimVigencia.HasValue)
                          {
                            @item.DtFimVigencia.Value.ToString("dd/MM/yyyy");
                          }
                        </td>
                        @*<td class="pull-rigth">
                            <a href="@Url.Action("Edit", "TabelaProgressivaINSS", new { id = item.Codigo  })" class="btn btn-sm TIcones btn-outline-warning" title="Atualizar">
                              <i class="fa fa-edit"></i>
                            </a>
                            <a onclick="confirmaDelete('@String.Format("Deseja excluir a tabela {0}?",item.Descricao)', '@Url.Action("Delete", "TabelaProgressivaINSS", new { id = item.Codigo })')" class="btn btn-sm TIcones btn-outline-danger" title="Excluir">
                              <i class="fa fa-trash"></i>
                            </a>
                          </td>*@
                      </tr>
                    }
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="box-footer with-border" style="padding: 0 10pt;">
        <div class="row">
          <div class="col-md-8 ">
            @Html.PagedListPager(Model, page => Url.Action("Index", new { page }))
          </div>
        </div>
      </div>
    </div>
  </section>
</div>
@*Html.Partial("_ModalNovaGRD", new ModalNovaGRD())*@
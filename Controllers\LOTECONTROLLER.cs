﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Controls;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace RepasseConvenio.Controllers
{
  [Security("F6192FBC-B19B-4523-AA62-0E6138D83BAF")]
  public class LoteController : LibController
  {
    // GET: Lote
    private LoteServices LoteService
    {
      get
      {
        if (_LoteService == null)
          _LoteService = new LoteServices(ContextoUsuario.UserLogged);

        return _LoteService;
      }
    }
    private LoteServices _LoteService;

    [Security("700E0E35-A0D1-4C9E-962B-8A68807379FF")]
    public ActionResult Index(int? page, string filtro)
    {
      try
      {
        int pageNumber = page ?? 1;
        ViewBag.page = pageNumber;
        ViewBag.filtro = filtro;

        var list = LoteService.Get(pageNumber, filtro);
        return View(list);
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return View();
      }
    }
    [Security("9896711B-4AA2-43EF-9F82-225ABA079E20")]
    public ActionResult Detalhes (int codigo)
    {
      string nr = LoteService.GetNrLoteByLoteId(codigo);
      ViewBag.NrLote = nr;
      List<ItensLoteModel> itensLote = new ItensLoteServices(ContextoUsuario.UserLogged).GetById(codigo);
      return View(itensLote);
    }


  }
}
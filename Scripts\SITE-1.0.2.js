﻿var RepasseConvenio = function () {
};

RepasseConvenio.init = function () {
  RepasseConvenio.IniciaMascaras();
  RepasseConvenio.IniciaInputs();
}

RepasseConvenio.SignalRGeral = function () {
  SignalR.HubRef.client.openModalRetornoUsuario = function (titulo) {
    $('#ModalRetornoUsuario .modal-title').html(titulo);
    $('#ModalRetornoUsuario').modal("show");
    $(".progress-bar").css("background-color", "#007bff");
    $(".progress-bar").html("");
    $('.progress-bar').width(0 + '%');

  };

  SignalR.HubRef.client.creatPStrong = function (texto) {
    $('#bodyModalRetornoUsuario').append(texto);
    var objDiv = document.getElementById("bodyModalRetornoUsuario");
    objDiv.scrollTop = objDiv.scrollHeight;
  };

  SignalR.HubRef.client.creatPSucess = function (texto) {
    $('#bodyModalRetornoUsuario').append(texto);
    var objDiv = document.getElementById("bodyModalRetornoUsuario");
    objDiv.scrollTop = objDiv.scrollHeight;
  };

  SignalR.HubRef.client.setPorcentagemProgress = function (porcentagem, textoPorcentagem) {
    $('.progress-bar').width(porcentagem + '%');
    $('.progress-bar').html(textoPorcentagem);
    $(".progress-bar").css("background-color", "#007bff");
    $(".progress-bar").html("");
  };

  SignalR.HubRef.client.concluirProgress = function (porcentagem, textoPorcentagem, corBarra) {
    $(".progress-bar").css("background-color", "green");
    $('.progress-bar').width(porcentagem + '%');
    $('.progress-bar').html(textoPorcentagem);
  };
}

RepasseConvenio.IniciaMascaras = function () {
  $('[data-toggle="tooltip"]').tooltip();

  $('.telefone').inputmask('(99)9999-9999[9]', { removeMaskOnSubmit: false });

  $('.Data').inputmask('99/99/9999');

  $('.DataHora').inputmask('99/99/9999 99:99');


  $('.CEP').inputmask('99.999-999', { removeMaskOnSubmit: true });

  $('.numerosOnly').inputmask('9{1,15}', { removeMaskOnSubmit: true });

  $('.numerosOnlyTen').inputmask('9{1,9}', { removeMaskOnSubmit: true });

  $('.numerosOnlyNine').inputmask('9{1,9}', { removeMaskOnSubmit: true });

  $('.numerosOnlySeven').inputmask('9{1,7}', { removeMaskOnSubmit: true });

  $('.numerosOnlyFour').inputmask('9{1,4}', { removeMaskOnSubmit: true });

  $('.numerosOnlyThree').inputmask('9{1,3}', { removeMaskOnSubmit: true });

  $('.CPF').inputmask('999.999.999-99', { removeMaskOnSubmit: true });

  $(".money").inputmask('decimal', {
    removeMaskOnSubmit: true,
    alias: 'numeric',
    groupSeparator: '.',
    autoGroup: true,
    digits: 2,
    integerDigits: 7,
    radixPoint: ",",
    digitsOptional: false,
    allowMinus: false,
    prefix: 'R$ ',
    placeholder: ''
  });

  $(".moneyWithOutPrefix").inputmask('decimal', {
    removeMaskOnSubmit: true,
    alias: 'numeric',
    groupSeparator: '.',
    autoGroup: true,
    digits: 2,
    integerDigits: 7,
    radixPoint: ",",
    digitsOptional: false,
    allowMinus: false,
    placeholder: ''
  });

  $(".money3").inputmask('decimal', {
    autoUnmask: true,
    removeMaskOnSubmit: true,
    alias: 'numeric',
    groupSeparator: '.',
    autoGroup: false,
    precision: 2,
    digits: 2,
    integerDigits: 9,
    radixPoint: ",",
    digitsOptional: false,
    allowMinus: false,
    

  });


  $(".moneyRegras").inputmask('decimal', {
    removeMaskOnSubmit: true,
    alias: 'numeric',
    groupSeparator: '.',
    autoGroup: true,
    digits: 2,
    integerDigits: 7,
    radixPoint: ".",
    digitsOptional: false,
    allowMinus: false,
    prefix: 'R$ ',
    placeholder: ''
  });

  $(".perAcresc").inputmask('decimal', {
    removeMaskOnSubmit: true,
    alias: 'numeric',
    groupSeparator: '.',
    autoGroup: true,
    digits: 2,
    integerDigits: 7,
    radixPoint: ",",
    digitsOptional: false,
    allowMinus: false,
    prefix: '',
    placeholder: ''
  });

  $('.Complemento').inputmask({
    mask: "a{1,20}",
    removeMaskOnSubmit: true,
    greedy: false,
    onBeforePaste: function (pastedValue, opts) {
      pastedValue = pastedValue.toLowerCase();
      return pastedValue.replace("mailto:", "");
    },
    definitions: {
      'a': {
        validator: "[.A-Za-z0-9 ]",
        cardinality: 1,
        casing: "upper"
      }
    }
  });

  $('.CNSIIS').inputmask('9{1,15}', { removeMaskOnSubmit: true });

  $('.MATRICULAF').inputmask('9{1,8}', { removeMaskOnSubmit: true });

  $('.NumeroCRM').inputmask('9{1,6}', { removeMaskOnSubmit: true });

  $('.CNPJ').inputmask('99.999.999/9999-99', { removeMaskOnSubmit: true });

  $('.INSS').inputmask('***********', { removeMaskOnSubmit: true });

  $('.numeros').inputmask('decimal', { 'alias': 'decimal', 'radixPoint': ',', 'groupSeparator': '', 'autoGroup': true });

  $('.EMAIL').inputmask({
    mask: "*{1,100}@*{1,20}[.*{2,6}][.*{1,2}]",
    greedy: false,
    onBeforePaste: function (pastedValue, opts) {
      pastedValue = pastedValue.toLowerCase();
      return pastedValue.replace("mailto:", "");
    },
    definitions: {
      '*': {
        validator: "[.0-9A-Za-z!#$%&'*+/=?^_`{|}~\-]",
        cardinality: 1,
        casing: "lower"
      }
    }
  });

  $('.URLINPUT').inputmask({
    mask: "http://*{1,100}",
    greedy: false,
    onBeforePaste: function (pastedValue, opts) {
      pastedValue = pastedValue.toLowerCase();
      return pastedValue.replace("mailto:", "");
    },
    definitions: {
      '*': {
        validator: "[.0-9A-Za-z!#$%&'*+/=?^_`{|}~\-]",
        cardinality: 1,
        casing: "lower"
      }
    }
  });

  $('.CRM').inputmask({
    mask: "a{1,20}[.a{1,20}][.a{1,20}][.a{1,20}]-9{1,6}",
    removeMaskOnSubmit: true,
    greedy: false,
    onBeforePaste: function (pastedValue, opts) {
      pastedValue = pastedValue.toLowerCase();
      return pastedValue.replace("mailto:", "");
    },
    definitions: {
      'a': {
        validator: "[A-Za-z]",
        cardinality: 1,
        casing: "upper"
      }
    }
  });

  $('.CRMAUX').inputmask({
    mask: "a{1,20}[.a{1,20}]-9{1,6}",
    removeMaskOnSubmit: true,
    greedy: false,
    onBeforePaste: function (pastedValue, opts) {
      pastedValue = pastedValue.toLowerCase();
      return pastedValue.replace("mailto:", "");
    },
    definitions: {
      'a': {
        validator: "[A-Za-z]",
        cardinality: 1,
        casing: "upper"
      }
    }
  });

  $(".porcentagem").inputmask('decimal', {
    removeMaskOnSubmit: true,
    alias: 'numeric',
    groupSeparator: '.',
    radixPoint: ",",
    digits: 2,
    autoGroup: true,
    integerDigits: 3,
    allowMinus: false,
    suffix: ' %',
    placeholder: ''
  });
}

RepasseConvenio.IniciaInputs = function () {
  $('.airDatePickerDateTime').datepicker({
    language: 'pt-BR',
    timepicker: true,
    dateFormat: 'dd/mm/yyyy',
    timeFormat: 'hh:ii aa'
  });

  $('.airDatePickerDate').datepicker({
    language: 'pt-BR',
    dateFormat: 'dd/mm/yyyy',
  });

  ////Date picker
  //$('.datepicker').datetimepicker({
  //  format: 'DD/MM/YYYY',
  //  widgetPositioning: {
  //    horizontal: 'auto',
  //    vertical: 'bottom'
  //  }
  //});

  //$('.timepicker').datetimepicker({
  //  format: 'HH:mm',
  //  widgetPositioning: {
  //    horizontal: 'auto',
  //    vertical: 'bottom'
  //  }
  //});

  //$('.hourpicker').datetimepicker({
  //  format: 'HH',
  //  widgetPositioning: {
  //    horizontal: 'auto',
  //    vertical: 'bottom'
  //  }
  //});

  //$('.minpicker').datetimepicker({
  //  format: 'mm',
  //  widgetPositioning: {
  //    horizontal: 'auto',
  //    vertical: 'bottom'
  //  }
  //});

  //$('.datepickerCurrentFalse').datetimepicker({
  //  format: 'DD/MM/YYYY',
  //  useCurrent: false,
  //  widgetPositioning: {
  //    horizontal: 'auto',
  //    vertical: 'bottom'
  //  }
  //});

  //$('.monyear').datetimepicker({
  //  format: 'YYYY-MM',
  //  useCurrent: false,
  //  widgetPositioning: {
  //    horizontal: 'auto',
  //    vertical: 'auto'
  //  }
  //});

  //$('.datetimepicker').datetimepicker({
  //  format: 'DD/MM/YYYY HH:mm',
  //  widgetPositioning: {
  //    horizontal: 'auto',
  //    vertical: 'bottom'
  //  }
  //});

  $(".monyear").on("dp.hide", function (e) {
    $(".monyear").datepicker('format', 'YYYY-MM');
  });
}

var Loading = {
  On: function () {
    $("#fundo-loading").css({ "opacity": "0.5", "display": "block", "z-index": "100000" });
  },
  OnPS: function () {
    $("#fundo-loading").css({ "opacity": "0.5", "display": "block", "z-index": "100000" });
    $("#fundo-loading").append("<div style='font-size: 25px; color: #fff; position: absolute; top: 45%;left: 50%;transform: translate(-50%, -50%);'><p>Gerando Guias... Favor não fechar o navegador!</p></div>");
  },
  Off: function () {
    $("#fundo-loading").css({ "opacity": "0", "display": "none" });
  }
}

window.onload = function () {
  Loading.Off();
  $('html, body').animate({
    scrollTop: 0
  }, 300);
};

jQuery.ajaxSetup({
  beforeSend: function (xhr) {
    Loading.On();
  },

  complete: function (xhr, status) {
    Loading.Off();
  }
});

//window.onload = function () {
//  Loading.Off();
//  $('html, body').animate({
//    scrollTop: 0
//  }, 300);
//};

$(document).ready(function () {
  RepasseConvenio.init();

  $(".modal .select-all > input").change(function () {
    if (this.checked) {
      $(".reply input").prop("checked", true);
    }
    else {
      $(".reply input").prop("checked", false);
    }
  });

  $(".modal-background").on("click", function () {
    $(".modal-background").hide();
    $(".modal").hide();
  });
});

function confirmaDelete(msg, url) {
  Swal.fire({
    title: "Deseja continuar a exclusão?",
    html: msg,
    icon: "warning",
    confirmButtonText: "Excluir",
    showCancelButton: true,
    cancelButtonText: "Cancelar",
    showLoaderOnConfirm: true,
    dangerMode: true,
  })
    .then((willDelete) => {
      if (willDelete.isConfirmed) {
        window.location = url;
      }
    });
}

function confirmaOperacao(title, msg, url) {
  swal({
    title: title,
    text: msg,
    type: "warning",
    showCancelButton: true,
    cancelButtonText: "N\u00e3o",
    confirmButtonColor: "#DD6B55",
    confirmButtonText: "Sim",
    closeOnConfirm: true
  }, function (isConfirm) {
    if (isConfirm) {
      location = url;
    } else {
      return false;
    }
  });
}

function SuccessToReload(title, msg) {
  swal({
    title: title,
    text: msg,
    type: "success",
    showCancelButton: false,
    cancelButtonText: "OK",
    //confirmButtonColor: "#DD6B55",
    //confirmButtonText: "OK",
    //closeOnConfirm: false
  }, function (isConfirm) {
    if (isConfirm) {
      location.reload();
    } else {
      return false;
    }
  });
}
function AlertToReload(title, message, type, textBtnConfirm) {
  if (textBtnConfirm == "" || textBtnConfirm == null || textBtnConfirm == undefined)
    textBtnConfirm = "OK";

  Swal.fire({
    title: title,
    html: message,
    icon: type,
    //showCancelButton: textBtnCancel,
    confirmButtonText: textBtnConfirm,
    showLoaderOnConfirm: true,
    dangerMode: true,
  })
    .then((will) => {
      window.location.reload();
    });
}

function ErroToReload(title, msg) {
  swal({
    title: title,
    text: msg,
    type: "error",
    showCancelButton: false,
    cancelButtonText: "OK",
    //confirmButtonColor: "#DD6B55",
    //confirmButtonText: "OK",
    //closeOnConfirm: false
  }, function (isConfirm) {
    if (isConfirm) {
      location.reload();
    } else {
      return false;
    }
  });
}

function inputAlert(title, msg, url) {

  swal({
    title: title,
    text: msg,
    type: "input",
    showCancelButton: true,
    closeOnConfirm: true,
    cancelButtonText: "Cancelar",
    animation: "slide-from-top",
    inputPlaceholder: title
  },
    function (inputValue) {
      if (inputValue === false) return false;

      if (inputValue === "") {
        swal.showInputError("Campo " + title + " é obrigatorio");
        return false
      }
      location = url + '/' + inputValue;
    });
}

function AlertaWithRedirect(title, message, type, textBtnConfirm, url) {
  Swal.fire({
    title: title,
    html: message,
    icon: type,
    //showCancelButton: textBtnCancel,
    confirmButtonText: textBtnConfirm,
    showLoaderOnConfirm: true,
    dangerMode: true,
  })
    .then((will) => {
      if (will.isConfirmed) {
        window.location = url;
      } else {
      }
    });
}

function AlertaWithCancelButtomRedirect(title, message, type, textBtnConfirm, textBtnCancel, url) {
  Swal.fire({
    title: title,
    html: message,
    icon: type,
    showCancelButton: true,
    confirmButtonText: textBtnConfirm,
    cancelButtonText: textBtnCancel,
    showLoaderOnConfirm: true,
    dangerMode: true,
  })
    .then((will) => {
      if (will.isConfirmed) {
        window.location = url;
      } else {
      }
    });
}

function Alerta(title, message, type, botao) {
  if (botao == "" || botao == null || botao == undefined)
    botao = "OK";
  swal.fire({
    title: title,
    html: message,
    icon: type,
    confirmButtonText: botao,
  })
}

function AlertaToast(title, message, type, timer, background) {
  swal.fire({
    toast: true,
    title: title,
    html: message,
    icon: type,
    showConfirmButton: false,
    background: background,
    timer: timer,
    position: 'top-end',
    timerProgressBar: true,
    onOpen: (toast) => {
      toast.addEventListener('mouseenter', Swal.stopTimer)
      toast.addEventListener('mouseleave', Swal.resumeTimer)
    }
  })

}

function GetURLBase() {
  var pathArray = location.href.split('/');
  var protocol = pathArray[0];
  var host = pathArray[2];
  var url = protocol + '//' + host;

  return url;
}

function GetURLBaseComplete() {
  var pathArray = location.href.split('/');
  var protocol = pathArray[0];
  var host = pathArray[2];
  var app = pathArray[3];
  var url = protocol + '//' + host + '/' + app;

  return url;
}
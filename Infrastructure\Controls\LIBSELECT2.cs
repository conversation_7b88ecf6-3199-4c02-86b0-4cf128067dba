﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Linq.Expressions;
using System.Text;
using System.Web.Mvc.Html;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using System.Web.Routing;
using System.Configuration;
using PortalCooperadoHelpers.Infraestrutura.Helpers;using PortalCooperadoHelpers.Models;

namespace RepasseConvenio.Infrastructure.Controls
{
  public class Select2Configuration
  {
    public Select2Configuration()
    {
    }

    public bool Disabled { get; set; }
  }

  public class LibSelect2<TModel, TProperty>
  {
    public LibSelect2(HtmlHelper<TModel> HtmlHelper, Expression<Func<TModel, TProperty>> Expression, object Atributes, Select2Configuration select2Configuration = null)
    {
    }

    public HtmlHelper<TModel> htmlHelper { get; set; }

    public Expression<Func<TModel, TProperty>> expression { get; set; }

    public object atributes { get; set; }
    public Select2Configuration customAtributes { get; set; }

    public string RenderHtml()
    {
      return "";
    }
  }
}
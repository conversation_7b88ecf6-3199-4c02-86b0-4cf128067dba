﻿ProtocoloGlosa = function () {
};

ProtocoloGlosa.init = function () {
  $(document).on("click", ".TrSelecionavel", function () {
    var IdProtocoloGlosa = $(this).data("codigoprotocologlosa");
    var urlDetalhes = GetURLBaseComplete() + '/ProtocoloGlosa/Detalhes?CodigoProtocoloGlosa=' + IdProtocoloGlosa;

    if (!$(this).hasClass("Selected")) {
      $('.Selected').each(function (index, element) {
        $(element).removeClass("Selected")
        $(element).addClass("UnSelected")
      });

      if ($(this).hasClass("Selected")) {
        $(this).removeClass("Selected")
        $(this).addClass("UnSelected")
      }
      else {
        $(this).addClass("Selected")
        $(this).removeClass("UnSelected")
      }
    }
    else {
      $(this).removeClass("Selected")
      $(this).addClass("UnSelected")
    }

    var countSelected = 0;
    $('.Selected').each(function (index, element) {
      if ($(element).hasClass("Selected")) {
        $('#DetalhesProtocolo').attr("href", urlDetalhes);
        countSelected++;
      }
    });

    if (countSelected == 0) {
      $('#DetalhesProtocolo').removeAttr("href");
      $('#DetalhesProtocolo').attr("disabled", true);
      $('#DetalhesProtocolo').addClass("disabled");
    }
    else {
      $('#DetalhesProtocolo').attr("disabled", false);
      $('#DetalhesProtocolo').removeClass("disabled");
    }
  });
}


$(document).ready(function () {
  ProtocoloGlosa.init();
});

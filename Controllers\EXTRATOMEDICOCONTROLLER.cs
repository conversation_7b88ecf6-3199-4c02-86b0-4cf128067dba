﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using Microsoft.AspNet.SignalR.Messaging;
using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Controls;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace RepasseConvenio.Controllers
{
  [Authorize]
  [Security("4C666EF7-B124-41DA-8758-08586A0BE894")]

  public class ExtratoMedicoController : LibController
  {

    private ExtratoMedicoServices ExtratoMedicoServices
    {
      get
      {
        if (_ExtratoMedicoServices == null)
          _ExtratoMedicoServices = new ExtratoMedicoServices(ContextoUsuario.UserLogged);

        return _ExtratoMedicoServices;
      }
    }
    private ExtratoMedicoServices _ExtratoMedicoServices;
    [Security("2D1275A9-9BAC-4593-8AE6-A7DBD73D533B")]

    public ActionResult Index(int id, int? Page)
    {
      try
      {
        MedicoService medicoService = new MedicoService();
        ViewBag.NomeMedico = medicoService.GetNomeMedicoById(id).ToUpper();
        int numpag = Page ?? 1;
        List<ExtratoMedicoIndex> model = ExtratoMedicoServices.Get(id, numpag);
        ExtratoMedicoFiltroIndex filtroModel = new ExtratoMedicoFiltroIndex(id);
        filtroModel.ListExtratoMedicoIndex = model;
        ViewBag.CodigoMedico = id;
        return View(filtroModel);
      }
      catch (Exception)
      {
        throw;
      }
    }

    [HttpPost]
    [Security("2D1275A9-9BAC-4593-8AE6-A7DBD73D533B")]

    public ActionResult Index(ExtratoMedicoFiltroIndex filtro)
    {
      try
      {
        ExtratoMedicoServices ExtratoMedicoServices = new ExtratoMedicoServices(ContextoUsuario.UserLogged);
        filtro.ListExtratoMedicoIndex = ExtratoMedicoServices.Get(filtro);

        if (filtro.ListExtratoMedicoIndex.Count > 0)
          MessageListToast.Add(new RepasseConvenio.Infrastructure.Controls.Message(MessageType.Success, "Busca realizada com sucesso."));
        else
          MessageListToast.Add(new RepasseConvenio.Infrastructure.Controls.Message(MessageType.Warning, "Nenhum extrato foi encontrado."));

        return View("Index", filtro);
      }
      catch (CustomException ex)
      {
        MessageListToast.Add(new RepasseConvenio.Infrastructure.Controls.Message(MessageType.Error, ex.Message));
        return RedirectToAction("Index");
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new RepasseConvenio.Infrastructure.Controls.Message(MessageType.Error, "Tivemos uma falha na sua solicitação."));
        return RedirectToAction("Index");
      }
    }
    [Security("F19C9701-BD0D-4F8A-BD1B-0B04710FAAB7")]

    public ActionResult Create(int id)
    {
      try
      {
        ExtratoMedicoModel extratoMedico = new ExtratoMedicoModel();

        extratoMedico.CodigoMedico = id;
        extratoMedico.DataApuracao = DateTime.Now;

        return View(extratoMedico);
      }
      catch (Exception)
      {
        throw;
      }
    }

    [HttpPost]
    [Security("F19C9701-BD0D-4F8A-BD1B-0B04710FAAB7")]

    public ActionResult Create(ExtratoMedicoModel extrato)
    {
      try
      {
        if (extrato.TipoContaEnum.Equals(EnumTipoConta.PF))
        {
          R_Medico medico = new MedicoService().GetById(extrato.CodigoMedico);

          if (medico == null)
            throw new Exception("Não foi possivel encontrar o Médico.");

          extrato.CPFCNPJ = medico.M_CPF;
        }

        if (ModelState.IsValid)
        {
          ExtratoMedicoServices.Create(extrato);
          MessageListToast.Add(new RepasseConvenio.Infrastructure.Controls.Message(MessageType.Success, "Extrato criado com sucesso."));
          return RedirectToAction("Index", new { id = extrato.CodigoMedico });
        }
        MessageListToast.Add(new RepasseConvenio.Infrastructure.Controls.Message(MessageType.Error, "Favor verificar os campos preenchidos."));
        return View(extrato);
      }
      catch (CustomException ex)
      {
        MessageListToast.Add(new RepasseConvenio.Infrastructure.Controls.Message(MessageType.Error, ex.Message));
        return View(extrato);
      }
      catch (Exception)
      {
        MessageListToast.Add(new RepasseConvenio.Infrastructure.Controls.Message(MessageType.Error, "Favor verificar os campos preenchidos."));
        return View(extrato);
      }
    }
    [Security("A6CD8BEA-1434-408E-B0F9-30B37AF451F8")]

    public ActionResult Edit(int? id)
    {
      try
      {
        if (id == null)
          throw new Exception("Nenhum Extrato Médico foi selecionado");

        ExtratoMedicoEdit extrato = ExtratoMedicoServices.GetExtratoMedicoEditById(id.Value);

        if (extrato == null)
          throw new Exception("Não foi possivel encontrar este extrato");

        return View(extrato);
      }
      catch (Exception)
      {
        throw;
      }
    }

    [HttpPost]
    [Security("A6CD8BEA-1434-408E-B0F9-30B37AF451F8")]

    public ActionResult Edit(ExtratoMedicoEdit extrato)
    {
      try
      {
        if (ModelState.IsValid)
        {
          ExtratoMedicoServices.Edit(extrato);
          MessageListToast.Add(new Infrastructure.Controls.Message(MessageType.Success, "ExtratoEditado", "12000"));
          return RedirectToAction("Index", new { id = extrato.CodigoMedico });
        }
        MessageListToast.Add(new Infrastructure.Controls.Message(MessageType.Error, "Gentileza verificar os campos preenchidos.", "12000"));
        return View(extrato);
      }
      catch (CustomException ex)
      {
        MessageListToast.Add(new Infrastructure.Controls.Message(MessageType.Error, ex.Message, "12000"));
        return View(extrato);
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Infrastructure.Controls.Message(MessageType.Error, "Não foi possível editar o Extrato, entre em contato com o administrador.", "12000"));
        return View(extrato);
      }
    }

    [HttpPost]
    [Security("F9693875-35E4-47F9-ADE0-93C6ED275A9C")]

    public JsonResult Delete(int IdExtrato)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        ExtratoMedicoServices extratoMedicoServices = new ExtratoMedicoServices(ContextoUsuario.UserLogged);
        extratoMedicoServices.Delete(IdExtrato);
        retornoAjax.Erro = false;
        retornoAjax.Mensagem = "Deletado com sucesso.";
        retornoAjax.Titulo = "Sucesso";
        return Json(retornoAjax);
      }
      catch (CustomException ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Titulo = "Erro";
        return Json(retornoAjax);

      }
      catch (Exception ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.Mensagem = "Ocorreu uma falha na sua solicitação.";
        retornoAjax.Titulo = "Erro";
        return Json(retornoAjax);
      }
    }


    [HttpGet]
    [Security("41601343-B60A-4277-A304-706C32B470D7")]

    public PartialViewResult _GridExtratos(int id, int? Page)
    {
      int numpag = Page ?? 1;
      List<ExtratoMedicoIndex> model = ExtratoMedicoServices.Get(id, numpag);
      ViewBag.CodigoMedico = id;
      return PartialView("_GridExtratos", model);
    }

    [HttpGet]
    [Security("41601343-B60A-4277-A304-706C32B470D7")]

    public PartialViewResult _GridLogs(int id)
    {
      LogExtratoMedicoServices logExtratoMedicoServices = new LogExtratoMedicoServices();
      List<LogExtratoMedicoModal> model = logExtratoMedicoServices.GetLogExtratoMedicoModal(id);
      return PartialView("_GridLogs", model);
    }

    [HttpGet]
    [Security("6661E58B-576C-447E-98FA-D357146DF4A7")]

    public PartialViewResult _GridLogsDetalhes(int id)
    {
      try
      {
        LogExtratoMedicoServices logExtratoMedicoServices = new LogExtratoMedicoServices();
        List<LogAux> model = logExtratoMedicoServices.GetLogLogAux(id);
        return PartialView("_GridLogsDetalhes", model);
      }
      catch (CustomException ex)
      {
        return PartialView();
      }
      catch (Exception ex)
      {
        return PartialView();
      }
    }
  }
}
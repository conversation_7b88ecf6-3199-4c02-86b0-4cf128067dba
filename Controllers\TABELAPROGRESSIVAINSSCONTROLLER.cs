﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Controls;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;

namespace RepasseConvenio.Controllers
{
  [Authorize]
  [Security("31847B60-E25E-4167-9AB1-A9EB7F6F1022")]
  public class TabelaProgressivaINSSController : LibController
  {
    private TabelaProgressivaINSSServices TabelaProgressivaINSSServices
    {
      get
      {
        if (_TabelaProgressivaINSSServices == null)
          _TabelaProgressivaINSSServices = new TabelaProgressivaINSSServices(ContextoUsuario.UserLogged);

        return _TabelaProgressivaINSSServices;
      }
    }
    private TabelaProgressivaINSSServices _TabelaProgressivaINSSServices;
    private ValoresTabelaProgressivaINSSServices ValoresTabelaProgressivaINSSServices
    {
      get
      {
        if (_ValoresTabelaProgressivaINSSServices == null)
          _ValoresTabelaProgressivaINSSServices = new ValoresTabelaProgressivaINSSServices(ContextoUsuario.UserLogged);

        return _ValoresTabelaProgressivaINSSServices;
      }
    }
    private ValoresTabelaProgressivaINSSServices _ValoresTabelaProgressivaINSSServices;
  [Security("863BCA4F-CE4A-40E0-BCC5-DAD8EB48ADAC")]
    public ActionResult Index(int? page, string filtro)
    {
      try
      {
        int pageNumber = page ?? 1;

        ViewBag.page = pageNumber;
        ViewBag.filtro = filtro;

        var list = TabelaProgressivaINSSServices.Get(pageNumber, filtro);
        return View(list);
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return View();
      }
    }
  [Security("7CBD0133-A2FA-4989-8AC2-1463209F0A12")]
    public ActionResult Create()
    {
      try
      {
        TabelaProgressivaINSSModel model = new TabelaProgressivaINSSModel();
        model.DtInicioVigencia = DateTime.Now;
        return View(model);
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return RedirectToAction("Index", "TabelaProgressivaINSS");
      }
    }
    [Security("7CBD0133-A2FA-4989-8AC2-1463209F0A12")]
    [HttpPost]
    public ActionResult Create(TabelaProgressivaINSSModel model)
    {
      try
      {
        TabelaProgressivaINSSServices.Create(model);
        MessageListToast.Add(new Message(MessageType.Success, LibMessageToast.MessageCreateSucess));
        return RedirectToAction("Index", "TabelaProgressivaINSS");
      }
      catch (CustomException ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return View(model);
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return View(model);
      }
    }

    [Security("41F3AF4A-7EF0-4385-B26B-FC7CA19B0203")]
    public ActionResult Edit(int codigo)
    {
      try
      {
        TabelaProgressivaINSSModel model = TabelaProgressivaINSSServices.GetById(codigo).OffEntityToModel();
        model.ValoresTabelaINSS = ValoresTabelaProgressivaINSSServices.GetByIdTabelaINSS(codigo).Select(a => a.OffEntityToModel()).ToList();
        return View(model);
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return RedirectToAction("Index", "TabelaProgressivaINSS");
      }
    }
    [Security("41F3AF4A-7EF0-4385-B26B-FC7CA19B0203")]
    [HttpPost]
    public ActionResult Edit(TabelaProgressivaINSSModel model)
    {
      try
      {
        TabelaProgressivaINSSServices.Edit(model);
        MessageListToast.Add(new Message(MessageType.Success, LibMessageToast.MessageEditSucess));
        return RedirectToAction("Index", "TabelaProgressivaINSS");
      }
      catch (CustomException ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return View(model);
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return View(model);
      }
    }

    [Security("EAFF320A-02A6-49C8-9A85-DD049B063E6D")]

    public ActionResult Delete(int codigo)
    {
      try
      {
        TabelaProgressivaINSSServices.Delete(codigo);

        MessageListToast.Add(new Message(MessageType.Success, LibMessageToast.MessageDeleteSucess));
        return RedirectToAction("Index", "TabelaProgressivaINSS");
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return RedirectToAction("Index", "TabelaProgressivaINSS");
      }
    }

    [HttpGet]
    public PartialViewResult GridValoresTPINSS(int id)
    {
      try
      {
        List<ValoresTabelaProgressivaINSSModel> list;
        list = ValoresTabelaProgressivaINSSServices.GetByIdTabelaINSS(id).Select(a => a.OffEntityToModel()).ToList();
        return PartialView("_GridValores", list);
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }

    [HttpPost]
    public ActionResult DeleteTabela(int codigo)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        ValoresTabelaProgressivaINSSServices.Delete(codigo);
        retornoAjax.Titulo = "Sucesso";
        retornoAjax.Mensagem = "Valor Tabela INSS removido com sucesso.";
        retornoAjax.TipoMensagem = "success";
        retornoAjax.Erro = false;
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
      catch (CustomException ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
      catch (Exception ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
    }
  }
}
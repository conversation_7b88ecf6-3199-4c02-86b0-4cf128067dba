﻿$(document).on("click", ".CrSelecionavel", function () {
  var id = $(this).data("codigo");
  var urlEdit = GetURLBaseComplete() + "/ClassificacaoRepasse/Edit?id=" + id;
  

  if (!$(this).hasClass("Selected")) {
    $('.Selected').each(function (index, element) {
      $(element).removeClass("Selected")
      $(element).addClass("UnSelected")
    });

    if ($(this).hasClass("Selected")) {
      $(this).removeClass("Selected")
      $(this).addClass("UnSelected")
    }
    else {
      $(this).addClass("Selected")
      $(this).removeClass("UnSelected")
    }
  }
  else {
    $(this).removeClass("Selected")
    $(this).addClass("UnSelected")
  }

  var countSelected = 0;
  $('.Selected').each(function (index, element) {
    if ($(element).hasClass("Selected")) {
      $('#Editar').attr("href", urlEdit);
      
      countSelected++;
    }
  });

  if (countSelected == 0) {
    $('#Editar').removeAttr("href");
  

    $('#Editar').attr("disabled", true);
    

    $('#Editar').addClass("disabled");
   
  }
  else {
    $('#Editar').attr("disabled", false);
    
    $('#Editar').removeClass("disabled");
   
  }
});

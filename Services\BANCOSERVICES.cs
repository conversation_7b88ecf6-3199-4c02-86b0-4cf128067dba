﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using PagedList;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RepasseConvenio.Services
{
  public class BancoServices : ServiceBase
  {
    public BancoServices(RepasseEntities Contexto)
        : base(Contexto)
    { }
    public BancoServices()
       : base()
    { }

    public BancoServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public BancoServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public R_Banco GetById(int Id)
    {
      return Contexto.R_Banco.Where(a => a.B_Id == Id).FirstOrDefault();
    }



    public List<R_Banco> GetByTerm(string term)
    {
      if (string.IsNullOrEmpty(term))
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                     R_Banco");

        return Contexto.Database.SqlQuery<R_Banco>(query).ToList();

      }
      else
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                    R_Banco
                                 WHERE B_Nome LIKE @termo  or B_Codigo Like @termo ");

        return Contexto.Database.SqlQuery<R_Banco>(query, new SqlParameter("@termo", string.Format("%{0}%", term))).ToList();

      }
    }
  }
}


﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Models;
using RepasseConvenio.WSPortalCooperado;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.Entity;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Text;
using System.Transactions;
using System.Web;
using System.Data.OleDb;
using RepasseConvenio.Infrastructure;
using Microsoft.Ajax.Utilities;
using System.Linq.Expressions;

namespace RepasseConvenio.Services
{
  public class GuiaAtendimentoService : ServiceBase
  {
    public GuiaAtendimentoService()
   : base()
    { }
    public GuiaAtendimentoService(RepasseEntities Contexto)
       : base(Contexto)
    { }

    public GuiaAtendimentoService(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public GuiaAtendimentoService(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public R_GuiaAtendimento GetGuiaAtendimentoNroUnicooper(string NroUnicooper)
    {
      string query = @"SELECT 
                        *
                       FROM R_GuiaAtendimento
                       WHERE  GA_NroUnicooper = @NroUnicooper";

      return Contexto.Database.SqlQuery<R_GuiaAtendimento>(query, new SqlParameter("@NroUnicooper", NroUnicooper)).FirstOrDefault();
    }


    public R_GuiaAtendimento GetByIdGuiaAtendimento(int Id)
    {
      string query = @"SELECT 
                        *
                       FROM R_GuiaAtendimento
                       WHERE  GA_Id = @Id";

      return Contexto.Database.SqlQuery<R_GuiaAtendimento>(query, new SqlParameter("Id", Id)).FirstOrDefault();
    }

    public R_GuiaAtendimento GetByNroUnicooperAtendimento(string Nrounicooper)
    {
      string query = @"SELECT 
                        *
                       FROM R_GuiaAtendimento
                       WHERE  GA_NroUnicooper = @Nrounicooper";

      return Contexto.Database.SqlQuery<R_GuiaAtendimento>(query, new SqlParameter("@Nrounicooper", Nrounicooper)).FirstOrDefault();
    }



    public R_GuiaAtendimento GetGuiaAtendimento(string NroUnicooper)
    {
      string query = @"SELECT 
                        *
                       FROM R_GuiaAtendimento
                       WHERE GA_NroUnicooper = @NroUnicooper";

      return Contexto.Database.SqlQuery<R_GuiaAtendimento>(query, new SqlParameter("@NroUnicooper", NroUnicooper)).FirstOrDefault();
    }

    public void IntegraGuias(RetornoGetGuiaLoteRepasse retornoGetGuiaLoteRepasse)
    {
      StatusLoteServices statusLoteServices = new StatusLoteServices();
      ConvenioServices convenioServices = new ConvenioServices();
      ProcGuiaAtendimentoService procGuiaAtendimentoService = new ProcGuiaAtendimentoService();
      PartGuiaAtendimentoService partGuiaAtendimentoService = new PartGuiaAtendimentoService();
      LoteServices loteServices = new LoteServices();
      ItensLoteServices itensLoteServices = new ItensLoteServices();
      LogGuiaAtendimentoService logGuiaAtendimentoService = new LogGuiaAtendimentoService(User);

      LoteRepasse loteRepasse = retornoGetGuiaLoteRepasse.loteRepasse;
      int IdLote = loteServices.IntegraLotes(loteRepasse);
      foreach (GuiaRepasseWS item in loteRepasse.ListaGuiasRepasse)
      {
        R_GuiaAtendimento guiaAtendimento = GetGuiaAtendimentoNroUnicooper(item.NroUnicooper);
        if (guiaAtendimento == null)
        {
          using (TransactionScope scope = new TransactionScope(TransactionScopeOption.RequiresNew))
          {
            int IdConvenio = convenioServices.GetIdByCNPJ(item.CNPJConvenio);
            guiaAtendimento = item.ToGuiaAtendimentoCreate(IdConvenio);
            Create(guiaAtendimento);
            logGuiaAtendimentoService.CreateLog("Guia Integrada do portal.");
            procGuiaAtendimentoService.IntegraProcGuiaAtendimento(item.Procedimentos.ToList(), guiaAtendimento.GA_Id);
            logGuiaAtendimentoService.CreateLog(string.Format("Integrado do portal {0} procedimentos para guia.", item.Procedimentos.Count()));
            partGuiaAtendimentoService.IntegraPartGuiaAtendimento(item.ParticipacaoGuia, guiaAtendimento.GA_Id, item.CPFCooperado);
            itensLoteServices.IntegraItensLotes(IdLote, guiaAtendimento.GA_Id, guiaAtendimento.GA_NroUnicooper);
            scope.Complete();
          }
        }
      }
    }

    public GuiaAtendimentoGrid GetGuiaAtendimentoGrid(int IdGuiaAtendimento)
    {
      ProcGuiaAtendimentoService procGuiaAtendimentoService = new ProcGuiaAtendimentoService();

      string query = @"SELECT
                        GA.GA_NomeSolicitante [Hospital],
                        C.C_RazaoSocial [Convenio],
                        GA.GA_AtendimentoHospital [AtendimentoHospital],
                        GA.GA_NroUnicooper [GuiaUnicooper],
                        GA.GA_Complemento [Complemento],
                        GA.GA_DescPlano [Plano],
                        GA.GA_DtAutorizacaoGuia [DataAutorizacao],
                        GA.GA_DataEntrega [DataEntrega],
                        GA.GA_TipoGuia [TipoGuia],
                        GA.GA_NroUnicooper [Guia],
                        GA.GA_CodGuiaPrincipal [GuiaPrincipal],
                        GA.GA_SolicitacaoInternacao [SolicitacaoInternacao],
                        GA.GA_CodSenhaGuia [Senha],
                        GA.GA_NomePaciente [NomePaciente],
                        GA.GA_CodCarteiraPaciente [CarteirinhaPaciente],
                        GA.GA_DtInicioFaturamento [DataInicioFaturamento],
                        GA.GA_DtFimFaturamento [DataFimFaturamento],
                        GA.GA_DescTipoAcomodacao [Acomodacao],
                        GA.GA_DescTipoAtendimento [TipoAtendimento],
                        GA.GA_DataInternacao [DataInternacao],
                        GA.GA_DataAlta [DataAlta],
                        M.M_CPF [CPFMedico],
                        M.M_Nome [NomeMedico],
                        M.M_CRM [CRMMedico]
                       FROM R_GuiaAtendimento GA
                       INNER JOIN R_Convenio C ON C.C_Id = GA.GA_IdConvenio AND GA.GA_Id = @IdGuiaAtendimento
                       INNER JOIN R_PartGuiaAtendimento PGA ON PGA.PGA_IdGuiaAtendimento = GA.GA_Id
                       INNER JOIN R_Medico M ON M.M_Id = PGA.PGA_IdMedico";


      GuiaAtendimentoGrid guiaAtendimentoGrid = Contexto.Database.SqlQuery<GuiaAtendimentoGrid>(query, new SqlParameter("@IdGuiaAtendimento", IdGuiaAtendimento)).FirstOrDefault();
      guiaAtendimentoGrid.ListaProcGuiaAtendimentoGrid = procGuiaAtendimentoService.GetListProcGuiaAtendimentoGrid(IdGuiaAtendimento);
      return guiaAtendimentoGrid;
    }

    public void UpdateValoresGuiaAtendimento(decimal valorPago, decimal valorGlosa, int IdGuiaAtendimento)
    {
      string query = @"UPDATE R_GuiaAtendimento SET GA_ValorPago = @valorPago, GA_VlrGlosa = @valorGlosa WHERE GA_Id = @IdGuiaAtendimento";
      Contexto.Database.ExecuteSqlCommand(query, new SqlParameter("@valorPago", valorPago)
                                               , new SqlParameter("@valorGlosa", valorGlosa)
                                               , new SqlParameter("@IdGuiaAtendimento", IdGuiaAtendimento));
    
    }

    public void UpdateValoresGuiaAtendimentoOnDelete(decimal valorPago, int IdGuiaAtendimento)
    {
      R_GuiaAtendimento guiaAtendimento = GetByIdGuiaAtendimento(IdGuiaAtendimento);
      guiaAtendimento.GA_ValorPago = valorPago;
      Edit(guiaAtendimento);

      guiaAtendimento.GA_VlrGlosa = guiaAtendimento.GA_ValorFaturado - guiaAtendimento.GA_ValorPago;
      Edit(guiaAtendimento);
    }
  }
}
﻿<?xml version="1.0" encoding="utf-8"?>

<!-- For more information on using Web.config transformation visit https://go.microsoft.com/fwlink/?LinkId=301874 -->

<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">
  <!--
    No exemplo abaixo, a transformação "SetAttributes" alterará o valor de
    "connectionString" para usar "ReleaseSQLServer" apenas quando o localizador "Match" 
    encontrar um atributo "name" que tenha um valor de "MyDB".

    <connectionStrings>
      <add name="MyDB"
        connectionString="Data Source=ReleaseSQLServer;Initial Catalog=MyReleaseDB;Integrated Security=True"
        xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>
    </connectionStrings>
  -->
  <system.web>
    <!--
      No exemplo abaixo, a transformação "Replace" vai substituir toda a seção
      <customErrors> do seu arquivo Web.confige.
      Observe que como há somente uma seção customErrors no nó 
      <system.web>, não há necessidade de usar o atributo "xdt:Locator".

      <customErrors defaultRedirect="GenericError.htm"
        mode="RemoteOnly" xdt:Transform="Replace">
        <error statusCode="500" redirect="InternalError.htm"/>
      </customErrors>
    -->
  </system.web>
</configuration>

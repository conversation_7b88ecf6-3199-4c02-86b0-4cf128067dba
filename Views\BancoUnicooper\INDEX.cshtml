﻿@using RepasseConvenio.Infrastructure
@using RepasseConvenio.Models

@using PagedList.Mvc;
@model PagedList.IPagedList<BancoUnicooperModel>
<script src="~/Views/BancoUnicooper/BancoUnicooper.js"></script>
<link href="~/Views/BancoUnicooper/TabelaBanco.css" rel="stylesheet" />


@{
  ViewBag.Title = "Banco Unicooper";
  Layout = "~/Views/Shared/_Layout.cshtml";
}




<div class="row">
  <section class="col-lg-12 connectedSortable ui-sortable">
    <div class="card">
      <div class="card-header ui-sortable-handle">
        <h3 class="card-title">
          <i class="fas fa-chart-pie mr-1"></i>
          Bancos
        </h3>

        <div class="card-tools">
          @if (ContextoUsuario.UserLogged.IsInPermissao(Guid.Parse("CBC72FD3-BDEB-4D7F-A457-2B63FE9B25BF")))
          {
            <button type="button" value="Create" class="btn btn-primary btn-sm-header" onclick="location.href='@Url.Action("Create", "BancoUnicooper")'">Novo</button>
          }
        </div>

      </div><!-- /.card-header -->
      <div class="card-body">
        <div class="tab-content p-0">
          <div class="box-body">
            <div class="card-acoes col-4">
              @if (ContextoUsuario.UserLogged.IsInPermissao(Guid.Parse("DE3D14E9-355C-4B76-A98E-E203F50C68F1")))
              {
                <a class="btn btn-block btn-outline-primary disabled" id="IdBancoEdit"> Editar </a>
              }
              @if (ContextoUsuario.UserLogged.IsInPermissao(Guid.Parse("3CF6F17C-3185-4794-88B9-8A640BBE5CCB"))) { 
                <a class="btn btn-block btn-outline-primary disabled" id="IdBancoDelete"> Excluir </a>
          }
            </div>

            <div class="row">
              <div class="col-md-12 table-responsive p-0 ">
                <table class="table table-sm table-striped table-hover text-nowrap">
                  <thead>
                    <tr>
                      <th>
                        Banco
                      </th>
                      <th>
                        Descrição
                      </th>
                      <th>
                        Agência
                      </th>
                      <th>
                        Conta
                      </th>
                      <th>
                        Dígito
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    @foreach (BancoUnicooperModel item in Model)
                    {
                      <tr class="TrSelecionavel" data-codigobanco="@item.Codigo">
                        <td>
                          @item.BancoNome
                        </td>
                        <td>
                          @item.Descricao
                        </td>
                        <td>
                          @item.Agencia
                        </td>
                        <td>
                          @item.conta
                        </td>
                        <td>
                          @item.Digito
                        </td>

                      </tr>
                    }
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <div class="box-footer with-border" style="padding: 0 10pt;">

          </div>
        </div>
      </div>
    </div>
    @*<div class="card-footer">
        <button type="button" value="Voltar" class="btn btn-secondary" onclick="window.location.href='@Url.Action("Index","LoteGlosa")'">Voltar</button>
      </div>*@
  </section>
</div>




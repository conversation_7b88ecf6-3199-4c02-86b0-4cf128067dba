﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Services;
using RepasseConvenio.WSPortalCooperado;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace RepasseConvenio.Models
{
  public class LoteModel
  {
    public int Codigo { get; set; }
    public string NumeroLote { get; set; }
    public DateTime DataCriacao { get; set; }
    public DateTime DataEnvio { get; set; }
    public DateTime? DataVencimento { get; set; }

    public int  IdStatusLote { get; set; }

    public string StatusLote 
   
    {
      get
      {
        if ( IdStatusLote > 0)
          return new LoteServices().GetById(IdStatusLote).SL_Descricao;
        else
          return "-";
      }
      set
      {
        this.StatusLote = value;
      }
    }

    public StatusLoteEnum statusLoteEnum { get; set; }
  }

  public class LoteRepassaeModal
  {
    public int Codigo { get; set; }

    public string NumeroLote { get; set; }

    public DateTime DataCriacao { get; set; }

    public DateTime DataEnvio { get; set; }

    public DateTime? DataVencimento { get; set; }

    public string DescricaoStatus { get; set; }

  }


  public static class LoteConversions
  {
    public static R_Lote ModelToEntityCreate(this LoteRepasse loteRepasse, int IdStatusLoteNaoProcessado)
    {
      return new R_Lote()
      {
        L_DataCriacao = loteRepasse.DataCriacao,
        L_DataEnvio = loteRepasse.DataEnvio,
        L_DataVencimento = loteRepasse.DataVencimento,
        L_NumeroLote = loteRepasse.NumeroLote.ToString(),
        L_IdStatusLote = IdStatusLoteNaoProcessado
      };
    }
  }
}
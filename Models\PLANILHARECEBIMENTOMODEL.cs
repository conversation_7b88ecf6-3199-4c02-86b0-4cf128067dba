﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace RepasseConvenio.Models
{
  public class PlanilhaRecebimentoModel
  {
    public int PRM_Codigo { get; set; }
    [DisplayName("Nome")]
    public string PRM_Nome { get; set; }
    [DisplayName("Número Guia")]
    public string PRM_NumeroGuia { get; set; }
    [DisplayName("Total Faturado")]
    public string PRM_TotalFaturadoText
    {
      get
      {
        return PRM_TotalFaturado.ToString();
      }
      set
      {
        string valText = string.IsNullOrEmpty(value) ? "0" : value.Replace("R$", "").Replace(" ", "");
        decimal val = 0;
        decimal.TryParse(valText, out val);
        PRM_TotalFaturado = val;
      }
    }
    [DisplayName("Total Glosado")]
    public string PRM_TotalGlosadoText
    {
      get
      {
        return PRM_TotalGlosado.ToString();
      }
      set
      {
        string valText = string.IsNullOrEmpty(value) ? "0" : value.Replace("R$", "").Replace(" ", "");
        decimal val = 0;
        decimal.TryParse(valText, out val);
        PRM_TotalGlosado = val;
      }
    }
    [DisplayName("Total Pago")]
    public string PRM_TotalPagoText
    {
      get
      {
        return PRM_TotalPago.ToString();
      }
      set
      {
        string valText = string.IsNullOrEmpty(value) ? "0" : value.Replace("R$", "").Replace(" ", "");
        decimal val = 0;
        decimal.TryParse(valText, out val);
        PRM_TotalPago = val;
      }
    }

    public decimal PRM_TotalFaturado { get; set; }
    public decimal PRM_TotalGlosado { get; set; }
    public decimal PRM_TotalPago { get; set; }

    public int PRM_CodigoRepasse { get; set; }
    public DateTime PRM_DataCriacao { get; set; }
    public int PRM_CodigoUsuarioCriacao { get; set; }
    public DateTime? PRM_DataAlteracao { get; set; }
    public int? PRM_CodigoUsuarioAlteracao { get; set; }

    public decimal? PRM_ValorApresentado { get; set; }
    public string PRM_NumAtendimento { get; set; }
    public string PRM_CarteiraBeneficiario { get; set; }
    public DateTime? PRM_DataAtendimento { get; set; }

    public bool? PRM_Processado { get; set; }
    public DateTime? PRM_DataProcessamento { get; set; }
    public string PRM_ObsProcessamento { get; set; }

    [NotMapped]
    public bool isSelected { get; set; }

  }

  public class PlanilhaRecebimentoDelete
  {
    public int Codigo { get; set; }

    public string NumeroGuia { get; set; }
  }

  public static class PlanilhaRecebimentoModelConversions
  {
    //public static R_PlanilhaRecebimento ToPlanilhaRecebimentoCreate(this PlanilhaRecebimentoModel model, int IdUsuario)
    //{
    //  R_PlanilhaRecebimento entity = new R_PlanilhaRecebimento();
    //  //entity.PR_Id = model.Codigo;
    //  entity.PR_NumeroGuia = model.PRM_NumeroGuia;
    //  entity.PR_Nome = model.PRM_Nome;
    //  entity.PR_TotalFaturado = model.PRM_TotalFaturado;
    //  entity.PR_TotalGlosado = model.PRM_TotalGlosado;
    //  entity.PR_TotalPago = model.PRM_TotalPago;
    //  entity.PR_IdRepasse = model.PRM_CodigoRepasse;
    //  entity.PR_DataCriacao = DateTime.Now;
    //  entity.PR_IdUsuarioCriacao = IdUsuario;
    //  entity.RP_DataAlteracao = model.PRM_DataAlteracao;
    //  entity.RP_IdUsuarioAlteracao = model.PRM_CodigoUsuarioAlteracao;
    //  entity.PR_ValorApresentado = model.PRM_ValorApresentado;
    //  entity.PR_NumAtendimento = model.PRM_NumAtendimento;
    //  entity.PR_CarteiraBeneficiario = model.PRM_CarteiraBeneficiario;
    //  entity.PR_DataAtendimento = model.PRM_DataAtendimento;

    //  return entity;
    //}

    //[Obsolete("Método a ser contruído.", true)]
    //public static bool ToCadastroValido(this PlanilhaRecebimentoModel model)
    //{
    //  model.PRM_NumeroGuia;
    //  model.PRM_Nome;
    //  model.PRM_TotalFaturado;
    //  model.PRM_TotalGlosado;
    //  model.PRM_TotalPago;
    //  model.PRM_CodigoRepasse;
    //  model.PRM_DataAlteracao;
    //  model.PRM_CodigoUsuarioAlteracao;
    //  model.PRM_ValorApresentado;
    //  model.PRM_NumAtendimento;
    //  model.PRM_CarteiraBeneficiario;
    //  model.PRM_DataAtendimento;

    //  return true;
    //}

    //public static R_PlanilhaRecebimento ToPlanilhaRecebimentoEdit(this PlanilhaRecebimentoModel model, R_PlanilhaRecebimento entity)
    //{
    //  entity.PR_NumeroGuia = model.PRM_NumeroGuia;
    //  entity.PR_Nome = model.PRM_Nome;
    //  entity.PR_TotalFaturado = model.PRM_TotalFaturado;
    //  entity.PR_TotalGlosado = model.PRM_TotalGlosado;
    //  entity.PR_TotalPago = model.PRM_TotalPago;
    //  entity.RP_DataAlteracao = model.PRM_DataAlteracao;
    //  entity.RP_IdUsuarioAlteracao = model.PRM_CodigoUsuarioAlteracao;
    //  entity.PR_ValorApresentado = model.PRM_ValorApresentado;
    //  entity.PR_NumAtendimento = model.PRM_NumAtendimento;
    //  entity.PR_CarteiraBeneficiario = model.PRM_CarteiraBeneficiario;
    //  entity.PR_DataAtendimento = model.PRM_DataAtendimento;
    //  return entity;
    //}

    //public static R_PlanilhaRecebimento ToPlanilhaRecebimentoEditImportacaoPlan(this PlanilhaRecebimentoModel model, R_PlanilhaRecebimento entity)
    //{
    //  //entity.PR_NumeroGuia = model.PRM_NumeroGuia;
    //  entity.PR_Nome = model.PRM_Nome;
    //  //entity.PR_TotalFaturado = model.PRM_TotalFaturado;
    //  entity.PR_TotalGlosado = model.PRM_TotalGlosado;
    //  entity.PR_TotalPago = model.PRM_TotalPago;
    //  entity.PR_ValorApresentado = model.PRM_ValorApresentado;
    //  entity.RP_DataAlteracao = model.PRM_DataAlteracao;
    //  entity.RP_IdUsuarioAlteracao = model.PRM_CodigoUsuarioAlteracao;
    //  //entity.PR_NumAtendimento = model.PRM_NumAtendimento;
    //  //entity.PR_CarteiraBeneficiario = model.PRM_CarteiraBeneficiario;
    //  //entity.PR_DataAtendimento = model.PRM_DataAtendimento;

    //  return entity;
    //}

    //public static PlanilhaRecebimentoModel ToPlanilhaRecebimentoModel(this R_PlanilhaRecebimento entity)
    //{
    //  PlanilhaRecebimentoModel model = new PlanilhaRecebimentoModel();
    //  model.PRM_Codigo = entity.PR_Id;
    //  model.PRM_Nome = entity.PR_Nome;
    //  model.PRM_NumeroGuia = entity.PR_NumeroGuia;
    //  model.PRM_TotalFaturado = entity.PR_TotalFaturado;
    //  model.PRM_TotalGlosado = entity.PR_TotalGlosado;
    //  model.PRM_TotalPago = entity.PR_TotalPago;
    //  model.PRM_CodigoRepasse = entity.PR_IdRepasse;
    //  model.PRM_DataCriacao = entity.PR_DataCriacao;
    //  model.PRM_CodigoUsuarioCriacao = entity.PR_IdUsuarioCriacao;
    //  model.PRM_DataAlteracao = entity.RP_DataAlteracao;
    //  model.PRM_CodigoUsuarioAlteracao = entity.RP_IdUsuarioAlteracao;
    //  model.PRM_ValorApresentado = entity.PR_ValorApresentado;
    //  model.PRM_NumAtendimento = entity.PR_NumAtendimento;
    //  model.PRM_CarteiraBeneficiario = entity.PR_CarteiraBeneficiario;
    //  model.PRM_DataAtendimento = entity.PR_DataAtendimento;

    //  return model;
    //}
  }
}
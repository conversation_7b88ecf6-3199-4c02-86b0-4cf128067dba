﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using PagedList;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RepasseConvenio.Services
{
  public class StatusLoteServices : ServiceBase
  {
    public StatusLoteServices(RepasseEntities Contexto)
        : base(Contexto)
    { }
    public StatusLoteServices()
       : base()
    { }

    public StatusLoteServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public StatusLoteServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public int GetIdByEnum(StatusLoteEnum statusLoteEnum)
    {
      return Contexto.R_StatusLote.Where(a => a.SL_Enum == (int)statusLoteEnum).Select(a => a.SL_Id).FirstOrDefault();
    }
    public R_StatusLote GetByEnum(StatusLoteEnum statusLoteEnum)
    {
      return Contexto.R_StatusLote.Where(a => a.SL_Enum == (int)statusLoteEnum).FirstOrDefault();
    }

    public R_StatusLote GetById(int Id)
    {
      return Contexto.R_StatusLote.Where(a => a.SL_Id == Id).FirstOrDefault();
    }

    public List<R_StatusLote> Getall()
    {
      string query = @"select * from R_StatusLote";
      return Contexto.Database.SqlQuery<R_StatusLote>(query).ToList();
    }
  }
}


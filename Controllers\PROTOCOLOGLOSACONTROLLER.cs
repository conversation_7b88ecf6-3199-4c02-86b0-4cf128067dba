﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using PagedList;
using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Controls;
using RepasseConvenio.Models;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace RepasseConvenio.Controllers
{
  [Authorize]
  public class ProtocoloGlosaController : LibController
  {

    public ActionResult Index(int IdLoteGosa, int? Page)
    {
      try
      {
        ProtocoloGlosaServices protocoloGlosaServices = new ProtocoloGlosaServices();
        Page = Page ?? 1;
        ProtocoloGlosaIndexCabecalho protocoloGlosaIndexCabecalho = protocoloGlosaServices.GetPagedProtocoloGlosaIndex(IdLoteGosa, Page.Value);
        return View(protocoloGlosaIndexCabecalho);
      }
      catch (Exception)
      {
        throw;
      }
    }

    public ActionResult Create(int CodigoLoteGlosa)
    {
      try
      {
        ProtocoloGlosaCreate protocoloGlosaCreate = new ProtocoloGlosaCreate();
        protocoloGlosaCreate.CodigoLoteGlosa = CodigoLoteGlosa;
        return View(protocoloGlosaCreate);
      }
      catch (Exception)
      {
        throw;
      }
    }

    [HttpPost]
    public ActionResult Create(ProtocoloGlosaCreate protocoloGlosaCreate)
    {
      try
      {
        if (ModelState.IsValid)
        {
          ProtocoloGlosaServices protocoloGlosaServices = new ProtocoloGlosaServices(ContextoUsuario.UserLogged);
          protocoloGlosaServices.Create(protocoloGlosaCreate);
          MessageListToast.Add(new Message(MessageType.Success, "Protocolo gerado."));
          return RedirectToAction("Index", new { IdLoteGosa = protocoloGlosaCreate.CodigoLoteGlosa });
        }
        MessageListToast.Add(new Message(MessageType.Error, "Gentileza verificar os campos preenchidos."));
        return View(protocoloGlosaCreate);

      }
      catch (CustomException ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return View(protocoloGlosaCreate);
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, "Ocorreu uma falha na sua solicitação, tente novamente mais tarde ou contate o administrador."));
        return View(protocoloGlosaCreate);
      }
    }

    public ActionResult Detalhes(int CodigoProtocoloGlosa)
    {
      try
      {
        ProtocoloGlosaServices protocoloGlosaServices = new ProtocoloGlosaServices();
        ProtocoloGlosaDetalhes protocoloGlosaDetalhes = protocoloGlosaServices.GetProtocoloGlosaDetalhes(CodigoProtocoloGlosa);
        return View(protocoloGlosaDetalhes);
      }
      catch (Exception)
      {
        throw;
      }
    }
  }
}
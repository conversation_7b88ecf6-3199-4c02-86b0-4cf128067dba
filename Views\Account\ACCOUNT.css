/*body {
    background: url("../../Content/img/back.png") no-repeat;
    background-position: 100% 25%;
}

.form-background {
    height: calc(100vh);
    width: 33%;
    background-color: white;
    right: -7.5px;
    position: absolute;
    box-shadow: 0 0px 180px 0px #00094e;
}

.div-position {
    width: 60%;
    position: absolute;
    text-align: start;
    left: 50%;
    bottom: 55%;
    transform: translate(-50%, 50%);
}

input {
    width: calc(100% - 20px);
    border: none;
    background-color: #f3f3f3;
    padding: 7.5px 10px;
    outline: 0;
    font-size: 11pt;
    border-radius: 2px;
    margin-top: 2.5px;
}

.div-position>div>label {
    color: rgb(142, 142, 142);
    font-size: 10pt;
}

.info {
    color: rgba(0, 0, 0, 0.4);
    width: 100%;
    position: absolute;
    font-size: 10pt;
    bottom: 20px;
    left: 50%;
    transform: translate(-50%);*/
    /*cursor: pointer;*/
/*}
.btn-info-login:hover {
    color: #2036de !important;
  }

img {
    width: 215px;
    margin: 25px auto 40px auto;
    display: table;
}


@media screen and (max-width: 1170px) {
  body {
    background: none !important;
  }

  .form-background {
    position: static !important;
    box-shadow: none !important;
  }

  .div-position {
    top: 50% !important;
    bottom: unset !important;
    transform: translate(-50%, -50%) !important;
  }

  img {
    width: 285px !important;
    margin-bottom: 70px !important;
  }*/

:root {
  --input-padding-x: 1.5rem;
  --input-padding-y: .75rem;
}

body {
  background: #fafafa;
/*  background: linear-gradient(to right, #0062E6, #33AEFF);*/
}

.card-signin {
  border: 0;
  border-radius: 1rem;
  box-shadow: 0 0.5rem 1rem 0 rgb(52 58 64);
}

  .card-signin .card-title {
    margin-bottom: 2rem;
    font-weight: 300;
    font-size: 1.5rem;
  }

  .card-signin .card-body {
    padding: 3rem;
  }
.btn-primary:hover {
  color: #ffffff;
  background-color: #6b7075;
  border-color: #343a40;
}
.btn-primary {
  color: #ffffff;
  background-color: #343a40;
  border-color: #343a40;
  box-shadow: none;
}
  .btn-primary:focus, .btn-primary.focus {
    color: #ffffff;
    background-color: #6b7075;
    border-color: #6b7075;
    box-shadow: none, 0 0 0 0 rgb(52 58 64);
  }
  .btn-primary:not(:disabled):not(.disabled):active:focus, .btn-primary:not(:disabled):not(.disabled).active:focus, .show > .btn-primary.dropdown-toggle:focus {
    box-shadow: 0 0 0 0 rgb(52 58 64);
  }
  .btn-primary:not(:disabled):not(.disabled):active, .btn-primary:not(:disabled):not(.disabled).active, .show > .btn-primary.dropdown-toggle {
    color: #ffffff;
    background-color: #6b7075;
    border-color: #6b7075;
  }
/*.card-body {
  background-color: #4b545d;
  color: white;
  border: 0 solid rgba(0, 0, 0, 0.125);
  border-radius: 0.25rem;
}*/
.card-title {
  font-weight: 600 !important;
}
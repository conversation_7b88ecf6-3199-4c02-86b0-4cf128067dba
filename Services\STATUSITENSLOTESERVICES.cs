﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using PagedList;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RepasseConvenio.Services
{
  public class StatusItensLoteServices : ServiceBase
  {
    public StatusItensLoteServices(RepasseEntities Contexto)
        : base(Contexto)
    { }
    public StatusItensLoteServices()
       : base()
    { }

    public StatusItensLoteServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public StatusItensLoteServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public int GetIdByEnum(StatusItensLoteEnum statusItensLoteEnum)
    {
      return Contexto.R_StatusItensLote.Where(a => a.SIL_Enum == (int)statusItensLoteEnum).Select(a => a.SIL_Id).FirstOrDefault();
    }
    public R_StatusItensLote GetByEnum(StatusItensLoteEnum statusItensLoteEnum)
    {
      return Contexto.R_StatusItensLote.Where(a => a.SIL_Enum == (int)statusItensLoteEnum).FirstOrDefault();
    }

    public R_StatusItensLote GetById(int Id)
    {
      return Contexto.R_StatusItensLote.Where(a => a.SIL_Id == Id).FirstOrDefault();
    }

    public List<R_StatusItensLote> Getall()
    {
      string query = @"select * from R_StatusItensLote";
      return Contexto.Database.SqlQuery<R_StatusItensLote>(query).ToList();
    }

  }
}


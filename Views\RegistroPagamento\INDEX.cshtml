﻿@using PagedList.Mvc
@using RepasseConvenio.Models
@using RepasseConvenio.Infrastructure
@using RepasseConvenio.Infrastructure.Controls
@model RegistroPagamentoIndex

@{
  ViewBag.Title = "Registro Pagamento";
}

@Scripts.Render("~/Views/RegistroPagamento/RegistroPagamento.js?nh=aa")

@using (Html.BeginForm("Index", "RegistroPagamento", FormMethod.Post))
{
  <div class="row">
    <section class="col-lg-12 connectedSortable ui-sortable">
      <div class="card">
        <div class="card-header ui-sortable-handle">
          <h3 class="card-title">
            <i class="fas fa-clip mr-1"></i>
            Apuração Pagamento
          </h3>
          <div class="card-tools">
            @*<button type="button" class="btn btn-block btn-outline-primary" id="IntegrarMedicos"> Integrar Médicos </button>*@
            <a class="nav-link active"></a>
          </div>
          <div class="card-tools">
          </div>
        </div><!-- /.card-header -->
        <div class="card-body">
          <div class="row">
            <div class="col-md-3">
              <div class="form-group">
                @Html.LabelFor(m => m.DataDe)
                @Html.LibEditorFor(m => m.DataDe, new { @class = "form-control airDatePickerDate Data" })
                @Html.ValidationMessageFor(m => m.DataDe, "", new { @class = "text-danger" })
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                @Html.LabelFor(m => m.DataAte)
                @Html.LibEditorFor(m => m.DataAte, new { @class = "form-control airDatePickerDate Data" })
                @Html.ValidationMessageFor(m => m.DataAte, "", new { @class = "text-danger" })
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                @Html.LabelFor(m => m.MedicoSelect)
                @Html.LibSelect2For(m => m.MedicoSelect, new { @class = "form-control" })

              </div>
            </div>
            <div class="col-md-2">

              <button type="submit" class="btn btn-info pull-rigth">Processar</button>
            </div>

          </div>
        </div>
        <div class="card-footer">
          <button type="submit" class="btn btn-info pull-rigth">Processar</button>
        </div>
      </div>
    </section>
  </div>
}
<div class="row">
  <section class="col-lg-12 connectedSortable ui-sortable">
    <div class="card">
      <div class="card-header ui-sortable-handle" style="display:flex;width: 100%;">
        <div class="row" style="display:flex;width: 70%;">
          <div class="col-md-5">
            <div class="form-group">
              @*<label for="MedicoFilter">Médico</label>*@
              <input placeholder="Digite o nome do médico" class="form-control" id="MedicoFilter" name="MedicoFilter" type="text" value="">
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              @*<label for="DataDe">Data Pagamento</label>*@
              <input placeholder="Dt Pagamento" class="form-control airDatePickerDate" id="DtPagFilter" name="DtPagFilter" type="text" value="">
            </div>
          </div>
          <div class="col-md-1">
            <button type="button" class="btn btn-sm btn-info" id="BtnFiltro" onclick="RegistroPagamento.FiltroGrid()">Buscar</button>
          </div>
          </div>
          <div class="card-tools">
            <button type="button" class="btn btn-sm btn-danger" id="RemoverSelecionado" disabled="true" readonly="readonly">Remover Selecionados</button>
            <button type="button" class="btn btn-sm btn-info" id="PagarSelecionado" disabled="true" readonly="readonly">Pagar Selecionados</button>
          </div>
        </div><!-- /.card-header -->
      <div class="card-body">
        <div class="row" id="GridPartial">
          @Html.Partial("_PartialGrid", Model.ListRegistro)
        </div>
      </div>
    </div>
  </section>
</div>

@Html.Partial("_ModalExtratos")

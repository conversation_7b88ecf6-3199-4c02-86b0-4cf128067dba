﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using PagedList;
using RepasseConvenio.Infrastructure;
using RepasseConvenio.Models;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Web;

namespace RepasseConvenio.Services
{
  public class BancoUnicooperService : ServiceBase
  {
    public BancoUnicooperService()
 : base()
    { }
    public BancoUnicooperService(RepasseEntities Contexto)
       : base(Contexto)
    { }

    public BancoUnicooperService(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public BancoUnicooperService(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }


    public IPagedList<BancoUnicooperModel> Get(int pageNumber, string filtro)
    {
      string query = String.Format(@" SELECT 
	                                        B.BU_Id AS Codigo,
	                                        B.BU_IdBanco AS IdBanco,
	                                        B.BU_Descricao AS Descricao,
	                                        B.BU_Agencia AS Agencia,
											                    R.B_Nome As BancoNome,
										                      B.BU_Conta As Conta,
										                      B.BU_Digito As Digito
                                    FROM R_BancosUnicooper B inner join R_Banco R on B.BU_IdBanco = R.B_Id
                                    ");

      return Contexto.Database.SqlQuery<BancoUnicooperModel>(query)
             .ToList().OrderBy(a => a.Descricao).ToPagedList(pageNumber, PageSize);
    }

    public R_BancosUnicooper Create(BancoUnicooperModel bancoUnicooperModel)
    {

      if (bancoUnicooperModel.IdBanco == 0 || bancoUnicooperModel.Agencia == null || bancoUnicooperModel.conta == null || bancoUnicooperModel.Descricao == null || bancoUnicooperModel.Digito == null)
      {
        throw new CustomException("Favor prencher todos os campos.");

      }

      BancoUnicooperModel model = validaCreate(bancoUnicooperModel.IdBanco, bancoUnicooperModel.Agencia, bancoUnicooperModel.conta, bancoUnicooperModel.Digito);

      if (model != null)
      {
        throw new CustomException("Já existe um banco cadastrado com esses dados.");
        
      }
      


      R_BancosUnicooper bancoModel = new R_BancosUnicooper();
      bancoModel = bancoUnicooperModel.toCreateBancounicooper();

      Create(bancoModel);
      return bancoModel;
    }

    public R_BancosUnicooper Edit(BancoUnicooperModel bancoUnicooperModel)
    {
      if (bancoUnicooperModel.IdBanco == 0 || bancoUnicooperModel.Agencia == null || bancoUnicooperModel.conta == null || bancoUnicooperModel.Descricao == null || bancoUnicooperModel.Digito == null)
      {
        throw new CustomException("Favor prencher todos os campos.");

      }

      BancoUnicooperModel model = validaEdit(bancoUnicooperModel.Codigo, bancoUnicooperModel.IdBanco, bancoUnicooperModel.Agencia, bancoUnicooperModel.conta, bancoUnicooperModel.Digito);

      if (model != null)
      {
        throw new CustomException("Já existe um banco cadastrado com esses dados.");

      }

      R_BancosUnicooper bancoModel = new R_BancosUnicooper();
      bancoModel = bancoUnicooperModel.toEditBancoUnicooper();

      Edit(bancoModel);
      return bancoModel;
    }

    public BancoUnicooperModel validaCreate(int IdBanco, string Agencia ,string Conta,  string Digito )
    {
      string query = String.Format(@" SELECT 
	                                        B.BU_Id AS Codigo,
	                                        B.BU_IdBanco AS IdBanco,
	                                        B.BU_Descricao AS Descricao,
	                                        B.BU_Agencia AS Agencia,
											                    R.B_Nome As BancoNome,
										                      B.BU_Conta As Conta,
										                      B.BU_Digito As Digito
                                    FROM R_BancosUnicooper B inner join R_Banco R on B.BU_IdBanco = R.B_Id
								                  	where (B.BU_IdBanco = @IdBanco  And B.BU_Agencia = @Agencia and B.BU_Conta = @Conta and B.BU_Digito = @Digito ) 
                                    ");

      return Contexto.Database.SqlQuery<BancoUnicooperModel>(query, new SqlParameter("IdBanco", IdBanco)
                                                                  , new SqlParameter("Agencia", Agencia)                                
                                                                  , new SqlParameter("Conta", Conta) 
                                                                  , new SqlParameter("Digito", Digito)).FirstOrDefault();

    }

    public BancoUnicooperModel validaEdit( int Id ,int IdBanco, string Agencia, string Conta, string Digito)
    {
      string query = String.Format(@" SELECT 
	                                        B.BU_Id AS Codigo,
	                                        B.BU_IdBanco AS IdBanco,
	                                        B.BU_Descricao AS Descricao,
	                                        B.BU_Agencia AS Agencia,
											                    R.B_Nome As BancoNome,
										                      B.BU_Conta As Conta,
										                      B.BU_Digito As Digito
                                    FROM R_BancosUnicooper B inner join R_Banco R on B.BU_IdBanco = R.B_Id
								                  	where (B.BU_IdBanco = @IdBanco  And B.BU_Agencia = @Agencia and B.BU_Conta = @Conta and B.BU_Digito = @Digito )  and B.BU_Id != @Id
                                    ");

      return Contexto.Database.SqlQuery<BancoUnicooperModel>(query, new SqlParameter("IdBanco", IdBanco)
                                                                  , new SqlParameter("Agencia", Agencia)
                                                                  , new SqlParameter("Conta", Conta)
                                                                  , new SqlParameter("Digito", Digito)
                                                                  , new SqlParameter("Id", Id)).FirstOrDefault();


    }




    public BancoUnicooperModel GetEdit(int id)
    {
      string query = String.Format(@" SELECT 
	                                        B.BU_Id AS Codigo,
	                                        B.BU_IdBanco AS IdBanco,
	                                        B.BU_Descricao AS Descricao,
	                                        B.BU_Agencia AS Agencia,
											                    R.B_Nome As BancoNome,
										                      B.BU_Conta As Conta,
										                      B.BU_Digito As Digito
                                    FROM R_BancosUnicooper B inner join R_Banco R on B.BU_IdBanco = R.B_Id
									where B.BU_Id = @id
                                    ");

      return Contexto.Database.SqlQuery<BancoUnicooperModel>(query, new SqlParameter("id", id)).FirstOrDefault();
    }

    public R_BancosUnicooper GetById(int id)
    {
      string query = String.Format(@" SELECT 
	                                        B.BU_Id ,
	                                        B.BU_IdBanco,
	                                        B.BU_Descricao ,
	                                        B.BU_Agencia ,
										                      B.BU_Conta ,
										                      B.BU_Digito 
                                    FROM R_BancosUnicooper B 
									                  where B.BU_Id = @id
                                    ");

      return Contexto.Database.SqlQuery<R_BancosUnicooper>(query, new SqlParameter("id", id)).FirstOrDefault();
    }

    public void Delete(int Id)
    {

      R_BancosUnicooper r_BancosUnicooper = GetById(Id);
      if (r_BancosUnicooper == null)
        throw new CustomException(" Não foi possívèl deletar, O Banco não foi encontrado .");

      Contexto.Entry(r_BancosUnicooper).State = EntityState.Deleted;
      Contexto.SaveChanges();
    }

    public List<R_BancosUnicooper> GetByTerm(string term)
    {
      if (string.IsNullOrEmpty(term))
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                     R_BancosUnicooper");

        return Contexto.Database.SqlQuery<R_BancosUnicooper>(query).ToList();

      }
      else
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                    R_BancoUnicooper
                                 WHERE BU_Descricao LIKE @termo  ");

        return Contexto.Database.SqlQuery<R_BancosUnicooper>(query, new SqlParameter("@termo", string.Format("%{0}%", term))).ToList();

      }
    }
  }
}
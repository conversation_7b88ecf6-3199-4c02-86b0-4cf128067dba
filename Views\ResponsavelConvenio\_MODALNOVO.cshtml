﻿@using RepasseConvenio.Models
@using RepasseConvenio.Infrastructure
@using RepasseConvenio.Infrastructure.Controls
@model ResponsavelConvenioModel

<div class="modal fade" id="ModalNovoResponsavel" data-keyboard="false" data-backdrop="static">
  <div class="modal-dialog" style="max-width: 70%;">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">Novo Responsável do Convênio</h4>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">×</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-md-4">
            <div class="form-group">
              @Html.LabelFor(m => m.CPF)
              @Html.LibEditorFor(m => m.CPF, new { @class = "form-control CPF" })
            </div>
          </div>
          <div class="col-md-4">
            <div class="form-group">
              @Html.LabelFor(m => m.NomeResponsavel)
              @Html.LibEditorFor(m => m.NomeResponsavel, new { @class = "form-control" })
            </div>
          </div>
          <div class="col-md-4">
            <div class="form-group">
              @Html.LabelFor(m => m.Email)
              @Html.LibEditorFor(m => m.Email, new { @class = "form-control EMAIL" })
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary" id="AdicionarResponsavel">Salvar</button>
      </div>
    </div>
  </div>
</div>
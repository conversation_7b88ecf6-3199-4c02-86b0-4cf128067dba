﻿@using Auditoria.Infrastructure.Controls
@model Auditoria.Domain.Models.RegisterModelAux

@{
  Layout = null;
}
<!DOCTYPE html>

<html>
<head>
  <meta charset="utf-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
  <title>Plataforma da Inovação MIP</title>
  @Styles.Render("~/Content/css")
  @Scripts.Render("~/bundles/modernizr")
  @Scripts.Render("~/bundles/jquery")
  @Scripts.Render("~/bundles/bootstrap")
  @Scripts.Render("~/bundles/inputmask")
  @Scripts.Render("~/bundles/plugins")
  @Scripts.Render("~/Views/Login/Login.js")
</head>
<body class="hold-transition login-page">
  @using (Html.BeginForm("Dados", "Account", FormMethod.Post, new { enctype = "multipart/form-data" }))
  {
    @*@using GestaoInovacao.Infrastructure.Controls*@
    <nav class="navbar navbar-default nav-header-login">
      <div class="container-fluid header-login">
        <div class="navbar-header">
          <img src="@Url.Content("~/Content/Images/logo.png")" />
        </div>
        <div class="collapse navbar-collapse" id="bs-example-navbar-collapse-1">
        </div>
      </div>
    </nav>
    <ul class="nav nav-tabs">
      <li role="presentation" id="Dadostab" class="active"><a data-toggle="tab" href="#Dados">Registre sua sugestão</a></li>
      <li role="presentation" id="Doctab"><a data-toggle="tab" href="#Doc">Registrar-se</a></li>
    </ul>
    <div class="tab-content" id="tab-content">
      <div class="tab-pane fade in active" id="Dados">
        <div class="box box-primary">
          <div class="box-body">
            @Html.ValidationSummary(true, "", new { @class = "text-danger" })
            @Html.HiddenFor(a => a.Id)

            <div class="row">
              <div class="col-md-8">
                <div class="col-md-12">
                  <div class="form-group">
                    @Html.LibLabelFor(model => model.Problema)
                    @Html.TextAreaFor(model => model.Problema, new { @class = "form-control", @rows = 5, @readonly = "readonly" })
                    @Html.ValidationMessageFor(model => model.Problema, "", new { @class = "text-danger" })
                  </div>
                </div>
                <div class="col-md-12">
                  <div class="form-group">
                    @Html.LabelFor(model => model.Solucao)
                    @Html.TextAreaFor(model => model.Solucao, new { @class = "form-control", @rows = 5 ,@placeholder="Dige a solução para o problema acima..."})
                    @Html.ValidationMessageFor(model => model.Solucao, "", new { @class = "text-danger" })
                  </div>
                </div>
                <div class="col-md-8">
                  <div class="form-group">
                    <button class="inputfile" type="button" id="adicionaFile">Adicionar Arquivo</button>
                    <div class="InputMultiplefiles" id="ArquivosNomes">
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                @if (Model.IdArquivoDesafio != null)
                {
                  <img style="height: 400px; width:400px" src="@Url.Action("showImage", "AnexosDesafio", new { id = Model.IdArquivoDesafio })" id="img-file-upload1">
                }
              </div>
            </div>

          </div>
          <div class="box-footer">
            <a href="#" class="btn btn-warn pull-right margin-left ProximaAba" id="ProximaAba" title="Próxima Aba">Próximo &#x2192; </a>
          </div>
        </div>
      </div>

      <div class="tab-pane fade in" id="Doc">
        <div class="box box-primary">
          <div class="box-body">
            <div class="row">
              <div class="register-box">
                <div class="register-box-body register">
                  @Html.LibMessageBox()
                  @*@using (Html.BeginForm())
                    {*@

                  @Html.ValidationSummary(true, "", new { @class = "text-danger" })
                  <div class="row">
                    <div class="col-md-12">
                      <div class="form-group has-feedback">
                        @Html.LibEditorFor(m => m.Nome, new { @class = "form-control", placeholder = "Nome" })
                        <span class="glyphicon glyphicon-user form-control-feedback"></span>
                        @Html.ValidationMessageFor(m => m.Nome, "", new { @class = "text-danger" })
                      </div>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-md-12">
                      <div class="form-group has-feedback">
                        @Html.LibEditorFor(m => m.CPF, new { @class = "form-control CPF", placeholder = "CPF" })
                        <span class="glyphicon glyphicon-credit-card form-control-feedback"></span>
                        @Html.ValidationMessageFor(m => m.CPF, "", new { @class = "text-danger" })
                      </div>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-md-12">
                      <div class="form-group has-feedback">
                        @Html.LibEditorFor(m => m.Email, new { @class = "form-control EMAIL", placeholder = "Email" })
                        <span class="glyphicon glyphicon-envelope form-control-feedback"></span>
                        @Html.ValidationMessageFor(m => m.Email, "", new { @class = "text-danger" })
                      </div>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-md-12">
                      <div class="form-group has-feedback">
                        @Html.LibEditorFor(m => m.Telefone, new { @class = "form-control telefone", placeholder = "Telefone Celular" })
                        <span class="glyphicon glyphicon-earphone form-control-feedback"></span>
                        @Html.ValidationMessageFor(m => m.Telefone, "", new { @class = "text-danger" })
                      </div>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-md-6">
                      <a href="@Url.Action("Login", "Account")">Voltar</a><br>
                    </div>
                    <div class="col-md-6" style="float:right;">
                      <button type="submit" value="Log in" class="btn btn-warn btn-block">Registrar</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="box-footer">
            <a href="#" class="btn btn-warn pull-left margin-left  EtapaAnterior" id="EtapaAnterior" title="Voltar">&#8592; Voltar</a>
          </div>
        </div>
      </div>
      @*<div class="box-footer">
          <button type="button" value="Voltar" class="btn btn-secondary" onclick="location.href='@Url.Action("Login", "Account")'">Cancelar</button>
          <button type="submit" value="Create" class="btn btn-primary pull-right">Salvar &#x2713;</button>
        </div>*@
    </div>
  }
</body>
</html>

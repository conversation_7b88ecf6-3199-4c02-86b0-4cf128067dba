﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Linq.Expressions;
using System.Text;
using System.Web.Mvc.Html;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using RepasseConvenio.Domain.Infrastructure.Helpers;
using RepasseConvenio.Infrastructure.Helpers;
using PortalCooperadoHelpers.Infraestrutura.Helpers;

namespace RepasseConvenio.Infrastructure.Controls
{
  public class LibDropDown<TModel, TProperty>
  {
    public HtmlHelper<TModel> HtmlHelper { get; set; }

    public Expression<Func<TModel, TProperty>> ExpressionModel { get; set; }

    public DropDownConfiguration Configuration { get; set; }

    public LibDropDown(HtmlHelper<TModel> htmlHelper, Expression<Func<TModel, TProperty>> expression, DropDownConfiguration configuration)
    {
    }

    public string RenderHtml(string id = null)
    {
      return "";
    }

    private IEnumerable<SelectListItem> GetSelectList()
    {
      return new SelectList();
    }
  }

  public class DropDownConfiguration
  {
    public DropDownConfiguration()
    {
    }

    public string CSSClass { get; set; }
    public bool ReadOnly { get; set; }
    public bool OrdemValores { get; set; }
  }

  public static class DropDownHelper
  {
    public static IEnumerable<SelectListItem> GetSelectListSimNao()
    {
      List<SelectListItem> items = new List<SelectListItem>();
      return items;
    }
  }
}
﻿@using RepasseConvenio.Models
@model List<LotePortalModel>

@{
  ViewBag.Title = "Busca Lote";
}
@using (Html.BeginForm("InserirLotes", "Repasse", FormMethod.Post, new { id = "PostInserirLotes", @style = "width: 98%;" }))
{
  <div class="col-md-12 divtable">
    <table class="table table-striped table-sm">
      <thead>
        <tr>
          <th>
          </th>
          <th>
            Número Lote
          </th>
          <th>
            Data Emissão
          </th>
          <th>
            Valor
          </th>
        </tr>
      </thead>
      <tbody>
        @for (var i = 0; i < Model.ToArray().Length; i++)
        {
          <tr>
            <td>
              @Html.CheckBoxFor(m => Model[i].BLM_Inserir)
              @Html.HiddenFor(m => Model[i].BLM_IdRepasse)
            </td>
            <td>
              @Html.HiddenFor(m => Model[i].BLM_NumLote)
              @Model[i].BLM_NumLote
            </td>
            <td>
              @Html.HiddenFor(m => Model[i].BLM_Data)
              @Model[i].BLM_Data
            </td>
            <td>
              @Html.HiddenFor(m => Model[i].BLM_Valor)
              @Model[i].BLM_Valor
            </td>
          </tr>
        }
      </tbody>
    </table>
  </div>
  <div class="box-footer">
    <button type="button" class="btn btn-outline-dark pull-left" data-dismiss="modal">Cancelar</button>
    <button type="button" value="Create" class="btn btn-info pull-right" id="btnInserirLotes">Inserir</button>
  </div>
}

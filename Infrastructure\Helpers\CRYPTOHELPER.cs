﻿using RepasseConvenio.Models;
using System;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web;
using System.Web.Security;
using System.Xml.Serialization;
using RepasseConvenio.Domain.Infrastructure;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Configuration;
//using System.Data.Entity;
using System.IO;
using System.Security.Cryptography;

namespace RepasseConvenio.Infrastructure.Helpers
{
  public class CryptoHelper
  {
    private const string SSecurityKey = "";

    public static string Encrypt(string toEncrypt)
    {
      try
      {
        return "";
      }
      catch (Exception ex)
      {
        throw new ApplicationException("Erro ao criptografar", ex);
      }
    }

    public static string Decrypt(string cipherString)
    {
      try
      {
        return "";
      }
      catch (Exception ex)
      {
        throw new ApplicationException("Erro ao decriptograr", ex);
      }
    }

    public static string GeneratePassword(int min, int max)
    {
      string pass = String.Empty;
      return pass;
    }
  }
}

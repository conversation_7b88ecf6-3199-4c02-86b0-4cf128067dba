﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using PagedList;
using RepasseConvenio.Infrastructure;
using RepasseConvenio.Models;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;

namespace RepasseConvenio.Services
{
  public class TabelaProgressivaINSSServices : ServiceBase
  {
    public TabelaProgressivaINSSServices(RepasseEntities Contexto)
        : base(Contexto)
    { }
    public TabelaProgressivaINSSServices()
       : base()
    { }
    public TabelaProgressivaINSSServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }
    public TabelaProgressivaINSSServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public IPagedList<TabelaProgressivaINSSModel> Get(int pageNumber, string filtro)
    {
      string query = String.Format(@"SELECT 
	                                    V.T_Id AS Codigo,
	                                    V.T_DtInicioVigencia AS DtInicioVigencia,
	                                    V.T_DtFimVigencia AS DtFimVigencia,
	                                    V.T_Descricao AS Descricao
                                    FROM R_TabelaProgressivaINSS V
                                    ");

      return Contexto.Database.SqlQuery<TabelaProgressivaINSSModel>(query)
             .ToList().OrderBy(a => a.DtInicioVigencia).ToPagedList(pageNumber, PageSize);
    }
    public R_TabelaProgressivaINSS GetById(int Id)
    {
      return Contexto.R_TabelaProgressivaINSS.Where(a => a.T_Id == Id).FirstOrDefault();
    }
    public bool PeriodoExistente(DateTime InicioVigencia, DateTime FimVigencia)
    {
      string query = @"SELECT
                      COUNT(*)
                      FROM R_TabelaProgressivaINSS T
                      WHERE 
                      T_DtInicioVigencia BETWEEN @InicioVigencia AND @FimVigencia
                      OR
                      T_DtFimVigencia BETWEEN @InicioVigencia AND @FimVigencia
                      ";
      int val = Contexto.Database.SqlQuery<int>(query
                                               , new SqlParameter("@InicioVigencia", InicioVigencia)
                                               , new SqlParameter("@FimVigencia", FimVigencia)
                                               ).FirstOrDefault();
      return val > 0;
    }
    public List<int> GetIdTabelasByPeriodo(DateTime InicioVigencia, DateTime FimVigencia)
    {
      string query = @"SELECT
                      T.T_Id
                      FROM R_TabelaProgressivaINSS T
                      WHERE 
                      T_DtInicioVigencia BETWEEN @InicioVigencia AND @FimVigencia
                      OR
                      T_DtFimVigencia BETWEEN @InicioVigencia AND @FimVigencia
                      ";
      return Contexto.Database.SqlQuery<int>(query
                                               , new SqlParameter("@InicioVigencia", InicioVigencia)
                                               , new SqlParameter("@FimVigencia", FimVigencia)
                                               ).ToList();
    }

    public List<int> GetIdTabelasByPeriodoEdit(int Id, DateTime InicioVigencia, DateTime FimVigencia)
    {
      string query = @"SELECT
                      T.T_Id
                      FROM R_TabelaProgressivaINSS T
                      WHERE (T.T_Id != @Id
                      AND
                     (( T_DtInicioVigencia BETWEEN @InicioVigencia AND @FimVigencia)
                      OR
                     ( T_DtFimVigencia BETWEEN @InicioVigencia AND @FimVigencia)))
                      ";
      return Contexto.Database.SqlQuery<int>(query
                                               , new SqlParameter("@Id", Id)
                                               , new SqlParameter("@InicioVigencia", InicioVigencia)
                                               , new SqlParameter("@FimVigencia", FimVigencia)
                                               ).ToList();
    }
    public void ValidaModel(TabelaProgressivaINSSModel model)
    {
      if (model.DtInicioVigencia == new DateTime(0001, 01, 01))
        throw new CustomException("Campo Início Vigência é obrigatório.");

      if (!model.DtFimVigencia.HasValue)
        throw new CustomException("Campo Final Vigência é obrigatório.");

      if (model.DtFimVigencia <= model.DtInicioVigencia)
        throw new CustomException("Campo Final Vigência deve ser maior que a data Início Vigência.");

      if (string.IsNullOrEmpty(model.Descricao))
        throw new CustomException("Campo Descrição é obrigatório.");

      List<int> IdTabelas = GetIdTabelasByPeriodo( model.DtInicioVigencia, model.DtFimVigencia.Value);

      if (IdTabelas.Count > 0 && !IdTabelas.Contains(model.Codigo))
        throw new CustomException("Já existe tabela com esse periodo cadastrado.");
    }
    public void Create(TabelaProgressivaINSSModel model)
    {
      ValidaModel(model);
      R_TabelaProgressivaINSS entity = model.ToEntityCreate();
      Create(entity);
    }
    public void Edit(TabelaProgressivaINSSModel model)
    {
      ValidaModelEdit(model);
      R_TabelaProgressivaINSS valor = GetById(model.Codigo);
      valor.ToEntityEdit(model);
      Edit(valor);
    }
    public void Delete(int Id)
    {
      int valoresTabela = new ValoresTabelaProgressivaINSSServices(Contexto).GetQtdItensByIdTabelaINSS(Id);

      if (valoresTabela > 0)
        throw new CustomException("Não é possível deletar pois existem valores para a tabela.");

      R_TabelaProgressivaINSS R_TabelaProgressivaINSSs = GetById(Id);

      Contexto.R_TabelaProgressivaINSS.Remove(R_TabelaProgressivaINSSs);
      Contexto.SaveChanges();
    }

    public void ValidaModelEdit(TabelaProgressivaINSSModel model)
    {
      if (model.DtInicioVigencia == new DateTime(0001, 01, 01))
        throw new CustomException("Campo Início Vigência é obrigatório.");

      if (!model.DtFimVigencia.HasValue)
        throw new CustomException("Campo Final Vigência é obrigatório.");

      if (model.DtFimVigencia <= model.DtInicioVigencia)
        throw new CustomException("Campo Final Vigência deve ser maior que a data Início Vigência.");

      if (string.IsNullOrEmpty(model.Descricao))
        throw new CustomException("Campo Descrição é obrigatório.");

      List<int> IdTabelas = GetIdTabelasByPeriodoEdit(model.Codigo, model.DtInicioVigencia, model.DtFimVigencia.Value);

      if (IdTabelas.Count > 0 && !IdTabelas.Contains(model.Codigo))
        throw new CustomException("Já existe tabela com esse periodo cadastrado.");
    }

  }
}


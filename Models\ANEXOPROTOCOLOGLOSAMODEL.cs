﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;

namespace RepasseConvenio.Models
{
  public class AnexoProtocoloGlosaDetalhes
  {
    public int Codigo { get; set; }
    public string NomeArquivoWithExtension { get; set; }
    public DateTime DataAnexo { get; set; }
  }

  public class AnexoProtocoloDownload
  {
    public int APG_Id { get; set; }
    public string APG_NomeArquivoWithOutExtension { get; set; }
    public string APG_NomeArquivoWithExtension { get; set; }
    public string APG_ExtensaoArquivo { get; set; }
    public string APG_MimeType { get; set; }
    public DateTime APG_DataAnexo { get; set; }
    public int APG_IdProtocoloGlosa { get; set; }
    public int IdLoteGlosa { get; set; }
  }

  public static class AnexoProtocoloGlosaConversions
  {
    public static R_AnexoProtocoloGlosa EntityCreate(this R_AnexoProtocoloGlosa anexoProtocoloGlosa, HttpPostedFileBase httpPostedFileBase, int IdProtocoloGlosa)
    {
      string FileNameWithOutExtension = "Protocolo_" + IdProtocoloGlosa + "_" + Path.GetFileNameWithoutExtension(httpPostedFileBase.FileName);
      string FileNameWithExtension = FileNameWithOutExtension + Path.GetExtension(httpPostedFileBase.FileName);

      return new R_AnexoProtocoloGlosa()
      {
        APG_DataAnexo = DateTime.Now,
        APG_ExtensaoArquivo = Path.GetExtension(httpPostedFileBase.FileName),
        APG_MimeType = httpPostedFileBase.ContentType,
        APG_NomeArquivoWithExtension = FileNameWithExtension,
        APG_NomeArquivoWithOutExtension = FileNameWithOutExtension,
        APG_IdProtocoloGlosa = IdProtocoloGlosa
      };
    }
  }
}
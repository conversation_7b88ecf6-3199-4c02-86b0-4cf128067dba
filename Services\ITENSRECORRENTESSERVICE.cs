﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using PagedList;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;
using RepasseConvenio.WSPortalCooperado;

namespace RepasseConvenio.Services
{
  public class ItensRecorrentesServices : ServiceBase
  {
    public ItensRecorrentesServices(RepasseEntities Contexto)
        : base(Contexto)
    { }
    public ItensRecorrentesServices()
       : base()
    { }

    public ItensRecorrentesServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public ItensRecorrentesServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public List<ItensRecorrentesIndex> Get(int id, int page)
    {
      string query = string.Format(@"SELECT 
                                          IR.IR_Id [Codigo]
                                        , IR.IR_Ativo [Ativo]
                                        , IR.IR_TipoRecorrencia [TipoRecorrencia]
                                        , IR.IR_DataRecorrencia [DataRecorrencia]
                                        , IR.IR_DataFim [DataFim]
                                        , IR.IR_TipoLancamento [TipoLancamento]
                                        , IR.IR_IdClassificacao [CodigoClassificacao]
                                        , IR.IR_TipoConta [TipoConta]
                                        , IR.IR_CPFCNPJ [CPFCNPJ]
                                        , IR.IR_IdMedico [CodigoMedico]
                                        , IR.IR_Valor [Valor] 
                                        , IR.DateLastExcute [DateLastExcute] 
                                        , M.M_Nome [NomeMedico]
                                        , CR.CR_Descricao [DescricaoClassificacao] 
                                       FROM 
                                          R_ItensRecorrentes IR
                                       INNER JOIN R_Medico M ON IR.IR_IdMedico = M.M_Id AND IR.IR_IdMedico = @Id
                                       INNER JOIN R_ClassificacaoRepasse CR ON CR.CR_Id = IR.IR_IdClassificacao");
      //OFFSET (@pag - 1) * 20 ROWS
      //FETCH NEXT 20 ROWS ONLY , new SqlParameter("@pag", page)

      return Contexto.Database.SqlQuery<ItensRecorrentesIndex>(query, new SqlParameter("@Id", id)).ToList();
    }

    public ItensRecorrentesModel GetById(int Id)
    {
      R_ItensRecorrentes medico = Contexto.R_ItensRecorrentes.Where(a => a.IR_Id == Id).FirstOrDefault();

      if (medico != null)
        return new ItensRecorrentesModel()
        {
          Codigo = medico.IR_Id,
          CodigoClassificacao = medico.IR_IdClassificacao,
          CodigoMedico = medico.IR_IdMedico,
          CPFCNPJ = medico.IR_CPFCNPJ,
          DataRecorrencia = medico.IR_DataRecorrencia,
          DataFim = medico.IR_DataFim,
          TipoContaEnum = medico.IR_TipoConta.Equals("PF") ? EnumTipoConta.PF : EnumTipoConta.PJ,
          TipoLancamentoEnum = medico.IR_TipoLancamento.Equals("C") ? EnumTipoLancamento.C : EnumTipoLancamento.D,
          Valor = medico.IR_Valor,
          TipoRecorrenciaEnum = medico.IR_TipoRecorrencia == (int)TipoRecorrenciaEnum.Anual ? TipoRecorrenciaEnum.Anual
                              : medico.IR_TipoRecorrencia == (int)TipoRecorrenciaEnum.Diario ? TipoRecorrenciaEnum.Diario
                              : medico.IR_TipoRecorrencia == (int)TipoRecorrenciaEnum.Semanal ? TipoRecorrenciaEnum.Semanal
                              : medico.IR_TipoRecorrencia == (int)TipoRecorrenciaEnum.Mensal ? TipoRecorrenciaEnum.Mensal
                              : 0,

          Recorrente = medico.IR_Recorrente
        };

      else
        return null;
    }

    public R_ItensRecorrentes GetEntityById(int Id)
    {
      string query = @"SELECT
                        *
                       FROM R_ItensRecorrentes
                       where IR_Id = @Id";

      return Contexto.Database.SqlQuery<R_ItensRecorrentes>(query, new SqlParameter("@Id", Id)).FirstOrDefault();
    }

    public R_ItensRecorrentes GetEntityByIdRegistroPagamento(int IdRegistroPagamento)
    {
      string query = @"SELECT
                        *
                       FROM R_ItensRecorrentes
                       where IR_IdRegistroPagamento = @IdRegistroPagamento";

      return Contexto.Database.SqlQuery<R_ItensRecorrentes>(query, new SqlParameter("@IdRegistroPagamento", IdRegistroPagamento)).FirstOrDefault();
    }

    public R_ItensRecorrentes GetByClassificacao(int IdMedico, EnumClassificacaoRepasse Classificacao)
    {
      int IdClassificacao = new ClassificacaoRepasseServices(Contexto).GetIdByEnum(Classificacao);

      return Contexto.R_ItensRecorrentes.Where(a => a.IR_IdMedico == IdMedico && a.IR_IdClassificacao == IdClassificacao).FirstOrDefault();
    }

    public List<R_ItensRecorrentes> GetByTerm(string term)
    {
      if (string.IsNullOrEmpty(term))
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_ItensRecorrentes");

        return Contexto.Database.SqlQuery<R_ItensRecorrentes>(query).ToList();

      }
      else
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_ItensRecorrentes
                                       WHERE IR_CPFCNPJDeposito LIKE @termo");

        return Contexto.Database.SqlQuery<R_ItensRecorrentes>(query, new SqlParameter("@termo", string.Format("%{0}%", term))).ToList();

      }
    }

    public void Create(ItensRecorrentesModel model)
    {
      try
      {
        R_ItensRecorrentes medico = model.ItensRecorrentesToCreate();
        Create(medico);
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }

    public void Edit(ItensRecorrentesModel model)
    {
      R_ItensRecorrentes medico = model.ItensRecorrentesToEdit();
      Edit(medico);
    }

    public void Delete(int Id)
    {
      R_ItensRecorrentes R_ItensRecorrentes = GetEntityById(Id);
      string query = string.Empty;

      if (R_ItensRecorrentes.IR_IdClassificacao == (int)EnumClassificacaoRepasse.PROCESSAMENTODEREPASSE)
      {
        using (var scope = new TransactionScope(TransactionScopeOption.RequiresNew))
        {
          Delete(R_ItensRecorrentes);
          scope.Complete();
        }
      }
      else
        Delete(R_ItensRecorrentes);
    }

    public void DeleteAllByIdRepasse(string idRepasse)
    {
      string query = @"delete from R_ExtratoMedico where EX_CodigoRepasse = @idRepasse";
      Contexto.Database.ExecuteSqlCommand(query, new SqlParameter("@idRepasse", idRepasse));

    }

    public void RemoveIdRegistro(int idRegistro)
    {
      try
      {
        string query = @"UPDATE R_ExtratoMedico SET EX_IdRegistroPagamento = NULL WHERE EX_IdRegistroPagamento = @Id";
        Contexto.Database.ExecuteSqlCommand(query, new SqlParameter("@Id", idRegistro));
      }
      catch (Exception)
      {
        throw;
      }
    }    
  }
}


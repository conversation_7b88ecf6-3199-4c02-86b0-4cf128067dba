﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Web.Mvc;

namespace RepasseConvenio.Infrastructure.Helpers
{
  public static class EnumHelper
  {
    public static string Description(this Enum value)
    {
      return "";
    }

    public static string Description<T>(object valueEnum)
    {
      return "";
    }

    public static IEnumerable<SelectListItem> GetEnumSelectList<T>(string selectecValue)
    {
      var items;
      return items;
    }

    public static T Parse<T>(int value)
    {
      return (T);
    }

  }
}

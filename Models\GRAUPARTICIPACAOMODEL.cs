﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Web;

namespace RepasseConvenio.Models
{
  public class GrauParticipacaoModel
  {
    public int Codigo { get; set; }
    public string CodigoGrau { get; set; }
    public string Nome { get; set; }
  }

  public static class GrauParticipacaoConversion
  {

    public static R_GrauParticipacao toRateioFixoCreate(this GrauParticipacaoModel model)
    {
      R_GrauParticipacao entity = new R_GrauParticipacao();

      entity.GP_Codigo = model.CodigoGrau;
      entity.GP_Nome = model.Nome;
      return entity;
    }
    public static R_GrauParticipacao toRateioFixoEdit(this GrauParticipacaoModel model)
    {
      R_GrauParticipacao entity = new R_GrauParticipacao();
      entity.GP_Id = model.Codigo;
      entity.GP_Codigo = model.CodigoGrau;
      entity.GP_Nome = model.Nome;
      return entity;

    }
  }
}
﻿var Lote = function () {

};

  $(document).on("click", ".TrSelecionavel", function () {
    var codLote = $(this).data("codigoirpf");
    var urlLote = GetURLBaseComplete() + "/Lote/Detalhes?codigo=" + codLote;
   


    if (!$(this).hasClass("Selected")) {
      $('.Selected').each(function (index, element) {
        $(element).removeClass("Selected")
        $(element).addClass("UnSelected")
       
      });

      if ($(this).hasClass("Selected")) {
        $(this).removeClass("Selected")
        $(this).addClass("UnSelected")
        
      }
      else {
        $(this).addClass("Selected")
        $(this).removeClass("UnSelected")
      }
    }
    else {
      $(this).removeClass("Selected")
      $(this).addClass("UnSelected")
    }

    var countSelected = 0;
    $('.Selected').each(function (index, element) {
      if ($(element).hasClass("Selected")) {
        $('#Loteid').attr("href", urlLote);
        countSelected++;
      }
    });

    if (countSelected == 0) {
      $('#Loteid').removeAttr("href");
     


      $('#Loteid').attr("disabled", true);
     


      $('#Loteid').addClass("disabled");
     

    }
    else {
      $('#Loteid').attr("disabled", false);
    


      $('#Loteid').removeClass("disabled");
    

    }
  });

$(document).ready(function () {
  Lote.init();
});
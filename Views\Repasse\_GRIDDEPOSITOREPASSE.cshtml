﻿@using RepasseConvenio.Models
@using System.Globalization;

@model  List<DepositoRepasseModel>

@{
  ViewBag.Title = "Repasse";
  string action = ViewContext.RouteData.Values["action"].ToString();
}

<div class="col-md-12  ">
  <table class="table table-sm table-striped  text-nowrap">
    <thead>
      <tr>
        <th>
          Número
        </th>
        <th>
          Dt Lancamento
        </th>
        <th>
          Valor do depósito
        </th>
        <th>
          Valor utilizado
        </th>

        <th>

        </th>
      </tr>
    </thead>
    <tbody>
      @foreach (DepositoRepasseModel item in Model)
      {
        <tr>
          <td>

            @item.NumeroDocumento
          </td>
          <td>
            @item.DataDeposito.ToString("dd/MM/yyyy")
          </td>
          <td>
            @item.ValorDeposito.ToString("C2", CultureInfo.CreateSpecificCulture("pt-BR"))
          </td>
          <td>
            @item.ValorUtilizado.ToString("C2", CultureInfo.CreateSpecificCulture("pt-BR"))
          </td>

          @*<td>
               <button style="float:right" id="EditarDepositoRepasse" type="button" class="btn btn-outline-secondary btn-newitem" title="Novo Deposito">
                Editar
               </button>
             </td>
                <td>
                 <a data-idnota="@item.Codigo" class="btn btn-outline-danger btn-circle btn-sm btnRemoverDepositoRepasse" title="Editar">
                   Excluir
                 </a>

            </td>*@
        </tr>

      }
    </tbody>
  </table>
</div>

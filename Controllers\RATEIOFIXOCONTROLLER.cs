﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace RepasseConvenio.Controllers
{
  [Authorize]
  [Security("C09DE6C9-A47A-46D0-A72B-5FB46A2444B7")]

  public class RateioFixoController : LibController
  {

    private RateioFixoService RateioFixoService
    {
      get
      {
        if (_RateioFixoService == null)
          _RateioFixoService = new RateioFixoService(ContextoUsuario.UserLogged);

        return _RateioFixoService;
      }
    }
    private RateioFixoService _RateioFixoService;
    [Security("E900C79E-F8A5-41C3-89C6-1269CBBDB0EC")]

    public ActionResult Index(int? codRateio)
    {
      try
      {
        if (!codRateio.HasValue)
          throw new Exception("Nenhum médico foi selecionado");

        List<RateioFixoIndex> medico = new RateioFixoService().GettoIndex(codRateio.Value);
        ViewBag.CodigoRateio = codRateio;

        return View(medico);
      }
      catch (Exception)
      {
        throw;
      }
    }
    [Security("1339351A-91C1-4390-9B36-832E3E4D3A9E")]

    public ActionResult Create(int? codRateio)
    {
      try
      {
        if (!codRateio.HasValue)
          throw new Exception("Nenhum rateio selecionado");

        RateioFixoModel model = new RateioFixoModel();

        model.CodigoRateio = codRateio.Value;

        return View(model);
      }
      catch (Exception)
      {
        throw;
      }
    }

    [HttpPost]
    [Security("1339351A-91C1-4390-9B36-832E3E4D3A9E")]

    public ActionResult Create(RateioFixoModel model)
    {
      try
      {
        if (ModelState.IsValid)
        {
          if (RateioFixoService.Exist(model.CodigoRateio, int.Parse(model.MedicoSelect.id), model.Funcao))
            throw new Exception("Já existe um Rateio Fixo cadastrado.");

          RateioFixoService.Create(model);

          return RedirectToAction("Index", "RateioFixo", new { cod = model.CodigoMedico });
        }
        return View(model);
      }
      catch (Exception)
      {
        throw;
      }
    }
    [Security("62625D53-AE8C-457B-B102-4E88217E1186")]
    public ActionResult Edit(int? CodFixo)
    {
      try
      {
        if (!CodFixo.HasValue)
          throw new Exception("Nenhum rateio foi selecionado");

        RateioFixoModel model = new RateioFixoService().GetbyId(CodFixo.Value);

        if (model == null)
          throw new Exception("Rateio não encontrado");

        return View(model);
      }
      catch (Exception)
      {
        throw;
      }
    }

    [HttpPost]
    [Security("62625D53-AE8C-457B-B102-4E88217E1186")]

    public ActionResult Edit(RateioFixoModel model)
    {
      try
      {
        if (ModelState.IsValid)
        {
          //if (RateioFixoService.Exist(model.CodigoRateio, int.Parse(model.MedicoSelect.id), model.Funcao))
          //  throw new Exception("Já existe um Rateio Fixo cadastrado.");

          RateioFixoService.Edit(model);

          return RedirectToAction("Index", "RateioFixo", new { codRateio = model.CodigoRateio });
        }
        return View(model);
      }
      catch (Exception)
      {
        throw;
      }
    }

  }
}
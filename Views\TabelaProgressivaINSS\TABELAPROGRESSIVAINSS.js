﻿var TabelaProgressivaINSS = function () {

};

TabelaProgressivaINSS.init = function () {

  $(document).on("click", "#open-modal-valores", function () {
    $('#ModalValores').modal("show");
  });

  $(document).on("click", "#btnCreate", function () {
    TabelaProgressivaINSS.CreateValor();
  });

  $(document).on("click", ".btnEdit", function () {
    TabelaProgressivaINSS.EditValor();
  });

  $(document).on("click", ".btnRemover", function () {
    var codigo = $(this).data("codigo");
    TabelaProgressivaINSS.RemoveValor(codigo);
  });


};

TabelaProgressivaINSS.CreateValor = function () {
  $.ajax({
    type: 'POST',
    url: GetURLBaseComplete() + '/ValoresTabelaProgressivaINSS/Create',
    processData: false,
    data: $("#FormValores").serialize(),
    tradicional: true,
    success: function (data) {
      if (!data.Erro) {
        AlertaToast(data.Titulo, data.Mensagem, data.TipoMensagem, 5000, "#97f7c1");
        TabelaProgressivaINSS.GetValores();
      }
      else {
        Alerta(data.Titulo, data.Mensagem, "error");
      }
    },
    error: function (err) {
      Alerta("Erro", "Gentileza tentar mais tarde.", "error");
    }
  });
}

TabelaProgressivaINSS.EditValor = function () {
  $.ajax({
    type: 'POST',
    url: GetURLBaseComplete() + '/ValoresTabelaProgressivaINSS/Edit',
    processData: false,
    data: $("#FormValores").serialize(),
    tradicional: true,
    success: function (data) {
      if (!data.Erro) {
        TabelaProgressivaINSS.GetValores();
        AlertaToast(data.Titulo, data.Mensagem, data.TipoMensagem, 5000, "#97f7c1");
      }
      else {
        Alerta(data.Titulo, data.Mensagem, "error");
      }
    },
    error: function (err) {
      Alerta("Erro", "Gentileza tentar mais tarde.", "error");
    }
  });
}

TabelaProgressivaINSS.GetValores = function () {
  var codTabelaProgressivaINSS = $("#Codigo").val();
  $.ajax({
    type: 'GET',
    url: GetURLBaseComplete() + '/TabelaProgressivaINSS/GridValoresTPINSS',
    dataType: 'html',
    data: {
      id: codTabelaProgressivaINSS
    },
    success: function (data) {
      $("#DivGridValoresTabela").html(data);
      $('#ModalValores').modal("hide");
    },
  });
}

TabelaProgressivaINSS.RemoveValor = function (codigo) {
  if (codigo != 0)
    Swal.fire({
      title: "Deseja continuar a exclusão?",
      html: "Você irá excluir este item da Tabela Progressiva INSS, deseja continuar?",
      icon: "warning",
      confirmButtonText: "Excluir",
      showCancelButton: true,
      cancelButtonText: "Cancelar",
      showLoaderOnConfirm: true,
      dangerMode: true,
    })
      .then((willDelete) => {
        if (willDelete.isConfirmed) {
          $.ajax({
            type: 'POST',
            url: GetURLBaseComplete() + '/ValoresTabelaProgressivaINSS/Delete',
            dataType: "json",
            data: {
              id: codigo,
            },
            success: function (data) {
              if (!data.Erro) {
                AlertaToast(data.Titulo, data.Mensagem, data.TipoMensagem, 5000, "#97f7c1");
                TabelaProgressivaINSS.GetValores();
              }
              else {
                Alerta(data.Titulo, data.Mensagem, "error");
              }
            },
            error: function (err) {
              Alerta("Erro", "Gentileza tentar mais tarde.", "error");
            }
          });
        } else {
        }
      });
  else
    Alerta("Error", "Você deve selecionar pelo menos um item.", "error", "OK");
}



$(document).on("click", ".TrSelecionavel", function () {
  var idINSS = $(this).data("codigomedico");
  var idValores = $(this).data("codigovalores");
  var urlEdit = GetURLBaseComplete() + "/TabelaProgressivaINSS/Edit?codigo=" + idINSS;
  var urlDelete = GetURLBaseComplete() + "/TabelaProgressivaINSS/Delete?codigo=" + idINSS;
  //var urlDeleteValores = GetURLBaseComplete() + "/TabelaProgressivaINSS/DeleteTabela?codigo=" + idValores;



  if (!$(this).hasClass("Selected")) {
    $('.Selected').each(function (index, element) {
      $(element).removeClass("Selected")
      $(element).addClass("UnSelected")
    });

    if ($(this).hasClass("Selected")) {
      $(this).removeClass("Selected")
      $(this).addClass("UnSelected")
    }
    else {
      $(this).addClass("Selected")
      $(this).removeClass("UnSelected")
    }
  }
  else {
    $(this).removeClass("Selected")
    $(this).addClass("UnSelected")
  }

  var countSelected = 0;
  $('.Selected').each(function (index, element) {
    if ($(element).hasClass("Selected")) {
      $('#InssEdit').attr("href", urlEdit);
      $('#InssDelete').attr("href", urlDelete);
      //$('#TabelaDelete').attr("href", urlDeleteValores);
     
      countSelected++;
    }
  });

  if (countSelected == 0) {
    $('#InssEdit').removeAttr("href");
    $('#InssDelete').removeAttr("href");
    //$('#TabelaDelete').removeAttr("href");
   

    $('#InssEdit').attr("disabled", true);
    $('#InssDelete').attr("disabled", true);
    //$('#TabelaDelete').attr("disabled", true);
   

    $('#InssEdit').addClass("disabled");
    $('#InssDelete').addClass("disabled");
    //$('#TabelaDelete').addClass("disabled");
   
  }
  else {
    $('#InssEdit').attr("disabled", false);
    $('#InssDelete').attr("disabled", false);
    //$('#TabelaDelete').attr("disabled", false);
   

    $('#InssEdit').removeClass("disabled");
    $('#InssDelete').removeClass("disabled");
    //$('#TabelaDelete').removeClass("disabled");
   
  }
});


$(document).ready(function () {
  TabelaProgressivaINSS.init();
});
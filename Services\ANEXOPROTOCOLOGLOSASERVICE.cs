﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Transactions;
using RepasseConvenio.Infrastructure;
using System.Text.RegularExpressions;
using RepasseConvenio.WSPortalCooperado;
using PagedList;
using System.Configuration;
using System.IO;
using RepasseConvenio.Infrastructure.Helpers;

namespace RepasseConvenio.Services
{
  public class AnexoProtocoloGlosaService : ServiceBase
  {
    public AnexoProtocoloGlosaService()
   : base()
    { }
    public AnexoProtocoloGlosaService(RepasseEntities Contexto)
       : base(Contexto)
    { }

    public AnexoProtocoloGlosaService(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public AnexoProtocoloGlosaService(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public List<AnexoProtocoloGlosaDetalhes> GetListaAnexoProtocoloGlosaDetalhes(int IdProtocoloGlosa)
    {
      string query = @"SELECT
                        APG_Id [Codigo],
                         Replace(APG_NomeArquivoWithExtension, @Replace, '') [NomeArquivoWithExtension],
                        APG_DataAnexo [DataAnexo]
                       FROM R_AnexoProtocoloGlosa
                       WHERE APG_IdProtocoloGlosa = @IdProtocoloGlosa";

      return Contexto.Database.SqlQuery<AnexoProtocoloGlosaDetalhes>(query, new SqlParameter("@IdProtocoloGlosa", IdProtocoloGlosa)
                                                                          , new SqlParameter("@Replace", string.Format("Protocolo_{0}_", IdProtocoloGlosa))).ToList();
    }

    public AnexoProtocoloDownload GetAnexoProtocoloGlosa(int Id)
    {
      string query = @"SELECT 
                        APG.*,
                        LG.LG_Id [IdLoteGlosa]
                       FROM R_AnexoProtocoloGlosa APG
                       INNER JOIN R_ProtocoloGlosa PG ON PG.PG_Id = APG.APG_IdProtocoloGlosa AND APG.APG_Id = @Id
                       INNER JOIN R_LoteGlosa LG ON LG.LG_Id = PG.PG_IdLoteGlosa";
      return Contexto.Database.SqlQuery<AnexoProtocoloDownload>(query, new SqlParameter("@Id", Id)).FirstOrDefault();
    }
  }
}
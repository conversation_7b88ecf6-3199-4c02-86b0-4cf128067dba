﻿function formatRepo(repo) {
  return repo.text;
}

function formatRepoSelection(repo) {
  return repo.full_name || repo.text;
}

function MakeDropDown(FieldId, Url, PlaceHolder) {
  $("#" + FieldId + "Lookup").select2({
    ajax: {
      url: Url,
      dataType: 'json',
      delay: 250,
      data: function (params) {
        var idpai = $("#" + FieldId + "Lookup")[0].getAttribute("IdPai");
        var controlpai = "";
        if (idpai) {
          controlpai = $("#" + idpai).val();
        }

        var idpai2 = $("#" + FieldId + "Lookup")[0].getAttribute("IdPai2");
        var controlpai2 = "";
        if (idpai2) {
          controlpai2 = $("#" + idpai2).val();
        }

        return {
          term: params.term,
          id: controlpai,
          idaux: controlpai2
        };
      },
      processResults: function (data, params) {
        params.page = params.page || 1;
        return {
          results: data.items,
          pagination: {
            more: (params.page * 30) < data.total_count
          }
        };
      },
      cache: true
    },
    placeholder: PlaceHolder,
    allowClear: true,
    minimumInputLength: 0,
    templateResult: formatRepo,
    templateSelection: formatRepoSelection
  });

  $("#" + FieldId + "Lookup").on('select2:select', function (evt) {
    $('#' + FieldId + "_id").val(evt.params.data.id);
    $('#' + FieldId + "_text").val(evt.params.data.text);
    var idpai = $("#" + FieldId + "Lookup")[0].getAttribute("IdPai");

    var IdSelect = FieldId + "Lookup";

    ValidaFilhos(IdSelect);
  });

  $("#" + FieldId + "Lookup").on('select2:clear', function (evt) {
    //if ($('.select2-selection__clear').length == 1) {
    //  $('#' + FieldId + "_id").val(0);
    //}

    $('#' + FieldId + "_id").val("0");

    var IdSelect = FieldId + "Lookup";
    var idpai = $("#" + FieldId + "Lookup")[0].getAttribute("IdPai");

    ValidaFilhos(IdSelect);
  });

}

function ValidaFilhos(FieldId) {
  if (!$('#' + FieldId)[0].hasAttribute("idpai")) {
    $("select").each(function (index, element) {
      if ($(this)[0].hasAttribute("idpai") && $(this)[0].getAttribute("IdPai") == FieldId) {
        var NameField = $(this).attr("id");
        NameField = NameField.replace('Lookup', '');
        $(this).val(null).trigger('change');
        $(this).val(null).trigger('change');
        $("#" + NameField + "_id").val(0);
        $("#" + NameField + "_text").val('');
      }
    });
  }
}

function LimparTodosSelect() {
  $("select").each(function (index, element) {
    var NameField = $(this).attr("id");
    NameField = NameField.replace('Lookup', '');
    $(this).val(null).trigger('change');
    $(this).val(null).trigger('change');
    $("#" + NameField + "_id").val(0);
    $("#" + NameField + "_text").val('');
  });
}

function getAllElementsWithAttribute(attribute, fieldName) {
  var matchingElements = [];
  var allElements = document.getElementsByTagName('*');
  for (var i = 0, n = allElements.length; i < n; i++) {
    if (allElements[i].getAttribute(attribute) !== null && allElements[i].getAttribute(attribute) == fieldName) {
      // Element exists with attribute. Add to array.
      matchingElements.push(allElements[i]);
    }
  }
  return matchingElements;
}

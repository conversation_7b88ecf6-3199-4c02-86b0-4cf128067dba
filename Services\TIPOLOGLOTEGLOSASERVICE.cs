﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Models;
using System.Linq;

namespace RepasseConvenio.Services
{
  public class TipoLogLoteGlosaService : ServiceBase
  {
    public TipoLogLoteGlosaService(RepasseEntities Contexto)
        : base(Contexto)
    { }
    public TipoLogLoteGlosaService()
       : base()
    { }

    public TipoLogLoteGlosaService(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public TipoLogLoteGlosaService(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public int GetIdByEnum(EnumTipoLogLoteGlosa tipoLog)
    {
      return Contexto.R_TipoLogLoteGLosa.Where(a => a.TLG_Enum == (int)tipoLog).Select(a => a.TLG_Id).FirstOrDefault();
    }

    public R_TipoLogLoteGLosa GetByEnum(EnumTipoLogLoteGlosa tipoLog)
    {
      return Contexto.R_TipoLogLoteGLosa.Where(a => a.TLG_Enum == (int)tipoLog).FirstOrDefault();
    }

    public R_TipoLogLoteGLosa GetById(int Id)
    {
      return Contexto.R_TipoLogLoteGLosa.Where(a => a.TLG_Id == Id).FirstOrDefault();
    }
  }
}
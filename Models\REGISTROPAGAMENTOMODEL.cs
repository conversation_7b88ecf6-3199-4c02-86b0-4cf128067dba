﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using PagedList;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Services;
using RepasseConvenio.WSPortalCooperado;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Web;

namespace RepasseConvenio.Models
{
  public class RegistroPagamentoModel
  {
    public int Codigo { get; set; }
    public bool IsChecked { get; set; }
    public int CodigoMedico { get; set; }
    public string NomeMedico { get; set; }
    public int UsuarioProcessamento { get; set; }
    public DateTime DataGeracao { get; set; }
    public string CPFCNPJ { get; set; }
    public string Agencia { get; set; }
    public string Banco { get; set; }
    public string Conta { get; set; }
    public decimal Valor { get; set; }
    public DateTime? DataPagamento { get; set; }
    public EnumStatusRegistroPagamento EnumStatusRegistroPagamento { get; set; }

    public string DescricaoStatusRegistroPagamento { get; set; }
  }

  public class RegistroPagamentoIndex
  {

    public IPagedList<RegistroPagamentoModel> ListRegistro { get; set; }

    [DisplayName("Data De")]
    public DateTime? DataDe { get; set; }

    [DisplayName("Data Até")]
    public DateTime? DataAte { get; set; }
    public int? CodigoMedico { get; set; }

    [DisplayName("Selecione o Médico")]
    [URLSelect("Select2/GetMedicoSelect")]
    [PlaceHolderAttr("Selecione o Médico")]
    public Select2Model MedicoSelect
    {
      get
      {
        MedicoService MedicoService = new MedicoService();
        return MedicoService.GetById(this.CodigoMedico.GetValueOrDefault()).ToSelect2Model();
      }
      set
      {
        if (value != null && !String.IsNullOrEmpty(value.id) && !value.id.Equals("0"))
          this.CodigoMedico = int.Parse(value.id);
      }
    }
  }

  public static class RegistroPagamentoModelConversion
  {
    public static R_RegistroPagamento toCreateRegistroPagamento(this ExtratoRegistroPagamentoCreateModel model
                                                                , int IdUsuarioProcessamento
                                                                , int IdStatusRegistroPagamento)
    {
      return new R_RegistroPagamento()
      {
        RP_DataGeracao = DateTime.Now,
        RP_IdUsuarioProcessamento = IdUsuarioProcessamento,
        RP_CPFCNPJPagamento = model.CPFCNPJ,
        RP_IdMedico = model.IdMedico,
        RP_Banco = model.Banco,
        RP_Agencia = model.Agencia,
        RP_Conta = model.Conta,
        RP_Valor = model.ValorTotal,
        RP_IdStatusRegistroPagamento = IdStatusRegistroPagamento
      };
    }

    public static R_RegistroPagamento toEditRegistroPagamento(this RegistroPagamentoModel model)
    {
      R_RegistroPagamento entity = new R_RegistroPagamento();

      entity.RP_Id = model.Codigo;
      entity.RP_IdMedico = model.CodigoMedico;
      entity.RP_IdUsuarioProcessamento = model.UsuarioProcessamento;
      entity.RP_Agencia = model.Agencia;
      entity.RP_Banco = model.Banco;
      entity.RP_Conta = model.Conta;
      entity.RP_CPFCNPJPagamento = model.CPFCNPJ;
      entity.RP_DataGeracao = model.DataGeracao;
      entity.RP_DataPagamento = model.DataPagamento;
      entity.RP_Valor = model.Valor;

      return entity;
    }
  }
}
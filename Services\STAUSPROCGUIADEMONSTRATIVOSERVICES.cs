﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using PagedList;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RepasseConvenio.Services
{
  public class StausProcGuiaDemonstrativoServices : ServiceBase
  {
    public StausProcGuiaDemonstrativoServices(RepasseEntities Contexto)
        : base(Contexto)
    { }
    public StausProcGuiaDemonstrativoServices()
       : base()
    { }

    public StausProcGuiaDemonstrativoServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public StausProcGuiaDemonstrativoServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public int GetIdByEnum(StatusProcGuiaDemonstrativoEnum statusProcGuiaDemonstrativoEnum)
    {
      return Contexto.R_StausProcGuiaDemonstrativo.Where(a => a.SPGD_Enum == (int)statusProcGuiaDemonstrativoEnum).Select(a => a.SPGD_Id).FirstOrDefault();
    }
    public R_StausProcGuiaDemonstrativo GetByEnum(StatusProcGuiaDemonstrativoEnum statusProcGuiaDemonstrativoEnum)
    {
      return Contexto.R_StausProcGuiaDemonstrativo.Where(a => a.SPGD_Enum == (int)statusProcGuiaDemonstrativoEnum).FirstOrDefault();
    }

    public R_StausProcGuiaDemonstrativo GetById(int Id)
    {
      return Contexto.R_StausProcGuiaDemonstrativo.Where(a => a.SPGD_Id == Id).FirstOrDefault();
    }

    public List<R_StausProcGuiaDemonstrativo> Getall()
    {
      string query = @"select * from R_StausProcGuiaDemonstrativo";
      return Contexto.Database.SqlQuery<R_StausProcGuiaDemonstrativo>(query).ToList();
    }

  }
}


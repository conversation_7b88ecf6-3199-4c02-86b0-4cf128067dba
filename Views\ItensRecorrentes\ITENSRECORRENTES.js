﻿var ItensRecorrentes;

ItensRecorrentes = function () {
};

ItensRecorrentes.init = function () {

  $(document).on('change', '#TipoContaEnum', function () {

    var tipo = $(this).val();
    if (tipo == "PJ") {
      $("#EmpresaCNPJ").removeAttr("hidden", "hidden");
    }
    else {
      $("#EmpresaCNPJ").attr("hidden", "hidden");
    }
  });

  $(document).on('click', '#RemoverExtrato', function () {
    var IdExtrato = $(this).data("idextrato");
    var IdMedico = $(this).data("codigomedico");
    var Codigoclassificacao = $(this).data("codigoclassificacao");

    ItensRecorrentes.ConfirmarDelete(IdExtrato, IdMedico, Codigoclassificacao);
  });

  $(document).on('change', "#Recorrente", function () {
    if ($(this).is(":checked")) {
      $(".recorrentediv").show(true);
      $("#recorrenteAux").val(1);
    }
    else {
      $(".recorrentediv").hide(true);
      $("#recorrenteAux").val(0);
    }
  });

  $(document).on("click", ".TrSelecionavel", function () {
    var iditem = $(this).data("codigoitem");
    var codmedico = $(this).data("codmedico");

    var urlDelete = GetURLBaseComplete() + "/ItensRecorrentes/Delete?id=" + iditem + "&CodigoMedico=" + codmedico;
    var urlEdit = GetURLBaseComplete() + "/ItensRecorrentes/Edit/" + iditem;

    if (!$(this).hasClass("Selected")) {
      $('.Selected').each(function (index, element) {
        $(element).removeClass("Selected")
        $(element).addClass("UnSelected")
      });

      if ($(this).hasClass("Selected")) {
        $(this).removeClass("Selected")
        $(this).addClass("UnSelected")
      }
      else {
        $(this).addClass("Selected")
        $(this).removeClass("UnSelected")
      }
    }
    else {
      $(this).removeClass("Selected")
      $(this).addClass("UnSelected")
    }

    var countSelected = 0;
    $('.Selected').each(function (index, element) {
      if ($(element).hasClass("Selected")) {
        $('#Deletar').attr("href", urlDelete);
        $('#Editar').attr("href", urlEdit);
        countSelected++;
      }
    });

    if (countSelected == 0) {
      $('#Deletar').removeAttr("href");
      $('#Editar').removeAttr("href");

      $('#Deletar').attr("disabled", true);
      $('#Editar').attr("disabled", true);

      $('#Deletar').addClass("disabled");
      $('#Editar').addClass("disabled");

    }
    else {
      $('#Deletar').attr("disabled", false);
      $('#Editar').attr("disabled", false);

      $('#Deletar').removeClass("disabled");
      $('#Editar').removeClass("disabled");
    }
  });
}

ItensRecorrentes.ConfirmarDelete = function (IdExtrato, IdMedico, CodicoClassificacao) {

  if (CodicoClassificacao == 5) {
    Swal.fire({
      title: "Deseja continuar a exclusão?",
      html: "Este Extrato é um Processamento de Repasse, irá excluir todos os demais do mesmo repasse, deseja continuar?",
      icon: "warning",
      confirmButtonText: "Excluir",
      showCancelButton: "Cancelar",
      showLoaderOnConfirm: true,
      dangerMode: true,
    })
      .then((willDelete) => {
        if (willDelete.isConfirmed) {
          ItensRecorrentes.AjaxDelete(IdExtrato, IdMedico);
        } else {
        }
      });
  }
  else {
    Swal.fire({
      title: "Deseja continuar a exclusão?",
      html: "",
      icon: "warning",
      confirmButtonText: "Excluir",
      showCancelButton: "Cancelar",
      showLoaderOnConfirm: true,
      dangerMode: true,
    })
      .then((willDelete) => {
        if (willDelete.isConfirmed) {
          ItensRecorrentes.AjaxDelete(IdExtrato, IdMedico);
        } else {
        }
      });
  }
}

ItensRecorrentes.AjaxDelete = function (IdExtrato, IdMedico) {
  var data = {
    IdExtrato: IdExtrato
  };

  $.ajax({
    type: 'POST',
    url: GetURLBaseComplete() + '/ItensRecorrentes/Delete',
    data: data,
    dataType: 'json',
    success: function (data) {
      if (!data.Erro) {
        Alerta(data.Titulo, data.Mensagem, "success", "Fechar");
        ItensRecorrentes.GridExtratos(IdMedico);
      }
      else
        Alerta(data.Titulo, data.Mensagem, "error", "Fechar");
    },
    error: function (err) {
      AlertaSwal("Erro", "Gentileza tentar mais tarde.", "error", "Fechar");
    }
  });
}

ItensRecorrentes.GridExtratos = function (IdMedico) {
  var data = {
    id: IdMedico
  };

  $.ajax({
    type: 'GET',
    url: GetURLBaseComplete() + '/ItensRecorrentes/_GridItensRecorrentes',
    data: data,
    dataType: 'html',
    success: function (data) {
      $('#GridItensRecorrentes').html(data);
    },
    error: function (err) {
    }
  });
}

$(document).ready(function () {
  ItensRecorrentes.init();

  if ($('#Recorrente').is(":checked")) {
    $(".recorrentediv").show(true);
  }
  else {
    $(".recorrentediv").hide(true);
  }

});


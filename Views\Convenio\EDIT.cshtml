﻿
@using RepasseConvenio.Models
@using RepasseConvenio.Infrastructure
@using RepasseConvenio.Infrastructure.Controls
@model ConvenioModel

@{
  ViewBag.Title = "Atualizar Convênio";
  Layout = "~/Views/Shared/_Layout.cshtml";
}

@using (Html.BeginForm("Edit", "Convenio", FormMethod.Post))
{
  @Html.HiddenFor(c => c.<PERSON>)
  @Html.HiddenFor(c => c.CodConvenioExterno)
  <div class="row">
    <section class="col-lg-12 connectedSortable ui-sortable">
      <div class="card">
        <div class="card-header ui-sortable-handle">
          <h3 class="card-title">
            <i class="fas fa-chart-pie mr-1"></i>

          </h3>
          <div class="card-tools">
          </div>
        </div><!-- /.card-header -->
        <div class="card-body">
          <div class="row">
            <div class="col-md-2">
              <div class="form-group">
                @Html.LabelFor(c => c.CNPJ)
                @Html.EditorFor(c => c.CNPJ, new { htmlAttributes = new { @class = "form-control", @readonly = "readonly" } })
                @Html.ValidationMessageFor(c => c.CNPJ, "", new { @class = "text-danger" })
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                @Html.LabelFor(c => c.RazaoSocial)
                @Html.EditorFor(c => c.RazaoSocial, new { htmlAttributes = new { @class = "form-control", @readonly = "readonly" } })
                @Html.ValidationMessageFor(c => c.RazaoSocial, "", new { @class = "text-danger" })
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-group">
                @Html.LabelFor(c => c.CodANS)
                @Html.EditorFor(c => c.CodANS, new { htmlAttributes = new { @class = "form-control", @readonly = "readonly" } })
                @Html.ValidationMessageFor(c => c.CodANS, "", new { @class = "text-danger" })
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-group">
                @Html.LibLabel(c => c.Autarquia)
                <br />
                @Html.RadioButtonFor(c => c.Autarquia, true) Sim
                @Html.RadioButtonFor(c => c.Autarquia, false) Não
                @Html.ValidationMessageFor(c => c.Autarquia)
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                @Html.LibLabel(c => c.VencLoteGlosa)
                @Html.EditorFor(c => c.VencLoteGlosa, new { htmlAttributes = new { @class = "form-control", @type = "number", @min = "0", @maxlength = "3" } })
                @Html.ValidationMessageFor(c => c.VencLoteGlosa, "", new { @class = "text-danger" })
              </div>
            </div>
          </div>


        </div>
        <div class="card-footer">
          <button type="button" value="Voltar" class="btn btn-secondary" onclick="window.location.href='@Url.Action("Index","Convenio")'">Voltar</button>
          <button type="submit" value="Create" class="btn btn-info pull-rigth">Salvar</button>
        </div>
      </div>
    </section>
  </div>
}

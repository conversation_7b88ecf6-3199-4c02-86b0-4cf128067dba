﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using PagedList;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RepasseConvenio.Services
{
  public class HospitalServices : ServiceBase
  {
    public HospitalServices(RepasseEntities Contexto)
        : base(Contexto)
    { }
    public HospitalServices()
       : base()
    { }

    public HospitalServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public HospitalServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public R_Hospital GetById(int Id)
    {
      return Contexto.R_Hospital.Where(a => a.H_Id == Id).FirstOrDefault();
    }
    public int GetIdByNome(string RazaoSocial)
    {
      return Contexto.R_Hospital.Where(a => a.H_Nome.Equals(RazaoSocial)).Select(s => s.H_Id).FirstOrDefault();
    }
    public int GetIdByCNPJ(string CNPJ)
    {
      return Contexto.R_Hospital.Where(a => a.H_CNPJ.Equals(CNPJ)).Select(s => s.H_Id).FirstOrDefault();
    }
    public R_Hospital GetByCNPJ(string CNPJ)
    {
      return Contexto.R_Hospital.Where(a => a.H_CNPJ.Equals(CNPJ)).FirstOrDefault();
    }
    public List<R_Hospital> GetByTerm(string term)
    {
      if (string.IsNullOrEmpty(term))
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_Hospital");

        return Contexto.Database.SqlQuery<R_Hospital>(query).ToList();

      }
      else
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_Hospital
                                       WHERE H_Nome LIKE @termo OR H_CNPJ Like @termo");

        return Contexto.Database.SqlQuery<R_Hospital>(query, new SqlParameter("@termo", string.Format("%{0}%", term))).ToList();

      }
    }
  }
}


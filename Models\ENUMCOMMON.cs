﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Web;

namespace RepasseConvenio.Models
{
  public enum TipoUsuario
  {
    Administrador = 1
  }
  public enum EnumStatusRepasse
  {
    [Description("Em Elaboração")]
    ELABORACAO = 1,
    [Description("Em conferência")]
    EMCONFERENCIA = 2,
    [Description("Concluído")]
    CONCLUIDO = 3,
    [Description("Processado")]
    PROCESSADO = 4,
    [Description("Processado Parcialmente")]
    PROCESSADOPARCIALMENTE = 5,
    [Description("Processado/Glosado")]
    ProcessadoGlosado = 6
  }

  public enum EnumStatusRegistroPagamento
  {
    [Description("Repasse de Ato Não Cooperado")]
    RepasseAtoNaoCooperado = 1,
    [Description("Prolabore de Ato Não Cooperado")]
    ProlaboreAtoNaoCooperado = 2,
    [Description("Prolabore")]
    Prolabore = 3,
    [Description("Repasse")]
    Repasse = 4
  }

  public enum EnumTipoLancamento
  {
    [Description("Débito")]
    D = 1,
    [Description("Crédito")]
    C = 2
  }

  public enum EnumTipoLogLoteGlosa
  {
    [Description("Responsável assumiu lote")]
    ResponsavelAssumiuLote = 1,
    [Description("Alteração de responsável")]
    AlteracaoResponsavel = 2,
    [Description("Item inserido no lote de glosa")]
    ItemInseridoNoLote = 3,
    [Description("Item justificado na glosa")]
    ItemJustificadoGlosa = 4,
    [Description("Enviado para o convênio")]
    EnviadoConvenio = 5,
    [Description("Item Aceito Glosa")]
    ItemAceitoGlosa = 6,
    [Description("Modificado Status Glosa")]
    ModificadoStatusGlosa = 7
  }

  public enum EnumTipoRegistroImposto
  {
    [Description("IRPF")]
    IRPF = 1,
    [Description("INSS")]
    INSS = 2
  }

  public enum EnumTipoConta
  {
    [Description("Pessoa Física")]
    PF = 1,
    [Description("Pessoa Jurídica")]
    PJ = 2
  }

  public enum TipoPessoaEnum
  {
    [Description("Pessoa Física")]
    PF = 1,
    [Description("Pessoa Jurídica")]
    PJ = 2
  }

  public enum EnumClassificacaoRepasse
  {
    [Description("Estorno de honoários indevidos ou pago a maior")]
    ESTORNOHONORARIOS = 1,
    [Description("Repasse de honorários")]
    REPASSEHONORARIOS = 2,
    [Description("Estorno dos impostos retidos anteriormente")]
    ESTORNOIMPOSTOS = 3,
    [Description("Devolução de taxa administrativa")]
    DEVOLUCAOTAXAADMINISTRATIVA = 4,
    [Description("Devolução de taxa administrativa")]
    PROCESSAMENTODEREPASSE = 5,
    [Description("Pró-labore")]
    PROLABORE = 6,
    [Description("INSS")]
    INSS = 7,
    [Description("IRPF")]
    IRPF = 8,
  }

  public enum OperadorLogicoEnum
  {
    [Description("E")]
    E = 1,
    [Description("OU")]
    OU = 2
  }

  public enum ParentesesEnum
  {
    [Description("(")]
    ParentesesEsquerdo = 1,
    [Description(")")]
    ParentesesDireito = 2
  }

  public enum StatusControlheMensalEnum
  {
    [Description("Não Processado")]
    NaoProcessado = 1,
    [Description("Processado")]
    Processado = 2
  }

  public enum StatusLoteEnum
  {
    [Description("Não Processado")]
    NaoProcessado = 1,
    [Description("Processado")]
    Processado = 2,
    [Description("Processado Parcialmente")]
    ProcessadoParcialmente = 3
  }

  public enum StatusItensLoteEnum
  {
    [Description("Não Processado")]
    NaoProcessado = 1,
    [Description("Pago")]
    Pago = 2,
    [Description("Glosado")]
    Glosado = 3,
    [Description("Não Pago")]
    NaoPago = 4,
    [Description("Reapresentação")]
    Reapresentacao = 5,
    [Description("Contestação")]
    Contestacao = 6,
    [Description("Tréplica")]
    Treplica = 7
  }

  public enum StatusGuiaDemonstrativoEnum
  {
    [Description("Não Processado")]
    NaoProcessado = 1,
    [Description("Processado")]
    Processado = 2
  }

  public enum StatusDemonstrativoEnum
  {
    [Description("Não Processado")]
    NaoProcessado = 1,
    [Description("Processado")]
    Processado = 2,
    [Description("Processado Parcialmente")]
    ProcessadoParcialmente = 3
  }
  public enum StatusGlosaEnum
  {
    [Description("Em Análise no Convênio")]
    EmAnaliseConvenio = 1,
    [Description("Em Análise na Unicooper")]
    EmAnaliseUnicooper = 2,
    [Description("Em Cobrança")]
    EmCobranca = 3,
    [Description("Vencido Prazo de Retorno da Operadora")]
    VencidoPrazoRetornoOperadora = 4,
    [Description("Glosas Geradas")]
    GlosasGeradas = 5,
    [Description("Disponível Enviar Convênio")]
    DisponivelEnviarConvenio = 6,
    [Description("Glosas Aceitas")]
    GlosasAceitas = 7
  }

  public enum TipoLoteGlosaEnum
  {
    [Description("Reapresentação")]
    Reapresentacao = 1,
    [Description("Contestação")]
    Contestacao = 2,
    [Description("Tréplica")]
    Treplica = 3
  }

  public enum TipoRecorrenciaEnum
  {
    [Description("Diário")]
    Diario = 1,
    [Description("Semanal")]
    Semanal = 2,
    [Description("Mensal")]
    Mensal = 3,
    [Description("Anual")]
    Anual = 4
  }

  public enum StatusProcGuiaDemonstrativoEnum
  {
    [Description("Pago")]
    Pago = 1,
    [Description("Não Pago")]
    NaoPago = 2,
    [Description("Glosado")]
    Glosado = 3,
    [Description("Glosa Aceita")]
    GlosaAceita = 4,
    [Description("Justificada")]
    Justificada = 5
  }

  public enum SituacaoItensLoteEnum
  {
    [Description("Em Análise no Convênio")]
    EmAnaliseConvenio = 1,
    [Description("Em Análise na Unicooper")]
    EmAnaliseUnicooper = 2,
    [Description("Em Cobrança")]
    EmCobranca = 3,
    [Description("Vencido Prazo de Retorno da Operadora")]
    VencidoPrazoRetornoOperadora = 4,
    [Description("Glosas Geradas")]
    GlosasGeradas = 5,
    [Description("Glosa Devolvida")]
    GlosaDevolvida = 6,
    [Description("Pago")]
    Pago = 7,
    [Description("Não Pago")]
    NaoPago = 8,
    [Description("Justificada")]
    Justificada = 9,
    [Description("Não Processada")]
    NaoProcessada = 10
  }

  public enum ParametrosEnum
  {
    [Description("Lembrete Venciento Glosa")]
    LembreteVencientoGlosa = 1
  }

  public enum EnumSimNao
  {
    [Description("Sim")]
    Sim = 1,

    [Description("Não")]
    Não = 0,
  }

  public enum TipoImportacaoGuiaDemonstrativoEnum
  {
    [Description("Nota Fiscal")]
    NotaFiscal = 1,
    [Description("Planilha")]
    Planilha = 2
  }

  public enum StatusGuiaNotaFiscalEnum
  {
    [Description("Pago")]
    Pago = 1,
    [Description("Não Pago")]
    NaoPago = 2,
    [Description("Glosado")]
    Glosado = 3,
    [Description("Não Processada")]
    NaoProcessada = 4
  }
}
﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using PagedList;
using RepasseConvenio.WSPortalCooperado;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;

namespace RepasseConvenio.Models
{
  public class ConvenioIndex
  {
    public IPagedList<int> QuantidadeConvenios { get; set; }

    public List<ConvenioModel> ListaConvenio { get; set; }
  }

  public class ConvenioModel
  {
    public ConvenioModel()
    {
      this.ListProcedimentos = new List<ProcedimentoIndex>();
    }
    public int Codigo { get; set; }
    [DisplayName("Razão social")]
    public string RazaoSocial { get; set; }
    [DisplayName("CNPJ")]
    public string CNPJ { get; set; }
    [DisplayName("Código ANS")]
    public string CodANS { get; set; }
    [DisplayName("Convênio externo")]
    public string CodConvenioExterno { get; set; }
    [DisplayName("Autarquia")]
    public bool Autarquia { get; set; }
    [DisplayName("Dias para Vencimento do Lote de Glosa")]
    [Range(0, 180, ErrorMessage = "Por favor insira um valor entre 0 e 180 dias.")]
    public int? VencLoteGlosa { get; set; }
    public List<ProcedimentoIndex> ListProcedimentos { get; set; }
  }

  public static class ConvenioModelConversions
  {
    public static R_Convenio ToConvenioCreate(this ConvenioModel model)
    {
      R_Convenio entity = new R_Convenio();
      entity.C_CNPJ = model.CNPJ;
      entity.C_CodANS = model.CodANS;
      entity.C_RazaoSocial = model.RazaoSocial;
      entity.C_IdConvenioExterno = model.CodConvenioExterno;
      entity.C_Autarquia = model.Autarquia;
      entity.C_VencLoteGlosa = model.VencLoteGlosa;
      return entity;
    }



    public static R_Convenio ToConvenioEdit(this ConvenioModel model)
    {
      R_Convenio entity = new R_Convenio();
      entity.C_Id = model.Codigo;
      entity.C_CNPJ = model.CNPJ;
      entity.C_CodANS = model.CodANS;
      entity.C_RazaoSocial = model.RazaoSocial;
      entity.C_IdConvenioExterno = model.CodConvenioExterno;
      entity.C_Autarquia = model.Autarquia;
      entity.C_VencLoteGlosa = model.VencLoteGlosa;
      return entity;
    }

    public static R_Convenio ConvenioRepasseToEntityCreate(this R_Convenio convenio, ConvenioRepasse convenioRepasse)
    {
      return new R_Convenio()
      {
        C_RazaoSocial = convenioRepasse.RazaoSocial,
        C_CodANS = convenioRepasse.CodANS,
        C_CNPJ = convenioRepasse.CNPJConvenio,
        C_IdConvenioExterno = convenioRepasse.Codigo.ToString()
      };
    }
  }
}
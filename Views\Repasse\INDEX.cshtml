﻿@using RepasseConvenio.Models
@using RepasseConvenio.Infrastructure
@model List<RepasseModelIndex>

@{
  ViewBag.Title = "Repasse";
}

@Scripts.Render("~/Views/Repasse/Repasse.js?ia=ia")
@Styles.Render("~/Views/Repasse/Repasse.css?pu=pu")

<div class="row">
  <section class="col-lg-12 connectedSortable ui-sortable">
    <div class="card">
      <div class="card-header ui-sortable-handle">
        <h3 class="card-title">
          <i class="fas fa-chart-pie mr-1"></i>
          Repasses
        </h3>
        <div class="card-tools">
          @if(ContextoUsuario.UserLogged.IsInPermissao(Guid.Parse("978D21DD-B8E5-4A0E-8BD0-595B7BFE93AB")))
          {
            <a class="btn btn-outline-secondary btn-circle btn-sm" href="@Url.Action("Create", "Repasse")">Novo</a>
          }
        </div>
      </div><!-- /.card-header -->
      <div class="card-body">
        <div class="tab-content p-0">
          <div class="box-body">
            <div class="card-acoes col-4">
              <a class="btn btn-block btn-outline-primary disabled" id="RepasseEdit"> Editar </a>
              <a class="btn btn-block btn-outline-primary disabled" id="RepasseDetalhes"> Detalhes </a>
              <a class="btn btn-block btn-outline-primary disabled" id="RepasseExcluir"> Excluir </a>

            </div>

            <div class="row" style="justify-content: space-around;">

              <div class="stats">
                <div class="emelaboracao"></div>
                &nbsp;Em elaboração
              </div>

              <div class="stats">
                <div class="emconferencia"></div>
                &nbsp;Em conferência
              </div>

              <div class="stats">
                <div class="concluido"></div>
                &nbsp;Concluído
              </div>

              <div class="stats">
                <div class="processado"></div>
                &nbsp;Processado
              </div>

              <div class="stats">
                <div class="parcProcessado"></div>
                &nbsp;Parcialmente Processado
              </div>

              <div class="stats">
                <div class="glossado"></div>
                &nbsp;Glossado
              </div>

            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-12 table-responsive p-0 ">
            <table class="table table-sm table-striped table-hover text-nowrap">
              <thead>
                <tr>
                  <th>
                  </th>
                  <th>
                    Numero
                  </th>
                  <th>
                    Convênio
                  </th>
                  <th>
                    Data
                  </th>
                  <th>
                    Responsável
                  </th>
                </tr>
              </thead>
              <tbody>
                @foreach (RepasseModelIndex item in Model)
                {
                  <tr class="TrSelecionavel" data-codigorepasse="@item.Codigo" data-statusrepasse="@Convert.ToInt32(item.StatusRepasseEnum)">
                    <td class="statusrepasse">
                      @switch ((EnumStatusRepasse)item.StatusRepasseEnum)
                      {
                        case EnumStatusRepasse.EMCONFERENCIA:
                          <div class="emconferencia"></div>
                          break;
                        case EnumStatusRepasse.ELABORACAO:
                          <div class="emelaboracao"></div>
                          break;
                        case EnumStatusRepasse.CONCLUIDO:
                          <div class="concluido"></div>
                          break;
                        case EnumStatusRepasse.PROCESSADO:
                          <div class="processado"></div>
                          break;
                        case EnumStatusRepasse.PROCESSADOPARCIALMENTE:
                          <div class="parcProcessado"></div>
                          break;
                        case EnumStatusRepasse.ProcessadoGlosado:
                          <div class="glossado"></div>
                          break;
                        default:
                          break;
                      }
                    </td>
                    <td>
                      @item.Numero
                    </td>
                    <td>
                      @item.Convenio
                    </td>
                    <td>
                      @item.DataInicio.ToString("dd/MM/yyyy")
                    </td>
                    <td>
                      @item.Responsavel
                    </td>
                  </tr>
                }
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
</div><!-- /.card-body -->
        </div>
    </section>
</div>


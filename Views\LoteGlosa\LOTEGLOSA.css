﻿.select2{
  width: 100% !important;
}

.formGuiaAtendimento > input {
  height: 17px !important;
  font-size: 15px !important;
  padding: 1px 5px !important;
  line-height: 1 !important;
}

  .formGuiaAtendimento > input:disabled {
    background-color: white;
  }

.formGuiaAtendimento > label {
  font-size: 10px !important;
  margin-bottom: 0px !important;
  font-weight: 700 !important;
}

#ProcedimentosGuia > thead > tr > th {
  font-size: 10px !important;
  margin-bottom: 0px !important;
  font-weight: 700 !important;
}

#ProcedimentosGuia > tbody > tr > td > input {
  height: 17px !important;
  font-size: 12px !important;
  padding: 1px 5px !important;
  line-height: 1 !important;
}

  #ProcedimentosGuia > tbody > tr > td > input:disabled {
    background-color: white;
  }

#ProcedimentosGuia > tbody > tr > td {
  height: 17px !important;
  font-size: 12px !important;
  padding: 1px 5px !important;
  line-height: 1 !important;
}

.TrSelecionavel {
  cursor: pointer;
}

.Selected {
  background-color: #d6101085 !important;
  transition: all 100ms;
}

.UnSelected {
  transition: all 100ms;
}

.card-tools > button {
  margin: 0 2pt !important;
}

.card-tools > a {
  margin: 0 2pt !important;
}

.card-acoes {
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
  margin: 5px 0;
}

  .card-acoes > a {
    margin: 0 2pt;
  }

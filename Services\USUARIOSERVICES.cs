﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using PagedList;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RepasseConvenio.Services
{
  public class UsuarioServices : ServiceBase
  {
    public UsuarioServices(RepasseEntities Contexto)
        : base(Contexto)
    { }
    public UsuarioServices()
       : base()
    { }

    public UsuarioServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public UsuarioServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public void Create(UsuarioModel model)
    {
      ValidaCreateBasic(model);
      R_Usuario Usuario = new R_Usuario();
      Usuario = model.ToUsuarioCreate();
      Create(Usuario);
    }

    public void ValidaCreateBasic(UsuarioModel model)
    {
      R_Usuario R_Usuario = GetByCPF(model.CPF);

      if (R_Usuario != null)
        throw new CustomException("Usuário com este CPF já cadastrado no sistema.");
    }

    public List<UsuarioModel> Get(int pageNumber, string filtro)
    {
      string Query = String.Format(@"SELECT 
                                      U.U_Id [Codigo],
                                      U.U_Nome [Nome],
                                      U.U_CPF [CPF],
                                      U.U_Email [Email],
                                      U.U_IdTipoUsuario [TipoUsuario]
                                     FROM
                                      R_Usuario U
                                     ORDER BY U.U_Nome");
      return Contexto.Database.SqlQuery<UsuarioModel>(Query).ToList();/*.ToPagedList(pageNumber, PageSize)*/
    }

    //public IPagedList<UsuarioIndex> GetFiltro(int pageNumber, UsuarioIndex usuarioIndex)
    //{
    //  string Query;

    //  if (String.IsNullOrEmpty(usuarioIndex.NomeUsuario)
    //    && String.IsNullOrEmpty(usuarioIndex.CPFUsuario)
    //    && String.IsNullOrEmpty(usuarioIndex.EmailUsuario)
    //    && (usuarioIndex.StatusUsuario == null || usuarioIndex.StatusUsuario == 0))
    //  {

    //    Query = String.Format(@"SELECT 
    //                                  U.U_Id [CodigoUsuario],
    //                                  U.U_Nome [NomeUsuario],
    //                                  U.U_CPF [CPFUsuario],
    //                                  U.U_Email [EmailUsuario],
    //                                  SU.SU_Id [StatusUsuario]
    //                                 FROM
    //                                  TC_Usuarios U
    //                                 INNER JOIN TC_StatusUsuario SU ON SU.SU_Id = U.U_StatusUsuario_Id
    //                                 ORDER BY U.U_Nome");

    //    return Contexto.Database.SqlQuery<UsuarioIndex>(Query).ToList().ToPagedList(pageNumber, PageSize);
    //  }

    //  else
    //  {
    //    Query = String.Format(@"SELECT 
    //                                  U.U_Id [CodigoUsuario],
    //                                  U.U_Nome [NomeUsuario],
    //                                  U.U_CPF [CPFUsuario],
    //                                  U.U_Email [EmailUsuario],
    //                                  SU.SU_Id [StatusUsuario]
    //                                 FROM
    //                                  TC_Usuarios U
    //                                 INNER JOIN TC_StatusUsuario SU ON SU.SU_Id = U.U_StatusUsuario_Id
    //                                 WHERE 
    //                                  U.U_Nome LIKE @NomeUsuario
    //                                  OR U.U_CPF LIKE @CPFUsuario
    //                                  OR U.U_Email LIKE @EmailUsuario
    //                                  OR SU.SU_Id = @StatusUsuario
    //                                 ORDER BY U.U_Nome");
    //  }
    //  usuarioIndex.NomeUsuario = (String.IsNullOrEmpty(usuarioIndex.NomeUsuario)) ? null : string.Format(@"%{0}%", usuarioIndex.NomeUsuario);
    //  usuarioIndex.CPFUsuario = (String.IsNullOrEmpty(usuarioIndex.CPFUsuario)) ? null : string.Format(@"%{0}%", usuarioIndex.CPFUsuario);
    //  usuarioIndex.EmailUsuario = (String.IsNullOrEmpty(usuarioIndex.EmailUsuario)) ? null : string.Format(@"%{0}%", usuarioIndex.EmailUsuario);
    //  usuarioIndex.StatusUsuario = (usuarioIndex.StatusUsuario == null) ? null : usuarioIndex.StatusUsuario;

    //  return Contexto.Database.SqlQuery<UsuarioIndex>(Query
    //                                                  , new SqlParameter("@NomeUsuario", (object)usuarioIndex.NomeUsuario ?? DBNull.Value)
    //                                                  , new SqlParameter("@CPFUsuario", (object)usuarioIndex.CPFUsuario ?? DBNull.Value)
    //                                                  , new SqlParameter("@EmailUsuario", (object)usuarioIndex.EmailUsuario ?? DBNull.Value)
    //                                                  , new SqlParameter("@StatusUsuario", (object)usuarioIndex.StatusUsuario ?? DBNull.Value)
    //                                                  ).ToList().ToPagedList(pageNumber, PageSize);
    //}

    public R_Usuario GetById(int Id)
    {
      return Contexto.R_Usuario.Where(a => a.U_Id == Id).FirstOrDefault();
    }

    public R_Usuario GetByIdSqlQuery(int Id)
    {
      string query = @"SELECT * FROM R_Usuario WHERE U_Id = @IdUsuario";
      return Contexto.Database.SqlQuery<R_Usuario>("@IdUsuario", Id).FirstOrDefault();
    }

    public R_Usuario GetByUsuarioLogado()
    {
      string query = @"Select * from R_Usuario where U_Id = @IdUsuario";

      return Contexto.Database.SqlQuery<R_Usuario>(query, new SqlParameter("@IdUsuario", User.IdUsuario)).FirstOrDefault();
    }

    public R_Usuario GetByIdNoTracking(int Id)
    {
      return Contexto.R_Usuario.Where(a => a.U_Id == Id).AsNoTracking().FirstOrDefault();
    }

    //public UsuarioModel GetByIdModel(Guid Id)
    //{
    //  return Contexto.R_Usuario.Where(a => a.U_Id == Id).FirstOrDefault().ToR_UsuariosEdit();
    //}

    public R_Usuario GetByCPF(string CPF)
    {
      return Contexto.R_Usuario.Where(a => a.U_CPF.Equals(CPF)).FirstOrDefault();
    }

    public R_Usuario GetByCPFAndSenha(string CPF, string Senha)
    {
      Senha = CryptoHelper.Encrypt(Senha);

      string query = @"SELECT 
                        * 
                        FROM 
                        R_Usuario
                        WHERE 
                        U_CPF = @CPF AND U_Senha = @Senha";

      return Contexto.Database.SqlQuery<R_Usuario>(query
                                                  , new SqlParameter("@CPF", CPF)
                                                  , new SqlParameter("@Senha", Senha)).FirstOrDefault();
    }

    public R_Usuario GetByEmail(string Email)
    {
      return Contexto.R_Usuario.Where(a => a.U_Email.Equals(Email)).FirstOrDefault();
    }

    //public WSLoggedInfo GetWSLoggedInfo(string Email)
    //{
    //  TC_Usuarios WSLoggedInfo = Contexto.TC_Usuarios.Where(a => a.U_Email.Equals(Email)).FirstOrDefault();
    //  return new WSLoggedInfo()
    //  {
    //    UserId = WSLoggedInfo.U_Id,
    //    Status = WSLoggedInfo.TC_StatusUsuario.SU_Enum,
    //    NomeUser = WSLoggedInfo.U_Nome,
    //    Medico = new TipoUsuarioService().GetById(WSLoggedInfo.U_IdTipoUsuario).Equals("Medico") ? true : false,
    //    UltimoLogin = DateTime.Now.ToString("yyyy-MM-ddTHH\\:mm\\:ss.fffffffzzz")
    //  };
    //}

    //public WSEditProfile GetProfileWS(Guid Id)
    //{
    //  string Query = @"SELECT
    //                     U.U_Email AS Email,
    //                     U.U_NomeMae as NomeMae,
    //                     U.U_TelefoneCelular AS TelefoneCelular,
    //                     U.U_TelefoneFixo AS TelefoneFixo,
    //                     U.U_CEP AS CEP,
    //                     M.M_NomeMunIBGE AS Cidade,
    //                     U.U_Complemento AS Complemento,
    //                     UF.UF_DesUF AS Estado,
    //                     U.U_NumeroLogradouro AS Numero,
    //                     U.U_Logradouro AS Rua,
    //			            U.U_Bairro AS Bairro
    //                    FROM TC_Usuarios U
    //                    LEFT JOIN TC_Municipio M ON M.M_Id=U.U_IfMunicipio
    //                    LEFT JOIN TC_UF UF ON UF.UF_Id=U.U_IdUF
    //                    WHERE U.U_Id=@Id";

    //  return Contexto.Database.SqlQuery<WSEditProfile>(Query, new SqlParameter("@Id", Id.ToString())).FirstOrDefault();
    //}

   
    public void Edit(UsuarioModel UsuarioModel)
    {
      R_Usuario user = UsuarioModel.ToUsuarioEdit();
      Edit(user);
    }


    //public void Edit(UsuarioModel Usuarios)
    //{
    //  R_Usuario R_Usuario = GetById(Usuarios.Codigo);
    //  R_Usuario = Usuarios.ToUsuarioEdit();;
    //  Edit(TC_Usuarios);
    //}

    //public void Send(UsuarioModel Usuarios)
    //{
    //  TC_Usuarios TC_Usuarios = GetById(Usuarios.Codigo);

    //  TC_Usuarios.ToTC_UsuariosEditAprovacao(Usuarios);
    //  Edit(TC_Usuarios);
    //}

    //public void Edit(R_Usuario R_Usuario)
    //{
    //  Contexto.Entry(R_Usuario).State = EntityState.Modified;
    //  Contexto.SaveChanges();
    //}

    public void Delete(int Id)
    {
      R_Usuario R_Usuarios = GetById(Id);

      Contexto.R_Usuario.Remove(R_Usuarios);
      Contexto.SaveChanges();
    }

    //public void ResendEmail(string Email)
    //{
    //  R_Usuario User = GetByEmail(Email);

    //  if (User == null)
    //    throw new Exception("Usuário não encontrado. Favor entrar com contato com suporte.");

    //  string ConfirmCode = Convert.ToBase64String(Encoding.UTF8.GetBytes(Email));
    //  string Mensagem = string.Format(@"Olá {0} <br>
    //                                      <p>
    //                                      Agora falta apenas você confirmar sua conta clicando</p> <a href='{1}Account/Confirm/{2}'>aqui</a><p>.
    //                                      Caso não consiga acessar você pode abrir o link diretamente: <br/>
    //                                      {1}Account/Confirm/{2} <br/>
    //                                      Equipe Digital Cash
    //                                      </p>",
    //                                      User.U_Nome, ConfigurationManager.AppSettings["URLBase"], ConfirmCode);

    //  EMailHelper.Send(new string[1] { Email }, "Confirme sua conta Digital Cash", Mensagem);

    //}

    //public void SendLoginEmail(string Email, string IP = null)
    //{
    //  R_Usuario User = Contexto.R_Usuario.Where(a => a.U_Email.Equals(Email)).FirstOrDefault();

    //  if (User == null)
    //    throw new Exception("Usuário não encontrado");

    //  //string Token = AuthHelper.SendVerificationEmail("<EMAIL>", IP);
    //  //string Token = AuthHelper.SendVerificationEmail(Email, IP);

    //  //User.U_UltimoToken = DateTime.Now;
    //  //User.U_Token = Token;
    //  Edit(User);
    //}

    //public string VerifyLoginToken(string Email, string Token)
    //{
    //  R_Usuario User = Contexto.R_Usuario.Where(a => a.U_Email.Equals(Email)).FirstOrDefault();

    //  if (User == null)
    //    return "Usuário não encontrado.";

    //  Edit(User);
    //  return "Success";
    //}

    public List<R_Usuario> GetByTerm(string term)
    {
      if (string.IsNullOrEmpty(term))
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_Usuario");

        return Contexto.Database.SqlQuery<R_Usuario>(query).ToList();

      }
      else
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_Usuario
                                       WHERE U_Nome LIKE @termo");

        return Contexto.Database.SqlQuery<R_Usuario>(query, new SqlParameter("@termo", string.Format("%{0}%", term))).ToList();

      }
    }

    //public UsuarioCompleteRegister GetUsuarioCompleteRegister()
    //{
    //  string query = @"SELECT
    //                    U.U_Id [IdUsuario],
    //                    U.U_Nome [Nome],
    //                    U.U_Email [Email],
    //                    U.U_CPF [CPF],
    //                    U.U_TelefoneFixo [TelefoneFixo],
    //                    U.U_TelefoneCelular [TelefoneCelular],
    //                    U.U_DtNascimento [DataNascimento],
    //                    U.U_RG [RG],
    //                    U.U_OrgaoExpedidorRG [OrgaoExpedidorRG],
    //                    U.U_DataEmissaoRG [DataEmissaoRG],
    //                    U.U_IdSexo [IdSexo],
    //                    U.U_IdEstadoCivil [IdEstadoCivil],
    //                    U.U_IdRacaCor [IdRacaCor],
    //                    U.U_IdGrauInstrucao [IdGrauInstrucao],
    //                    U.U_IdConselho [IdConselho],
    //                    U.U_IdUfConselho [IdUFConselho],
    //                    U.U_NroCrm [NumeroCRM],
    //                    U.U_DtEmissaoCRM [DataEmissaoCRM]
    //                   FROM
    //                    TC_Usuarios U
    //                   WHERE U.U_Id = @IdUsuarioLogado";

    //  return Contexto.Database.SqlQuery<UsuarioCompleteRegister>(query, new SqlParameter("@IdUsuarioLogado", User.IdUsuario)).FirstOrDefault();
    //}

    //public EnderecoUsuario GetEnderecoUsuario()
    //{
    //  string query = @"SELECT
    //                    U_Id [IdUsuario],
    //                    U_CEP [CEP],
    //                    U_Logradouro [Logradouro],
    //                    U_NumeroLogradouro [Numero],
    //                    U_Complemento [Complemento],
    //                    U_Bairro [Bairro],
    //                    U_IdUF [IdUF],
    //                    U_IfMunicipio[IdMunicipio],
    //                    U_Cnes [NumeroCnes],
    //		            UF.UF_DesUF [UF],
    //		            M.M_NomeMunIBGE [Municipio],
    //					      U.U_TelefoneComercial [TelefoneComercial],
    //					      U.U_EmailComercial [EmailComercial]
    //                   FROM
    //                    TC_Usuarios U
    //		           LEFT JOIN TC_UF UF ON UF.UF_Id = U.U_IdUF
    //		           LEFT JOIN TC_Municipio M ON M.M_Id = U.U_IfMunicipio
    //                   WHERE U_Id = @IdUsuarioLogado";

    //  return Contexto.Database.SqlQuery<EnderecoUsuario>(query, new SqlParameter("@IdUsuarioLogado", User.IdUsuario)).FirstOrDefault();
    //}

    //public DadosBasicosEnderecoUsuario GetDadosBasicoANDEnderecoUsuario()
    //{
    //  string query = @"SELECT
    //                    U_Id [IdUsuario],
    //		            U_Nome [Nome],
    //		            U_Email [Email],
    //		            U_CPF [CPF],
    //		            U_TelefoneFixo [TelefoneFixo],
    //		            U_TelefoneCelular [TelefoneCelular],
    //		            U_RG [RG],
    //		            U_OrgaoExpedidorRG [OrgaoExpedidorRG],
    //		            U_DataEmissaoRG [DataEmissaoRG],
    //		            U_DtNascimento [DataNascimento],
    //		            S_Descricao [Sexo],
    //		            EC_EstadoCivil [EstadoCivil],
    //		            RC.RC_RacaCor [RacaCor],
    //		            GI.GI_GrauInstrucao [GrauInstrucao],
    //		            C.C_Conselho [Conselho],
    //		            UFCONSELHO.UF_DesUF [UFCONSELHO],
    //		            U.U_NroCrm [NumeroCRM],
    //		            u.U_DtEmissaoCRM [DataEmissaoCRM],
    //                    U_CEP [CEP],
    //                    U_Logradouro [Logradouro],
    //                    U_NumeroLogradouro [Numero],
    //                    U_Complemento [Complemento],
    //                    U_Bairro [Bairro],
    //                    U_IdUF [IdUF],
    //                    U_IfMunicipio[IdMunicipio],
    //                    U_Cnes [NumeroCnes],
    //		            UFUSER.UF_CodUF [UF],
    //		            M.M_NomeMunIBGE [Municipio],
    //					      U.U_TelefoneComercial [TelefoneComercial],
    //					      U.U_EmailComercial [EmailComercial]
    //                   FROM TC_Usuarios U
    //		           LEFT JOIN TC_UF UFUSER ON UFUSER.UF_Id = U.U_IdUF
    //		           LEFT JOIN TC_Municipio M ON M.M_Id = U.U_IfMunicipio
    //				       LEFT JOIN TC_Sexo S ON S.S_Id = U.U_IdSexo
    //				       LEFT JOIN TC_EstadoCivil EC ON EC.EC_Id = U.U_IdEstadoCivil
    //				       LEFT JOIN TC_RacaCor RC ON RC.RC_Id = U.U_IdRacaCor
    //				       LEFT JOIN TC_GrauInstrucao GI ON GI.GI_Id = U.U_IdGrauInstrucao
    //				       LEFT JOIN TC_Conselho C ON C.C_Id = U.U_IdConselho
    //				       LEFT JOIN TC_UF UFCONSELHO ON UFCONSELHO.UF_Id = U.U_IdUfConselho
    //                   WHERE U_Id = @IdUsuarioLogado";

    //  return Contexto.Database.SqlQuery<DadosBasicosEnderecoUsuario>(query, new SqlParameter("@IdUsuarioLogado", User.IdUsuario)).FirstOrDefault();
    //}

    //public InfoPacienteRdlc GetInfoPacienteRdlc(Guid IdConsulta)
    //{
    //  string query = @"SELECT
    //                    CONVE.C_RazaoSocial [NomeConvenio],
    //                    PC.PC_Descricao [NomePlano],
    //                    UCP.UPC_NumeroCarteirinha [NumeroCarteirinha],
    //                    UCP.UPC_ValidadeCarteirinha [ValidadeCarteirinha],
    //                    UPAC.U_Nome [NomePaciente]
    //                   FROM TC_Consulta CONSU
    //                   INNER JOIN TC_Convenio CONVE ON CONVE.C_Id = CONSU.C_IdConvenio
    //                   INNER JOIN TC_PlanoConvenio PC ON PC.PC_Id = CONSU.C_IdPlanoConvenio
    //                   INNER JOIN TC_Usuarios UPAC ON UPAC.U_Id = CONSU.C_IdUsuario
    //                   INNER JOIN TC_UsuarioConvenioPlano UCP ON UCP.UCP_IdUsuario = UPAC.U_Id AND UCP_IdConvenio = CONVE.C_Id
    //                   where CONSU.C_Id = @IdConsulta";


    //  InfoPacienteRdlc infoPacienteRdlc = Contexto.Database.SqlQuery<InfoPacienteRdlc>(query, new SqlParameter("@IdConsulta", IdConsulta)).FirstOrDefault();
    //  if (infoPacienteRdlc == null)
    //    return new InfoPacienteRdlc();
    //  else
    //    return infoPacienteRdlc;
    //}

    //public InfoMedicoRdlc GetInfoMedicoRdlc(Guid IdConsulta)
    //{
    //  string query = @"SELECT
    //                    UMED.U_Nome [NomeProfissionalExecutante],
    //                    C.C_Conselho [ConselhoProfissional],
    //                    C.C_Conselho [NumeroConselho],
    //                    UF.UF_CodUF [UFConselho],
    //                    E.E_Codigo [CodigoCBO]
    //                   FROM TC_Consulta CONSU
    //                   INNER JOIN TC_Usuarios UMED ON UMED.U_Id = CONSU.C_IdUsuarioAtendimento
    //                   LEFT JOIN TC_Conselho C ON UMED.U_IdConselho = C.C_Id
    //                   LEFT JOIN TC_UF UF ON UF.UF_Id = UMED.U_IdConselho
    //                   INNER JOIN TC_EspecialidadeUsuario EU ON EU.EU_IdUsuario = UMED.U_Id AND EU.EU_Principal = 1
    //                   INNER JOIN TC_Especialidade E ON E.E_Id = EU.EU_IdEspecialidade
    //                   WHERE CONSU.C_Id = @IdConsulta";

    //  InfoMedicoRdlc infoMedicoRdlc = Contexto.Database.SqlQuery<InfoMedicoRdlc>(query, new SqlParameter("@IdConsulta", IdConsulta)).FirstOrDefault();

    //  if (infoMedicoRdlc == null)
    //    return new InfoMedicoRdlc();
    //  else
    //    return infoMedicoRdlc;
    //}
  }
}


﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Controls;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;


namespace RepasseConvenio.Controllers
{
  public class GuiaNotaFiscalController : LibController
  {
    [HttpPost]
    public JsonResult Create(List<int> ListaIdGuiasAtendimento, int IdNotaFiscal)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        GuiaNotaFiscalService guiaNotaFiscalService = new GuiaNotaFiscalService();
        guiaNotaFiscalService.Create(ListaIdGuiasAtendimento, IdNotaFiscal);
        retornoAjax.Erro = false;
        retornoAjax.messageType = MessageType.Success;
        retornoAjax.Mensagem = "Guias adicionadas na nota fiscal.";
        retornoAjax.Titulo = "Sucesso.";
        return Json(retornoAjax);
      }
      catch (CustomException ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.messageType = MessageType.Error;
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Titulo = "Erro.";
        return Json(retornoAjax);
      }
      catch (Exception ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.messageType = MessageType.Error;
        retornoAjax.Mensagem = "Houve um erro no momento de processar sua solicitação.";
        retornoAjax.Titulo = "Erro.";
        return Json(retornoAjax);
      }
    }

    [HttpPost]
    public JsonResult Edit(int IdGuiaNotaFiscal, decimal valorValorPago, decimal valorApresentado)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        GuiaNotaFiscalService guiaNotaFiscalService = new GuiaNotaFiscalService();
        guiaNotaFiscalService.Edit(IdGuiaNotaFiscal, valorValorPago, valorApresentado);
        retornoAjax.Erro = false;
        retornoAjax.messageType = MessageType.Success;
        retornoAjax.Mensagem = "Guias adicionadas na nota fiscal.";
        retornoAjax.Titulo = "Sucesso.";
        return Json(retornoAjax);
      }
      catch (CustomException ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.messageType = MessageType.Error;
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Titulo = "Erro.";
        return Json(retornoAjax);
      }
      catch (Exception ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.messageType = MessageType.Error;
        retornoAjax.Mensagem = "Houve um erro no momento de processar sua solicitação.";
        retornoAjax.Titulo = "Erro.";
        return Json(retornoAjax);
      }
    }

    [HttpPost]
    public JsonResult Delete(int IdGuiaNotaFiscal)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        GuiaNotaFiscalService guiaNotaFiscalService = new GuiaNotaFiscalService();
        guiaNotaFiscalService.Delete(IdGuiaNotaFiscal);
        retornoAjax.Erro = false;
        retornoAjax.messageType = MessageType.Success;
        retornoAjax.Mensagem = "Guia removida na nota fiscal.";
        retornoAjax.Titulo = "Sucesso.";
        return Json(retornoAjax);
      }
      catch (CustomException ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.messageType = MessageType.Error;
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Titulo = "Erro.";
        return Json(retornoAjax);
      }
      catch (Exception ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.messageType = MessageType.Error;
        retornoAjax.Mensagem = "Houve um erro no momento de processar sua solicitação.";
        retornoAjax.Titulo = "Erro.";
        return Json(retornoAjax);
      }
    }

    [HttpPost]
    public JsonResult ValidaExistLoteOrGuia(string NumeroGuia, string NumeroLote)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        GuiaNotaFiscalService guiaNotaFiscalService = new GuiaNotaFiscalService();
        guiaNotaFiscalService.ValidaExistLoteOrGuia(NumeroGuia, NumeroLote);
        retornoAjax.Erro = false;
        retornoAjax.messageType = MessageType.Success;
        retornoAjax.Mensagem = "Sucesso.";
        retornoAjax.Titulo = "Sucesso.";
        return Json(new { RetornoAjax = retornoAjax });
      }
      catch (CustomException ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.messageType = MessageType.Error;
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Titulo = "Erro.";
        return Json(new { RetornoAjax = retornoAjax, CustomException = true });
      }
      catch (Exception ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.messageType = MessageType.Error;
        retornoAjax.Mensagem = "Houve um erro no momento de processar sua solicitação.";
        retornoAjax.Titulo = "Erro.";
        return Json(new { RetornoAjax = retornoAjax, CustomException = false });
      }
    }

    [HttpPost]
    public JsonResult IntegraLoteGuia(string NumeroGuia, string NumeroLote, int IdRepasse)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        GuiaNotaFiscalService guiaNotaFiscalService = new GuiaNotaFiscalService(ContextoUsuario.UserLogged);
        guiaNotaFiscalService.IntegraLoteGuia(NumeroGuia, NumeroLote, IdRepasse);
        retornoAjax.Erro = false;
        retornoAjax.messageType = MessageType.Success;
        retornoAjax.Mensagem = "Sucesso.";
        retornoAjax.Titulo = "Sucesso.";
        return Json(retornoAjax);
      }
      catch (CustomException ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.messageType = MessageType.Error;
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Titulo = "Erro.";
        return Json(retornoAjax);
      }
      catch (Exception ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.messageType = MessageType.Error;
        retornoAjax.Mensagem = "Houve um erro no momento de processar sua solicitação.";
        retornoAjax.Titulo = "Erro.";
        return Json(retornoAjax);
      }
    }
  }
}
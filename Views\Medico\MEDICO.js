﻿var Medico;

Medico = function () {
};

Medico.init = function () {
  $(document).on("click", "#IntegrarMedicos", function () {
    var connectionid = $.connection.hub.id;
    $('#bodyModalRetornoUsuario').html('');
    var model = {
      ConnectionId: connectionid
    };

    $.ajax({
      beforeSend: function (xhr) {
      },
      complete: function (xhr, status) {
      },
      type: 'POST',
      url: GetURLBaseComplete() + '/Medico/IntegraMedico',
      data: model,
      success: function (data) {
        AlertaToast(data.Titulo, data.Mensagem, data.Tipo, data.Timer, data.Color)
        if (!data.Erro) {
          //$('#ModalRetornoUsuario').modal("hide");
        }
      },
      error: function (err) {
      }
    });
  });

  $(document).on("click", ".TrSelecionavel", function () {
    var idMedico = $(this).data("codigomedico");
    var urlCartaConversao = GetURLBaseComplete() + "/RegraCartaConversao/Index?CodigoMedico=" + idMedico;
    var urlRateioMedico = GetURLBaseComplete() + "/RateioMedico/Index?cod=" + idMedico;
    var urlExtratoMedico = GetURLBaseComplete() + "/ExtratoMedico/Index/" + idMedico;
    var urlEmpresaMedico = GetURLBaseComplete() + "/EmpresaMedico/Index/" + idMedico;
    var urlDetalhesMedico = GetURLBaseComplete() + "/Medico/Detalhes?IdMedico=" + idMedico;
    var urlItensRecorrentes = GetURLBaseComplete() + "/ItensRecorrentes/Index/" + idMedico;

    if (!$(this).hasClass("Selected")) {
      $('.Selected').each(function (index, element) {
        $(element).removeClass("Selected")
        $(element).addClass("UnSelected")
      });

      if ($(this).hasClass("Selected")) {
        $(this).removeClass("Selected")
        $(this).addClass("UnSelected")
      }
      else {
        $(this).addClass("Selected")
        $(this).removeClass("UnSelected")
      }
    }
    else {
      $(this).removeClass("Selected")
      $(this).addClass("UnSelected")
    }

    var countSelected = 0;
    $('.Selected').each(function (index, element) {
      if ($(element).hasClass("Selected")) {
        $('#CartaConversao').attr("href", urlCartaConversao);
        $('#RateioMedico').attr("href", urlRateioMedico);
        $('#ExtratoMedico').attr("href", urlExtratoMedico);
        $('#EmpresaMedico').attr("href", urlEmpresaMedico);
        $('#DetalhesMedico').attr("href", urlDetalhesMedico);
        $('#ItensRecorrentes').attr("href", urlItensRecorrentes);
        countSelected++;
      }
    });

    if (countSelected == 0) {
      $('#CartaConversao').removeAttr("href");
      $('#RateioMedico').removeAttr("href");
      $('#ExtratoMedico').removeAttr("href");
      $('#EmpresaMedico').removeAttr("href");
      $('#DetalhesMedico').removeAttr("href");
      $('#ItensRecorrentes').removeAttr("href");

      $('#CartaConversao').attr("disabled", true);
      $('#RateioMedico').attr("disabled", true);
      $('#ExtratoMedico').attr("disabled", true);
      $('#EmpresaMedico').attr("disabled", true);
      $('#DetalhesMedico').attr("disabled", true);
      $('#ItensRecorrentes').attr("disabled", true);

      $('#CartaConversao').addClass("disabled");
      $('#RateioMedico').addClass("disabled");
      $('#ExtratoMedico').addClass("disabled");
      $('#EmpresaMedico').addClass("disabled");
      $('#DetalhesMedico').addClass("disabled");
      $('#ItensRecorrentes').addClass("disabled");

    }
    else {
      $('#CartaConversao').attr("disabled", false);
      $('#RateioMedico').attr("disabled", false);
      $('#ExtratoMedico').attr("disabled", false);
      $('#EmpresaMedico').attr("disabled", false);
      $('#DetalhesMedico').attr("disabled", false);
      $('#ItensRecorrentes').attr("disabled", false);

      $('#CartaConversao').removeClass("disabled");
      $('#RateioMedico').removeClass("disabled");
      $('#ExtratoMedico').removeClass("disabled");
      $('#EmpresaMedico').removeClass("disabled");
      $('#DetalhesMedico').removeClass("disabled");
      $('#ItensRecorrentes').removeClass("disabled");
    }
  });

}

$(document).ready(function () {
  Medico.init();
});

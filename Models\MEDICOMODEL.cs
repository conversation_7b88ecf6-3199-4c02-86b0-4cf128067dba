﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using PagedList;
using RepasseConvenio.WSPortalCooperado;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Web;

namespace RepasseConvenio.Models
{
  public class MedicoModel
  {
    public int Codigo { get; set; }
    public string CRM { get; set; }
    public string CPF { get; set; }
    public string Nome { get; set; }
    public string Email { get; set; }
    [DisplayName("Agência")]
    public string AgenciaMedico { get; set; }
    [DisplayName("Banco")]
    public string BancoMedico { get; set; }
    [DisplayName("Conta")]
    public string ContaMedico { get; set; }
    [DisplayName("Valor Pró-labore")]
    public decimal? ValorProLabore { get; set; }
    [DisplayName("Diretor")]
    public bool? Diretor { get; set; }

    public bool Ativo { get; set; }
  }

  public class MedicoIndex
  {
    [DisplayName("Nome")]
    public string Nome { get; set; }
    [DisplayName("CPF")]
    public string CPF { get; set; }
    [DisplayName("CRM")]
    public string CRM { get; set; }
    [DisplayName("E-mail")]
    public string Email { get; set; }
    [DisplayName("Valor Pró-labore")]
    public decimal? ValorProLabore { get; set; }
    public bool? Diretor { get; set; }

    public IPagedList<MedicoModel> ListaMedicoModel { get; set; }
  }

  public class MedicoRegra
  {
    public string CPF { get; set; }
  }

  public static class MedicoModelConversion
  {
    public static R_Medico MedicoRepasseToEntityCreate(this R_Medico medico, MedicoRepasse medicoRepasse)
    {
      bool ativo;

      if (medicoRepasse.statusCooperado == StatusCooperado.Ativo)
        ativo = true;
      else
        ativo = false;

      return new R_Medico()
      {
        M_CRM = medicoRepasse.CRM,
        M_Email = medicoRepasse.Email,
        M_CPF = medicoRepasse.CPF,
        M_Nome = medicoRepasse.Nome,
        M_AgenciaMedico = medicoRepasse.AgenciaMedico,
        M_BancoMedico = medicoRepasse.BancoMedico,
        M_ContaMedico = medicoRepasse.ContaMedico,
        M_IdMedicoExterno = medicoRepasse.Codigo.ToString(),
        M_Ativo = ativo
      };
    }
  }
}
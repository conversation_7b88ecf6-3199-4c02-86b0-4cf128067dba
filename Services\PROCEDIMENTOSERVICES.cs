﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using PagedList;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RepasseConvenio.WSPortalCooperado;
using RepasseConvenio.Infrastructure.Hubs;

namespace RepasseConvenio.Services
{
  public class ProcedimentoServices : ServiceBase
  {
    public ProcedimentoServices(RepasseEntities Contexto)
        : base(Contexto)
    { }
    public ProcedimentoServices()
       : base()
    { }

    public ProcedimentoServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public ProcedimentoServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public List<ProcedimentoIndex> Get(int codConvenio, int page)
    {
      string query = string.Format(@"SELECT 
	                                      P.P_Id [Codigo]
	                                    , P.P_Codigo [CodProcedimento]
	                                    , P.P_Descricao [Descricao]
	                                    , C.C_Id [CodigoConvenio]
	                                    , C.C_CNPJ [CNPJ]
	                                    , C.C_RazaoSocial [RazaoSocial]
                                      , C.C_CodANS [CodANS]
                                    FROM R_Procedimentos P
                                    INNER JOIN R_Convenio C ON P.P_IdConvenio = C.C_Id
                                    WHERE P.P_IdConvenio = @codConvenio
                                    ORDER BY P.P_Descricao
                                    OFFSET (@pag - 1) * 20 ROWS
                                    FETCH NEXT 20 ROWS ONLY");

      return Contexto.Database.SqlQuery<ProcedimentoIndex>(query, new SqlParameter("@codConvenio",codConvenio), new SqlParameter("@pag",page)).ToList();
    }

    //public IPagedList<ProcedimentoModel> GetPagedList(int pageNumber)
    //{
    //  return Contexto.R_Procedimentos.Select(item => new ProcedimentoIndex.FromDatabase(item)).ToPagedList(pageNumber, PageSize);
    //}

    public R_Procedimentos GetById(string Id)
    {
      return Contexto.R_Procedimentos.Where(a => a.P_Codigo == Id).FirstOrDefault();
    }

    public List<R_Procedimentos> GetByTerm(string term)
    {
      if (string.IsNullOrEmpty(term))
      {
        string query = string.Format(@"SELECT 
                                         TOP 15
                                          *
                                       FROM 
                                          R_Procedimentos");

        return Contexto.Database.SqlQuery<R_Procedimentos>(query).ToList();

      }
      else
      {
        string query = string.Format(@"SELECT 
                                          TOP 15
                                          *
                                       FROM 
                                          R_Procedimentos
                                       WHERE P_Codigo LIKE @termo OR P_Descricao LIKE @termo");

        return Contexto.Database.SqlQuery<R_Procedimentos>(query, new SqlParameter("@termo", string.Format("%{0}%", term))).ToList();

      }
    }

    public bool IfExistByCodigoAndConvenio(string codigo, int idConvenioExterno)
    {
      string query = @"SELECT
                        COUNT(1)
                        FROM R_Procedimentos P
                        INNER JOIN R_Convenio C ON C.C_Id = P.P_IdConvenio AND C_IdConvenioExterno = @idConvenioExterno AND P_Codigo = @codigo";
      int quantidade = Contexto.Database.SqlQuery<int>(query
                                                        , new SqlParameter("@codigo", codigo)
                                                        , new SqlParameter("@idConvenioExterno", idConvenioExterno)).FirstOrDefault();
      if (quantidade == 0)
        return false;
      else
        return true;
    }

    public void CreateProcedimentoIntegra(ProcedimentosConvenioRepasse procedimentosConvenioRepasse, int idConvenio)
    {
      R_Procedimentos procedimentos = new R_Procedimentos();
      procedimentos = procedimentos.ProcedimentosConvenioRepasseToEntityCreate(procedimentosConvenioRepasse, idConvenio);
      Create(procedimentos);
    }

    public void IntegraProcedimentos(string connectionId, int idConvenioExterno, int idConvenio, string RazaoSocialConvenio, int ConvenioAtual, int QuantidadeTotalConvenio)
    {
      SendSignal sendSignal = new SendSignal();
      sendSignal.openModalRetornoUsuario(connectionId, "Integrando Procedimentos");
      sendSignal.creatPStrong(connectionId, string.Format("Procurando Procedimentos Convênio: {0}...  {1} de {2}", RazaoSocialConvenio, ConvenioAtual, QuantidadeTotalConvenio));

      IntegraRepasse integraRepasse = new IntegraRepasse();
      List<ProcedimentosConvenioRepasse> ListaProcedimentosConvenioRepasses = integraRepasse.GetProcedimentosConvenios("EE06F933-F27E-4640-AD24-378F64CFF007", idConvenioExterno).ToList();
      sendSignal.creatPSucess(connectionId, string.Format("{0} Procedimentos Encontrados", ListaProcedimentosConvenioRepasses.Count()));
      sendSignal.creatPSucess(connectionId, "Sincronizando... Aguarde");
      int i = 0;
      int z = 0;
      int quantidadePular = ListaProcedimentosConvenioRepasses.Count() / 100;
      int quantidade = 0;
      foreach (ProcedimentosConvenioRepasse procedimentosConvenioRepasse in ListaProcedimentosConvenioRepasses)
      {
        if (i == quantidade)
        {
          quantidade += quantidadePular;
          sendSignal.setPorcentagemProgress(connectionId, (z + 1).ToString());
          z++;
        }

        bool ifExistByCodigoAndConvenio = IfExistByCodigoAndConvenio(procedimentosConvenioRepasse.Codigo, idConvenioExterno);
        if (!ifExistByCodigoAndConvenio)
          CreateProcedimentoIntegra(procedimentosConvenioRepasse, idConvenio);
        i++;
      }
    }
  }
}


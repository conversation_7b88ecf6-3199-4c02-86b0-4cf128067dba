﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Models;
using RepasseConvenio.WSPortalCooperado;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.Entity;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Text;
using System.Transactions;
using System.Web;
using System.Data.OleDb;
using RepasseConvenio.Infrastructure;
using Microsoft.Ajax.Utilities;
using System.Security.Cryptography.Xml;

namespace RepasseConvenio.Services
{
  public class LogGuiaAtendimentoService : ServiceBase
  {
    public LogGuiaAtendimentoService(RepasseEntities Contexto)
       : base(Contexto)
    { }

    public LogGuiaAtendimentoService(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public LogGuiaAtendimentoService(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public void CreateLog(string Descricao)
    {
      R_LogGuiaAtendimento logGuiaAtendimento = new R_LogGuiaAtendimento()
      {
        LGA_Descricao = Descricao,
        LGA_DataCriacao = DateTime.Now,
        LGA_IdUsuarioCriacao = User.IdUsuario
      };

      Create(logGuiaAtendimento);
    }

  }
}
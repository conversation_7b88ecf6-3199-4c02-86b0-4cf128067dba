﻿@using RepasseConvenio.Models
@model List<UsuarioModel>

@{
  ViewBag.Title = "Lista Usuário";
}

<button type="button" value="Create" class="btn btn-primary pull-rigth" onclick="location.href='@Url.Action("Create", "Usuario")'">Novo Usuário</button>
<div class="box-body">
  <div class="row">
    <div class="col-md-12">
      <table class="table">
        <thead>
          <tr>
            <th>
              Nome
            </th>
            <th>
              CPF
            </th>
            <th>
              Tipo Usuário
            </th>
            <th>
              Email
            </th>
            <th>
            </th>
          </tr>
        </thead>
        <tbody>
          @foreach (UsuarioModel item in Model)
          {
            <tr>
              <td>
                @item.Nome
              </td>
              <td>
                @item.CPF
              </td>
              <td>
                @item.TipoUsuario
              </td>
              <td>
                @item.Email
              </td>
              <td>
                <a href="@Url.Action("Edit", "Usuario", new { id = item.Codigo  })" class="btn btn-warning" title="Ver recurso">
                  Editar
                </a>
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>
  </div>
</div>
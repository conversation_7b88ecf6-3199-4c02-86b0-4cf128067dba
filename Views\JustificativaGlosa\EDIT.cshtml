﻿

@using PagedList.Mvc
@using RepasseConvenio.Models
@using RepasseConvenio.Infrastructure
@using RepasseConvenio.Infrastructure.Controls
@model JustificativaGlosaModel

@{
  ViewBag.Title = " Atualizar justificativa de Glosa";
  Layout = "~/Views/Shared/_Layout.cshtml";
}

@using (Html.BeginForm("Edit", "JustificativaGlosa", FormMethod.Post))
{
  @Html.HiddenFor(j=> j.<PERSON>digo)
  <div class="row">
    <section class="col-lg-12 connectedSortable ui-sortable">
      <div class="card">
        <div class="card-header ui-sortable-handle">
          <h3 class="card-title">
            <i class="fas fa-chart-pie mr-1"></i>

          </h3>
          <div class="card-tools">
          </div>
        </div><!-- /.card-header -->
        <div class="card-body">
          <div class="row">
            <div class="col-md-2">
              <div class="form-group">
                @Html.LabelFor(j => j.CodigoJustificativa)
                @Html.LibEditorFor(j => j.CodigoJustificativa, new { @class = "form-control" })
                @Html.ValidationMessageFor(j => j.CodigoJustificativa, "", new { @class = "text-danger" })
              </div>
            </div>

            <div class="col-md-8">
              <div class="form-group">
                @Html.LibLabelFor(j => j.Descricao)
                @Html.LibEditorFor(j => j.Descricao, new { @class = "form-control " })
                @Html.ValidationMessageFor(j => j.Descricao, "", new { @class = "text-danger" })
              </div>
            </div>



          </div>
        </div><!-- /.card-body -->
        <div class="card-footer">
          <button type="button" value="Voltar" class="btn btn-secondary" onclick="window.location.href='@Url.Action("Index","JustificativaGlosa")'">Voltar</button>
          <button type="submit" value="Create" class="btn btn-info pull-rigth">Salvar</button>
        </div>
      </div>
    </section>
  </div>
}
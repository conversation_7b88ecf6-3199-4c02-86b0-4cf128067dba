﻿@using RepasseConvenio.Infrastructure.Controls
@using RepasseConvenio.Models
@model RegraCartaConversaoModel

@{
  ViewBag.Title = "Adicionar Regra Carta Conversão";
  ViewBag.DescricaoTela = "Regra Carta Conversão";
  ViewBag.ResumoTela = "Adicionar Regra Carta Conversão";
  Layout = "~/Views/Shared/_Layout.cshtml";
}

@Scripts.Render("~/Views/RegraCartaConversao/RegraCartaConversao.js?lp=tgb")
@Styles.Render("~/Views/RegraCartaConversao/RegraCartaConversao.css?lp=tgb")

@using (Html.BeginForm("SalvarRegra", "RegraCartaConversao", FormMethod.Post, new { enctype = "multipart/form-data" }))
{
  @Html.HiddenFor(a => a.Codigo)
  @Html.HiddenFor(a => a.CodigoMedico)
  <div class="row">
    <section class="col-lg-12 connectedSortable ui-sortable">
      <div class="card">
        <div class="card-header ui-sortable-handle">
          <h3 class="card-title">
            <i class="fas fa-chart-pie mr-1"></i>
            Nova Regra Carta Conversão
          </h3>
          <div class="card-tools">
            <button type="submit" value="SalvarRegra" name="action" class="btn btn-info pull-right">Salvar</button>
            <button type="submit" value="SalvarValidarRegras" name="action" class="btn btn-info pull-right">Salvar e Ativar Regra</button>
            @*<a value="Create" class="btn btn-info pull-right" href="@Url.Action("SalvarValidarRegras", "RegraCartaConversao", new { idRegra = Model.Codigo  })">Salvar e Ativar Regra</a>*@
          </div>
        </div><!-- /.card-header -->
        <div class="card-body">

          <div class="row">
            <div class="col-md-2">
              <div class="form-group">
                @Html.LabelFor(m => m.Prioridade)
                @Html.TextBoxFor(m => m.Prioridade, new { @class = "form-control numerosOnlyFour" })
              </div>
            </div>
            <div class="col-md-5">
              <div class="form-group">
                @Html.LabelFor(a => a.Descricao)
                @Html.TextBoxFor(a => a.Descricao, new { @class = "form-control " })
              </div>
            </div>
            <div class="col-md-5">
              <div class="form-group">
                @Html.LabelFor(m => m.EmpresaMedicoSelect)
                @Html.LibSelect2For(m => m.EmpresaMedicoSelect, new { @class = "form-control", IdPai = "CodigoMedico" })
              </div>
            </div>
          </div>
        </div><!-- /.card-body -->
      </div>
    </section>
  </div>
}

<div class="row">
  <section class="col-lg-12 connectedSortable ui-sortable">
    <div class="card">
      <div class="card-header ui-sortable-handle">
        <h3 class="card-title">
          <i class="fas fa-chart-pie mr-1"></i>
          Condições
        </h3>
        <div class="card-tools">
        </div>
      </div><!-- /.card-header -->
      <div class="card-body">


        @using (Html.BeginForm("Create", "RegraCartaConversao", FormMethod.Post, new { enctype = "multipart/form-data", id = "FormCreate" }))
        {
          @Html.HiddenFor(a => a.CodigoEmpresaMedico)
          <div class="row" style="align-items: flex-end; border-bottom: 1px solid rgba(0, 0, 0, 0.125);">
            <div class="col-md-3">
              <div class="form-group">
                @Html.LabelFor(m => m.RegraCartaConversaoCampoSelect)
                @Html.LibSelect2For(m => m.RegraCartaConversaoCampoSelect, new { @class = "form-control" })
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                @Html.LabelFor(m => m.RegraCartaConversaoAcaoSelect)
                @Html.LibSelect2For(m => m.RegraCartaConversaoAcaoSelect, new { @class = "form-control", IdPai = "RegraCartaConversaoCampoSelectLookup" })
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                @Html.LabelFor(m => m.Conteudo)
                @Html.TextBoxFor(m => m.Conteudo, new { @class = "form-control", disabled = "true" })
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-group" style="display: flex; justify-content: center; ">
                <div class="btn-group" id="BotaoAdicionar">
                  <button type="button" class="btn btn-info disabled" id="AdicionarCondicao"><i class="fas fa-plus"></i></button>
                </div>

                <div class="btn-group" id="BotoesEditar" style="display: none;">
                  <button type="button" class="btn btn-info disabled AdicionarCondicaoBefore"><i class="fas fa-arrow-left"></i></button>
                  <button type="button" class="btn btn-info disabled EditarCondicao"><i class="fas fa-edit"></i></button>
                  <button type="button" class="btn btn-info disabled AdicionarCondicaoAfter"><i class="fas fa-arrow-right"></i></button>
                </div>
                @*<button type="button" class="btn btn-block btn-outline-primary disabled" id="AdicionarCondicao">Adicionar</button>*@
              </div>
            </div>
          </div>
        }


        <div class="row" style="justify-content: space-between; padding: 5pt; margin-top: 5pt;">
          @using (Html.BeginForm("Create", "RegraCartaConversao", FormMethod.Post, new { enctype = "multipart/form-data", id = "addBefore" }))
          {
            <div class="btn-group">
              <button type="button" class="btn btn-info OpLogicoButtom addBefore" data-enum="@Convert.ToInt32(OperadorLogicoEnum.E)">E</button>
              <button type="button" class="btn btn-info OpLogicoButtom addBefore" data-enum="@Convert.ToInt32(OperadorLogicoEnum.OU)">OU</button>
              <button type="button" class="btn btn-info ParentesesButtom addBefore" data-enum="@Convert.ToInt32(ParentesesEnum.ParentesesEsquerdo)">(</button>
            </div>
          }
          <div>
            <span class="info-box-icon bg-danger RemoverItem" style="padding: 3pt; border-radius: 3pt; cursor:pointer"><i class="fa fa-trash"></i></span>
          </div>
          @using (Html.BeginForm("Create", "RegraCartaConversao", FormMethod.Post, new { enctype = "multipart/form-data", id = "addAfter" }))
          {
            <div class="btn-group">
              <button type="button" class="btn btn-info OpLogicoButtom addAfter" data-enum="@Convert.ToInt32(OperadorLogicoEnum.E)">E</button>
              <button type="button" class="btn btn-info OpLogicoButtom addAfter" data-enum="@Convert.ToInt32(OperadorLogicoEnum.OU)">OU</button>
              <button type="button" class="btn btn-info ParentesesButtom addAfter" data-enum="@Convert.ToInt32(ParentesesEnum.ParentesesDireito)">)</button>
            </div>
          }

        </div>
        <div class="row" id="RegraCompleta">
          <div class="col-md-12">
            <div class="info-box" style="margin-top: 10pt;">
              <div class="info-box-content">
                <div style="display: flex; flex-wrap: wrap;" id="PartialRegra">
                  @Html.Partial("_PartialRegra", Model.ListaRegraCartaConversaoCondicaoModel)
                </div>
              </div>
            </div>
          </div>
        </div>
      </div><!-- /.card-body -->
      <div class="card-footer">
        <a class="btn btn-secondary" href="@Url.Action("Index", "RegraCartaConversao", new { CodigoMedico = Model.CodigoMedico })">Cancelar</a>
      </div>
    </div>
  </section>
</div>



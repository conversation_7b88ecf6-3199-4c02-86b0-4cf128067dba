﻿var TabelaProgressivaINSS = function () {

};



$(document).on("click", ".TrSelecionavel", function () {
  var idJustificativa = $(this).data("codigojustificativa");
  //var idValores = $(this).data("codigovalores");
  var urlEdit = GetURLBaseComplete() + "/JustificativaGlosa/Edit?codigo=" + idJustificativa;
  var urlDelete = GetURLBaseComplete() + "/JustificativaGlosa/Delete?codigo=" + idJustificativa;
  //var urlDeleteValores = GetURLBaseComplete() + "/TabelaProgressivaINSS/DeleteTabela?codigo=" + idValores;



  if (!$(this).hasClass("Selected")) {
    $('.Selected').each(function (index, element) {
      $(element).removeClass("Selected")
      $(element).addClass("UnSelected")
    });

    if ($(this).hasClass("Selected")) {
      $(this).removeClass("Selected")
      $(this).addClass("UnSelected")
    }
    else {
      $(this).addClass("Selected")
      $(this).removeClass("UnSelected")
    }
  }
  else {
    $(this).removeClass("Selected")
    $(this).addClass("UnSelected")
  }

  var countSelected = 0;
  $('.Selected').each(function (index, element) {
    if ($(element).hasClass("Selected")) {
      $('#JustificativaEdit').attr("href", urlEdit);
      $('#JustificativaDelete').attr("href", urlDelete);
      //$('#TabelaDelete').attr("href", urlDeleteValores);
     
      countSelected++;
    }
  });

  if (countSelected == 0) {
    $('#JustificativaEdit').removeAttr("href");
    $('#JustificativaDelete').removeAttr("href");
    //$('#TabelaDelete').removeAttr("href");
   

    $('#JustificativaEdit').attr("disabled", true);
    $('#JustificativaDelete').attr("disabled", true);
    //$('#TabelaDelete').attr("disabled", true);
   

    $('#JustificativaEdit').addClass("disabled");
    $('#JustificativaDelete').addClass("disabled");
    //$('#TabelaDelete').addClass("disabled");
   
  }
  else {
    $('#JustificativaEdit').attr("disabled", false);
    $('#JustificativaDelete').attr("disabled", false);
    //$('#TabelaDelete').attr("disabled", false);
   

    $('#JustificativaEdit').removeClass("disabled");
    $('#JustificativaDelete').removeClass("disabled");
    //$('#TabelaDelete').removeClass("disabled");
   
  }
});


$(document).ready(function () {
  TabelaProgressivaINSS.init();
});
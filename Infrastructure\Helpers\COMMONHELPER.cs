﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Net;
using System.Net.Mail;
using System.Threading;
using System.Configuration;
using System.IO;
using System.Xml.Serialization;
using System.Xml;

namespace RepasseConvenio.Domain.Infrastructure.Helpers
{
  public static class CommonHelper
  {
    public static string GeneratePassword(int min, int max)
    {
      string pass = String.Empty;

      return pass;
    }

    public static string GeneratePassword(int tamanho, bool contemNumeros = false, bool contemCaracterEspecial = false, bool contemMaiusculas = false)
    {
      string senha = "";
      return senha;
    }

    public static void WriteErrorLogGuias(string message)
    {
    }

    public static void WriteErrorLogServicos(string message)
    {
    }

    public static void WriteLogImportacaoPlanilhaRepasse(string message)
    {
    }

    public static void WriteLogImportacaoLotesRepasse(string message)
    {
    }

    public static void WriteLogProcessamentoRepasse(string message)
    {
    }

    public static string ObjMensagemTISSToXML(object obj, Encoding encoding)
    {
      return "";
    }
  }
}
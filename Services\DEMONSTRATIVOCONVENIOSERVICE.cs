﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Models;
using RepasseConvenio.WSPortalCooperado;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.Entity;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Text;
using System.Transactions;
using System.Web;
using System.Data.OleDb;
using RepasseConvenio.Infrastructure;
using Microsoft.Ajax.Utilities;
using System.Security.Cryptography.Xml;

namespace RepasseConvenio.Services
{
  public class DemonstrativoConvenioService : ServiceBase
  {
    public DemonstrativoConvenioService()
   : base()
    { }
    public DemonstrativoConvenioService(RepasseEntities Contexto)
       : base(Contexto)
    { }

    public DemonstrativoConvenioService(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public DemonstrativoConvenioService(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public R_DemonstrativoConvenio Create(DemonstrativoConvenioCreate demonstrativoConvenioCreate)
    {
      StatusDemonstrativoConvenioServices statusDemonstrativoConvenioServices = new StatusDemonstrativoConvenioServices();
      int IdStatusDemonstrativoNaoProcessado = statusDemonstrativoConvenioServices.GetIdByEnum(StatusDemonstrativoEnum.NaoProcessado);

      R_DemonstrativoConvenio demonstrativoConvenio = demonstrativoConvenioCreate.ModelToEntityCreate(User.IdUsuario, IdStatusDemonstrativoNaoProcessado);
      Create(demonstrativoConvenio);

      return demonstrativoConvenio;
    }

    public void UpdateValores(int IdDemonstrativo)
    {
      string query = @"SELECT 
                        ISNULL(SUM(GD_TotalFaturado), 0) [TotalFaturado],
                        ISNULL(SUM(GD_TotalPago), 0) [TotalPago],
                        ISNULL(SUM(GD_TotalApresentado), 0) [TotalApresentado],
                        ISNULL(SUM(GD_TotalGlosado), 0) [TotalGlosado]
                       FROM R_GuiaDemonstrativo
					             WHERE GD_IdDemonstrativoConvenio = @IdDemonstrativo";

      DemonstrativoUpdateValores demonstrativoUpdateValores = Contexto.Database.SqlQuery<DemonstrativoUpdateValores>(query, new SqlParameter("@IdDemonstrativo", IdDemonstrativo)).FirstOrDefault();

      string update = @"UPDATE DC SET
                        DC.DC_TotalFaturado = @TotalFaturado,
                        DC.DC_TotalPago = @TotalPago,
                        DC.DC_TotalApresentado = @TotalApresentado,
                        DC.DC_TotalGlosado = @TotalGlosado
                       FROM R_DemonstrativoConvenio DC
                       where DC.DC_Id = @IdDemonstrativo";

      Contexto.Database.ExecuteSqlCommand(update, new SqlParameter("@TotalFaturado", demonstrativoUpdateValores.TotalFaturado)
                                                , new SqlParameter("@TotalPago", demonstrativoUpdateValores.TotalPago)
                                                , new SqlParameter("@TotalApresentado", demonstrativoUpdateValores.TotalApresentado)
                                                , new SqlParameter("@TotalGlosado", demonstrativoUpdateValores.TotalGlosado)
                                                , new SqlParameter("@IdDemonstrativo", IdDemonstrativo));
    }

    public List<DemonstrativoConvenioGrid> GetListaDemonstrativoConvenio(int IdRepasse)
    {
      string query = @"SELECT 
                        DC.DC_Id [Codigo],
                        DC_NomePlanilha [NomePlanilha],
                        DC.DC_TotalFaturado [TotalFaturado],
                        DC.DC_TotalApresentado [ValorApresentado],
                        DC.DC_TotalGlosado [TotalGlosado],
                        DC.DC_TotalPago [TotalPago],
                        ISNULL(DC.DC_Processado, Convert(Bit, 0)) [Processado],
                        DC.DC_IdRepasse [CodigoRepasse]
                       FROM R_DemonstrativoConvenio DC
                       WHERE DC.DC_IdRepasse = @IdRepasse";

      return Contexto.Database.SqlQuery<DemonstrativoConvenioGrid>(query, new SqlParameter("@IdRepasse", IdRepasse)).ToList();
    }

    public R_DemonstrativoConvenio GetById(int IdDemonstrativo)
    {
      string query = @"SELECT
                        *
                       FROM R_DemonstrativoConvenio
                       WHERE DC_Id = @IdDemonstrativo";

      return Contexto.Database.SqlQuery<R_DemonstrativoConvenio>(query, new SqlParameter("@IdDemonstrativo", IdDemonstrativo)).FirstOrDefault();
    }

    public List<R_DemonstrativoConvenio> GetListaDemonstrativoConvenio(int IdRepasse, bool Processado)
    {
      string where = string.Empty;
      if (Processado)
        where = "WHERE DC_IdRepasse = @IdRepasse AND DC_Processado = @Processado";
      else
        where = "WHERE DC_IdRepasse = @IdRepasse AND (DC_Processado = @Processado OR DC_Processado IS NULL)";

      string query = @"SELECT
                        *
                       FROM R_DemonstrativoConvenio
                       #Where#";

      query = query.Replace("#Where#", where);

      return Contexto.Database.SqlQuery<R_DemonstrativoConvenio>(query, new SqlParameter("@IdRepasse", IdRepasse)
                                                                      , new SqlParameter("@Processado", Processado)).ToList();
    }

    public List<R_DemonstrativoConvenio> GetListaRDemonstrativoConvenio(int IdRepasse)
    {
      string query = @"SELECT
                        *
                       FROM R_DemonstrativoConvenio
                      WHERE DC_IdRepasse = @IdRepasse";

      return Contexto.Database.SqlQuery<R_DemonstrativoConvenio>(query, new SqlParameter("@IdRepasse", IdRepasse)).ToList();
    }

    public void ProcessaDemonstrativo(R_DemonstrativoConvenio demonstrativoConvenio)
    {
      GuiaDemonstrativoService guiaDemonstrativoService = new GuiaDemonstrativoService();
      StatusGuiaDemonstrativoConvenioServices statusGuiaDemonstrativoConvenioServices = new StatusGuiaDemonstrativoConvenioServices();
      StatusDemonstrativoConvenioServices statusDemonstrativoConvenioServices = new StatusDemonstrativoConvenioServices();

      List<R_GuiaDemonstrativo> ListGuiaDemonstrativo = guiaDemonstrativoService.GetListGuiaDemonstrativo(demonstrativoConvenio.DC_Id);

      List<R_StatusGuiaDemonstrativoConvenio> ListaStatusGuia = statusGuiaDemonstrativoConvenioServices.GetAll();
      int IdStatusGuiaNaoProcessado = ListaStatusGuia.Where(a => a.SGDC_Enum == (int)StatusGuiaDemonstrativoEnum.NaoProcessado).Select(a => a.SGDC_Id).FirstOrDefault();
      int IdStatusGuiaProcessado = ListaStatusGuia.Where(a => a.SGDC_Enum == (int)StatusGuiaDemonstrativoEnum.Processado).Select(a => a.SGDC_Id).FirstOrDefault();

      List<R_StatusDemonstrativoConvenio> ListaStatusDemonstrativo = statusDemonstrativoConvenioServices.GetAll();
      int IdStatusDemonstrativoNaoProcessado = ListaStatusDemonstrativo.Where(a => a.SDC_Enum == (int)StatusDemonstrativoEnum.NaoProcessado).Select(a => a.SDC_Id).FirstOrDefault();
      int IdStatusDemonstrativoProcessado = ListaStatusDemonstrativo.Where(a => a.SDC_Enum == (int)StatusDemonstrativoEnum.Processado).Select(a => a.SDC_Id).FirstOrDefault();
      int IdStatusDemonstrativoProcessadoParcialmente = ListaStatusDemonstrativo.Where(a => a.SDC_Enum == (int)StatusDemonstrativoEnum.ProcessadoParcialmente).Select(a => a.SDC_Id).FirstOrDefault();


      demonstrativoConvenio.DC_Processado = false;
      if (ListGuiaDemonstrativo.Where(a => a.DC_IdStatusGuiaDemonstrativoConvenio == IdStatusGuiaNaoProcessado).Count() == ListGuiaDemonstrativo.Count())
      {
        demonstrativoConvenio.DC_IdStatusDemonstrativoConvenio = IdStatusDemonstrativoNaoProcessado;
        demonstrativoConvenio.DC_InfoProcessamento = "Não processado";
      }
      else if (ListGuiaDemonstrativo.Where(a => a.DC_IdStatusGuiaDemonstrativoConvenio == IdStatusGuiaProcessado).Count() != ListGuiaDemonstrativo.Count()
            && ListGuiaDemonstrativo.Where(a => a.DC_IdStatusGuiaDemonstrativoConvenio == IdStatusGuiaProcessado).Count() > 0)
      {
        demonstrativoConvenio.DC_IdStatusDemonstrativoConvenio = IdStatusDemonstrativoProcessadoParcialmente;
        demonstrativoConvenio.DC_InfoProcessamento = "Processado Parcialmente";
      }
      else
      {
        demonstrativoConvenio.DC_IdStatusDemonstrativoConvenio = IdStatusDemonstrativoProcessado;
        demonstrativoConvenio.DC_Processado = true;
        demonstrativoConvenio.DC_InfoProcessamento = "Processado";
      }

      Edit(demonstrativoConvenio);
    }

    public void Delete(List<int> idItensToDelete, int IdRepasse)
    {
      StatusRepasseServices statusRepasseServices = new StatusRepasseServices();
      StatusItensLoteServices statusItensLoteServices = new StatusItensLoteServices();
      GuiaDemonstrativoService guiaDemonstrativoService = new GuiaDemonstrativoService(Contexto);
      GuiaAtendimentoService guiaAtendimentoService = new GuiaAtendimentoService(Contexto);
      LogGuiaAtendimentoService logGuiaAtendimentoService = new LogGuiaAtendimentoService(User);

      int IdStatusRepasseEmConferencia = statusRepasseServices.GetIdByEnum(EnumStatusRepasse.EMCONFERENCIA);

      RepasseService repasseService = new RepasseService(Contexto);
      R_Repasse repasse = repasseService.GetById(IdRepasse);

      if (repasse.R_IdStatusRepasse != IdStatusRepasseEmConferencia)
        throw new CustomException("Só pode excluir um demonstrativo se o repasse estiver em conferência");

      int IdStatusItensLoteNaoPago = statusItensLoteServices.GetIdByEnum(StatusItensLoteEnum.NaoPago);
      int IdStatusItensPago = statusItensLoteServices.GetIdByEnum(StatusItensLoteEnum.Pago);
      int IdStatusItensGlosado = statusItensLoteServices.GetIdByEnum(StatusItensLoteEnum.Glosado);
      foreach (var IdDemonstrativo in idItensToDelete)
      {
        using (TransactionScope scope = new TransactionScope(TransactionScopeOption.RequiresNew))
        {
          LoteServices loteServices = new LoteServices();

          List<GuiaDemonstrativoDelete> listaGuiaDemonstrativo = guiaDemonstrativoService.GetListIdGuiaAtendimento(IdDemonstrativo);
          string IdsGuiaAtendimento = string.Join(",", listaGuiaDemonstrativo.Select(a => a.IdGuiaAtendimento).ToArray());
          //string updateItensLote = @"UPDATE 
          //                            R_ItensLote 
          //                           SET IL_IdStatusItensLote = @IdStatusItensLoteNaoPago 
          //                           WHERE IL_IdGuiaAtendimento in (#Ids#)
          //                           AND (IL_IdStatusItensLote = @IdStatusItensPago OR IL_IdStatusItensLote = @IdStatusItensGlosado)";
          //updateItensLote = updateItensLote.Replace("#Ids#", IdsGuiaAtendimento);
          //Contexto.Database.ExecuteSqlCommand(updateItensLote, new SqlParameter("@IdStatusItensLoteNaoPago", IdStatusItensLoteNaoPago)
          //                                                   , new SqlParameter("@IdStatusItensPago", IdStatusItensPago)
          //                                                   , new SqlParameter("@IdStatusItensGlosado", IdStatusItensGlosado));

          List<int> ListIdGuiaDemonstrativo = guiaDemonstrativoService.GetAllIdGuiaDemonstrativo(IdDemonstrativo);
          string DeleteProcGuiaDemonstrativo = @"delete from R_ProcGuiaDemonstrativo where PGD_IdGuiaDemonstrativo in (#Ids#)";
          string IdsGuiaDemonstrativo = string.Join(",", ListIdGuiaDemonstrativo.ToArray());
          DeleteProcGuiaDemonstrativo = DeleteProcGuiaDemonstrativo.Replace("#Ids#", IdsGuiaDemonstrativo);
          Contexto.Database.ExecuteSqlCommand(DeleteProcGuiaDemonstrativo);

          string DeleteGuiaDemonstrativo = @"DELETE FROM R_GuiaDemonstrativo WHERE GD_Id IN (#Ids#)";
          DeleteGuiaDemonstrativo = DeleteGuiaDemonstrativo.Replace("#Ids#", IdsGuiaDemonstrativo);
          Contexto.Database.ExecuteSqlCommand(DeleteGuiaDemonstrativo);

          loteServices.AtualizarLoteParcialmente(IdDemonstrativo, listaGuiaDemonstrativo.Select(a => a.IdGuiaAtendimento).ToList());

          string DeleteDemonstrativo = @"DELETE FROM R_DemonstrativoConvenio WHERE DC_Id = @IdDemonstrativo";
          Contexto.Database.ExecuteSqlCommand(DeleteDemonstrativo, new SqlParameter("@IdDemonstrativo", IdDemonstrativo));


          if (GetQuantidadeByIdRepasse(IdRepasse) == 0)
          {
            repasse.R_Valorizacao = false;
            Edit(repasse);
          }

          foreach (var item in listaGuiaDemonstrativo.Select(a => a.IdGuiaAtendimento).ToArray())
          {
            GuiaDemonstrativoDelete guiaDemonstrativoDelete = guiaDemonstrativoService.GetGuiaDemonstrativoDelete(item);
            if (guiaDemonstrativoDelete != null)
              guiaAtendimentoService.UpdateValoresGuiaAtendimentoOnDelete(guiaDemonstrativoDelete.TotalPago, item);
            else
              guiaAtendimentoService.UpdateValoresGuiaAtendimento(0, 0, item);
          }

          foreach (var item in listaGuiaDemonstrativo)
          {
            logGuiaAtendimentoService.CreateLog(string.Format("Guia de número {0} foi removida do repasse {1} e lote da guia atualizado para não pago.", item.NumeroGuia, IdRepasse));
          }
          scope.Complete();
        }
      }
    }

    public List<int> GetListIdDemonstrativoByRepasse(int IdRepasse)
    {
      string query = @"SELECT
                        DC_Id
                       FROM R_DemonstrativoConvenio
                       WHERE DC_IdRepasse = @IdRepasse";

      return Contexto.Database.SqlQuery<int>(query, new SqlParameter("@IdRepasse", IdRepasse)).ToList();
    }

    public int GetQuantidadeByIdRepasse(int IdRepasse)
    {
      string query = @"select count(1) from R_DemonstrativoConvenio where DC_IdRepasse = @IdRepasse";
      return Contexto.Database.SqlQuery<int>(query, new SqlParameter("@IdRepasse", IdRepasse)).FirstOrDefault();
    }
  }
}
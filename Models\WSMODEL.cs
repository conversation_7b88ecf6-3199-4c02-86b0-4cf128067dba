﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.WSPortalCooperado;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Web;

namespace RepasseConvenio.Models
{
  public class BuscaLote
  {
    public int? BL_NumLote { get; set; }
    public DateTime? BL_DataInicio { get; set; }
    public DateTime? BL_DataFim { get; set; }
    public int BL_IdConvenio { get; set; }
    public int BL_IdRepasse { get; set; }
  }
  public class LoteGlosaVencendo
  {
    public string Codigo { get; set; }
    public string NomeConvenio { get; set; }
    public string Status { get; set; }
    public string Tipo { get; set; }
    public DateTime DataCriacao { get; set; }
    public DateTime DataVencimento { get; set; }
  }
  public class LotePortalModel
  {
    public LotePortalModel()
    {
      this.BLM_Inserir = false;
    }
    [DisplayName("Número Lote")]
    public int BLM_NumLote { get; set; }

    [DisplayName("Data")]
    public DateTime BLM_Data { get; set; }

    [DisplayName("Valor")]
    public decimal BLM_Valor { get; set; }

    [DisplayName("Inserir")]
    public bool BLM_Inserir { get; set; }
    public int BLM_IdRepasse { get; set; }
  }
  public class WSEntrada
  {
    public string Hash { get; set; }
  }
  public class WSRetorno
  {
    public WSRetorno(bool error = false, string message = "", dynamic data = null)
    {
      this.Error = error;
      this.Message = message;
      this.Data = data;
    }

    public bool Error { get; set; }
    public string Message { get; set; }
    public dynamic Data { get; set; }
  }
  public static class WSRepasseModelConversions
  {
    public static LotePortalModel ToLoteBasicoWSModel(this LoteBasicoWS lote, int idRepasse)
    {
      if (lote == null)
        return null;

      return new LotePortalModel()
      {
        BLM_NumLote = lote.NumeroLote,
        BLM_Data = lote.DataEmissao,
        BLM_Valor = lote.Valor,
        BLM_IdRepasse = idRepasse
      };
    }
    public static BuscaLoteRepasse ToBuscaLoteRepasseModel(this BuscaLote lote, string cnpj)
    {
      if (lote == null)
        return null;

      return new BuscaLoteRepasse()
      {
        CNPJConvenio = cnpj,
        DataAte = lote.BL_DataInicio,
        DataDe = lote.BL_DataFim,
        NumeroLote = lote.BL_NumLote
      };
    }
    //public static R_PlanilhaRecebimento ToR_PlanilhaRecebimento(this GuiaRepasseWS guia, int IdRepasse, int IdUsuario)
    //{
    //  if (guia == null)
    //    return null;

    //  R_PlanilhaRecebimento planilha = new R_PlanilhaRecebimento();
    //  planilha.PR_IdRepasse = IdRepasse;
    //  planilha.PR_IdUsuarioCriacao = IdUsuario;
    //  planilha.PR_Nome = "";
    //  planilha.PR_NumeroGuia = guia.NroUnicooper;
    //  planilha.PR_TotalFaturado = guia.VlrTotal.HasValue ? guia.VlrTotal.Value : 0;
    //  planilha.PR_DataCriacao = DateTime.Now;
    //  planilha.PR_NumAtendimento = guia.AtendimentoHospital;
    //  planilha.PR_CarteiraBeneficiario = guia.CodCarteiraPaciente;
    //  planilha.PR_DataAtendimento = guia.DataAtendimento;

    //  return planilha;
    //}
    //public static R_PlanilhaRecebimento ToR_PlanilhaRecebimentoEditByIntegracaoLote(this GuiaRepasseWS guia, R_PlanilhaRecebimento planEntity, int IdUsuario)
    //{
    //  if (guia == null)
    //    return null;

    //  planEntity.PR_NumeroGuia = guia.NroUnicooper;
    //  planEntity.RP_IdUsuarioAlteracao = IdUsuario;
    //  planEntity.RP_DataAlteracao = DateTime.Now;
    //  planEntity.PR_TotalFaturado = guia.VlrTotal.HasValue ? guia.VlrTotal.Value : 0;

    //  return planEntity;
    //}

    public static R_GuiaAtendimento ToR_GuiaAtendimento(this GuiaRepasseWS guia, int IdConvenio)
    {
      if (guia == null)
        return null;

      R_GuiaAtendimento entity = new R_GuiaAtendimento();

      entity.GA_IdConvenio = IdConvenio;
      entity.GA_CodGuiaPrestador = guia.CodGuiaPrestador;
      entity.GA_DtInicioFaturamento = guia.DtInicioFaturamento;
      entity.GA_DtFimFaturamento = guia.DtFimFaturamento;
      entity.GA_DtEmissao = guia.DtEmissao;
      entity.GA_CodGuiaPrincipal = guia.CodGuiaPrincipal;
      entity.GA_DtAutorizacaoGuia = guia.DtAutorizacaoGuia;
      entity.GA_CodSenhaGuia = guia.CodSenhaGuia;
      entity.GA_DtValidadeSenha = guia.DtValidadeSenha;
      entity.GA_CodGuiaOperadora = guia.CodGuiaOperadora;
      entity.GA_CodCarteiraPaciente = guia.CodCarteiraPaciente;
      entity.GA_DtValidadeCarteira = guia.DtValidadeCarteira;
      entity.GA_NomePaciente = guia.NomePaciente;
      entity.GA_CodCartaoNacSaude = guia.CodCartaoNacSaude;
      entity.GA_IdentRN = guia.IdentRN;
      entity.GA_NomeSolicitante = guia.NomeSolicitante;
      entity.GA_ConselhoSolicitante = guia.ConselhoSolicitante;
      entity.GA_CodNumConselho = guia.CodNumConselho;
      entity.GA_CodUF = guia.CodUF;
      entity.GA_CodCBO = guia.CodCBO;
      entity.GA_CaraterAtendimento = guia.CaraterAtendimento;
      entity.GA_DtSolicitacao = guia.DtSolicitacao;
      entity.GA_DescIndClinica = guia.DescIndClinica;
      entity.GA_CodigoProcSolicitado = guia.CodigoProcSolicitado;
      entity.GA_QtdeSolicitado = guia.QtdeSolicitado;
      entity.GA_QtdeAutorizada = guia.QtdeAutorizada;
      entity.GA_CodPrestContratado = guia.CodPrestContratado;
      entity.GA_NomePrestador = guia.NomePrestador;
      entity.GA_CodNCNES = guia.CodNCNES;
      entity.GA_DescTipoAtendimento = guia.DescTipoAtendimento;
      entity.GA_DescIndAcidente = guia.DescIndAcidente;
      entity.GA_DescTipoConsulta = guia.DescTipoConsulta;
      entity.GA_MotEnceramento = guia.MotEnceramento;
      entity.GA_ObsJustificativa = guia.ObsJustificativa;
      entity.GA_VlrTotal = guia.VlrTotal;
      entity.GA_VlrGlosa = guia.VlrGlosa;
      entity.GA_DescTipoSaidaGuiaConsulta = guia.DescTipoSaidaGuiaConsulta;
      entity.GA_NroUnicooper = guia.NroUnicooper;
      entity.GA_TipoGuia = guia.TipoGuia;
      entity.GA_Complemento = guia.Complemento;
      //entity.GA_DescPlano = guia.DescPlano;
      entity.GA_DataInternacao = guia.DataInternacao;
      entity.GA_DataAlta = guia.DataAlta;
      entity.GA_SolicitacaoInternacao = guia.SolicitacaoInternacao;
      entity.GA_DataEntrega = guia.DataEntrega;
      entity.GA_AtendimentoHospital = guia.AtendimentoHospital;
      entity.GA_DescTipoAcomodacao = guia.DescTipoAcomodacao;
      entity.GA_OrigemGuia = guia.OrigemGuia;
      entity.GA_DataAtendimento = guia.DataAtendimento;
      entity.GA_CNPJFaturamento = guia.CNPJFaturamento;

      return entity;
    }
    public static R_ProcGuiaAtendimento ToR_Procedimentos(this ProcedimentoRepasseWS procWS, int IdGuiaAtendimento)
    {
      if (procWS == null)
        return null;

      R_ProcGuiaAtendimento entity = new R_ProcGuiaAtendimento();

      entity.PGA_IdGuiaAtendimento = IdGuiaAtendimento;
      entity.PGA_SeqProcedimento = procWS.SeqProcedimento;
      entity.PGA_DtRealizacao = procWS.DtRealizacao;
      entity.PGA_HrIniRealizacao = procWS.HrIniRealizacao;
      entity.PGA_HrFimRealizacao = procWS.HrFimRealizacao;
      entity.PGA_CodProcedimento = procWS.CodProcedimento;
      entity.PGA_DescProcedimento = procWS.DescProcedimento;
      entity.PGA_QtdeRealizada = procWS.QtdeRealizada;
      entity.PGA_CodViaAcesso = procWS.CodViaAcesso;
      entity.PGA_CodTecUtilizada = procWS.CodTecUtilizada;
      entity.PGA_PercFator = procWS.PercFator;
      entity.PGA_VlrUnit = procWS.VlrUnit;
      entity.PGA_VlrTotal = procWS.VlrTotal;
      entity.PGA_DescTipoProcedimento = procWS.DescTipoProcedimento;
      entity.PGA_CodTabANS = procWS.CodTabANS;
      entity.PGA_PercFatorFaturamento = procWS.PercFatorFaturamento;
      entity.PGA_Urgencia = procWS.Urgencia;

      return entity;
    }
    public static R_PartGuiaAtendimento ToR_PartGuiaAtendimento(this ParticipacaoGuiaRepasseWS partWS, int IdGuiaAtendimento, int IdMedico)
    {
      if (partWS == null)
        return null;

      R_PartGuiaAtendimento entity = new R_PartGuiaAtendimento();

      entity.PGA_IdGuiaAtendimento = IdGuiaAtendimento;
      entity.PGA_SeqProcedimento = partWS.SeqProcedimento;
      entity.PGA_CodGrauPart = partWS.CodGrauPart;
      entity.PGA_IdMedico = IdMedico;
      entity.PGA_CodCBO = partWS.CodCBO;
      entity.PGA_DtRealizacaoProcSerie = partWS.DtRealizacaoProcSerie;

      return entity;
    }

    public static R_Medico ToR_Medico(this MedicoRepasse medico)
    {
      if (medico == null)
        return null;

      R_Medico entity = new R_Medico();

      entity.M_CRM = medico.CRM;
      entity.M_CPF = medico.CPF;
      entity.M_Nome = medico.Nome;
      entity.M_Email = medico.Email;
      entity.M_AgenciaMedico = medico.AgenciaMedico;
      entity.M_BancoMedico = medico.BancoMedico;
      entity.M_ContaMedico = medico.ContaMedico;

      return entity;
    }


    //public static R_DetalhePlanilhaRecebimento ToR_DetalhePlanilhaRecebimento(this ProcedimentoRepasseWS procedimento,int IdPlanilha)
    //{
    //  if (procedimento == null)
    //    return null;

    //  R_DetalhePlanilhaRecebimento detalhes = new R_DetalhePlanilhaRecebimento();
    //  detalhes.DP_IdPlanilhaRecebimento = IdPlanilha;
    //  detalhes.DP_CodigoProcedimento = procedimento.Codigo;
    //  detalhes.DP_DescricaoProcedimento = procedimento.Descricao;
    //  detalhes.DP_ValorProcedimento = procedimento.Valor;

    //  return detalhes;
    //}
  }
}
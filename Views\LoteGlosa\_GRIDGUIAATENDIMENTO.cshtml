﻿@using RepasseConvenio.Models
@model GuiaAtendimentoGrid


<div class="container" style="background-color: #cbd0d2 !important; width: 100%; max-width: 100%;">
  <div class="row" style="height: 30pt;">
    <div class="col-md-3">
      <div class="form-group formGuiaAtendimento">
        @Html.LabelFor(model => model.Hospital)
        @Html.EditorFor(model => model.Hospital, new { htmlAttributes = new { @class = "form-control", disabled = true } })
      </div>
    </div>

    <div class="col-md-3">
      <div class="form-group formGuiaAtendimento">
        @Html.LabelFor(model => model.Convenio)
        @Html.EditorFor(model => model.Convenio, new { htmlAttributes = new { @class = "form-control", disabled = true } })
      </div>
    </div>

    <div class="col-md-3">
      <div class="form-group formGuiaAtendimento">
        @Html.LabelFor(model => model.AtendimentoHospital)
        @Html.EditorFor(model => model.AtendimentoHospital, new { htmlAttributes = new { @class = "form-control", disabled = true } })
      </div>
    </div>

    <div class="col-md-3">
      <div class="form-group formGuiaAtendimento">
        @Html.LabelFor(model => model.GuiaUnicooper)
        @Html.EditorFor(model => model.GuiaUnicooper, new { htmlAttributes = new { @class = "form-control", disabled = true } })
      </div>
    </div>
  </div>

  <div class="row" style=" height: 30pt;">
    <div class="col-md-3">
      <div class="form-group formGuiaAtendimento">
        @Html.LabelFor(model => model.Complemento)
        @Html.EditorFor(model => model.Complemento, new { htmlAttributes = new { @class = "form-control", disabled = true } })
      </div>
    </div>

    <div class="col-md-3">
      <div class="form-group formGuiaAtendimento">
        @Html.LabelFor(model => model.Plano)
        @Html.EditorFor(model => model.Plano, new { htmlAttributes = new { @class = "form-control", disabled = true } })
      </div>
    </div>

    <div class="col-md-3">
      <div class="form-group formGuiaAtendimento">
        @Html.LabelFor(model => model.DataAutorizacao)
        @Html.EditorFor(model => model.DataAutorizacao, new { htmlAttributes = new { @class = "form-control Data", disabled = true } })
      </div>
    </div>


    <div class="col-md-3">
      <div class="form-group formGuiaAtendimento">
        @Html.LabelFor(model => model.DataEntrega)
        @Html.EditorFor(model => model.DataEntrega, new { htmlAttributes = new { @class = "form-control Data", disabled = true } })
      </div>
    </div>


  </div>

  <div class="row" style=" height: 30pt;">
    <div class="col-md-3">
      <div class="form-group formGuiaAtendimento">
        @Html.LabelFor(model => model.Guia)
        @Html.EditorFor(model => model.Guia, new { htmlAttributes = new { @class = "form-control", disabled = true } })
      </div>
    </div>

    <div class="col-md-3">
      <div class="form-group formGuiaAtendimento">
        @Html.LabelFor(model => model.GuiaPrincipal)
        @Html.EditorFor(model => model.GuiaPrincipal, new { htmlAttributes = new { @class = "form-control", disabled = true } })
      </div>
    </div>

    <div class="col-md-3">
      <div class="form-group formGuiaAtendimento">
        @Html.LabelFor(model => model.SolicitacaoInternacao)
        @Html.EditorFor(model => model.SolicitacaoInternacao, new { htmlAttributes = new { @class = "form-control", disabled = true } })
      </div>
    </div>

    <div class="col-md-3">
      <div class="form-group formGuiaAtendimento">
        @Html.LabelFor(model => model.Senha)
        @Html.EditorFor(model => model.Senha, new { htmlAttributes = new { @class = "form-control", disabled = true } })
      </div>
    </div>
  </div>

  <div class="row" style=" height: 30pt;">
    <div class="col-md-8">
      <div class="form-group formGuiaAtendimento">
        @Html.LabelFor(model => model.NomePaciente)
        @Html.EditorFor(model => model.NomePaciente, new { htmlAttributes = new { @class = "form-control", disabled = true } })
      </div>
    </div>

    <div class="col-md-4">
      <div class="form-group formGuiaAtendimento">
        @Html.LabelFor(model => model.CarteirinhaPaciente)
        @Html.EditorFor(model => model.CarteirinhaPaciente, new { htmlAttributes = new { @class = "form-control", disabled = true } })
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-md-2">
      <div class="form-group formGuiaAtendimento">
        @Html.LabelFor(model => model.DataInicioFaturamento)
        @Html.EditorFor(model => model.DataInicioFaturamento, new { htmlAttributes = new { @class = "form-control Data", disabled = true } })
      </div>
    </div>

    <div class="col-md-2">
      <div class="form-group formGuiaAtendimento">
        @Html.LabelFor(model => model.DataFimFaturamento)
        @Html.EditorFor(model => model.DataFimFaturamento, new { htmlAttributes = new { @class = "form-control Data", disabled = true } })
      </div>
    </div>

    <div class="col-md-2">
      <div class="form-group formGuiaAtendimento">
        @Html.LabelFor(model => model.Acomodacao)
        @Html.EditorFor(model => model.Acomodacao, new { htmlAttributes = new { @class = "form-control", disabled = true } })
      </div>
    </div>

    <div class="col-md-2">
      <div class="form-group formGuiaAtendimento">
        @Html.LabelFor(model => model.TipoAtendimento)
        @Html.EditorFor(model => model.TipoAtendimento, new { htmlAttributes = new { @class = "form-control", disabled = true } })
      </div>
    </div>

    <div class="col-md-2">
      <div class="form-group formGuiaAtendimento">
        @Html.LabelFor(model => model.DataInternacao)
        @Html.EditorFor(model => model.DataInternacao, new { htmlAttributes = new { @class = "form-control Data", disabled = true } })
      </div>
    </div>

    <div class="col-md-2">
      <div class="form-group formGuiaAtendimento">
        @Html.LabelFor(model => model.DataAlta)
        @Html.EditorFor(model => model.DataAlta, new { htmlAttributes = new { @class = "form-control Data", disabled = true } })
      </div>
    </div>
  </div>

  <div class="row formGuiaAtendimento">
    <div class="col-md-12 table-responsive p-0 ">
      <table id="ProcedimentosGuia" class="table table-responsive">
        <thead>
          <tr>
            <th></th>
            <th>Data</th>
            <th>Hr Ini</th>
            <th>Hr Fim</th>
            <th>Urgência</th>
            <th>Procedimento</th>
            <th>Descrição</th>
            <th>Qtde</th>
            <th>Tipo Procedimento</th>
            <th>Via</th>
            <th>Tec</th>
            <th>%</th>
            <th>Valor</th>
          </tr>
        </thead>

        <tbody>
          @for (int i = 0; i < Model.ListaProcGuiaAtendimentoGrid.Count(); i++)
          {
            <tr>
              <td>
                @(i + 1)
              </td>
              <td style=" width: 65pt;">
                @Html.EditorFor(model => Model.ListaProcGuiaAtendimentoGrid[i].Data, new { htmlAttributes = new { @class = "form-control Data", disabled = true } })
              </td>
              <td style=" width: 40pt;">
                @Html.EditorFor(model => Model.ListaProcGuiaAtendimentoGrid[i].HoraInicio, new { htmlAttributes = new { @class = "form-control", disabled = true } })
              </td>
              <td style=" width: 40pt;">
                @Html.EditorFor(model => Model.ListaProcGuiaAtendimentoGrid[i].HoraFim, new { htmlAttributes = new { @class = "form-control", disabled = true } })
              </td>
              <td>
                @Html.EditorFor(model => Model.ListaProcGuiaAtendimentoGrid[i].UrgenciaPersonalizada, new { htmlAttributes = new { @class = "form-control", disabled = true } })
              </td>
              <td style="width: 70pt;">
                @Html.EditorFor(model => Model.ListaProcGuiaAtendimentoGrid[i].Procedimento, new { htmlAttributes = new { @class = "form-control", disabled = true } })
              </td>
              <td style="width: 325pt;">
                @Html.EditorFor(model => Model.ListaProcGuiaAtendimentoGrid[i].Descricao, new { htmlAttributes = new { @class = "form-control", disabled = true } })
              </td>
              <td>
                @Html.EditorFor(model => Model.ListaProcGuiaAtendimentoGrid[i].Quantidade, new { htmlAttributes = new { @class = "form-control", disabled = true } })
              </td>
              <td style="width: 150pt; ">
                @Html.EditorFor(model => Model.ListaProcGuiaAtendimentoGrid[i].TipoProcedimento, new { htmlAttributes = new { @class = "form-control", disabled = true } })
              </td>
              <td>
                @Html.EditorFor(model => Model.ListaProcGuiaAtendimentoGrid[i].Via, new { htmlAttributes = new { @class = "form-control", disabled = true } })
              </td>
              <td>
                @Html.EditorFor(model => Model.ListaProcGuiaAtendimentoGrid[i].Tecnica, new { htmlAttributes = new { @class = "form-control", disabled = true } })
              </td>
              <td>
                @Html.EditorFor(model => Model.ListaProcGuiaAtendimentoGrid[i].Porcentagem, new { htmlAttributes = new { @class = "form-control", disabled = true } })
              </td>
              <td>
                @Html.EditorFor(model => Model.ListaProcGuiaAtendimentoGrid[i].ValorFaturado, new { htmlAttributes = new { @class = "form-control", disabled = true } })
              </td>
            </tr>
          }
        </tbody>

      </table>
    </div>
  </div>

  <div class="row" style="border-top: solid 1px white;">
    <div class="col-md-2">
      <div class="form-group formGuiaAtendimento">
        @Html.LabelFor(model => model.CPFMedico)
        @Html.EditorFor(model => model.CPFMedico, new { htmlAttributes = new { @class = "form-control", disabled = true } })
      </div>
    </div>

    <div class="col-md-8">
      <div class="form-group formGuiaAtendimento">
        @Html.LabelFor(model => model.NomeMedico)
        @Html.EditorFor(model => model.NomeMedico, new { htmlAttributes = new { @class = "form-control", disabled = true } })
      </div>
    </div>

    <div class="col-md-2">
      <div class="form-group formGuiaAtendimento">
        @Html.LabelFor(model => model.CRMMedico)
        @Html.EditorFor(model => model.CRMMedico, new { htmlAttributes = new { @class = "form-control", disabled = true } })
      </div>
    </div>
  </div>


</div>


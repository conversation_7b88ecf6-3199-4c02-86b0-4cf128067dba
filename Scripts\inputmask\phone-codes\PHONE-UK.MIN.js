/*!
* phone-codes/phone-uk.min.js
* https://github.com/RobinHerbots/Inputmask
* Copyright (c) 2010 - 2017 <PERSON>
* Licensed under the MIT license (http://www.opensource.org/licenses/mit-license.php)
* Version: 3.3.7
*/

!function(a){"function"==typeof define&&define.amd?define(["../inputmask"],a):"object"==typeof exports?module.exports=a(require("../inputmask")):a(window.Inputmask)}(function(a){return a.extendAliases({phoneuk:{alias:"abstractphone",countrycode:"44",phoneCodes:[{mask:"+44(113)-###-####",cc:"UK",cd:"United Kingdom",city:"Leeds"},{mask:"+44(114)-###-####",cc:"UK",cd:"United Kingdom",city:"Sheffield"},{mask:"+44(115)-###-####",cc:"UK",cd:"United Kingdom",city:"Nottingham"},{mask:"+44(116)-###-####",cc:"UK",cd:"United Kingdom",city:"Leicester"},{mask:"+44(117)-###-####",cc:"UK",cd:"United Kingdom",city:"Bristol"},{mask:"+44(118)-###-####",cc:"UK",cd:"United Kingdom",city:"Reading"},{mask:"+44(1200)-######",cc:"UK",cd:"United Kingdom",city:"Clitheroe"},{mask:"+44(1202)-######",cc:"UK",cd:"United Kingdom",city:"Bournemouth"},{mask:"+44(1204)-######",cc:"UK",cd:"United Kingdom",city:"Bolton"},{mask:"+44(1205)-######",cc:"UK",cd:"United Kingdom",city:"Boston"},{mask:"+44(1206)-######",cc:"UK",cd:"United Kingdom",city:"Colchester"},{mask:"+44(1207)-######",cc:"UK",cd:"United Kingdom",city:"Consett"},{mask:"+44(1208)-######",cc:"UK",cd:"United Kingdom",city:"Bodmin"},{mask:"+44(1209)-######",cc:"UK",cd:"United Kingdom",city:"Redruth"},{mask:"+44(121)-###-####",cc:"UK",cd:"United Kingdom",city:"Birmingham"},{mask:"+44(1223)-######",cc:"UK",cd:"United Kingdom",city:"Cambridge"},{mask:"+44(1224)-######",cc:"UK",cd:"United Kingdom",city:"Aberdeen"},{mask:"+44(1225)-######",cc:"UK",cd:"United Kingdom",city:"Bath"},{mask:"+44(1226)-######",cc:"UK",cd:"United Kingdom",city:"Barnsley"},{mask:"+44(1227)-######",cc:"UK",cd:"United Kingdom",city:"Canterbury"},{mask:"+44(1228)-######",cc:"UK",cd:"United Kingdom",city:"Carlisle"},{mask:"+44(1229)-######",cc:"UK",cd:"United Kingdom",city:"Barrow-in-Furness(2,4,5,6,8), Millom(3,7,9)"},{mask:"+44(1233)-######",cc:"UK",cd:"United Kingdom",city:"Ashford (Kent)"},{mask:"+44(1234)-######",cc:"UK",cd:"United Kingdom",city:"Bedford"},{mask:"+44(1235)-######",cc:"UK",cd:"United Kingdom",city:"Abingdon"},{mask:"+44(1236)-######",cc:"UK",cd:"United Kingdom",city:"Coatbridge"},{mask:"+44(1237)-######",cc:"UK",cd:"United Kingdom",city:"Bideford"},{mask:"+44(1239)-######",cc:"UK",cd:"United Kingdom",city:"Cardigan"},{mask:"+44(1241)-######",cc:"UK",cd:"United Kingdom",city:"Arbroath"},{mask:"+44(1242)-######",cc:"UK",cd:"United Kingdom",city:"Cheltenham"},{mask:"+44(1243)-######",cc:"UK",cd:"United Kingdom",city:"Chichester"},{mask:"+44(1244)-######",cc:"UK",cd:"United Kingdom",city:"Chester"},{mask:"+44(1245)-######",cc:"UK",cd:"United Kingdom",city:"Chelmsford"},{mask:"+44(1246)-######",cc:"UK",cd:"United Kingdom",city:"Chesterfield"},{mask:"+44(1248)-######",cc:"UK",cd:"United Kingdom",city:"Bangor (Gwynedd)"},{mask:"+44(1249)-######",cc:"UK",cd:"United Kingdom",city:"Chippenham"},{mask:"+44(1250)-######",cc:"UK",cd:"United Kingdom",city:"Blairgowrie"},{mask:"+44(1252)-######",cc:"UK",cd:"United Kingdom",city:"Aldershot"},{mask:"+44(1253)-######",cc:"UK",cd:"United Kingdom",city:"Blackpool"},{mask:"+44(1254)-######",cc:"UK",cd:"United Kingdom",city:"Blackburn"},{mask:"+44(1255)-######",cc:"UK",cd:"United Kingdom",city:"Clacton-on-Sea"},{mask:"+44(1256)-######",cc:"UK",cd:"United Kingdom",city:"Basingstoke"},{mask:"+44(1257)-######",cc:"UK",cd:"United Kingdom",city:"Coppull"},{mask:"+44(1258)-######",cc:"UK",cd:"United Kingdom",city:"Blandford"},{mask:"+44(1259)-######",cc:"UK",cd:"United Kingdom",city:"Alloa"},{mask:"+44(1260)-######",cc:"UK",cd:"United Kingdom",city:"Congleton"},{mask:"+44(1261)-######",cc:"UK",cd:"United Kingdom",city:"Banff"},{mask:"+44(1262)-######",cc:"UK",cd:"United Kingdom",city:"Bridlington"},{mask:"+44(1263)-######",cc:"UK",cd:"United Kingdom",city:"Cromer"},{mask:"+44(1264)-######",cc:"UK",cd:"United Kingdom",city:"Andover"},{mask:"+44(1267)-######",cc:"UK",cd:"United Kingdom",city:"Carmarthen"},{mask:"+44(1268)-######",cc:"UK",cd:"United Kingdom",city:"Basildon"},{mask:"+44(1269)-######",cc:"UK",cd:"United Kingdom",city:"Ammanford"},{mask:"+44(1270)-######",cc:"UK",cd:"United Kingdom",city:"Crewe"},{mask:"+44(1271)-######",cc:"UK",cd:"United Kingdom",city:"Barnstaple"},{mask:"+44(1273)-######",cc:"UK",cd:"United Kingdom",city:"Brighton"},{mask:"+44(1274)-######",cc:"UK",cd:"United Kingdom",city:"Bradford"},{mask:"+44(1275)-######",cc:"UK",cd:"United Kingdom",city:"Clevedon"},{mask:"+44(1276)-######",cc:"UK",cd:"United Kingdom",city:"Camberley"},{mask:"+44(1277)-######",cc:"UK",cd:"United Kingdom",city:"Brentwood"},{mask:"+44(1278)-######",cc:"UK",cd:"United Kingdom",city:"Bridgwater"},{mask:"+44(1279)-######",cc:"UK",cd:"United Kingdom",city:"Bishops Stortford"},{mask:"+44(1280)-######",cc:"UK",cd:"United Kingdom",city:"Buckingham"},{mask:"+44(1282)-######",cc:"UK",cd:"United Kingdom",city:"Burnley"},{mask:"+44(1283)-######",cc:"UK",cd:"United Kingdom",city:"Burton-on-Trent"},{mask:"+44(1284)-######",cc:"UK",cd:"United Kingdom",city:"Bury St Edmunds"},{mask:"+44(1285)-######",cc:"UK",cd:"United Kingdom",city:"Cirencester"},{mask:"+44(1286)-######",cc:"UK",cd:"United Kingdom",city:"Caernarfon"},{mask:"+44(1287)-######",cc:"UK",cd:"United Kingdom",city:"Guisborough"},{mask:"+44(1288)-######",cc:"UK",cd:"United Kingdom",city:"Bude"},{mask:"+44(1289)-######",cc:"UK",cd:"United Kingdom",city:"Berwick-upon-Tweed"},{mask:"+44(1290)-######",cc:"UK",cd:"United Kingdom",city:"Cumnock"},{mask:"+44(1291)-######",cc:"UK",cd:"United Kingdom",city:"Chepstow"},{mask:"+44(1292)-######",cc:"UK",cd:"United Kingdom",city:"Ayr"},{mask:"+44(1293)-######",cc:"UK",cd:"United Kingdom",city:"Crawley"},{mask:"+44(1294)-######",cc:"UK",cd:"United Kingdom",city:"Ardrossan"},{mask:"+44(1295)-######",cc:"UK",cd:"United Kingdom",city:"Banbury"},{mask:"+44(1296)-######",cc:"UK",cd:"United Kingdom",city:"Aylesbury"},{mask:"+44(1297)-######",cc:"UK",cd:"United Kingdom",city:"Axminster"},{mask:"+44(1298)-######",cc:"UK",cd:"United Kingdom",city:"Buxton"},{mask:"+44(1299)-######",cc:"UK",cd:"United Kingdom",city:"Bewdley"},{mask:"+44(1300)-######",cc:"UK",cd:"United Kingdom",city:"Cerne Abbas"},{mask:"+44(1301)-######",cc:"UK",cd:"United Kingdom",city:"Arrochar"},{mask:"+44(1302)-######",cc:"UK",cd:"United Kingdom",city:"Doncaster"},{mask:"+44(1303)-######",cc:"UK",cd:"United Kingdom",city:"Folkestone"},{mask:"+44(1304)-######",cc:"UK",cd:"United Kingdom",city:"Dover"},{mask:"+44(1305)-######",cc:"UK",cd:"United Kingdom",city:"Dorchester"},{mask:"+44(1306)-######",cc:"UK",cd:"United Kingdom",city:"Dorking"},{mask:"+44(1307)-######",cc:"UK",cd:"United Kingdom",city:"Forfar"},{mask:"+44(1308)-######",cc:"UK",cd:"United Kingdom",city:"Bridport"},{mask:"+44(1309)-######",cc:"UK",cd:"United Kingdom",city:"Forres"},{mask:"+44(131)-###-###",cc:"UK",cd:"United Kingdom",city:"Edinburgh"},{mask:"+44(1320)-######",cc:"UK",cd:"United Kingdom",city:"Fort Augustus"},{mask:"+44(1322)-######",cc:"UK",cd:"United Kingdom",city:"Dartford"},{mask:"+44(1323)-######",cc:"UK",cd:"United Kingdom",city:"Eastbourne"},{mask:"+44(1324)-######",cc:"UK",cd:"United Kingdom",city:"Falkirk"},{mask:"+44(1325)-######",cc:"UK",cd:"United Kingdom",city:"Darlington"},{mask:"+44(1326)-######",cc:"UK",cd:"United Kingdom",city:"Falmouth"},{mask:"+44(1327)-######",cc:"UK",cd:"United Kingdom",city:"Daventry"},{mask:"+44(1328)-######",cc:"UK",cd:"United Kingdom",city:"Fakenham"},{mask:"+44(1329)-######",cc:"UK",cd:"United Kingdom",city:"Fareham"},{mask:"+44(1330)-######",cc:"UK",cd:"United Kingdom",city:"Banchory"},{mask:"+44(1332)-######",cc:"UK",cd:"United Kingdom",city:"Derby"},{mask:"+44(1333)-######",cc:"UK",cd:"United Kingdom",city:"Peat Inn"},{mask:"+44(1334)-######",cc:"UK",cd:"United Kingdom",city:"St Andrews"},{mask:"+44(1335)-######",cc:"UK",cd:"United Kingdom",city:"Ashbourne"},{mask:"+44(1337)-######",cc:"UK",cd:"United Kingdom",city:"Ladybank"},{mask:"+44(1339)-######",cc:"UK",cd:"United Kingdom",city:"Aboyne(2,3,5,8), Ballater(4,6,7,9)"},{mask:"+44(1340)-######",cc:"UK",cd:"United Kingdom",city:"Craigellachie"},{mask:"+44(1341)-######",cc:"UK",cd:"United Kingdom",city:"Barmouth"},{mask:"+44(1342)-######",cc:"UK",cd:"United Kingdom",city:"East Grinstead"},{mask:"+44(1343)-######",cc:"UK",cd:"United Kingdom",city:"Elgin"},{mask:"+44(1344)-######",cc:"UK",cd:"United Kingdom",city:"Bracknell"},{mask:"+44(1346)-######",cc:"UK",cd:"United Kingdom",city:"Fraserburgh"},{mask:"+44(1347)-######",cc:"UK",cd:"United Kingdom",city:"Easingwold"},{mask:"+44(1348)-######",cc:"UK",cd:"United Kingdom",city:"Fishguard"},{mask:"+44(1349)-######",cc:"UK",cd:"United Kingdom",city:"Dingwall"},{mask:"+44(1350)-######",cc:"UK",cd:"United Kingdom",city:"Dunkeld"},{mask:"+44(1352)-######",cc:"UK",cd:"United Kingdom",city:"Mold"},{mask:"+44(1353)-######",cc:"UK",cd:"United Kingdom",city:"Ely"},{mask:"+44(1354)-######",cc:"UK",cd:"United Kingdom",city:"Chatteris"},{mask:"+44(1355)-######",cc:"UK",cd:"United Kingdom",city:"East Kilbride"},{mask:"+44(1356)-######",cc:"UK",cd:"United Kingdom",city:"Brechin"},{mask:"+44(1357)-######",cc:"UK",cd:"United Kingdom",city:"Strathaven"},{mask:"+44(1358)-######",cc:"UK",cd:"United Kingdom",city:"Ellon"},{mask:"+44(1359)-######",cc:"UK",cd:"United Kingdom",city:"Pakenham"},{mask:"+44(1360)-######",cc:"UK",cd:"United Kingdom",city:"Killearn"},{mask:"+44(1361)-######",cc:"UK",cd:"United Kingdom",city:"Duns"},{mask:"+44(1362)-######",cc:"UK",cd:"United Kingdom",city:"Dereham"},{mask:"+44(1363)-######",cc:"UK",cd:"United Kingdom",city:"Crediton"},{mask:"+44(1364)-######",cc:"UK",cd:"United Kingdom",city:"Ashburton"},{mask:"+44(1366)-######",cc:"UK",cd:"United Kingdom",city:"Downham Market"},{mask:"+44(1367)-######",cc:"UK",cd:"United Kingdom",city:"Faringdon"},{mask:"+44(1368)-######",cc:"UK",cd:"United Kingdom",city:"Dunbar"},{mask:"+44(1369)-######",cc:"UK",cd:"United Kingdom",city:"Dunoon"},{mask:"+44(1371)-######",cc:"UK",cd:"United Kingdom",city:"Great Dunmow"},{mask:"+44(1372)-######",cc:"UK",cd:"United Kingdom",city:"Esher"},{mask:"+44(1373)-######",cc:"UK",cd:"United Kingdom",city:"Frome"},{mask:"+44(1375)-######",cc:"UK",cd:"United Kingdom",city:"Grays Thurrock"},{mask:"+44(1376)-######",cc:"UK",cd:"United Kingdom",city:"Braintree"},{mask:"+44(1377)-######",cc:"UK",cd:"United Kingdom",city:"Driffield"},{mask:"+44(1379)-######",cc:"UK",cd:"United Kingdom",city:"Diss"},{mask:"+44(1380)-######",cc:"UK",cd:"United Kingdom",city:"Devizes"},{mask:"+44(1381)-######",cc:"UK",cd:"United Kingdom",city:"Fortrose"},{mask:"+44(1382)-######",cc:"UK",cd:"United Kingdom",city:"Dundee"},{mask:"+44(1383)-######",cc:"UK",cd:"United Kingdom",city:"Dunfermline"},{mask:"+44(1384)-######",cc:"UK",cd:"United Kingdom",city:"Dudley"},{mask:"+44(1386)-######",cc:"UK",cd:"United Kingdom",city:"Evesham"},{mask:"+44(1387)-######",cc:"UK",cd:"United Kingdom",city:"Dumfries"},{mask:"+44(13873)-####[#]",cc:"UK",cd:"United Kingdom",city:"Langholm"},{mask:"+44(1388)-######",cc:"UK",cd:"United Kingdom",city:"Bishop Auckland(3,4,6,7,8,9), Stanhope(2,5)"},{mask:"+44(1389)-######",cc:"UK",cd:"United Kingdom",city:"Dumbarton"},{mask:"+44(1392)-######",cc:"UK",cd:"United Kingdom",city:"Exeter"},{mask:"+44(1394)-######",cc:"UK",cd:"United Kingdom",city:"Felixstowe"},{mask:"+44(1395)-######",cc:"UK",cd:"United Kingdom",city:"Budleigh Salterton"},{mask:"+44(1397)-######",cc:"UK",cd:"United Kingdom",city:"Fort William"},{mask:"+44(1398)-######",cc:"UK",cd:"United Kingdom",city:"Dulverton"},{mask:"+44(1400)-######",cc:"UK",cd:"United Kingdom",city:"Honington"},{mask:"+44(1403)-######",cc:"UK",cd:"United Kingdom",city:"Horsham"},{mask:"+44(1404)-######",cc:"UK",cd:"United Kingdom",city:"Honiton"},{mask:"+44(1405)-######",cc:"UK",cd:"United Kingdom",city:"Goole"},{mask:"+44(1406)-######",cc:"UK",cd:"United Kingdom",city:"Holbeach"},{mask:"+44(1407)-######",cc:"UK",cd:"United Kingdom",city:"Holyhead"},{mask:"+44(1408)-######",cc:"UK",cd:"United Kingdom",city:"Golspie"},{mask:"+44(1409)-######",cc:"UK",cd:"United Kingdom",city:"Holsworthy"},{mask:"+44(141)-###-###",cc:"UK",cd:"United Kingdom",city:"Glasgow"},{mask:"+44(1420)-######",cc:"UK",cd:"United Kingdom",city:"Alton"},{mask:"+44(1422)-######",cc:"UK",cd:"United Kingdom",city:"Halifax"},{mask:"+44(1423)-######",cc:"UK",cd:"United Kingdom",city:"Boroughbridge(3,4,9), Harrogate(2,5,6,7,8)"},{mask:"+44(1424)-######",cc:"UK",cd:"United Kingdom",city:"Hastings"},{mask:"+44(1425)-######",cc:"UK",cd:"United Kingdom",city:"Ringwood"},{mask:"+44(1427)-######",cc:"UK",cd:"United Kingdom",city:"Gainsborough"},{mask:"+44(1428)-######",cc:"UK",cd:"United Kingdom",city:"Haslemere"},{mask:"+44(1429)-######",cc:"UK",cd:"United Kingdom",city:"Hartlepool"},{mask:"+44(1430)-######",cc:"UK",cd:"United Kingdom",city:"Market Weighton(6,7,8,9), North Cave(2,3,4,5)"},{mask:"+44(1431)-######",cc:"UK",cd:"United Kingdom",city:"Helmsdale"},{mask:"+44(1432)-######",cc:"UK",cd:"United Kingdom",city:"Hereford"},{mask:"+44(1433)-######",cc:"UK",cd:"United Kingdom",city:"Hathersage"},{mask:"+44(1434)-######",cc:"UK",cd:"United Kingdom",city:"Bellingham(2,4,9), Haltwhistle(3,5), Hexham(6,7,8)"},{mask:"+44(1435)-######",cc:"UK",cd:"United Kingdom",city:"Heathfield"},{mask:"+44(1436)-######",cc:"UK",cd:"United Kingdom",city:"Helensburgh"},{mask:"+44(1437)-######",cc:"UK",cd:"United Kingdom",city:"Clynderwen(2,3,4,5), Haverfordwest(6,7,8,9)"},{mask:"+44(1438)-######",cc:"UK",cd:"United Kingdom",city:"Stevenage"},{mask:"+44(1439)-######",cc:"UK",cd:"United Kingdom",city:"Helmsley"},{mask:"+44(1440)-######",cc:"UK",cd:"United Kingdom",city:"Haverhill"},{mask:"+44(1442)-######",cc:"UK",cd:"United Kingdom",city:"Hemel Hempstead"},{mask:"+44(1443)-######",cc:"UK",cd:"United Kingdom",city:"Pontypridd"},{mask:"+44(1444)-######",cc:"UK",cd:"United Kingdom",city:"Haywards Heath"},{mask:"+44(1445)-######",cc:"UK",cd:"United Kingdom",city:"Gairloch"},{mask:"+44(1446)-######",cc:"UK",cd:"United Kingdom",city:"Barry"},{mask:"+44(1449)-######",cc:"UK",cd:"United Kingdom",city:"Stowmarket"},{mask:"+44(1450)-######",cc:"UK",cd:"United Kingdom",city:"Hawick"},{mask:"+44(1451)-######",cc:"UK",cd:"United Kingdom",city:"Stow-on-the-Wold"},{mask:"+44(1452)-######",cc:"UK",cd:"United Kingdom",city:"Gloucester"},{mask:"+44(1453)-######",cc:"UK",cd:"United Kingdom",city:"Dursley"},{mask:"+44(1454)-######",cc:"UK",cd:"United Kingdom",city:"Chipping Sodbury"},{mask:"+44(1455)-######",cc:"UK",cd:"United Kingdom",city:"Hinckley"},{mask:"+44(1456)-######",cc:"UK",cd:"United Kingdom",city:"Glenurquhart"}]}}),a});
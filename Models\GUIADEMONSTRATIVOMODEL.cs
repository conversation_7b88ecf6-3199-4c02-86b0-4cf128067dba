﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace RepasseConvenio.Models
{
  public class GuiaDemonstrativoAtualizaLote
  {
    public decimal TotalFaturado { get; set; }
    public decimal TotalPago { get; set; }

    public int? IdGuia { get; set; }
  }

  public class GuiaDemonstrativoIntegraCabecalho
  {
    public string NroUnicooper { get; set; }
    public string NumeroCarteirinha { get; set; }
    public string NumeroAtendimento { get; set; }
    public DateTime? DataAtendimento { get; set; }

    public decimal ValorFaturado { get; set; }

    public decimal? ValorApresentado { get; set; }

    public decimal ValorGlosado { get; set; }

    public decimal ValorPago { get; set; }

    public string NomePlanilha { get; set; }

    public int IdRepasse { get; set; }

    public List<GuiaDemonstrativoIntegra> ListaGuiaDemonstrativoIntegra { get; set; }
  }

  public class GuiaDemonstrativoDelete
  {
    public int IdGuiaAtendimento { get; set; }
    public string NumeroGuia { get; set; }
    public decimal TotalPago { get; set; }
    public decimal TotalGlosado { get; set; }
  }

  public class GuiaDemonstrativoUpdate
  {
    public int IdGuiaAtendimento { get; set; }
    public string NumeroGuia { get; set; }
    public decimal TotalPago { get; set; }
    public decimal TotalGlosado { get; set; }
  }

  public class GuiaDemonstrativoPreDelete
  {
    public int IdGuiaDemonstartivo { get; set; }
    public int IdDemonstrativo { get; set; }

  }

  public class GuiaDemonstrativoIntegra
  {
    public int? IdGuia { get; set; }
    public int IdRepasse { get; set; }
    public string NomePlanilha { get; set; }
    public string NumeroGuia { get; set; }
    public string NumeroCarteirinha { get; set; }

    public string NumeroAtendimento { get; set; }

    public string CodigoProcedimento { get; set; }

    public string DescricaoProcedimento { get; set; }

    public DateTime? DataAtendimento { get; set; }

    public int ItensGlosados { get; set; }

    public decimal ValorFaturado { get; set; }

    public decimal? ValorApresentado { get; set; }

    public decimal ValorGlosado { get; set; }

    public decimal ValorPago { get; set; }
    public string DescricaoGlosa { get; set; }
    public string CodigoGlosa { get; set; }
    public string ObservacoesGerais { get; set; }

    public string NroUnicooper { get; set; }
  }

  public class GuiaDemonstrativoIndex
  {
    public int Codigo { get; set; }

    public int? CodigoGuia { get; set; }
    public int IdRepasse { get; set; }

    public string NumeroGuia { get; set; }
    public string NumeroCarteirinha { get; set; }

    public string NumeroAtendimento { get; set; }

    public DateTime DataAtendimento { get; set; }

    public decimal ValorFaturado { get; set; }

    public decimal ValorApresentado { get; set; }

    public decimal ValorGlosado { get; set; }

    public decimal ValorPago { get; set; }
  }

  public static class GuiaDemonstrativoConversions
  {
    public static void EntityUpdate(this R_GuiaDemonstrativo entity
                                  , GuiaDemonstrativoIntegraCabecalho guiaDemonstrativoIntegraCabecalho
                                  , int IdTipoImportacao
                                  , decimal ValorFaturado)
    {
      entity.GD_DataAtendimento = guiaDemonstrativoIntegraCabecalho.DataAtendimento.Value;
      entity.GD_NumeroAtendimento = guiaDemonstrativoIntegraCabecalho.NumeroAtendimento;
      entity.GD_NumeroCarteirinha = guiaDemonstrativoIntegraCabecalho.NumeroCarteirinha;
      entity.GD_TotalApresentado = guiaDemonstrativoIntegraCabecalho.ValorApresentado.Value;
      entity.GD_TotalFaturado = ValorFaturado;
      entity.GD_TotalGlosado = guiaDemonstrativoIntegraCabecalho.ValorGlosado;
      entity.GD_TotalPago = guiaDemonstrativoIntegraCabecalho.ValorPago;
      entity.GD_NumeroGuia = guiaDemonstrativoIntegraCabecalho.NroUnicooper;
      entity.GD_IdTipoImportacaoGuiaDemonstrativo = IdTipoImportacao;
    }

    public static R_GuiaDemonstrativo ModeltoEntityCreate(this GuiaDemonstrativoIntegraCabecalho guiaDemonstrativoIntegraCabecalho
                                                         , int IdDemonstrativoConvenio
                                                         , int IdGuia
                                                         , string NumeroGuia
                                                         , decimal ValorFaturado
                                                         , int IdStatusGuiaDemonstrativo
                                                         , int IdTipoImportacao)
    {
      return new R_GuiaDemonstrativo()
      {
        GD_DataAtendimento = guiaDemonstrativoIntegraCabecalho.DataAtendimento.Value,
        GD_IdDemonstrativoConvenio = IdDemonstrativoConvenio,
        GD_NumeroAtendimento = guiaDemonstrativoIntegraCabecalho.NumeroAtendimento,
        GD_NumeroCarteirinha = guiaDemonstrativoIntegraCabecalho.NumeroCarteirinha,
        GD_TotalApresentado = guiaDemonstrativoIntegraCabecalho.ValorApresentado.Value,
        GD_TotalFaturado = ValorFaturado,
        GD_TotalGlosado = guiaDemonstrativoIntegraCabecalho.ValorGlosado,
        GD_TotalPago = guiaDemonstrativoIntegraCabecalho.ValorPago,
        GD_IdGuia = IdGuia,
        GD_NumeroGuia = NumeroGuia,
        DC_IdStatusGuiaDemonstrativoConvenio = IdStatusGuiaDemonstrativo,
        GD_IdTipoImportacaoGuiaDemonstrativo = IdTipoImportacao
      };
    }
  }
}
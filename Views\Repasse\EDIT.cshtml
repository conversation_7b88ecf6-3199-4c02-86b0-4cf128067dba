﻿@using RepasseConvenio.Infrastructure.Controls
@using RepasseConvenio.Models
@using System.Globalization;
@model R_RepasseModel

@{
  ViewBag.Title = "Alterar Repasse";
  ViewBag.DescricaoTela = "Repasse";
  ViewBag.ResumoTela = "Alterar Repasse";
  Layout = "~/Views/Shared/_Layout.cshtml";
}
@Scripts.Render("~/Views/Repasse/Repasse.js?a=ia")
@Styles.Render("~/Views/Repasse/Repasse.css?l=tgb")

@using (Html.BeginForm("Edit", "Repasse", FormMethod.Post, new { enctype = "multipart/form-data" }))
{
  @Html.AntiForgeryToken()
  @Html.ValidationSummary(true, "", new { @class = "text-danger" })
  @Html.HiddenFor(a => a.Codigo)
  @Html.HiddenFor(a => a.NF)
  @Html.HiddenFor(a => a.<PERSON>)
  @Html.HiddenFor(a => a.Deposito)
  @Html.HiddenFor(a => a.IdDepositoRepasse);
  @Html.Hidden("IdNotaFiscal")<div class="row">
    <section class="col-lg-12 connectedSortable ui-sortable">
      <div class="card">
        <div class="card-header ui-sortable-handle">
          <div class="card-title">
            @{
              switch ((EnumStatusRepasse)Model.CodigoStatusRepasse)
              {
                case EnumStatusRepasse.EMCONFERENCIA:
                  <div class="emconferencia">
                  </div>
                  break;
                case EnumStatusRepasse.ELABORACAO:
                  <div class="emelaboracao">
                  </div>
                  break;
                case EnumStatusRepasse.CONCLUIDO:
                  <div class="concluido">
                  </div>
                  break;
                case EnumStatusRepasse.PROCESSADO:
                  <div class="processado">
                  </div>
                  break;
                case EnumStatusRepasse.PROCESSADOPARCIALMENTE:
                  <div class="parcProcessado">
                  </div>
                  break;
                case EnumStatusRepasse.ProcessadoGlosado:
                  <div class="glossado">
                  </div>
                  break;
              }
              @RepasseConvenio.Infrastructure.Helpers.EnumHelper.Description((EnumStatusRepasse)Model.CodigoStatusRepasse)
            }
          </div>
          <div class="card-tools">
            <button id="ProcessaRepasse" data-idrepasse="@Model.Codigo" data-toggle="modal" type="button" class="btn btn-outline-secondary btn-newitem" title="Processar Repasse">
              Processar Repasse
            </button>
          </div>
        </div><!-- /.card-header -->
        <div class="card-body">
          <div class="tab-content p-0">
            <div class="row">
              <div class="col-md-2">
                <div class="form-group">
                  @Html.LabelFor(model => model.Numero)
                  @Html.EditorFor(model => model.Numero, new { htmlAttributes = new { @class = "form-control", @readonly = "readonly" } })
                  @Html.ValidationMessageFor(model => model.Numero, "", new { @class = "text-danger" })
                </div>
              </div>
              <div class="col-md-2">
                <div class="form-group">
                  @Html.LabelFor(model => model.DataInicio)
                  @Html.EditorFor(model => model.DataInicio, new { htmlAttributes = new { @class = "form-control airDatePickerDate Data" } })
                  @Html.ValidationMessageFor(model => model.DataInicio, "", new { @class = "text-danger" })
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  @Html.LabelFor(m => m.ResponsavelSelect)
                  @Html.LibSelect2For(m => m.ResponsavelSelect, new { @class = "form-control" })
                </div>
              </div>
              @if (!Model.Valorizacao && !Model.NF && !Model.Deposito)
              {
                <div class="col-md-4">
                  <div class="form-group">
                    @Html.LabelFor(m => m.ConvenioSelect)
                    @Html.LibSelect2For(m => m.ConvenioSelect, new { @class = "form-control" })
                  </div>
                </div>
              }
              else
              {
                @Html.HiddenFor(a => a.CodigoConvenio)
                <div class="col-md-4">

                  <div class="form-group">
                    @Html.LabelFor(model => model.RazaoSocialConvenio)
                    @Html.EditorFor(model => model.RazaoSocialConvenio, new { htmlAttributes = new { @class = "form-control", @readonly = "readonly" } })
                  </div>
                </div>
              }
            </div>
            <div class="row">
              <div class="col-md-2">
                <div class="form-group">
                  @Html.CheckBoxFor(a => a.NF, new { disabled = true })
                  @Html.LabelFor(a => a.NF)
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  @Html.CheckBoxFor(a => a.Valorizacao, new { disabled = true })
                  @Html.LabelFor(a => a.Valorizacao)
                </div>
              </div>


              <div class="col-md-2">
                <div class="form-group">
                  @Html.CheckBoxFor(a => a.Deposito, new { disabled = true })
                  @Html.LabelFor(a => a.Deposito)
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="card">
        <div class="card-header ui-sortable-handle">
          <h3 class="card-title">
            Nota Fiscal
          </h3>
          <div class="card-tools">
            @if (!Model.NF)
            {
              <button style="float:right" id="NovaNotaFiscal" data-idrepasse="@Model.Codigo" data-toggle="modal" type="button" class="btn btn-outline-secondary btn-newitem" title="Nova Nota">
                Nova Nota
              </button>
            }
          </div>
        </div><!-- /.card-header -->
        <div class="card-body">
          <div class="tab-content p-0">
            <div class="box-body">
              <div class="row">
                <div class="col-md-12 table-responsive p-0" id="GridNotaFiscalRepasse">
                  @Html.Partial("_GridNotaFiscalRepasse", Model.ListNotaFiscalRepasse)
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="card">
        <div class="card-header ui-sortable-handle">
          <h3 class="card-title">
            Demonstrativo
          </h3>
          <div class="card-tools">
            <input type="file" style="display:none" accept=".xls,.xlsx" class="inputfile2" id="inputfileimportplan" name="inputfileimportplan" value="" />
            <a class="btn btn-outline-danger btn-circle btn-sm btnExcluirSelecionados disabled" title="Excluir">
              Excluir Selecionados
            </a>
            <button id="ImportPlan" data-idrepasse="@Model.Codigo" data-toggle="modal" type="button" class="btn btn-outline-secondary btn-newitem" title="Importar Planilha">
              Importar Planilha
            </button>
            <button id="ImportNotaFiscal" data-idrepasse="@Model.Codigo" data-toggle="modal" type="button" class="btn btn-outline-secondary btn-newitem" title="Importar da Nota Fiscal">
              Importar da Nota Fiscal
            </button>
            <button id="VisuLotes" data-idrepasse="@Model.Codigo" data-toggle="modal" type="button" class="btn btn-outline-secondary btn-newitem" title="Visualizar Lotes">
              Visualizar Lotes
            </button>
            @*<button id="NovaPlanRecebimento" data-idrepasse="@Model.Codigo" data-toggle="modal" type="button" class="btn btn-outline-secondary btn-newitem" title="Nova Nota">
                Nova Planilha
              </button>
              <button id="open-modal-buscalote" data-idrepasse="@Model.Codigo" data-idconvenio="@Model.CodigoConvenio" data-toggle="modal" type="button" class="btn btn-outline-secondary btn-newitem" title="Buscar Lotes">
                Buscar Lotes
              </button>*@
          </div>
        </div>
        <div class="card-body">
          <div class="tab-content p-0">
            <div class="box-body">
              <div class="row">
                <div class="col-md-12 table-responsive p-0" id="GridPlanilhaRecebimento">
                  @Html.Partial("_GridDemonstrativoConvenio", Model.ListDemonstrativoConvenioGrid)
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="card-footer" style=" display: flex; justify-content: space-between;">
          <p><b>Apresentado:</b> @Model.ListDemonstrativoConvenioGrid.Sum(a => a.ValorApresentado).ToString("C2", CultureInfo.CreateSpecificCulture("pt-BR"))</p>
          <p><b>Faturado: </b> @Model.ListDemonstrativoConvenioGrid.Sum(a => a.TotalFaturado).ToString("C2", CultureInfo.CreateSpecificCulture("pt-BR"))</p>
          <p><b>Glosado:</b> @Model.ListDemonstrativoConvenioGrid.Sum(a => a.TotalGlosado).ToString("C2", CultureInfo.CreateSpecificCulture("pt-BR"))</p>
          <p><b>Pago:</b> @Model.ListDemonstrativoConvenioGrid.Sum(a => a.TotalPago).ToString("C2", CultureInfo.CreateSpecificCulture("pt-BR"))</p>
        </div>
      </div>

      <div class="card">
        <div class="card-header ui-sortable-handle">
          <h3 class="card-title">
            Depósito
          </h3>
          <div class="card-tools">
            @if (Model.IdDepositoRepasse != null)
            {
              <button style="float:right" id="ExcluirDepositoRepasse" type="button" data-iddepositorepasse=@Model.IdDepositoRepasse class="btn btn-outline-secondary   btn-newitem" title="Novo Deposito">
                Excluir
              </button>
            }
            else
            {
              <button style="float:right" id="InserirDepositoRepasse" type="button" class="btn btn-outline-secondary   btn-newitem" title="Inserir Deposito">
                Inserir
              </button>

              <button style="float:right" id="NovoDepositoRepasse" type="button" class="btn btn-outline-secondary   btn-newitem" title="Novo Deposito">
                Novo
              </button>
            }

          </div>
        </div><!-- /.card-header -->
        <div class="card-body">
          <div class="tab-content p-0">
            <div class="row">
              <div class="col-md-12 " id="GridDepositoRepasse">
                @Html.Partial("_GridDepositoRepasse", Model.ListDepositoRepasse)
              </div>

            </div>
          </div>
        </div>
      </div>
      <br />

      <div class="card" style="display:none">
        <div class="card-header ui-sortable-handle">
          <h3 class="card-title">
            Valores Repasse
          </h3>
          <div class="card-tools">
          </div>
        </div><!-- /.card-header -->
        <div class="card-body">
          <div class="tab-content p-0">
            <div class="row">
              <div class="col-md-2">
                <div class="form-group">
                  @Html.LabelFor(model => model.Valor)
                  @Html.EditorFor(model => model.Valor, new { htmlAttributes = new { @class = "form-control money" } })
                  @Html.ValidationMessageFor(model => model.Valor, "", new { @class = "text-danger" })
                </div>
              </div>
              <div class="col-md-2">
                <div class="checkbox form-group">
                  @Html.LabelFor(model => model.Imposto)
                  @Html.EditorFor(model => model.Imposto, new { htmlAttributes = new { @class = "form-control perAcresc" } })
                  @Html.ValidationMessageFor(model => model.Imposto, "", new { @class = "text-danger" })
                </div>
              </div>
              <div class="col-md-3">
                <div class="checkbox form-group">
                  @Html.LabelFor(model => model.TaxaAdministrativa)
                  @Html.EditorFor(model => model.TaxaAdministrativa, new { htmlAttributes = new { @class = "form-control perAcresc" } })
                  @Html.ValidationMessageFor(model => model.TaxaAdministrativa, "", new { @class = "text-danger" })
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>

  <div class="box box-primary">
    <div class="box-footer">
      <button type="button" value="Voltar" class="btn btn-secondary" onclick="location.href='@Url.Action("Index", "Repasse")'">Cancelar</button>
      <button type="submit" value="Create" class="btn btn-info pull-right" style="float:right">Salvar</button>
    </div>
    <!-- /.box-footer -->
  </div>
  <br />
}

@Html.Partial("_ModalGuiaNotaFiscal")
@Html.Partial("_ModalNotaFiscal")
@Html.Partial("_ModalPlanRecebimento")
@Html.Partial("_ModalBuscaLote")
@Html.Partial("_ModalGuiasDemonstrativo")
@Html.Partial("_ModalGuiaAtendimento")
@Html.Partial("_ModalProcedimentosDemonstrativo")
@Html.Partial("_ModalLotes")
@Html.Partial("_ModalItensLote")
@Html.Partial("_ModalDepositoRepasse")
@Html.Partial("_ModalInserteDepositoRepasse")


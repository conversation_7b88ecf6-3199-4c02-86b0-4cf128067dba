﻿.customBtnDanger {
  background-color: #bb1d1d;
  border: none;
  color: white;
  padding: 5px 10px;
  font-size: 10px;
  cursor: pointer;
  border-radius: 3px;
}

  .customBtnDanger:hover {
    background-color: #ff9393;
  }

.customBtnNeutro {
  background-color: #bb1d1d;
  border: none;
  color: white;
  padding: 5px 10px;
  font-size: 10px;
  cursor: pointer;
  border-radius: 3px;
}

  .customBtnNeutro:hover {
    background-color: #ff9393;
  }


.card-footer {
  border-top: 1px solid rgba(0, 0, 0, 0.125);
  background-color: #00000017;
}

#fundo-loading {
  width: 100%;
  height: 100%;
  z-index: 9999;
}


.PSucess {
  color: green;
}

.text-sm {
  font-size: .875rem !important;
}

.pace {
  -webkit-pointer-events: none;
  pointer-events: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.pace-inactive {
  display: none;
}

.pace .pace-progress {
  background: #29d;
  position: fixed;
  z-index: 2000;
  top: 0;
  right: 100%;
  width: 100%;
  height: 2px;
}

.form-control {
  height: calc(1.0em + 0.75rem + 0.5px) !important;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  line-height: 18px !important;
}

.pull-rigth {
  float: right;
}

.card-header-sm {
  padding: 0.35rem 1.25rem !important;
}

.btn-sm-header {
  padding: 0.08rem 0.15rem !important;
}

.TIcones {
  height: 28px;
  font-size: small;
}

#TableTela tbody tr td {
  font-size: 10.5pt;
}

a.btn-primary, a.btn-secondary, a.btn-tertiary, a.btn-danger, a.btn-warning, a.btn-info {
  color: #fff;
}

.table-responsive {
  overflow-y: auto;
  max-height: calc(100vh - 270px);
}
#fundo-loading {
  opacity: 0.7;
  width: 100%;
  height: 100%;
  z-index: 9999;
}

.overlay > .fa, .overlay-wrapper .overlay > .fa {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -15px;
  margin-top: -15px;
  color: #fff;
  font-size: 30px;
}
﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using PagedList;
using RepasseConvenio.Infrastructure;
using RepasseConvenio.Models;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;

namespace RepasseConvenio.Services
{
  public class JustificativaGlosaService: ServiceBase
  {
    public JustificativaGlosaService() : base()
    { }

    public JustificativaGlosaService(RepasseEntities Contexto)
        : base(Contexto)
    { }   

    public JustificativaGlosaService(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public JustificativaGlosaService(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public IPagedList<JustificativaGlosaModel> Get(int pageNumber)
    {
      string query = string.Format(@"SELECT 
	                                        JG_Id AS Codigo,
	                                        JG_Codigo AS CodigoJustificativa,
	                                        JG_Descricao AS Descricao	                                       
                                      FROM R_JustificativaGlosa");

      return Contexto.Database.SqlQuery<JustificativaGlosaModel>(query)
             .ToList().OrderBy(a => a.Descricao).ToPagedList(pageNumber, PageSize);
    }

    public List<R_JustificativaGlosa> GetByTerm(string term)
    {
      if (string.IsNullOrEmpty(term))
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_JustificativaGlosa");

        return Contexto.Database.SqlQuery<R_JustificativaGlosa>(query).ToList();

      }
      else
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_JustificativaGlosa
                                       WHERE JG_Codigo LIKE @termo OR JG_Descricao LIKE @termo");

        return Contexto.Database.SqlQuery<R_JustificativaGlosa>(query, new SqlParameter("@termo", string.Format("%{0}%", term))).ToList();

      }
    }

    public void Create(JustificativaGlosaModel model)
    {
      ValidaModelCreate(model);

      R_JustificativaGlosa entity = model.JustificativaToCreate();
      Create(entity);
    }

    public R_JustificativaGlosa GetById(int Id)
    {
      return Contexto.R_JustificativaGlosa.Where(a => a.JG_Id == Id).FirstOrDefault();
    }

    public R_JustificativaGlosa GetByCodigo(string Codigo)
    {
      return Contexto.R_JustificativaGlosa.Where(a => a.JG_Codigo.Equals(Codigo)).FirstOrDefault();
    }

    public void Edit(JustificativaGlosaModel model)
    {
      
      try
      {
        R_JustificativaGlosa GlosaAtual = GetById(model.Codigo);
        R_JustificativaGlosa GlosaValidacao = GetByCodigo(model.CodigoJustificativa);

        if (GlosaValidacao != null && GlosaValidacao.JG_Id != GlosaAtual.JG_Id)
          throw new CustomException("Jà existe um código igual a esse");

        GlosaAtual.JustificativaToEdit(model);
        Edit(GlosaAtual);

      }
      catch (CustomException ex)
      {
        throw new CustomException(ex.Message);
      }
    }

    public void Delete(int Id)
    {
      R_JustificativaGlosa justificativaGlosa = GetById(Id);

      Contexto.R_JustificativaGlosa.Remove(justificativaGlosa);
      Contexto.SaveChanges();
    }

    public void ValidaModelCreate(JustificativaGlosaModel model)
    {
      R_JustificativaGlosa glosa = GetByCodigo(model.CodigoJustificativa);

      if (glosa != null)
        throw new CustomException("Jà existe um código igual a esse");     
    }

    public void ValidaModelEdit(R_JustificativaGlosa model)
    {
      R_JustificativaGlosa glosa = GetById(model.JG_Id);

      if(glosa.JG_Codigo != null && (!model.JG_Codigo.Equals(glosa.JG_Codigo)))
        throw new CustomException("Jà existe um código igual a esse");
    } 
  }
}
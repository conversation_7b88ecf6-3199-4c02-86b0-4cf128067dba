﻿@using RepasseConvenio.Models
@using RepasseConvenio.Infrastructure
@using RepasseConvenio.Infrastructure.Controls
@model ProtocoloGlosaCreate

@{
  ViewBag.Title = "Criar Protocolo";
  Layout = "~/Views/Shared/_Layout.cshtml";
}

@using (Html.BeginForm("Create", "ProtocoloGlosa", FormMethod.Post, new { enctype = "multipart/form-data" }))
{
  @Html.HiddenFor(c => c.CodigoLoteGlosa)
  <div class="row">
    <section class="col-lg-12 connectedSortable ui-sortable">
      <div class="card">
        <div class="card-header ui-sortable-handle">
          <h3 class="card-title">
            <i class="fas fa-chart-pie mr-1"></i>
          </h3>
          <div class="card-tools">
          </div>
        </div><!-- /.card-header -->
        <div class="card-body">
          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                @Html.LabelFor(c => c.NumeroProtocolo)
                @Html.EditorFor(c => c.NumeroProtocolo, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(c => c.NumeroProtocolo, "", new { @class = "text-danger" })
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                @Html.LabelFor(c => c.DataEmissao)
                @Html.EditorFor(c => c.DataEmissao, new { htmlAttributes = new { @class = "form-control airDatePickerDateTime" } })
                @Html.ValidationMessageFor(c => c.DataEmissao, "", new { @class = "text-danger" })
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                @Html.LabelFor(c => c.DataPrazo)
                @Html.EditorFor(c => c.DataPrazo, new { htmlAttributes = new { @class = "form-control airDatePickerDateTime" } })
                @Html.ValidationMessageFor(c => c.DataPrazo, "", new { @class = "text-danger" })
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-12">
              <div class="form-group">
                @Html.LibLabel(c => c.ObservacoesGerais)
                @Html.TextAreaFor(c => c.ObservacoesGerais, new { @class = "form-control" })
                @Html.ValidationMessageFor(c => c.ObservacoesGerais, "", new { @class = "text-danger" })
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-12">
              @Html.TextBoxFor(model => model.Files, new { @type = "file", @multiple = "multiple" })
            </div>
          </div>

        </div>
        <div class="card-footer">
          <button type="button" value="Voltar" class="btn btn-secondary" onclick="window.location.href='@Url.Action("Index","ProtocoloGlosa", new { IdLoteGosa = Model.CodigoLoteGlosa })'">Voltar</button>
          <button type="submit" value="Create" class="btn btn-info pull-rigth">Salvar</button>
        </div>
      </div>
    </section>
  </div>
}

﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Models;
using RepasseConvenio.WSPortalCooperado;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Transactions;
using System.Web;

namespace RepasseConvenio.Services
{
  public class GuiaNotaFiscalService : ServiceBase
  {
    public GuiaNotaFiscalService()
   : base()
    { }
    public GuiaNotaFiscalService(RepasseEntities Contexto)
       : base(Contexto)
    { }

    public GuiaNotaFiscalService(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public GuiaNotaFiscalService(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public void Create(List<int> ListaIdGuiasAtendimento, int IdNotaFiscal)
    {
      if (ListaIdGuiasAtendimento == null)
        throw new CustomException("Gentileza selecionar uma guia.");
      if (ListaIdGuiasAtendimento.Count == 0)
        throw new CustomException("Gentileza selecionar uma guia.");
      if (IdNotaFiscal == 0)
        throw new CustomException("Nota fiscal não encontrada.");

      List<GuiaNotaFiscalPreCreate> ListaGuiaNotaFiscalPreCreate = GetGuiaNotaFiscalPreCreate(ListaIdGuiasAtendimento);

      string Insert = @"INSERT INTO R_GuiaNotaFiscal
                         (GNF_IdNotaFiscal, GNF_IdGuiaAtendimento, GNF_IdProcGuiaAtendimento, GNF_IdStatusGuiaNotaFiscal, GNF_ValorFaturado, GNF_ValorApresentado, GNF_ValorGlosado, GNF_ValorPago)
                        VALUES #Valores#";

      List<string> Values = new List<string>();
      StatusGuiaNotaFiscalServices statusGuiaNotaFiscalServices = new StatusGuiaNotaFiscalServices();
      int IdStatusNaoProcessada = statusGuiaNotaFiscalServices.GetIdByEnum(StatusGuiaNotaFiscalEnum.NaoProcessada);
      foreach (GuiaNotaFiscalPreCreate guiaNotaFiscalPreCreate in ListaGuiaNotaFiscalPreCreate)
      {
        string value = @"(@IdNotaFiscal, @IdGuiaAtendimento, @IdProcGuiaAtendimento, @IdStatusGuiaNotaFiscal, @ValorFaturado, @ValorFaturado, 0, 0)";
        value = value.Replace("@IdNotaFiscal", IdNotaFiscal.ToString())
             .Replace("@IdGuiaAtendimento", guiaNotaFiscalPreCreate.IdGuia.ToString())
             .Replace("@IdProcGuiaAtendimento", guiaNotaFiscalPreCreate.IdProcGuia.ToString())
             .Replace("@IdStatusGuiaNotaFiscal", IdStatusNaoProcessada.ToString())
             .Replace("@ValorFaturado", guiaNotaFiscalPreCreate.ValorFaturado.ToString().Replace(",", "."));
        Values.Add(value);
      }

      using (TransactionScope scope = new TransactionScope(TransactionScopeOption.RequiresNew))
      {
        Insert = Insert.Replace("#Valores#", string.Join(",", Values.ToArray()));
        Contexto.Database.ExecuteSqlCommand(Insert);

        RepasseService repasseService = new RepasseService();
        R_Repasse repasse = repasseService.GetRepasseByIdNotaFiscal(IdNotaFiscal);
        repasse.R_NF = true;
        Edit(repasse);
        scope.Complete();
      }
    }

    public void Create(List<GuiaNotaFiscalPreCreateByPlanilha> ListaGuiaNotaFiscalPreCreate, int IdNotaFiscal)
    {
      StatusGuiaNotaFiscalServices statusGuiaNotaFiscalServices = new StatusGuiaNotaFiscalServices();
      List<R_StatusGuiaNotaFiscal> ListaStatusGuiaNota = statusGuiaNotaFiscalServices.GetAll();

      string Insert = @"INSERT INTO R_GuiaNotaFiscal
                         (GNF_IdNotaFiscal, GNF_IdGuiaAtendimento, GNF_IdProcGuiaAtendimento, GNF_IdStatusGuiaNotaFiscal, GNF_ValorFaturado, GNF_ValorApresentado, GNF_ValorGlosado, GNF_ValorPago)
                        VALUES #Valores#";

      List<string> Values = new List<string>();
      foreach (GuiaNotaFiscalPreCreateByPlanilha guiaNotaFiscalPreCreate in ListaGuiaNotaFiscalPreCreate)
      {
        int IdStatusGuiaNota = 0;
        if (guiaNotaFiscalPreCreate.ValorPago == 0)
          IdStatusGuiaNota = ListaStatusGuiaNota.Where(a => a.SGNF_Enum == (int)StatusGuiaNotaFiscalEnum.NaoPago).Select(a => a.SGNF_Id).FirstOrDefault();
        else if (guiaNotaFiscalPreCreate.ValorFaturado > guiaNotaFiscalPreCreate.ValorPago)
          IdStatusGuiaNota = ListaStatusGuiaNota.Where(a => a.SGNF_Enum == (int)StatusGuiaNotaFiscalEnum.Glosado).Select(a => a.SGNF_Id).FirstOrDefault();
        else if (guiaNotaFiscalPreCreate.ValorFaturado == guiaNotaFiscalPreCreate.ValorPago || guiaNotaFiscalPreCreate.ValorPago > guiaNotaFiscalPreCreate.ValorFaturado)
          IdStatusGuiaNota = ListaStatusGuiaNota.Where(a => a.SGNF_Enum == (int)StatusGuiaNotaFiscalEnum.Pago).Select(a => a.SGNF_Id).FirstOrDefault();

        string value = @"(@IdNotaFiscal, @IdGuiaAtendimento, @IdProcGuiaAtendimento, @IdStatusGuiaNotaFiscal, @ValorFaturado, @ValorApresentado, @ValorGlosado, @ValorPago)";
        value = value.Replace("@IdNotaFiscal", IdNotaFiscal.ToString())
             .Replace("@IdGuiaAtendimento", guiaNotaFiscalPreCreate.IdGuia.ToString())
             .Replace("@IdProcGuiaAtendimento", guiaNotaFiscalPreCreate.IdProcGuia.ToString())
             .Replace("@IdStatusGuiaNotaFiscal", IdStatusGuiaNota.ToString())
             .Replace("@ValorFaturado", guiaNotaFiscalPreCreate.ValorFaturado.ToString().Replace(",", "."))
             .Replace("@ValorApresentado", guiaNotaFiscalPreCreate.ValorApresentado.ToString().Replace(",", "."))
             .Replace("@ValorGlosado", guiaNotaFiscalPreCreate.ValorGlosado.ToString().Replace(",", "."))
             .Replace("@ValorPago", guiaNotaFiscalPreCreate.ValorPago.ToString().Replace(",", "."));
        Values.Add(value);
      }

      Insert = Insert.Replace("#Valores#", string.Join(",", Values.ToArray()));
      Contexto.Database.ExecuteSqlCommand(Insert);
    }


    public void CreateByPlanilha(List<GuiaDemonstrativoIntegraCabecalho> ListaGuiaDemonstrativoIntegraCabecalho, R_NotaFiscaRepasse notaFiscaRepasse, int IdRepasse)
    {
      RepasseService repasseService = new RepasseService(User);
      NotaFiscalRepasseService notaFiscalRepasseService = new NotaFiscalRepasseService(User);
      bool ValidaPreenchimento = notaFiscalRepasseService.ValidaPreenchimento(notaFiscaRepasse);
      List<GuiaDemonstrativoIntegra> ListaGuiaDemonstrativoIntegra = new List<GuiaDemonstrativoIntegra>();

      foreach (var item in ListaGuiaDemonstrativoIntegraCabecalho)
      {
        foreach (var item2 in item.ListaGuiaDemonstrativoIntegra)
        {
          ListaGuiaDemonstrativoIntegra.Add(item2);
        }
      }

      List<GuiaNotaFiscalPreCreateByPlanilha> ListaGuiaNotaFiscalPreCreate = GetGuiaNotaFiscalPreCreateByPlanilha(ListaGuiaDemonstrativoIntegra.Select(a => a.NroUnicooper).ToList());

      if (ListaGuiaNotaFiscalPreCreate.Count != 0)
      {
        Create(ListaGuiaNotaFiscalPreCreate, notaFiscaRepasse.NF_Id);

        if (ValidaPreenchimento)
        {
          R_Repasse repasse = repasseService.GetRepasseByIdNotaFiscal(notaFiscaRepasse.NF_Id);
          repasse.R_NF = true;
          Edit(repasse);
        }
      }
    }

    public void Edit(int IdGuiaNotaFiscal, decimal valorValorPago, decimal valorApresentado)
    {
      R_GuiaNotaFiscal guiaNotaFiscal = GetById(IdGuiaNotaFiscal);
      guiaNotaFiscal.GNF_ValorPago = valorValorPago;
      guiaNotaFiscal.GNF_ValorApresentado = valorApresentado;

      StatusGuiaNotaFiscalServices statusGuiaNotaFiscalServices = new StatusGuiaNotaFiscalServices();
      List<R_StatusGuiaNotaFiscal> ListaStatusGuiaNotaFiscal = statusGuiaNotaFiscalServices.GetAll();

      if (guiaNotaFiscal.GNF_ValorPago > guiaNotaFiscal.GNF_ValorFaturado || guiaNotaFiscal.GNF_ValorPago == guiaNotaFiscal.GNF_ValorFaturado)
        guiaNotaFiscal.GNF_IdStatusGuiaNotaFiscal = ListaStatusGuiaNotaFiscal.Where(a => a.SGNF_Enum == (int)StatusGuiaNotaFiscalEnum.Pago).Select(a => a.SGNF_Id).FirstOrDefault();
      else if (guiaNotaFiscal.GNF_ValorPago == 0)
        guiaNotaFiscal.GNF_IdStatusGuiaNotaFiscal = ListaStatusGuiaNotaFiscal.Where(a => a.SGNF_Enum == (int)StatusGuiaNotaFiscalEnum.NaoPago).Select(a => a.SGNF_Id).FirstOrDefault();
      else if (guiaNotaFiscal.GNF_ValorPago < guiaNotaFiscal.GNF_ValorFaturado)
      {
        guiaNotaFiscal.GNF_IdStatusGuiaNotaFiscal = ListaStatusGuiaNotaFiscal.Where(a => a.SGNF_Enum == (int)StatusGuiaNotaFiscalEnum.Glosado).Select(a => a.SGNF_Id).FirstOrDefault();
        guiaNotaFiscal.GNF_ValorGlosado = guiaNotaFiscal.GNF_ValorFaturado - guiaNotaFiscal.GNF_ValorPago;
      }

      Edit(guiaNotaFiscal);
    }

    public void Delete(int IdGuiaNotaFiscal)
    {
      using (TransactionScope scope = new TransactionScope(TransactionScopeOption.RequiresNew))
      {
        GuiaNotaFiscalDelete guiaNotaFiscalDelete = GetGuiaNotaFiscalDelete(IdGuiaNotaFiscal);
        string delete = @"DELETE FROM R_GuiaNotaFiscal WHERE GNF_Id = @IdGuiaNotaFiscal AND GNF_IdGuiaAtendimento = @IdGuiaAtendimento";
        Contexto.Database.ExecuteSqlCommand(delete, new SqlParameter("@IdGuiaNotaFiscal", IdGuiaNotaFiscal)
                                                  , new SqlParameter("@IdGuiaAtendimento", guiaNotaFiscalDelete.IdGuiaAtendimento));

        GuiaDemonstrativoService guiaDemonstrativoService = new GuiaDemonstrativoService();
        guiaDemonstrativoService.Delete(guiaNotaFiscalDelete.IdGuiaAtendimento, guiaNotaFiscalDelete.IdRepasse);

        int QuantidadeGuiasNota = GetQuantidadeByNotaFiscal(IdGuiaNotaFiscal);
        if (QuantidadeGuiasNota == 0)
        {
          RepasseService repasseService = new RepasseService();
          R_Repasse repasse = repasseService.GetRepasseByIdNotaFiscal(guiaNotaFiscalDelete.IdNotaFiscal);
          repasse.R_NF = false;
          Edit(repasse);
        }
        scope.Complete();
      }
    }

    public int GetQuantidadeByNotaFiscal(int IdGuiaNotaFiscal)
    {
      string query = @"SELECT
                        COUNT(1)
                       FROM R_GuiaNotaFiscal WHERE GNF_IdNotaFiscal = @IdGuiaNotaFiscal";

      return Contexto.Database.SqlQuery<int>(query, new SqlParameter("@IdGuiaNotaFiscal", IdGuiaNotaFiscal)).FirstOrDefault();
    }

    public R_GuiaNotaFiscal GetById(int IdGuiaNotaFiscal)
    {
      string query = @"SELECT
                        *
                       FROM R_GuiaNotaFiscal
                       WHERE GNF_Id = @IdGuiaNotaFiscal";

      return Contexto.Database.SqlQuery<R_GuiaNotaFiscal>(query, new SqlParameter("@IdGuiaNotaFiscal", IdGuiaNotaFiscal)).FirstOrDefault();
    }

    public GuiaNotaFiscalDelete GetGuiaNotaFiscalDelete(int IdGuiaNotaFiscal)
    {
      string query = @"SELECT
                        GNF.GNF_IdGuiaAtendimento [IdGuiaAtendimento],
                        GNF.GNF_IdNotaFiscal [IdNotaFiscal],
                        NFR.NF_IdRepasse [IdRepasse]
                       FROM R_GuiaNotaFiscal GNF
                       INNER JOIN R_NotaFiscaRepasse NFR ON NFR.NF_Id = GNF.GNF_IdNotaFiscal
                       WHERE GNF_Id = @IdGuiaNotaFiscal";

      return Contexto.Database.SqlQuery<GuiaNotaFiscalDelete>(query, new SqlParameter("@IdGuiaNotaFiscal", IdGuiaNotaFiscal)).FirstOrDefault();
    }

    public List<GuiaNotaFiscalPreCreate> GetGuiaNotaFiscalPreCreate(List<int> ListaIdGuiasAtendimento)
    {
      string query = @"SELECT
                        GA.GA_Id [IdGuia],
                        PGA.PGA_Id [IdProcGuia],
                        PGA.GA_ValorFaturado [ValorFaturado]
                       FROM R_GuiaAtendimento GA
                       INNER JOIN R_ProcGuiaAtendimento PGA ON GA.GA_Id = PGA.PGA_IdGuiaAtendimento AND GA.GA_Id IN (#IdsGuias#)";

      query = query.Replace("#IdsGuias#", string.Join(",", ListaIdGuiasAtendimento.ToArray()));

      return Contexto.Database.SqlQuery<GuiaNotaFiscalPreCreate>(query).ToList();
    }

    public List<GuiaNotaFiscalPreCreateByPlanilha> GetGuiaNotaFiscalPreCreateByPlanilha(List<string> ListNroUnicooper)
    {
      string query = @"SELECT
                        DISTINCT PGA.PGA_Id [IdProcGuia],
                        GA.GA_Id [IdGuia],
                        PGD.PGD_TotalFaturado [ValorFaturado],
                        PGD.PGD_TotalPago [ValorPago],
                        PGD.PGD_TotalApresentado [ValorApresentado],
                        PGD.PGD_TotalGlosado [ValorGlosado],
                        GNF.GNF_Id [IdGuiaNotafiscal]
                       FROM R_GuiaAtendimento GA
                       INNER JOIN R_ProcGuiaAtendimento PGA ON PGA.PGA_IdGuiaAtendimento = GA.GA_Id AND GA.GA_NroUnicooper IN (#NroUnicooperGuias#)
                       LEFT JOIN R_ProcGuiaDemonstrativo PGD ON PGD.PGD_IdProcGuiaAtendimento = PGA.PGA_Id
                       LEFT JOIN R_GuiaNotaFiscal GNF ON GNF.GNF_IdGuiaAtendimento = GA.GA_Id
                       LEFT JOIN R_NotaFiscaRepasse NFR ON NFR.NF_Id = GNF.GNF_IdNotaFiscal
                       LEFT JOIN R_Repasse R ON R.R_Id = NFR.NF_IdRepasse
                       LEFT JOIN R_StatusRepasse SR ON SR.SR_Id = R.R_IdStatusRepasse
                       WHERE (SR.SR_Enum = @Elaboracao OR SR.SR_Enum = @Conferencia OR SR.SR_Enum IS NULL) AND (GNF.GNF_Id IS NULL)";

      query = query.Replace("#NroUnicooperGuias#", string.Join(",", ListNroUnicooper.ToArray()));
      return Contexto.Database.SqlQuery<GuiaNotaFiscalPreCreateByPlanilha>(query, new SqlParameter("@Elaboracao", (int)EnumStatusRepasse.ELABORACAO)
                                                                                , new SqlParameter("@Conferencia", (int)EnumStatusRepasse.EMCONFERENCIA)).ToList();
    }

    public List<GuiaNotaFiscalIndex> GetGuiaNotaFiscalIndex(string NumeroLote, string NumeroGuia, int IdNotaFiscal)
    {
      string query = string.Empty;
      string innerjoin = string.Empty;
      string orderby = " Order By ga.GA_NroUnicooper";
      string where = " WHERE PGA.PGA_Id NOT IN (SELECT GNF_IdProcGuiaAtendimento FROM R_GuiaNotaFiscal WHERE GNF_IdNotaFiscal = @IdNotaFiscal)";

      query = @"SELECT
                   GA.GA_Id [IdGuia],
                   GA.GA_NroUnicooper [NroUnicooper],
                   PGA.PGA_CodProcedimento [CodProcedimento],
                   PGA.PGA_DescProcedimento [DescProcedimento],
                   PGA.GA_ValorFaturado [ValorFaturado]
                 FROM R_Lote L ";

      if (!string.IsNullOrEmpty(NumeroLote) && !string.IsNullOrEmpty(NumeroGuia))
        innerjoin = @"INNER JOIN R_ItensLote IL ON IL.IL_IdLote = L.L_Id AND L.L_NumeroLote = @NumeroLote  AND IL.IL_NumeroGuia = @NumeroGuia
                  INNER JOIN R_GuiaAtendimento GA ON GA.GA_Id = IL.IL_IdGuiaAtendimento 
                  INNER JOIN R_ProcGuiaAtendimento PGA ON PGA.PGA_IdGuiaAtendimento = GA.GA_Id";

      else if (!string.IsNullOrEmpty(NumeroLote) && string.IsNullOrEmpty(NumeroGuia))
        innerjoin = @"INNER JOIN R_ItensLote IL ON IL.IL_IdLote = L.L_Id AND L.L_NumeroLote = @NumeroLote
                  INNER JOIN R_GuiaAtendimento GA ON GA.GA_Id = IL.IL_IdGuiaAtendimento
                  INNER JOIN R_ProcGuiaAtendimento PGA ON PGA.PGA_IdGuiaAtendimento = GA.GA_Id";

      else if (string.IsNullOrEmpty(NumeroLote) && !string.IsNullOrEmpty(NumeroGuia))
        innerjoin = @"INNER JOIN R_ItensLote IL ON IL.IL_IdLote = L.L_Id AND IL.IL_NumeroGuia = @NumeroGuia
                  INNER JOIN R_GuiaAtendimento GA ON GA.GA_Id = IL.IL_IdGuiaAtendimento
                  INNER JOIN R_ProcGuiaAtendimento PGA ON PGA.PGA_IdGuiaAtendimento = GA.GA_Id";
      else
        return new List<GuiaNotaFiscalIndex>();

      query += innerjoin + where + orderby;

      return Contexto.Database.SqlQuery<GuiaNotaFiscalIndex>(query, new SqlParameter("@NumeroGuia", NumeroGuia)
                                                                  , new SqlParameter("@NumeroLote", NumeroLote)
                                                                  , new SqlParameter("@IdNotaFiscal", IdNotaFiscal)).ToList();
    }

    public void ValidaExistLoteOrGuia(string NumeroGuia, string NumeroLote)
    {
      if (!string.IsNullOrEmpty(NumeroLote))
      {
        LoteServices loteServices = new LoteServices();
        R_Lote lote = loteServices.GetByNumeroLote(NumeroLote);
        if (lote == null)
          throw new CustomException("Lote não encontrado, deseja buscar no portal?");
      }
      else if (string.IsNullOrEmpty(NumeroLote) && !string.IsNullOrEmpty(NumeroGuia))
      {
        GuiaAtendimentoService guiaAtendimentoService = new GuiaAtendimentoService();
        R_GuiaAtendimento guiaAtendimento = guiaAtendimentoService.GetByNroUnicooperAtendimento(NumeroGuia);
        if (guiaAtendimento == null)
          throw new CustomException("Guia não encontrada, deseja buscar no portal?");
      }
    }

    public void IntegraLoteGuia(string NumeroGuia, string NumeroLote, int IdRepasse)
    {
      IntegraRepasse integraRepasse = new IntegraRepasse();
      RepasseService repasseService = new RepasseService(User);
      RetornoGetGuiaLoteRepasse retornoGetGuiaLoteRepasse = new RetornoGetGuiaLoteRepasse();
      GuiaAtendimentoService guiaAtendimentoService = new GuiaAtendimentoService(User);
      string CNPJConvenio = repasseService.GetCNPJConvenioByIdRepasse(IdRepasse);

      if (!string.IsNullOrEmpty(NumeroLote))
      {
        IntegraLoteRepasse integraLoteRepasse = new IntegraLoteRepasse();
        integraLoteRepasse.NroLote = Convert.ToInt32(NumeroLote);
        integraLoteRepasse.CNPJConvenio = CNPJConvenio;
        retornoGetGuiaLoteRepasse = integraRepasse.GetLoteRepasse(integraLoteRepasse);

        if (!retornoGetGuiaLoteRepasse.Erro)
        {
          if (retornoGetGuiaLoteRepasse.loteRepasse.ListaGuiasRepasse.Count() == 0)
            throw new CustomException("Nenhum lote encontrado.");
          guiaAtendimentoService.IntegraGuias(retornoGetGuiaLoteRepasse);
        }
        else
        {
          if (retornoGetGuiaLoteRepasse.ErroPersonalizado)
            throw new CustomException(retornoGetGuiaLoteRepasse.Mensagem);
          else
            throw new CustomException("Ocorreu um erro na sua solicitação, gentileza tentar novamente mais tarde.");
        }
      }
      else if (string.IsNullOrEmpty(NumeroLote) && !string.IsNullOrEmpty(NumeroGuia))
      {
        IntegraGuiaRepasse integraGuiaRepasse = new IntegraGuiaRepasse();
        integraGuiaRepasse.NroUnicooper = NumeroGuia;
        integraGuiaRepasse.CNPJConvenio = CNPJConvenio;
        retornoGetGuiaLoteRepasse = integraRepasse.GetGuiaLoteRepasse(integraGuiaRepasse);

        if (!retornoGetGuiaLoteRepasse.Erro)
        {
          if (retornoGetGuiaLoteRepasse.loteRepasse.ListaGuiasRepasse.Count() == 0)
            throw new CustomException("Nenhuma guia encontrada");

          guiaAtendimentoService.IntegraGuias(retornoGetGuiaLoteRepasse);
        }
        else
        {
          if (retornoGetGuiaLoteRepasse.ErroPersonalizado)
            throw new CustomException(retornoGetGuiaLoteRepasse.Mensagem);
          else
            throw new CustomException("Ocorreu um erro na sua solicitação, gentileza tentar novamente mais tarde.");
        }
      }
    }

    public void IntegraByPlanilha(List<GuiaDemonstrativoIntegraCabecalho> ListaGuiaDemonstrativoIntegraCabecalho, R_NotaFiscaRepasse notaFiscaRepasse, int IdRepasse)
    {
      CreateByPlanilha(ListaGuiaDemonstrativoIntegraCabecalho, notaFiscaRepasse, IdRepasse);
    }

    public int GetQuantidadeGuiasNotaFiscalByIdRepasse(int IdRepasse)
    {
      string query = @"SELECT
                        COUNT(1)
                       FROM R_NotaFiscaRepasse NFR
                       INNER JOIN R_GuiaNotaFiscal GNF ON GNF.GNF_IdNotaFiscal = NFR.NF_Id AND NFR.NF_IdRepasse = @IdRepasse";

      return Contexto.Database.SqlQuery<int>(query, new SqlParameter("@IdRepasse", IdRepasse)).FirstOrDefault();
    }

    public List<GuiaNotaFiscalEdit> GetGuiaNotaFiscalEdit(int NumeroNotaFiscal)
    {
      string query = @"SELECT
                   GNF.GNF_Id [Id],
                   GA.GA_Id [IdGuia],
                   GA.GA_NroUnicooper [NroUnicooper],
                   PGA.PGA_CodProcedimento [CodProcedimento],
                   PGA.PGA_DescProcedimento [DescProcedimento],
                   GNF.GNF_ValorFaturado [ValorFaturado],
                   GNF.GNF_ValorApresentado [ValorApresentado],
                   GNF.GNF_ValorGlosado [ValorGlosado],
                   GNF.GNF_ValorPago [ValorPago]
                FROM R_GuiaNotaFiscal GNF
                INNER JOIN R_GuiaAtendimento GA ON GA.GA_Id = GNF.GNF_IdGuiaAtendimento  AND GNF.GNF_IdNotaFiscal = @NumeroNotaFiscal
                INNER JOIN R_ProcGuiaAtendimento PGA ON PGA.PGA_IdGuiaAtendimento = GA.GA_Id";

      return Contexto.Database.SqlQuery<GuiaNotaFiscalEdit>(query, new SqlParameter("@NumeroNotaFiscal", NumeroNotaFiscal)).ToList();
    }

    public List<R_GuiaNotaFiscal> GetAllByIdNotaFiscal(int IdNotaFiscal)
    {
      string query = @"SELECT
                         *
                        FROM R_GuiaNotaFiscal
                        WHERE GNF_IdNotaFiscal = @IdNotaFiscal";

      return Contexto.Database.SqlQuery<R_GuiaNotaFiscal>(query, new SqlParameter("@IdNotaFiscal", IdNotaFiscal)).ToList();
    }
  }
}
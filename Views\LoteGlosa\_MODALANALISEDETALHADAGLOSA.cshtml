﻿@using RepasseConvenio.Models
@using RepasseConvenio.Infrastructure.Controls
@model AnaliseDetalhada

<div class="modal fade" id="ModalAnaliseDetalhadaGlosa" data-keyboard="false" data-backdrop="static">
  <div class="modal-dialog" style="max-width: 99%;">
    <div class="modal-content">
      <div class="modal-header">
        <div class="row" style="display: flex; width: 100%; align-items: center; justify-content: space-around;">
          <div class="col-md-4">
            <div class="form-group">
              @Html.LabelFor(m => m.JustificativaAnaliseDetalhadaSelect)
              <br />
              @Html.LibSelect2For(m => m.JustificativaAnaliseDetalhadaSelect, new { @class = "form-control" })
            </div>
          </div>
          <div class="col-md-4">
            <div class="form-group">
              @Html.LabelFor(m => m.ComentarioAnaliseDetalhada)
              @Html.TextAreaFor(m => m.ComentarioAnaliseDetalhada, new { @class = "form-control" })
            </div>
          </div>
          <div class="col-md-2">
              <button class="btn btn-outline-primary SalvarJustificativaDetalhada">Justificar</button>
              <button class="btn btn-outline-primary AceitarGlosaDetalhada">Aceitar</button>
          </div>
          <div class="col-md-1">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">×</span>
            </button>
          </div>
        </div>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-md-12 container">
            <div id="PartialGuiasAtendimento">
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
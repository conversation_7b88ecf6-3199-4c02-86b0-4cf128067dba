﻿@using RepasseConvenio.Models
<div class="modal fade" id="ModalProcessarLoteGlosa" data-keyboard="false" data-backdrop="static">
  <div class="modal-dialog" style="max-width: 35%;">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">Selecione a Data Crédito da Fatura</h4>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">×</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-md-12 container">
            <div id="PartialProcessarLoteGlosa">
              @Html.Partial("_GridProcessamentoGlosa", new ProcessaGlosaRepasse())
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" id="SubmitProcessaGlosa" class="btn btn-primary">Processar Glosa</button>
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Fechar</button>
      </div>
    </div>
  </div>
</div>
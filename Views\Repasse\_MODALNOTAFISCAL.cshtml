﻿@using RepasseConvenio.Models
@*Modal Nota Fiscal*@
<div class="modal fade" id="ModalNotaFiscal" data-keyboard="false" data-backdrop="static">
  <div class="modal-dialog" style="max-width: 85%;">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">Nota Fiscal</h4>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">×</span>
        </button>
      </div>
      <div class="modal-body" style="height: calc(100vh - 150px); overflow-x: auto;">
        <div class="row">
          <div class="col-md-12 container">
            <div id="PartialNotaFiscal">
              @Html.Partial("_PartialNotaFiscal", new NotaFiscalRepasseModel())
            </div>
          </div>
        </div>
      </div>
      <div class="box-footer" style="margin-top: 15px;padding: 20px;">
        <button type="button" class="btn btn-outline-dark pull-left" data-dismiss="modal">Cancelar</button>
        <button type="button" value="Create" class="btn btn-info pull-right btnCreateEditNota" id="btnCreateNotaFiscal">Salvar</button>
        <button type="button" value="InserirGuias" class="btn btn-info pull-right" id="btnInserirGuias" style="display:none;">Adicionar Guias</button>
    </div>
  </div>
</div>
  </div>

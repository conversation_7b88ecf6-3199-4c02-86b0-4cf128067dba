﻿@using RepasseConvenio.Models
@model List<ItensRecorrentesIndex>

@{
  ViewBag.Title = "Lista Itens Recorrentes";
}

@Scripts.Render("~/Views/ItensRecorrentes/ItensRecorrentes.js?lp=tgb")

<div class="row">
  <section class="col-lg-12 connectedSortable ui-sortable">
    <div class="card">
      <div class="card-header ui-sortable-handle card-header-sm">
        <h3 class="card-title">
          <i class="fas fa-paperclip mr-1"></i>
          Lançamentos Recorrentes - Médico(a): @ViewBag.NomeMedico
        </h3>
        <div class="card-tools">
          <button type="button" value="Create" class="btn btn-primary btn-sm-header" onclick="location.href='@Url.Action("Create", "ItensRecorrentes", new {id = @ViewBag.CodigoMedico})'">Novo</button>
        </div>
      </div><!-- /.card-header -->
      <div class="card-body">
        <div class="tab-content p-0">
          <div class="box-body">

            <div class="row">
              <div class="col-md-12" id="GridExtratos">
                @Html.Partial("_GridItensRecorrentes", Model)
              </div>
            </div>

          </div>
        </div>
        <div class="box-footer">
          <button type="button" value="Voltar" class="btn btn-secondary" onclick="location.href='@Url.Action("Index", "Medico")'">Cancelar</button>
        </div>
      </div>
    </div>
  </section>
</div>

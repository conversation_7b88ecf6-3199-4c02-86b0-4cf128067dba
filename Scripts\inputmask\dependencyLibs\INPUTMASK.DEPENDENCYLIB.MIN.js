/*!
* dependencyLibs/inputmask.dependencyLib.min.js
* https://github.com/RobinHerbots/Inputmask
* Copyright (c) 2010 - 2017 <PERSON>
* Licensed under the MIT license (http://www.opensource.org/licenses/mit-license.php)
* Version: 3.3.7
*/

!function(a){"function"==typeof define&&define.amd?define(["../global/window","../global/document"],a):"object"==typeof exports?module.exports=a(require("../global/window"),require("../global/document")):window.dependencyLib=a(window,document)}(function(a,b){function c(a,b){for(var c=0,d=a.length;c<d;c++)if(a[c]===b)return c;return-1}function d(a){return null==a?a+"":"object"==typeof a||"function"==typeof a?i[i.toString.call(a)]||"object":typeof a}function e(a){return null!=a&&a===a.window}function f(a){var b="length"in a&&a.length,c=d(a);return"function"!==c&&!e(a)&&(!(1!==a.nodeType||!b)||("array"===c||0===b||"number"==typeof b&&b>0&&b-1 in a))}function g(a){return a instanceof Element}function h(c){return c instanceof h?c:this instanceof h?void(void 0!==c&&null!==c&&c!==a&&(this[0]=c.nodeName?c:void 0!==c[0]&&c[0].nodeName?c[0]:b.querySelector(c),void 0!==this[0]&&null!==this[0]&&(this[0].eventRegistry=this[0].eventRegistry||{}))):new h(c)}for(var i={},j="Boolean Number String Function Array Date RegExp Object Error".split(" "),k=0;k<j.length;k++)i["[object "+j[k]+"]"]=j[k].toLowerCase();return h.prototype={on:function(a,b){if(g(this[0]))for(var c=this[0].eventRegistry,d=this[0],e=a.split(" "),f=0;f<e.length;f++){var h=e[f].split("."),i=h[0],j=h[1]||"global";!function(a,e){d.addEventListener?d.addEventListener(a,b,!1):d.attachEvent&&d.attachEvent("on"+a,b),c[a]=c[a]||{},c[a][e]=c[a][e]||[],c[a][e].push(b)}(i,j)}return this},off:function(a,b){if(g(this[0]))for(var c=this[0].eventRegistry,d=this[0],e=a.split(" "),f=0;f<e.length;f++)for(var h=e[f].split("."),i=function(a,d){var e,f,g=[];if(a.length>0)if(void 0===b)for(e=0,f=c[a][d].length;e<f;e++)g.push({ev:a,namespace:d&&d.length>0?d:"global",handler:c[a][d][e]});else g.push({ev:a,namespace:d&&d.length>0?d:"global",handler:b});else if(d.length>0)for(var h in c)for(var i in c[h])if(i===d)if(void 0===b)for(e=0,f=c[h][i].length;e<f;e++)g.push({ev:h,namespace:i,handler:c[h][i][e]});else g.push({ev:h,namespace:i,handler:b});return g}(h[0],h[1]),j=0,k=i.length;j<k;j++)!function(a,b,e){if(a in c==1)if(d.removeEventListener?d.removeEventListener(a,e,!1):d.detachEvent&&d.detachEvent("on"+a,e),"global"===b)for(var f in c[a])c[a][f].splice(c[a][f].indexOf(e),1);else c[a][b].splice(c[a][b].indexOf(e),1)}(i[j].ev,i[j].namespace,i[j].handler);return this},trigger:function(a){if(g(this[0]))for(var c=this[0].eventRegistry,d=this[0],e="string"==typeof a?a.split(" "):[a.type],f=0;f<e.length;f++){var i=e[f].split("."),j=i[0],k=i[1]||"global";if(void 0!==b&&"global"===k){var l,m,n={bubbles:!0,cancelable:!0,detail:Array.prototype.slice.call(arguments,1)};if(b.createEvent){try{l=new CustomEvent(j,n)}catch(a){l=b.createEvent("CustomEvent"),l.initCustomEvent(j,n.bubbles,n.cancelable,n.detail)}a.type&&h.extend(l,a),d.dispatchEvent(l)}else l=b.createEventObject(),l.eventType=j,a.type&&h.extend(l,a),d.fireEvent("on"+l.eventType,l)}else if(void 0!==c[j])if(arguments[0]=arguments[0].type?arguments[0]:h.Event(arguments[0]),"global"===k)for(var o in c[j])for(m=0;m<c[j][o].length;m++)c[j][o][m].apply(d,arguments);else for(m=0;m<c[j][k].length;m++)c[j][k][m].apply(d,arguments)}return this},position:function(){if(g(this[0]))return{top:this[0].offsetTop,left:this[0].offsetLeft}}},h.isFunction=function(a){return"function"===d(a)},h.noop=function(){},h.isArray=Array.isArray,h.inArray=function(a,b,d){return null==b?-1:c(b,a)},h.valHooks=void 0,h.isPlainObject=function(a){return"object"===d(a)&&!a.nodeType&&!e(a)&&!(a.constructor&&!i.hasOwnProperty.call(a.constructor.prototype,"isPrototypeOf"))},h.extend=function(){var a,b,c,d,e,f,g=arguments[0]||{},i=1,j=arguments.length,k=!1;for("boolean"==typeof g&&(k=g,g=arguments[i]||{},i++),"object"==typeof g||h.isFunction(g)||(g={}),i===j&&(g=this,i--);i<j;i++)if(null!=(a=arguments[i]))for(b in a)c=g[b],d=a[b],g!==d&&(k&&d&&(h.isPlainObject(d)||(e=h.isArray(d)))?(e?(e=!1,f=c&&h.isArray(c)?c:[]):f=c&&h.isPlainObject(c)?c:{},g[b]=h.extend(k,f,d)):void 0!==d&&(g[b]=d));return g},h.each=function(a,b){var c=0;if(f(a))for(var d=a.length;c<d&&!1!==b.call(a[c],c,a[c]);c++);else for(c in a)if(!1===b.call(a[c],c,a[c]))break;return a},h.map=function(a,b){var c,d=0,e=a.length,g=f(a),h=[];if(g)for(;d<e;d++)null!=(c=b(a[d],d))&&h.push(c);else for(d in a)null!=(c=b(a[d],d))&&h.push(c);return[].concat(h)},h.data=function(a,b,c){if(void 0===c)return a.__data?a.__data[b]:null;a.__data=a.__data||{},a.__data[b]=c},h.Event=function a(c,d){d=d||{bubbles:!1,cancelable:!1,detail:void 0};var e;if(b.createEvent)try{e=new a(c,d)}catch(a){e=b.createEvent("CustomEvent"),e.initCustomEvent(c,d.bubbles,d.cancelable,d.detail)}else e=b.createEventObject(),e.eventType=c;return e},h.Event.prototype=a.Event.prototype,h});
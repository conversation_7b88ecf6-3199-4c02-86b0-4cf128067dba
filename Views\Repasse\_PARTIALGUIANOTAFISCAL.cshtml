﻿@using RepasseConvenio.Infrastructure.Controls
@using RepasseConvenio.Models
@model List<GuiaNotaFiscalIndex>

<div class="row" style="width: 100%;">
  <div class="col-md-12 table-responsive p-0 ">
    <table class="table table-sm table-striped table-hover text-nowrap">
      <thead>
        <tr>
          <th>
            @Html.CheckBox("CheckAllGuiaNotaFiscal")
          </th>
          <th>
            Nro Unicooper
          </th>
          <th>
            Código Procedimento
          </th>
          <th>
            Descrição Procedimento
          </th>
          <th>
            Faturado
          </th>
        </tr>
      </thead>
      <tbody>
        @foreach (GuiaNotaFiscalIndex item in Model)
        {
          <tr>
            <td>
              <input class="isSelectedPartialNotaFiscal" data-codigoGuia="@item.IdGuia" data-val="true" data-val-required="O campo isSelected é obrigatório." name="item.isSelected" type="checkbox" value="true">
            </td>
            <td>
              @item.NroUnicooper
            </td>
            <td>
              @item.CodProcedimento
            </td>
            <td>
              @item.DescProcedimento
            </td>
            <td>
              @item.ValorFaturado
            </td>
          </tr>
        }
      </tbody>
    </table>
  </div>

</div>



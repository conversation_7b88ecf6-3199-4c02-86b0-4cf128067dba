﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using PagedList;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RepasseConvenio.Services
{
  public class StatusRepasseServices : ServiceBase
  {
    public StatusRepasseServices(RepasseEntities Contexto)
        : base(Contexto)
    { }
    public StatusRepasseServices()
       : base()
    { }

    public StatusRepasseServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public StatusRepasseServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public int GetIdByEnum(EnumStatusRepasse enumStatus)
    {
      return Contexto.R_StatusRepasse.Where(a => a.SR_Enum == (int)enumStatus).Select(a => a.SR_Id).FirstOrDefault();
    }
    public R_StatusRepasse GetByEnum(EnumStatusRepasse enumStatus)
    {
      return Contexto.R_StatusRepasse.Where(a => a.SR_Enum == (int)enumStatus).FirstOrDefault();
    }

    public R_StatusRepasse GetById(int Id)
    {
      return Contexto.R_StatusRepasse.Where(a => a.SR_Id == Id).FirstOrDefault();
    }

    public List<R_StatusRepasse> GetByTerm(string term)
    {
      if (string.IsNullOrEmpty(term))
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_StatusRepasse");

        return Contexto.Database.SqlQuery<R_StatusRepasse>(query).ToList();

      }
      else
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_StatusRepasse
                                       WHERE SR_Descricao LIKE @termo");

        return Contexto.Database.SqlQuery<R_StatusRepasse>(query, new SqlParameter("@termo", string.Format("%{0}%", term))).ToList();

      }
    }
  }
}


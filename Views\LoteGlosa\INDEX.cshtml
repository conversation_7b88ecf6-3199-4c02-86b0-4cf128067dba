﻿@using RepasseConvenio.Models
@using RepasseConvenio.Infrastructure
@using RepasseConvenio.Infrastructure.Controls
@model LoteGlosaFiltroModel

@{
  ViewBag.Title = "Lote Glosa";
}

@Scripts.Render("~/Views/LoteGlosa/LoteGlosa.js?a=a")
@Styles.Render("~/Views/LoteGlosa/LoteGlosa.css?a=a")

@Html.Hidden("CodigoLoteGlosa")

@using (Html.BeginForm("Index", "LoteGlosa", FormMethod.Post))
{
  <div class="row">
    <section class="col-lg-12 connectedSortable ui-sortable">
      <div class="card">
        <div class="card-header ui-sortable-handle">
          <h3 class="card-title">
            <i class="fas fa-filter"></i>
            Filtro
          </h3>
          <div class="card-tools">
            <a class="nav-link active"></a>
          </div>
        </div><!-- /.card-header -->
        <div class="card-body">
          <div class="row">
            <div class="col-md-3">
              <div class="form-group">
                @Html.LabelFor(m => m.ConvenioSelect)
                @Html.LibSelect2For(m => m.ConvenioSelect, new { @class = "form-control" })
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                @Html.LabelFor(m => m.StatusGlosaSelect)
                @Html.LibSelect2For(m => m.StatusGlosaSelect, new { @class = "form-control" })
              </div>
            </div>
            <div class="col-md-1">
                <button type="submit" class="btn btn-info pull-rigth" style="margin-top: 18px;">Buscar</button>
            </div>
          </div>
        </div>
          </div>
    </section>
  </div>
}

<div class="row">
  <section class="col-lg-12 connectedSortable ui-sortable">
    <div class="card">
      <div class="card-header ui-sortable-handle">
        <h3 class="card-title">
          <i class="fas fa-chart-pie mr-1"></i>
          Lote de Glosas
        </h3>
        <div class="card-tools">
          <div class="card-acoes">
            <a class="btn btn-outline-info disabled" disabled style="margin-left: 10px" id="GerarXML" download>XML de Recurso</a>
            <a class="btn btn-outline-info disabled" disabled style="margin-left: 10px" id="GerarRelatorio" download>Carta de Recurso</a>
            <button type="button" class="btn btn-outline-info disabled" style="margin-left: 10px" disabled id="LogsLoteGlosa">Logs</button>
            <button type="button" class="btn btn-outline-info disabled" disabled style="margin-left: 10px" id="TransferirResponsavel">Transf. Resp.</button>
            <button type="button" class="btn btn-outline-info disabled" disabled style="margin-left: 10px" id="EnviarConvenioIndex">Enviar Convênio</button>
            <button type="button" class="btn btn-outline-info disabled" disabled style="margin-left: 10px" id="DetalhesLoteGlosa">Detalhes</button>
          </div>
        </div>
      </div><!-- /.card-header -->
      <div class="card-body">
        <div class="tab-content p-0">
          <div class="box-body">
            <div class="row">
              <div class="col-md-12 table-responsive p-0 ">
                <table class="table table-sm table-striped table-hover text-nowrap">
                  <thead>
                    <tr>
                      <th>
                        Código
                      </th>
                      <th>
                        Convênio
                      </th>
                      <th>
                        Status
                      </th>
                      <th>
                        Fatura
                      </th>
                      <th>
                        Gerada em
                      </th>
                      <th>
                        Vencimento
                      </th>
                      <th>
                        Criador
                      </th>
                      <th>
                        Reapresentação
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    @foreach (LoteGlosaIndex item in Model.ListLoteGlosa)
                    {
                      <tr class="TrSelecionavel" data-codigoloteglosa="@item.CodigoGlosa" data-statusloteglosa="@Convert.ToInt32(item.StatusGlosaEnum)">
                        <td>
                          @item.Codigo
                        </td>
                        <td>
                          @item.RazaoSocialConvenio
                        </td>
                        <td>
                          @item.DescricaoStatus
                        </td>
                        <td>
                          @item.DataInicio.ToString("dd/MM/yyyy")
                        </td>
                        <td>
                          @item.DataCriacao.ToString("dd/MM/yyyy")
                        </td>
                        <td>
                          @item.DataVencimentoAnalise.ToString("dd/MM/yyyy")
                        </td>
                        <td>
                          @item.UsuarioCriacao
                        </td>
                        <td>
                          @if (item.DataReapresentacao.HasValue)
                          {
                            @item.DataReapresentacao.Value.ToString("dd/MM/yyyy")
                          }
                          else
                          {
                            <text> - </text>
                          }
                        </td>
                      </tr>
                    }
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div><!-- /.card-body -->
    </div>
  </section>
</div>

@Html.Partial("_ModalTransfRespon")
@Html.Partial("_ModalLogs")
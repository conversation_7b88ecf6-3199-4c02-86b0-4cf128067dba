﻿@using RepasseConvenio.Models
@using PagedList
@using PagedList.Mvc;
@model IPagedList<RegistroPagamentoModel>

@{
  ViewBag.Title = "Lista Registro Pagamento";
}
<!-- /.card-header -->
<section class="col-lg-12 connectedSortable ui-sortable">
  <div class="box-body">
    <div class="col-md-12 table-responsive p-0">
      <table class="table table-sm table-striped table-hover text-nowrap">
        <thead>
          <tr>
            <th>
              @Html.CheckBox("CheckAllRegPagamento")
            </th>
            <th>
              Data Geração
            </th>
            <th>
              Médico
            </th>
            <th>
              CPF/CNPJ
            </th>
            <th>
              Banco
            </th>
            <th>
              Agência
            </th>
            <th>
              Conta
            </th>
            <th>
              Valor
            </th>
            <th>
              Data Pagamento
            </th>
            <th>
              Status
            </th>
            <th>
            </th>
          </tr>
        </thead>
        <tbody>
          @foreach (RegistroPagamentoModel item in Model)
          {
          <tr>
            <td>
              <input class="isSelected" data-codigoregistropagamento="@item.Codigo" type="checkbox">
            </td>
            <td>
              @item.DataGeracao.ToString("dd/MM/yyyy")
            </td>
            <td>
              @item.NomeMedico
            </td>
            <td>
              @item.CPFCNPJ
            </td>
            <td>
              @item.Banco
            </td>
            <td>
              @item.Agencia
            </td>
            <td>
              @item.Conta
            </td>
            <td>
              @item.Valor
            </td>
            <td>
              @if (item.DataPagamento.HasValue)
              {
                <text>
                  @item.DataPagamento.Value.ToString("dd/MM/yyyy")
                </text>
              }
            </td>
            <td>
              @item.DescricaoStatusRegistroPagamento
            </td>
            <td>
              <a class="btn btn-sm btn-info" id="DetalhesRegistro" data-idregistro="@item.Codigo"><i class="fa fa-info"></i></a>
            </td>
          </tr>
          }
        </tbody>
      </table>
    </div>
    <div class="box-footer with-border" style="padding: 0 10pt;">
      <div class="row">
        <div class="col-md-8 ">
          @Html.PagedListPager(Model, pagina => Url.Action("Index", new { pagina }))
        </div>
      </div>
    </div>
  </div>
</section>

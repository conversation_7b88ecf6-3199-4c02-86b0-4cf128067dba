﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Models;
using RepasseConvenio.WSPortalCooperado;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.Entity;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Text;
using System.Transactions;
using System.Web;
using System.Data.OleDb;
using RepasseConvenio.Infrastructure;
using Microsoft.Ajax.Utilities;
using Org.BouncyCastle.Bcpg.OpenPgp;
using System.Text.RegularExpressions;

namespace RepasseConvenio.Services
{
  public class ItensLoteGlosaServices : ServiceBase
  {
    public ItensLoteGlosaServices()
   : base()
    { }
    public ItensLoteGlosaServices(RepasseEntities Contexto)
       : base(Contexto)
    { }

    public ItensLoteGlosaServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public ItensLoteGlosaServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    #region[GETs]
    public List<ItensLoteGlosaIndex> GetItensLoteGlosaIndex(int IdLoteGlosa)
    {
      LoteGlosaServices loteGlosaServices = new LoteGlosaServices(User);
      R_LoteGlosa loteGlosa = loteGlosaServices.GetById(IdLoteGlosa);

      if (User.ConveniosResponsavel.Where(a => a.IdConvenio == loteGlosa.LG_IdConvenio && a.IdResponsavelConvenio == loteGlosa.LG_IdResponsavelConvenio).Count() == 0)
        throw new CustomException("Você não pode acessar esse lote");

      string query = @"SELECT
                        PGD.PGD_Id [CodigoProcDemonstrativo],
                        GD.GD_NumeroGuia [NumeroGuia],
                        PGD.PGD_CodigoGlosa [CodigoGlosa],
                        PGD.PGD_DescricaoGlosa [DescricaoGlosa],
                        PGD.PGD_TotalApresentado [TotalApresentado],
                        PGD.PGD_TotalFaturado [TotalFaturado],
                        PGD.PGD_TotalGlosado [TotalGlosado],
                        PGD.PGD_TotalPago [TotalPago],
                        PGD.PGD_CodigoProcedimento [CodigoProcedimento],
                        PGD.PGD_DescricaoProcedimento [DescricaoProcedimento],
						            JG.JG_Descricao [Justificativa],
                        GD.GD_IdGuia [CodigoGuiaAtendimento],
                        SPGD.SPGD_Descricao [DescricaoStatus]
                       FROM R_ItensLoteGlosa ILG
                       INNER JOIN R_GuiaDemonstrativo GD ON GD.GD_Id = ILG.ILG_IdGuiaDemonstrativo AND ILG.ILG_IdLoteGlosa = @IdLoteGlosa
                       INNER JOIN R_ProcGuiaDemonstrativo PGD ON PGD.PGD_IdGuiaDemonstrativo = GD.GD_Id
                       INNER JOIN R_StausProcGuiaDemonstrativo SPGD ON SPGD.SPGD_Id = PGD.PGD_IdStatusProcGuiaDemonstrativo AND (SPGD.SPGD_Enum = @EnumGlosado OR SPGD.SPGD_Enum = @EnumJustificado OR SPGD.SPGD_Enum = @EnumAceito)
					             LEFT JOIN R_JustificativaGlosa JG ON PGD.PGD_IdJustificativa=JG.JG_Id";

      List<ItensLoteGlosaIndex> ListaItensLoteGlosaIndex = Contexto.Database.SqlQuery<ItensLoteGlosaIndex>(query, new SqlParameter("@IdLoteGlosa", IdLoteGlosa)
                                                                  , new SqlParameter("@EnumGlosado", (int)StatusProcGuiaDemonstrativoEnum.Glosado)
                                                                  , new SqlParameter("@EnumJustificado", (int)StatusProcGuiaDemonstrativoEnum.Justificada)
                                                                  , new SqlParameter("@EnumAceito", (int)StatusProcGuiaDemonstrativoEnum.GlosaAceita)).ToList();

      if (ListaItensLoteGlosaIndex.Count() == 0)
        throw new CustomException("Lote sem itens para análisar");

      return ListaItensLoteGlosaIndex;
    }

    public List<StatusProcGuiaDemonstrativoEnum> GetStatusProcGuiaDemonstrativo(int IdLoteGlosa)
    {
      string query = @"SELECT
                        SPGD.SPGD_Enum
                       FROM R_ItensLoteGlosa ILG
                       INNER JOIN R_GuiaDemonstrativo GD ON GD.GD_Id = ILG.ILG_IdGuiaDemonstrativo AND ILG.ILG_IdLoteGlosa = @IdLoteGlosa
                       INNER JOIN R_ProcGuiaDemonstrativo PGD ON PGD.PGD_IdGuiaDemonstrativo = GD.GD_Id
                       INNER JOIN R_StausProcGuiaDemonstrativo SPGD ON SPGD.SPGD_Id = PGD.PGD_IdStatusProcGuiaDemonstrativo";

      return Contexto.Database.SqlQuery<StatusProcGuiaDemonstrativoEnum>(query, new SqlParameter("@IdLoteGlosa", IdLoteGlosa)).ToList();
    }

    public int GetQuantidadeItensNaoJustificados(int IdLoteGlosa)
    {
      string query = @"SELECT
                        Count(1)
                       FROM R_ItensLoteGlosa ILG
                       INNER JOIN R_GuiaDemonstrativo GD ON GD.GD_Id = ILG.ILG_IdGuiaDemonstrativo AND ILG.ILG_IdLoteGlosa = @IdLoteGlosa
                       INNER JOIN R_ProcGuiaDemonstrativo PGD ON PGD.PGD_IdGuiaDemonstrativo = GD.GD_Id AND PGD.PGD_IdJustificativa IS NULL
                       INNER JOIN R_StausProcGuiaDemonstrativo SPGD ON SPGD.SPGD_Id = PGD.PGD_IdStatusProcGuiaDemonstrativo AND SPGD.SPGD_Enum = @EnumGlosado";

      return Contexto.Database.SqlQuery<int>(query, new SqlParameter("@IdLoteGlosa", IdLoteGlosa)
                                                                 , new SqlParameter("@EnumGlosado", (int)StatusProcGuiaDemonstrativoEnum.Glosado)).FirstOrDefault();

    }

    public int GetQuantidadeItensGlosados(int IdLoteGlosa)
    {

      string query = @" SELECT
                  Count(1)
                 FROM R_ItensLoteGlosa ILG
                 INNER JOIN R_GuiaDemonstrativo GD ON GD.GD_Id = ILG.ILG_IdGuiaDemonstrativo AND ILG.ILG_IdLoteGlosa = @IdLoteGlosa
                 INNER JOIN R_ProcGuiaDemonstrativo PGD ON PGD.PGD_IdGuiaDemonstrativo = GD.GD_Id
                 INNER JOIN R_StausProcGuiaDemonstrativo SPGD ON SPGD.SPGD_Id = PGD.PGD_IdStatusProcGuiaDemonstrativo AND (SPGD.SPGD_Enum = @EnumGlosado OR SPGD.SPGD_Enum = @EnumJustificado)";

      return Contexto.Database.SqlQuery<int>(query, new SqlParameter("@IdLoteGlosa", IdLoteGlosa)
                                                  , new SqlParameter("@EnumGlosado", (int)StatusProcGuiaDemonstrativoEnum.Glosado)
                                                  , new SqlParameter("@EnumJustificado", (int)StatusProcGuiaDemonstrativoEnum.Justificada)).FirstOrDefault();

    }

    public bool HaveJustificativaPendente(int IdLoteGlosa)
    {
      int QuantidadeItensNaoJustificados = GetQuantidadeItensNaoJustificados(IdLoteGlosa);
      int QuantidadeItensGlosados = GetQuantidadeItensGlosados(IdLoteGlosa);

      if (QuantidadeItensNaoJustificados == 0 && QuantidadeItensGlosados > 0)
        return false;
      else
        return true;
    }

    public List<ItensLoteMotivoGlosaJustificativa> GetListItensLoteMotivoGlosaJustificativa(List<int> ListIdProcGuiaDemonstrativo, int IdLoteGlosa)
    {
      string query = @"SELECT
                        PGD.PGD_CodigoGlosa [CodigoGlosa],
                        JG.JG_Codigo [CodigoJustificativa]
                       FROM R_ItensLoteGlosa ILG
                       INNER JOIN R_GuiaDemonstrativo GD ON GD.GD_Id = ILG.ILG_IdGuiaDemonstrativo AND ILG.ILG_IdLoteGlosa = @IdLoteGlosa
                       INNER JOIN R_ProcGuiaDemonstrativo PGD ON PGD.PGD_IdGuiaDemonstrativo = GD.GD_Id AND PGD.PGD_IdJustificativa IS NOT NULL AND PGD.PGD_Id IN (#ids#)
                       INNER JOIN R_JustificativaGlosa JG ON JG.JG_Id = PGD.PGD_IdJustificativa";

      query = query.Replace("#ids#", string.Join(",", ListIdProcGuiaDemonstrativo.ToArray()));
      return Contexto.Database.SqlQuery<ItensLoteMotivoGlosaJustificativa>(query, new SqlParameter("@IdLoteGlosa", IdLoteGlosa)).ToList();
    }


    public List<int> ListaIdsGuiaAtendimento(int IdLoteGlosa)
    {
      string query = @"SELECT
                        ILG_IdGuiaAtendimento
                       FROM R_ItensLoteGlosa ILG
                       WHERE ILG_IdLoteGlosa = @IdLoteGlosa";

      return Contexto.Database.SqlQuery<int>(query, new SqlParameter("@IdLoteGlosa", IdLoteGlosa)).ToList();
    }

    public List<int> ListaIdsGuiaAtendimento(List<int> IdsProcGuiaDemonstrativo)
    {
      string query = @"SELECT
                        GD.GD_IdGuia 
                       FROM R_ProcGuiaDemonstrativo PGD
                       INNER JOIN R_GuiaDemonstrativo GD ON GD.GD_Id = PGD.PGD_IdGuiaDemonstrativo AND PGD_Id in (#ids#)";

      query = query.Replace("#ids#", string.Join(",", IdsProcGuiaDemonstrativo.ToArray()));
      return Contexto.Database.SqlQuery<int>(query).ToList();
    }
    #endregion


  }
}
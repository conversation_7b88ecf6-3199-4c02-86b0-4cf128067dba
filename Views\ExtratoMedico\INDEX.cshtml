﻿@using RepasseConvenio.Models
@using RepasseConvenio.Infrastructure
@using RepasseConvenio.Infrastructure.Controls
@model ExtratoMedicoFiltroIndex

@{
  ViewBag.Title = "Lista Extrato Médico";
}

@Scripts.Render("~/Views/ExtratoMedico/ExtratoMedico.js?lp=tgb")
@Styles.Render("~/Views/ExtratoMedico/ExtratoMedico.css?lp=tgb")

<style>
  .card-header > .card-tools {
    margin-right: 1.375rem !important;
  }
  .input-group-sm {
    width: 180px !important;
    margin-bottom: 0px !important;
    margin-top: 0px !important;
    padding: 0px;
    margin: 0px;
  }
  .input-seach {
    height: 2rem !important;
  }
</style>

@*@using (Html.BeginForm("Index", "ExtratoMedico", FormMethod.Post))
{
  <div class="row">
    <section class="col-lg-12 connectedSortable ui-sortable">
      <div class="card">
        <div class="card-header ui-sortable-handle">
          <h3 class="card-title">
            <i class="fas fa-filter"></i>
            Filtro
          </h3>
          <div class="card-tools">
            <a class="nav-link active"></a>
          </div>
        </div>
        <div class="card-body">
          @Html.HiddenFor(a=>a.IdMedico)
          <div class="row">

            <div class="col-md-3">
              <div class="form-group">
                @Html.LabelFor(m => m.Data)
                @Html.LibEditorFor(m => m.Data, new { @class = "form-control airDatePickerDate" })
                @Html.ValidationMessageFor(m => m.Data, "", new { @class = "text-danger" })
              </div>
            </div>
            <div class="col-md-1">
              <button type="submit" class="btn btn-info pull-rigth" style="margin-top: 18px;">Buscar</button>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
}*@
<div class="row">
  <section class="col-lg-12 connectedSortable ui-sortable">
    <div class="card">
      <div class="card-header ui-sortable-handle card-header-sm">
        <h3 class="card-title">
          <i class="fas fa-paperclip mr-1"></i>
          Extrato - Médico(a): @ViewBag.NomeMedico
        </h3>
        <div class="card-tools">
          <button type="button" value="Create" class="btn btn-primary btn-sm-header" onclick="location.href='@Url.Action("Create", "ExtratoMedico", new {id = @ViewBag.CodigoMedico})'">Novo</button>
        </div>
        <div class="card-tools">
          @using (Html.BeginForm("Index", "ExtratoMedico", FormMethod.Post))
          {
          @Html.HiddenFor(a=>a.IdMedico)
          <div class="input-group input-group-sm" style="width: 150px;margin-bottom: 0px !important;margin-top: 0px !important;">
            <input type="text" name="Data" id="Data" class="form-control float-right input-seach airDatePickerDate" placeholder="Data Pagamento" value="@Model.Data">

            <div class="input-group-append">
              <button type="submit" class="btn btn-default">
                <i class="fas fa-search"></i>
              </button>
            </div>
          </div>
          }
        </div>
      </div>
      <div class="card-tools" style=" display: flex; align-items: flex-end; margin-top: 8pt; padding-left: 1.25rem;">
        <a class="btn btn-block btn-outline-primary disabled" disabled id="Editar" style=" width: 100px; margin: 0pt 2pt;"> Editar </a>
        <a class="btn btn-block btn-outline-primary disabled" disabled id="Log" style=" width: 100px; margin: 0pt 2pt;"> Log </a>
        <a class="btn btn-block btn-outline-primary disabled" disabled id="Remover" style=" width: 100px; margin: 0pt 2pt;"> Remover </a>
      </div>
      <div class="card-body">
        <div class="tab-content p-0">
          <div class="box-body">

            <div class="row">
              <div class="col-md-12" id="GridExtratos">
                @Html.Partial("_GridExtratos", Model.ListExtratoMedicoIndex)
              </div>
            </div>

          </div>
        </div>
        <div class="box-footer">
          <button type="button" value="Voltar" class="btn btn-secondary" onclick="location.href='@Url.Action("Index", "Medico")'">Cancelar</button>
        </div>
      </div>
    </div>
  </section>
</div>

@Html.Partial("_ModalLogs")
@Html.Partial("_ModalLogsDetalhes")

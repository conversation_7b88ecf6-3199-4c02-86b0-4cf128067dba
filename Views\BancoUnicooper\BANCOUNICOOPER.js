﻿var TabelaProgressivaIRPF = function () {

};


$(document).on("click", ".TrSelecionavel", function () {
  var idCodigoBanco = $(this).data("codigobanco");
  var urlEdit = GetURLBaseComplete() + "/BancoUnicooper/Edit?codigo=" + idCodigoBanco;
  var urlDelete = GetURLBaseComplete() + "/BancoUnicooper/Delete?codigo=" + idCodigoBanco;


  if (!$(this).hasClass("Selected")) {
    $('.Selected').each(function (index, element) {
      $(element).removeClass("Selected")
      $(element).addClass("UnSelected")
    });

    if ($(this).hasClass("Selected")) {
      $(this).removeClass("Selected")
      $(this).addClass("UnSelected")
    }
    else {
      $(this).addClass("Selected")
      $(this).removeClass("UnSelected")
    }
  }
  else {
    $(this).removeClass("Selected")
    $(this).addClass("UnSelected")
  }

  var countSelected = 0;
  $('.Selected').each(function (index, element) {
    if ($(element).hasClass("Selected")) {
      $('#IdBancoEdit').attr("href", urlEdit);
      $('#IdBancoDelete').attr("href", urlDelete);

      countSelected++;
    }
  });

  if (countSelected == 0) {
    $('#IdBancoEdit').removeAttr("href");
    $('#IdBancoDelete').removeAttr("href");


    $('#IdBancoEdit').attr("disabled", true);
    $('#IdBancoDelete').attr("disabled", true);


    $('#IdBancoEdit').addClass("disabled");
    $('#IdBancoDelete').addClass("disabled");

  }
  else {
    $('#IdBancoEdit').attr("disabled", false);
    $('#IdBancoDelete').attr("disabled", false);


    $('#IdBancoEdit').removeClass("disabled");
    $('#IdBancoDelete').removeClass("disabled");

  }
});

$(document).ready(function () {
  TabelaProgressivaIRPF.init();
});
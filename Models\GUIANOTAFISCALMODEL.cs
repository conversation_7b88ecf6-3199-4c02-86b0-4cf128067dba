﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace RepasseConvenio.Models
{
  public class GuiaNotaFiscalModel
  {
  }

  public class GuiaNotaFiscalIndex
  {
    public int IdGuia { get; set; }
    public string NroUnicooper { get; set; }
    public string CodProcedimento { get; set; }
    public string DescProcedimento { get; set; }
    public decimal ValorFaturado { get; set; }
    public decimal? ValorGlosado { get; set; }

    public decimal? ValorPago { get; set; }
  }

  public class GuiaNotaFiscalEdit
  {
    public int Id { get; set; }
    public int IdGuia { get; set; }
    public string NroUnicooper { get; set; }
    public string CodProcedimento { get; set; }
    public string DescProcedimento { get; set; }
    public decimal ValorFaturado { get; set; }
    public decimal ValorGlosado { get; set; }
    public decimal ValorPago { get; set; }
    public decimal ValorApresentado { get; set; }
  }

  public class GuiaNotaFiscalDelete
  {
    public int IdGuiaAtendimento { get; set; }

    public int IdNotaFiscal { get; set; }

    public int IdRepasse { get; set; }
  }

  public class GuiaNotaFiscalPreCreate
  {
    public int IdGuia { get; set; }

    public int IdProcGuia { get; set; }

    public decimal ValorFaturado { get; set; }
  }

  public class GuiaNotaFiscalPreCreateByPlanilha
  {
    public int IdGuia { get; set; }

    public int IdProcGuia { get; set; }

    public decimal ValorFaturado { get; set; }
    public decimal ValorPago { get; set; }
    public decimal ValorApresentado { get; set; }
    public decimal ValorGlosado { get; set; }
  }
}
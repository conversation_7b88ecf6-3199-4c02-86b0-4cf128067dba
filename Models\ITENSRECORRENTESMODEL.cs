﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Services;
using RepasseConvenio.WSPortalCooperado;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Security.Principal;
using System.Web;

namespace RepasseConvenio.Models
{
  public class ItensRecorrentesModel
  {
    public int Codigo { get; set; }

    [DisplayName("Tipo de Lançamento")]
    [Required(ErrorMessage = "É necessário selecionar o Tipo de Lançamento.")]
    public EnumTipoLancamento TipoLancamentoEnum { get; set; }

    public string TipoLancamento { get; set; }

    [DisplayName("Data Início")]
    [Required(ErrorMessage = "É necessário selecionar a Data Início.")]
    public DateTime DataRecorrencia { get; set; }

    [DisplayName("Data Fim")]
    public DateTime? DataFim { get; set; }

    public int recorrenteAux { get; set; }

    [Required(ErrorMessage = "É necessário incluir o Valor.")]
    public decimal Valor { get; set; }

    [Required(ErrorMessage = "É necessário selecionar a Classificação.")]
    public int CodigoClassificacao { get; set; }

    [DisplayName("Tipo Conta")]
    public EnumTipoConta TipoContaEnum { get; set; }

    [DisplayName("Tipo Recorrencia")]
    public TipoRecorrenciaEnum? TipoRecorrenciaEnum { get; set; }

    [DisplayName("Ativo")]
    public bool Ativo { get; set; }

    [DisplayName("Recorrente?")]
    public bool Recorrente { get; set; }

    public string TipoConta { get; set; }

    [DisplayName("CPF/CNPJ")]
    public string CPFCNPJ { get; set; }

    [Required(ErrorMessage = "Médico não selecionado")]
    public int CodigoMedico { get; set; }

    [DisplayName("Selecione a Empresa")]
    [URLSelect("Select2/GetEmpresaMedicoCNPJSelect")]
    [PlaceHolderAttr("Selecione a Empresa")]
    public Select2Model EmpresaMedicoCNPJSelect
    {
      get
      {
        EmpresaMedicoService EmpresaMedicoService = new EmpresaMedicoService();
        return EmpresaMedicoService.GetByCNPJ(this.CPFCNPJ).ToSelect2Model();
      }
      set
      {
        if (value != null && !String.IsNullOrEmpty(value.id) && !value.id.Equals("0"))
          this.CPFCNPJ = value.id;
      }
    }


    [DisplayName("Classificação")]
    [URLSelect("Select2/GetClassificacaoSelect")]
    [PlaceHolderAttr("Selecione a Classificação")]
    [Required(ErrorMessage = "É necessário selecionar a Classificação.")]
    public Select2Model ClassificacaoSelect
    {
      get
      {
        ClassificacaoRepasseServices ClassificacaoRepasseServices = new ClassificacaoRepasseServices();
        return ClassificacaoRepasseServices.GetById(this.CodigoClassificacao).ToSelect2Model();
      }
      set
      {
        if (value != null && !String.IsNullOrEmpty(value.id) && !value.id.Equals("0"))
          this.CodigoClassificacao = int.Parse(value.id);
      }
    }

    public DateTime? DateLastExcute { get; set; }

  }

  public class ItensRecorrentesIndex
  {
    public int Codigo { get; set; }
    public string TipoLancamento { get; set; }
    public DateTime DataRecorrencia { get; set; }
    public DateTime? DataFim { get; set; }
    public int CodigoClassificacao { get; set; }
    public string TipoConta { get; set; }
    public string CPFCNPJ { get; set; }
    public int CodigoMedico { get; set; }
    public string NomeMedico { get; set; }
    public string DescricaoClassificacao { get; set; }
    public int TipoRecorrencia { get; set; }
    public bool Ativo { get; set; }
    public bool Recorrente { get; set; }
    public decimal Valor { get; set; }
    public DateTime? DateLastExcute { get; set; }
  }

  public static class ItensRecorrentesModelConversion
  {
    public static R_ItensRecorrentes ItensRecorrentesToCreate(this ItensRecorrentesModel model)
    {
      R_ItensRecorrentes entity = new R_ItensRecorrentes();

      entity.IR_CPFCNPJ = model.CPFCNPJ;
      entity.IR_DataRecorrencia = model.DataRecorrencia;
      entity.IR_DataFim = model.DataFim;
      entity.IR_IdClassificacao = model.CodigoClassificacao;
      entity.IR_IdMedico = model.CodigoMedico;
      entity.IR_TipoConta = String.Format("{0}", model.TipoContaEnum);
      entity.IR_TipoLancamento = String.Format("{0}", model.TipoLancamentoEnum);
      entity.IR_Recorrente = model.Recorrente;
      entity.IR_TipoRecorrencia = model.TipoRecorrenciaEnum.HasValue ? (int)model.TipoRecorrenciaEnum.Value: 0;
      entity.IR_Valor = model.Valor;
      entity.IR_Ativo = true;
      
      return entity;
    }
    public static R_ItensRecorrentes ItensRecorrentesToEdit(this ItensRecorrentesModel model)
    {
      R_ItensRecorrentes entity = new R_ItensRecorrentes();

      entity.IR_Id = model.Codigo;
      entity.IR_CPFCNPJ = model.CPFCNPJ;
      entity.IR_DataRecorrencia = model.DataRecorrencia;
      entity.IR_DataFim = model.DataFim;
      entity.IR_IdClassificacao = model.CodigoClassificacao;
      entity.IR_IdMedico = model.CodigoMedico;
      entity.IR_TipoConta = String.Format("{0}", model.TipoContaEnum);
      entity.IR_TipoLancamento = String.Format("{0}", model.TipoLancamentoEnum);
      entity.IR_Recorrente = model.Recorrente;
      entity.IR_Valor = model.Valor;
      entity.IR_TipoRecorrencia = model.TipoRecorrenciaEnum.HasValue ? (int)model.TipoRecorrenciaEnum.Value : 0;
      entity.IR_Ativo = true;
      entity.DateLastExcute = model.DateLastExcute;

      return entity;
    }
  }
}
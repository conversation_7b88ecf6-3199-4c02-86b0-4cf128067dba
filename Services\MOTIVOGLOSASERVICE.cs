﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Models;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;

namespace RepasseConvenio.Services
{
  public class MotivoGlosaService : ServiceBase
  {
    public MotivoGlosaService(RepasseEntities Contexto)
        : base(Contexto)
    { }
    public MotivoGlosaService()
       : base()
    { }

    public MotivoGlosaService(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public MotivoGlosaService(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public R_MotivoGlosa GetById(int id)
    {
      string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_MotivoGlosa
                                       WHERE MG_Id = @id");

      return Contexto.Database.SqlQuery<R_MotivoGlosa>(query, new SqlParameter("@id", id)).FirstOrDefault();
    }
    
    public List<R_MotivoGlosa> GetByTerm(string term)
    {
      if (string.IsNullOrEmpty(term))
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_MotivoGlosa");

        return Contexto.Database.SqlQuery<R_MotivoGlosa>(query).ToList();

      }
      else
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_MotivoGlosa
                                       WHERE MG_Id LIKE @termo OR MG_Descricao LIKE @termo");

        return Contexto.Database.SqlQuery<R_MotivoGlosa>(query, new SqlParameter("@termo", string.Format("%{0}%", term))).ToList();
      }
    }
  }
}

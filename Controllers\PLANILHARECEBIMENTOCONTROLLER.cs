﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

//using RepasseConvenio.Infrastructure;
//using RepasseConvenio.Infrastructure.Helpers;
//using RepasseConvenio.Models;
//using RepasseConvenio.Services;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Web;
//using System.Web.Mvc;


//namespace RepasseConvenio.Controllers
//{
//  public class PlanilhaRecebimentoController : LibController
//  {
//    private PlanilhaRecebimentoService PlanilhaRecebimentoService
//    {
//      get
//      {
//        if (_PlanilhaRecebimentoService == null)
//          _PlanilhaRecebimentoService = new PlanilhaRecebimentoService(ContextoUsuario.UserLogged);

//        return _PlanilhaRecebimentoService;
//      }
//    }
//    private PlanilhaRecebimentoService _PlanilhaRecebimentoService;

//    [HttpPost]
//    public JsonResult Create(PlanilhaRecebimentoModel model)
//    {
//      RetornoAjax retornoAjax = new RetornoAjax();
//      try
//      {
//        PlanilhaRecebimentoService.Create(model);
//        retornoAjax.Titulo = "Sucesso";
//        retornoAjax.Mensagem = "Planilha inserida com sucesso.";
//        retornoAjax.TipoMensagem = "success";
//        retornoAjax.Erro = false;
//        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
//      }
//      catch (CustomException ex)
//      {
//        retornoAjax.Titulo = "Erro";
//        retornoAjax.Mensagem = ex.Message;
//        retornoAjax.Erro = true;
//        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
//      }
//      catch (Exception ex)
//      {
//        retornoAjax.Titulo = "Erro";
//        retornoAjax.Mensagem = ex.Message;
//        retornoAjax.Erro = true;
//        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
//      }
//    }

//    [HttpPost]
//    public ActionResult Edit(PlanilhaRecebimentoModel model)
//    {
//      RetornoAjax retornoAjax = new RetornoAjax();
//      try
//      {
//        PlanilhaRecebimentoService.Edit(model);
//        retornoAjax.Titulo = "Sucesso";
//        retornoAjax.Mensagem = "Planilha inserida com sucesso.";
//        retornoAjax.TipoMensagem = "success";
//        retornoAjax.Erro = false;
//        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
//      }
//      catch (CustomException ex)
//      {
//        retornoAjax.Titulo = "Erro";
//        retornoAjax.Mensagem = ex.Message;
//        retornoAjax.Erro = true;
//        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
//      }
//      catch (Exception ex)
//      {
//        retornoAjax.Titulo = "Erro";
//        retornoAjax.Mensagem = ex.Message;
//        retornoAjax.Erro = true;
//        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
//      }
//    }

//    [HttpPost]
//    public ActionResult Delete(List<int> idItensToDelete, int IdRepasse)
//    {
//      RetornoAjax retornoAjax = new RetornoAjax();
//      try
//      {
//        PlanilhaRecebimentoService.Delete(idItensToDelete, IdRepasse);
//        retornoAjax.Titulo = "Sucesso";
//        retornoAjax.Mensagem = "Planilha(s) deletada(s) com sucesso.";
//        retornoAjax.TipoMensagem = "success";
//        retornoAjax.Erro = false;
//        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
//      }
//      catch (CustomException ex)
//      {
//        retornoAjax.Titulo = "Erro";
//        retornoAjax.Mensagem = ex.Message;
//        retornoAjax.Erro = true;
//        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
//      }
//      catch (Exception ex)
//      {
//        retornoAjax.Titulo = "Erro";
//        retornoAjax.Mensagem = ex.Message;
//        retornoAjax.Erro = true;
//        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
//      }
//    }

//    public JsonResult ImportPlanilha(int IdRepasse)
//    {
//      try
//      {
//        Anexo anexo = AnexoHelper.FilesUploadSemConversao(Request, new string[] { ".xls", ".xlsx" }).FirstOrDefault();
//        PlanilhaRecebimentoService.ImportacaoPlanilha(anexo, IdRepasse);

//        return Json(new { status = "success", message = "Itens Importados com sucesso" });
//      }
//      catch (Exception ex)
//      {
//        return Json(new { status = "error", message = ex.Message });
//      }
//    }
//  }
//}
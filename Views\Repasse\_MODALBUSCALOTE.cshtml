﻿@using RepasseConvenio.Models
@*Modal Nota Fiscal*@
<div class="modal fade" id="ModalBuscaLote" data-keyboard="false" data-backdrop="static">
  <div class="modal-dialog" style="max-width: 50%;">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">Buscar Lotes</h4>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">×</span>
        </button>
      </div>
      <div class="modal-body">
        @using (Html.BeginForm("PesquisaLotes", "Repasse", FormMethod.Get, new { id = "GetPesquisaLotes", @style = "" }))
        {
        <input id="BL_IdConvenio" name="BL_IdConvenio" type="hidden" value="">
        <input id="BL_IdRepasse" name="BL_IdRepasse" type="hidden" value="">
          <div class="row">
            <div class="col-md-2">
              <div class="form-group">
                <label for="BL_NumLote">Nº Lote</label>
                <input class="form-control numeros text-box single-line" id="BL_NumLote" name="BL_NumLote" type="text" value="">
                <span class="field-validation-valid text-danger" data-valmsg-for="BL_NumLote" data-valmsg-replace="true"></span>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label for="BL_DataInicio">Data de </label>
                <input class="form-control airDatePickerDate Data text-box single-line" data-val="true" id="BL_DataInicio" name="BL_DataInicio" type="datetime" value="">
                <span class="field-validation-valid text-danger" data-valmsg-for="BL_DataInicio" data-valmsg-replace="true"></span>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label for="BL_DataFim">Data até </label>
                <input class="form-control airDatePickerDate Data text-box single-line" data-val="true" id="BL_DataFim" name="BL_DataFim" type="datetime" value="">
                <span class="field-validation-valid text-danger" data-valmsg-for="BL_DataFim" data-valmsg-replace="true"></span>
              </div>
            </div>
            <div class="col-md-2">
              <button type="button" value="Create" class="btn btn-secondary pull-right" id="btnPesquisaLotes" style="margin-top: 28px;">Buscar</button>
            </div>
          </div>
        }
        <div class="row">
          <div class="col-md-12 container">
            <div id="DivGridLotes" style="display: none;">
              @Html.Partial("_GridLotes", new List<LotePortalModel>())
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
﻿@using PagedList.Mvc
@using RepasseConvenio.Models
@using RepasseConvenio.Infrastructure
@using RepasseConvenio.Infrastructure.Controls
@model  BancoUnicooperModel

@{
  ViewBag.Title = "Banco Unicooper";
  Layout = "~/Views/Shared/_Layout.cshtml";
}

@*@Scripts.Render("~/Views/ExtratoMedico/ExtratoMedico.js")*@

@using (Html.BeginForm("Edit", "BancoUnicooper", FormMethod.Post))
{
  @Html.HiddenFor(m => m.Codigo)

  <div class="row">
    <section class="col-lg-12 connectedSortable ui-sortable">
      <div class="card">
        <div class="card-header ui-sortable-handle">
          <h3 class="card-title">
            <i class="fas fa-user mr-1"></i>
            Atualizar Banco Unicooper
          </h3>
          <div class="card-tools">
          </div>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                @Html.LabelFor(m => m.BancoSelect)
                @Html.LibSelect2For(m => m.BancoSelect, new { @class = "form-control" })
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                @Html.LabelFor(m => m.Descricao)
                @Html.LibEditorFor(m => m.Descricao, new { @class = "form-control " })
                @Html.ValidationMessageFor(m => m.Descricao, "", new { @class = "text-danger" })
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-3">
              <div class="form-group">
                @Html.LabelFor(m => m.Agencia)
                @Html.LibEditorFor(m => m.Agencia, new { @class = "form-control" })
                @Html.ValidationMessageFor(m => m.Agencia, "", new { @class = "text-danger" })
              </div>
            </div>



            <div class="col-md-3">
              <div class="form-group">
                @Html.LabelFor(m => m.conta)
                @Html.LibEditorFor(m => m.conta, new { @class = "form-control" })
              </div>
            </div>

            <div class="col-md-2">
              <div class="form-group">
                @Html.LabelFor(m => m.Digito)
                @Html.LibEditorFor(m => m.Digito)
              </div>
            </div>

          </div>
          <div class="card-footer">
            <button type="button" value="Voltar" class="btn btn-secondary" onclick="window.history.back();">Cancelar</button>
            <button type="submit" value="Create" class="btn btn-info pull-rigth">Salvar</button>
          </div>
        </div>
    </section>
  </div>
}
<style>
  #TipoContaEnum, #TipoLancamentoEnum {
    height: auto !important;
  }
</style>

﻿@using PagedList.Mvc
@using RepasseConvenio.Models
@using RepasseConvenio.Infrastructure
@using RepasseConvenio.Infrastructure.Controls
@model LoginModel

@{
  ViewBag.Title = "Login";
  Layout = "~/Views/Shared/_LayoutLogin.cshtml";
}

@Styles.Render("~/Views/Account/Account.css?bbb=ccc")


<div class="row">
  <div class="col-sm-9 col-md-7 col-lg-5 mx-auto">
    <div class="card card-signin my-5">
      <div class="card-body">
        <div class="card-header ui-sortable-handle" style="text-align: center;">
          <h3 class="card-title">
            Repasse Médico
          </h3>
        </div>
        @using (Html.BeginForm("Login", "Account", FormMethod.Post, new { @class = "form-signin" }))
        {
          <div class="form-group">
            @Html.LabelFor(m => m.UserName)
            @*@Html.LibEditorFor(m => m.UserName, new { @class = "form-control CPF" })*@
            @Html.TextBoxFor(a => a.UserName, new { @class = "form-control CPF" })
            @Html.ValidationMessageFor(m => m.UserName, "", new { @class = "text-danger" })
          </div>

          <div class="form-group">
            @Html.LabelFor(m => m.Password)
            @Html.LibEditorFor(m => m.Password, new { @class = "form-control", @type = "password" })
            @Html.ValidationMessageFor(m => m.Password, "", new { @class = "text-danger" })
          </div>

          @*<div class="custom-control custom-checkbox mb-3">
      <input type="checkbox" class="custom-control-input" id="customCheck1">
      <label class="custom-control-label" for="customCheck1">Continuar Logado</label>
      </div>*@
          <button type="submit" class="btn btn-lg btn-primary btn-block text-uppercase">Login</button>
          <hr class="my-4">
        }
      </div>
    </div>
  </div>
</div>

@*<div class="form-background">
    <div class="container">
      <div class="row">
        <div class="col-md-4">
          <div class="form-group">
            @Html.LabelFor(m => m.UserName)
            @Html.LibEditorFor(m => m.UserName, new { @class = "form-control CPF" })
            @Html.ValidationMessageFor(m => m.UserName, "", new { @class = "text-danger" })
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-4">
          <div class="form-group">
            @Html.LabelFor(m => m.Password)
            @Html.LibEditorFor(m => m.Password, new { @class = "form-control", @type = "password" })
            @Html.ValidationMessageFor(m => m.Password, "", new { @class = "text-danger" })
          </div>
        </div>
      </div>
      <div>
        <button type="submit" class="btn btn-primary" style="width: 150px;display: table;padding: 7.5px 10px !important;">Login</button>
      </div>
    </div>
    <div class="info">
    </div>
  </div>*@

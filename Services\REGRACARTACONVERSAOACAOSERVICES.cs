﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using PagedList;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RepasseConvenio.Services
{
  public class RegraCartaConversaoAcaoServices : ServiceBase
  {
    public RegraCartaConversaoAcaoServices(RepasseEntities Contexto)
        : base(Contexto)
    { }
    public RegraCartaConversaoAcaoServices()
       : base()
    { }

    public RegraCartaConversaoAcaoServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public RegraCartaConversaoAcaoServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public R_RegraCartaConversaoAcao GetById(int? Id)
    {
      return Contexto.R_RegraCartaConversaoAcao.Where(a => a.RCCA_Id == Id).FirstOrDefault();
    }

    public List<R_RegraCartaConversaoAcao> GetByTerm(string term, int idPai)
    {
      if (string.IsNullOrEmpty(term))
      {
        string query = string.Format(@"SELECT 
                                        *
                                       FROM R_RegraCartaConversaoAcao RCCA
	                                     INNER JOIN R_RegraCartaConversaoCampo RCCC ON RCCC.RCCC_TipoCampo = RCCA_TipoCampo AND RCCC_Id = @idPai");

        return Contexto.Database.SqlQuery<R_RegraCartaConversaoAcao>(query, new SqlParameter("@idPai", idPai)).ToList();

      }
      else
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_RegraCartaConversaoAcao
                                       INNER JOIN R_RegraCartaConversaoCampo RCCC ON RCCC.RCCC_TipoCampo = RCCA_TipoCampo AND RCCC_Id = @idPai
                                       WHERE RCCA_Acao LIKE @termo");

        return Contexto.Database.SqlQuery<R_RegraCartaConversaoAcao>(query, new SqlParameter("@termo", string.Format("%{0}%", term))
                                                                          , new SqlParameter("@idPai", idPai)).ToList();

      }
    }

    public int GetIdByAcao(string Acao, string Tipo)
    {
      string query = @"SELECT RCCA_Id FROM R_RegraCartaConversaoAcao WHERE RCCA_Acao = @Acao AND RCCA_TipoCampo = @Tipo";

      return Contexto.Database.SqlQuery<int>(query, new SqlParameter("@Acao", Acao), new SqlParameter("@Tipo", Tipo)).FirstOrDefault();
    }

    public List<R_RegraCartaConversaoAcao> GetAll()
    {
      string query = @"SELECT * FROM R_RegraCartaConversaoAcao";

      return Contexto.Database.SqlQuery<R_RegraCartaConversaoAcao>(query).ToList();
    }

    public string GetTipoAcao(int IdAcao)
    {
      string query = @"SELECT
                        RCCA_TipoCampo
                       FROM R_RegraCartaConversaoAcao
                       WHERE RCCA_Id = @IdAcao";

      string tipoCampo = Contexto.Database.SqlQuery<string>(query, new SqlParameter("@IdAcao", IdAcao)).FirstOrDefault();

      return tipoCampo;
    }

  }
}


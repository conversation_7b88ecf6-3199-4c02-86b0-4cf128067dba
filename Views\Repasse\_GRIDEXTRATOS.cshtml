﻿@using RepasseConvenio.Models
@model List<ExtratoMedicoGrid>

<table class="table table-hover table-striped">
  <thead>
    <tr>
      <th>
        Medico
      </th>
      <th>
        Data Apuração
      </th>
      <th>
        Classificação
      </th>
      <th>
        Tipo Lançamento
      </th>
      <th>
        Valor
      </th>
      <th>
        Data Pagamento
      </th>
    </tr>
  </thead>
  <tbody>
    @foreach (ExtratoMedicoGrid item in Model)
    {
      <tr>
        <td>
          @item.NomeMedico
        </td>
        <td>
          @item.DataApuracao.ToString("dd/MM/yyyy")
        </td>
        <td>
          @item.DescricaoClassificacao
        </td>
        <td>
          @item.TipoLancamento
        </td>
        <td>
          R$ @item.Valor
        </td>
        <td>
          @if (item.DataPagamento.HasValue)
          {
            <text>
              @item.DataPagamento.Value.ToString("dd/MM/yyyy")
            </text>
          }
          else
          {
            <text>

            </text>
          }
        </td>
      </tr>
    }
  </tbody>
</table>

﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Controls;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;

namespace RepasseConvenio.Controllers
{
  [Security("E0324258-6032-412B-AEF4-8149FC005871")]
  public class TabelaProgressivaIRPFController : LibController
  {


    private TabelaProgressivaIRPFServices TabelaProgressivaIRPFServices
    {
      get
      {
        if (_TabelaProgressivaIRPFServices == null)
          _TabelaProgressivaIRPFServices = new TabelaProgressivaIRPFServices(ContextoUsuario.UserLogged);

        return _TabelaProgressivaIRPFServices;
      }
    }

    private TabelaProgressivaIRPFServices _TabelaProgressivaIRPFServices;


    private ValoresTabelaProgressivaIRPFServices ValoresTabelaProgressivaIRPFServices
    {
      get
      {
        if (_ValoresTabelaProgressivaIRPFServices == null)
          _ValoresTabelaProgressivaIRPFServices = new ValoresTabelaProgressivaIRPFServices(ContextoUsuario.UserLogged);

        return _ValoresTabelaProgressivaIRPFServices;
      }
    }

    private ValoresTabelaProgressivaIRPFServices _ValoresTabelaProgressivaIRPFServices;
    // GET: TabelaProgressivaIRPF


    [Security("B2CB4887-BA92-46C4-A395-8EAC7AD4D9B7")]

    public ActionResult Index(int? page, string filtro)
    {
      try
      {
        int pageNumber = page ?? 1;

        ViewBag.page = pageNumber;
        ViewBag.filtro = filtro;

        var list = TabelaProgressivaIRPFServices.Get(pageNumber, filtro);
        return View(list);
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return View();
      }
    }
    [Security("20AB9B79-9EC6-4AF4-BD4C-3512E36E89CB")]
    public ActionResult Create()
    {
      try
      {
        TabelaProgressivaIRPFModel model = new TabelaProgressivaIRPFModel();
        model.DtInicioVigencia = DateTime.Now;
        return View(model);
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return RedirectToAction("Index", "TabelaProgressivaINSS");
      }
    }
    [Security("20AB9B79-9EC6-4AF4-BD4C-3512E36E89CB")]
    [HttpPost]
    public ActionResult Create(TabelaProgressivaIRPFModel model)
    {
      try
      {
        TabelaProgressivaIRPFServices.Create(model);
        MessageListToast.Add(new Message(MessageType.Success, LibMessageToast.MessageCreateSucess));
        return RedirectToAction("Index", "TabelaProgressivaIRPF");
      }
      catch (CustomException ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return View(model);
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return View(model);
      }
    }
    [Security("D713ADC3-1E9D-40B1-9D82-FFA1C4762E65")]
    public ActionResult Edit(int codigo)
    {
      try
      {
        TabelaProgressivaIRPFModel model = TabelaProgressivaIRPFServices.GetById(codigo).OffEntityIRToModel();
        model.ValoresTabelaIRPF = ValoresTabelaProgressivaIRPFServices.GetByIdTabelaIRPF(codigo).Select(a => a.OffEntityIRToModel()).ToList();
        return View(model);
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return RedirectToAction("Index", "TabelaProgressivaIRPF");
      }
    }

    [Security("D713ADC3-1E9D-40B1-9D82-FFA1C4762E65")]
    [HttpPost]
    public ActionResult Edit(TabelaProgressivaIRPFModel model)
    {
      try
      {
        TabelaProgressivaIRPFServices.Edit(model);
        MessageListToast.Add(new Message(MessageType.Success, LibMessageToast.MessageEditSucess));
        return RedirectToAction("Index", "TabelaProgressivaIRPF");
      }
      catch (CustomException ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return View(model);
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return View(model);
      }
    }
[Security("D169537E-AFFB-4D69-ADCC-0ABDEBEA014E")]
    public ActionResult Delete(int codigo)
    {
      try
      {
        TabelaProgressivaIRPFServices.Delete(codigo);

        MessageListToast.Add(new Message(MessageType.Success, LibMessageToast.MessageDeleteSucess));
        return RedirectToAction("Index", "TabelaProgressivaIRPF");
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return RedirectToAction("Index", "TabelaProgressivaIRPF");
      }
    }

    public PartialViewResult GridValoresTPIRPF(int id)
    {
      try
      {
        List<ValoresTabelaProgressivaIRPFModel> list;
        list = ValoresTabelaProgressivaIRPFServices.GetByIdTabelaIRPF(id).Select(a => a.OffEntityIRToModel()).ToList();
        return PartialView("_GridValores", list);
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }
  }


}




﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Transactions;
using RepasseConvenio.Infrastructure;
using System.Text.RegularExpressions;
using RepasseConvenio.WSPortalCooperado;
using System.IO;
using Microsoft.Reporting.WebForms;
using System.Configuration;
using System.Web.Mvc;

namespace RepasseConvenio.Services
{
  public class LoteGlosaServices : ServiceBase
  {
    public LoteGlosaServices()
   : base()
    { }
    public LoteGlosaServices(RepasseEntities Contexto)
       : base(Contexto)
    { }

    public LoteGlosaServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public LoteGlosaServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    #region[GETs]
    public bool HaveResponsavel(int IdLote)
    {
      string query = @"SELECT LG_IdResponsavelConvenio FROM R_LoteGlosa where LG_Id = @IdLote";
      int? IdResponsavel = Contexto.Database.SqlQuery<int?>(query, new SqlParameter("@IdLote", IdLote)).FirstOrDefault();
      if (IdResponsavel.HasValue)
        return true;
      else
        return false;
    }

    public List<LoteGlosaIndex> GetLoteGlosaIndex()
    {
      string query = @"SELECT 
                        DISTINCT LG.LG_Id [CodigoGlosa],
                        LG.LG_Codigo [Codigo],
                        LG.LG_DataCriacao [DataCriacao],
                        LG.LG_DataVencimentoAnalise [DataVencimentoAnalise],
                        LG.LG_DataReapresentacao [DataReapresentacao],
                        C.C_RazaoSocial [RazaoSocialConvenio],
                        SG.SG_Descricao [DescricaoStatus],
                        LG.LG_DataInicio [DataInicio],
                        U.U_Nome [UsuarioCriacao],
                        SG.SG_Enum [StatusGlosaEnum]
                       FROM R_LoteGlosa LG
                       INNER JOIN R_ResponsavelConvenio RC ON RC.RC_Id = LG.LG_IdResponsavelConvenio AND LG.LG_IdResponsavelConvenio = @IdResponsavel OR LG.LG_IdResponsavelConvenio IS NULL
                       INNER JOIN R_Convenio C ON C.C_Id = LG.LG_IdConvenio  AND C.C_Id IN (@IdsConvenio)
                       INNER JOIN R_StatusGlosa SG ON SG.SG_Id = LG.LG_IdStatusGlosa
                       INNER JOIN R_Usuario U ON U.U_Id = LG.LG_IdUsuarioCriacao
                       [CONDICAO]";

      List<string> condicao = new List<string>();
      List<SqlParameter> listparametrs = new List<SqlParameter>();

      if (User.ConveniosResponsavel.Count() > 0)
      {
        query = query.Replace("@IdsConvenio", string.Join(",", User.ConveniosResponsavel.Select(a => a.IdConvenio).ToArray()));


        if (listparametrs.Count > 0)
          query = query.Replace("[CONDICAO]", String.Format("WHERE {0}", String.Join(" AND ", condicao.ToArray())));
        else
          query = query.Replace("[CONDICAO]", "");

        listparametrs.Add(new SqlParameter("@IdResponsavel", User.ConveniosResponsavel.Select(a => a.IdResponsavelConvenio).FirstOrDefault()));
        return Contexto.Database.SqlQuery<LoteGlosaIndex>(query, listparametrs.ToArray()).ToList();
      }
      else
        return new List<LoteGlosaIndex>();
    }

    public List<LoteGlosaIndex> GetLoteGlosaIndex(LoteGlosaFiltroModel filtro)
    {
      string query = @"SELECT 
                        DISTINCT LG.LG_Id [CodigoGlosa],
                        LG.LG_Codigo [Codigo],
                        LG.LG_DataCriacao [DataCriacao],
                        LG.LG_DataVencimentoAnalise [DataVencimentoAnalise],
                        LG.LG_DataReapresentacao [DataReapresentacao],
                        C.C_RazaoSocial [RazaoSocialConvenio],
                        SG.SG_Descricao [DescricaoStatus],
                        LG.LG_DataInicio [DataInicio],
                        U.U_Nome [UsuarioCriacao],
                        SG.SG_Enum [StatusGlosaEnum]
                       FROM R_LoteGlosa LG
                       INNER JOIN R_ResponsavelConvenio RC ON RC.RC_Id = LG.LG_IdResponsavelConvenio AND LG.LG_IdResponsavelConvenio = @IdResponsavel OR LG.LG_IdResponsavelConvenio IS NULL
                       INNER JOIN R_Convenio C ON C.C_Id = LG.LG_IdConvenio  AND C.C_Id IN (@IdsConvenio)
                       INNER JOIN R_StatusGlosa SG ON SG.SG_Id = LG.LG_IdStatusGlosa
                       INNER JOIN R_Usuario U ON U.U_Id = LG.LG_IdUsuarioCriacao
                       [CONDICAO]";

      List<string> condicao = new List<string>();
      List<SqlParameter> listparametrs = new List<SqlParameter>();

      if (User.ConveniosResponsavel.Count() > 0)
      {
        List<int> convenios = User.ConveniosResponsavel.Select(a => a.IdConvenio).ToList();
        if(filtro != null)
        {
          if (filtro.CodigoConvenio.HasValue)
          {
             convenios = new List<int>();
            convenios.Add(filtro.CodigoConvenio.Value);
          }
          if (filtro.CodigoStatusGlosa.HasValue)
          {
            listparametrs.Add(new SqlParameter("@Status", filtro.CodigoStatusGlosa.Value));
            condicao.Add("SG.SG_Id = @Status");
          }
        }
        query = query.Replace("@IdsConvenio", string.Join(",", convenios));

        if (condicao.Count > 0)
          query = query.Replace("[CONDICAO]", String.Format("WHERE {0}", String.Join(" AND ", condicao.ToArray())));
        else
          query = query.Replace("[CONDICAO]", "");

        listparametrs.Add(new SqlParameter("@IdResponsavel", User.ConveniosResponsavel.Select(a => a.IdResponsavelConvenio).FirstOrDefault()));
        return Contexto.Database.SqlQuery<LoteGlosaIndex>(query, listparametrs.ToArray()).ToList();
      }
      else
        return new List<LoteGlosaIndex>();
    }

    public R_LoteGlosa GetById(int IdLote)
    {
      string query = @"SELECT 
                        *
                       FROM R_LoteGlosa
                       WHERE LG_Id = @IdLote";

      return Contexto.Database.SqlQuery<R_LoteGlosa>(query, new SqlParameter("@IdLote", IdLote)).FirstOrDefault();
    }

    public StatusGlosaEnum GetStatusGlosaEnumById(int IdLote)
    {
      string query = @"SELECT 
                        SG.SG_Enum
                       FROM R_LoteGlosa LG
                       INNER JOIN R_StatusGlosa SG ON SG.SG_Id = LG.LG_IdStatusGlosa AND LG.LG_Id = @IdLote";

      return Contexto.Database.SqlQuery<StatusGlosaEnum>(query, new SqlParameter("@IdLote", IdLote)).FirstOrDefault();
    }

    public List<LoteGlosaProcessaCabecalho> GetListLoteGlosaProcessaCabecalho(int IdRepasse)
    {
      string query = @"SELECT 
                        IL.IL_Id [IdItensLote],
                        SIL.SIL_Id [IdStatusItensLote],
                        SIL.SIL_Enum [EnumStatusItensLote],
                        GA.GA_IdConvenio [IdConvenio],
                        IL.IL_IdGuiaAtendimento [IdGuiaAtendimento],
                        GD.GD_Id [IdGuiaDemonstrativo],
                        C.C_VencLoteGlosa [VencimentoGlosa],
                        L.L_Id [IdLote],
                        L.L_NumeroLote [NumeroLote]
                       FROM R_ItensLote IL
                       INNER JOIN R_StatusItensLote SIL ON SIL.SIL_Id = IL.IL_IdStatusItensLote AND (SIL.SIL_Enum = @IdStatusGlosado OR SIL.SIL_Enum = @IdStatusContestacao OR SIL.SIL_Enum = @IdStatusReapresentacao OR SIL.SIL_Enum = @IdStatusTreplica)
                       INNER JOIN R_Lote L ON L.L_Id = IL.IL_IdLote
                       INNER JOIN R_GuiaDemonstrativo GD ON IL.IL_IdGuiaAtendimento = GD.GD_IdGuia
                       INNER JOIN R_DemonstrativoConvenio DC ON DC.DC_Id = GD.GD_IdDemonstrativoConvenio AND DC.DC_IdRepasse = @IdRepasse
                       INNER JOIN R_GuiaAtendimento GA ON GA.GA_Id = IL.IL_IdGuiaAtendimento
                       INNER JOIN R_Convenio C ON C.C_Id = GA.GA_IdConvenio";

      List<LoteGlosaProcessa> ListLoteGlosaProcessa
                            = Contexto.Database.SqlQuery<LoteGlosaProcessa>(query, new SqlParameter("@IdRepasse", IdRepasse)
                                                                                 , new SqlParameter("@IdStatusGlosado", (int)StatusItensLoteEnum.Glosado)
                                                                                 , new SqlParameter("@IdStatusContestacao", (int)StatusItensLoteEnum.Contestacao)
                                                                                 , new SqlParameter("@IdStatusReapresentacao", (int)StatusItensLoteEnum.Reapresentacao)
                                                                                 , new SqlParameter("@IdStatusTreplica", (int)StatusItensLoteEnum.Treplica)).ToList();

      var GroupBy = ListLoteGlosaProcessa.GroupBy(a => new { a.IdConvenio, a.EnumStatusItensLote, a.VencimentoGlosa, a.IdStatusItensLote, a.IdLote, a.NumeroLote });

      return GroupBy.Select(a => new LoteGlosaProcessaCabecalho()
      {
        IdConvenio = a.Key.IdConvenio,
        statusItensLoteEnum = a.Key.EnumStatusItensLote,
        VencimentoGlosa = a.Key.VencimentoGlosa,
        IdStatusItensLote = a.Key.IdStatusItensLote,
        IdLote = a.Key.IdLote,
        NumeroLote = a.Key.NumeroLote,
        ListaLoteGlosaProcessa = a.ToList()
      }).ToList();

    }

    public R_LoteGlosa GetLoteByIdGuiaAtendimento(int IdGuiaAtendimento)
    {
      string query = @"SELECT 
                        LG.* 
                       FROM R_LoteGlosa LG
                       INNER JOIN R_ItensLoteGlosa ILG ON LG.LG_Id = ILG.ILG_IdLoteGlosa AND ILG.ILG_IdGuiaAtendimento = @IdGuiaAtendimento";

      return Contexto.Database.SqlQuery<R_LoteGlosa>(query, new SqlParameter("@IdGuiaAtendimento", IdGuiaAtendimento)).FirstOrDefault();
    }

    public UltimoRegistro GetUltimoRegistro(int IdLote, string prefixo)
    {
      string query = @"SELECT
                       TOP(1) MAX(Right(LG_Codigo, 10)) [Codigo],
                        LG_Codigo [CodigoCompleto]
                       FROM R_LoteGlosa
                       WHERE LG_IdLote = @IdLote AND LEFT(LG_Codigo, 1) = @prefixo
                       GROUP BY LG_Codigo
                       ORDER BY CONVERT(INT, SUBSTRING(LG_Codigo, 2, LEN(LG_Codigo) - 11)) DESC";

      return Contexto.Database.SqlQuery<UltimoRegistro>(query, new SqlParameter("@IdLote", IdLote)
                                                             , new SqlParameter("@prefixo", prefixo)).FirstOrDefault();
    }

    public UltimoRegistro GetUltimoRegistroByIdTipoLoteGlosa(int IdTipoLoteGlosa)
    {
      string query = @"SELECT
                        TOP(1) MAX(Right(LG_Codigo, 10)) [Codigo],
                        LG_Codigo [CodigoCompleto]
                       FROM R_LoteGlosa
                       WHERE LG_IdTipoLoteGlosa = @IdTipoLoteGlosa
                       GROUP BY LG_Codigo
                       ORDER BY Codigo DESC";

      return Contexto.Database.SqlQuery<UltimoRegistro>(query, new SqlParameter("@IdTipoLoteGlosa", IdTipoLoteGlosa)).FirstOrDefault();
    }

    public List<LoteGlosaToRemoveCabecalho> GetLoteGlosaToRemove(int IdRepasse)
    {
      string query = @"SELECT
                        LG.LG_Id [IdLote],
                        SG.SG_Id [IdStatus],
                        SG.SG_Enum [statusGlosaEnum],
                        ILG.ILG_IdGuiaAtendimento [IdGuiaAtendimento]
                       FROM R_ItensLoteGlosa ILG
                       INNER JOIN R_LoteGlosa LG ON LG.LG_Id = ILG.ILG_IdLoteGlosa AND LG.LG_IdRepasse = @IdRepasse
                       INNER JOIN R_StatusGlosa SG ON SG.SG_Id = LG.LG_IdStatusGlosa ";

      List<LoteGlosaToRemove> ListLoteGlosaToRemove = Contexto.Database.SqlQuery<LoteGlosaToRemove>(query, new SqlParameter("@IdRepasse", IdRepasse)).ToList();

      var GroupBy = ListLoteGlosaToRemove.GroupBy(a => new { a.IdLote, a.IdStatus, a.statusGlosaEnum });
      List<LoteGlosaToRemoveCabecalho> ListLoteGlosaCabecalho = GroupBy.Select(a => new LoteGlosaToRemoveCabecalho()
      {
        IdLote = a.Key.IdLote,
        IdStatus = a.Key.IdStatus,
        statusGlosaEnum = a.Key.statusGlosaEnum,
        ListLoteGlosaToRemove = a.ToList()
      }).ToList();

      return ListLoteGlosaCabecalho;
    }

    public List<LogLoteGlosa> GetLogs(int IdLote)
    {
      string Query = @"SELECT
	                        LLG.LLG_Id [Codigo],
                          CASE
		                        WHEN LLG.LLG_Log = '' THEN TLG.TLG_Descricao
		                        ELSE LLG.LLG_Log
	                        END [Log],		
                          TLG.TLG_Enum [TipoLog],
                          LLG.LLG_Data [Data],
                          LLG.LLG_IdLoteGlosa [CodigoLoteGlosa],
                          U.U_Nome [NomeUsuario]
                        FROM R_LogLoteGlosa LLG
                        INNER JOIN R_TipoLogLoteGLosa TLG ON TLG.TLG_Id=LLG.LLG_IdTipoLog
                        LEFT JOIN R_Usuario U ON U.U_Id=LLG.LLG_IdUsuario
                        WHERE LLG_IdLoteGlosa = @IdLoteGlosa ORDER BY LLG.LLG_Data DESC";

      return Contexto.Database.SqlQuery<LogLoteGlosa>(Query, new SqlParameter("@IdLoteGlosa", IdLote)).ToList();
    }

    public List<XMLLoteGlosa> GetXMLLoteGlosa(int IdLoteGlosa)
    {
      string query = @"SELECT
                        GD.GD_NumeroCarteirinha [NumeroCarteirinha],
                        GD.GD_NumeroAtendimento [NumeroAtendimento],
                        PGD.PGD_CodigoProcedimento [CodigoProcedimento],
                        PGD.PGD_DescricaoProcedimento [DescricaoProcedimento],
                        GD.GD_DataAtendimento [DataAtendimento],
                        PGD.PGD_TotalApresentado [TotalApresentado],
                        PGD.PGD_TotalGlosado [TotalGlosado],
                        PGD.PGD_TotalPago [TotalPago],
                        PGD.PGD_ObservacoesGerais [ObservacoesGerais],
                        PGD.PGD_CodigoGlosa [CodigoGlosa],
                        PGD.PGD_DescricaoGlosa [DescricaoGlosa],
                        JG.JG_Codigo [CodigoJustificativa],
                        JG.JG_Descricao [DescricaoJustificativa],
                        PGD.PGD_Comentario [Comentario]
                       FROM R_LoteGlosa LG
                       INNER JOIN R_ItensLoteGlosa ILG ON ILG.ILG_IdLoteGlosa = LG.LG_Id AND LG.LG_Id = @IdLoteGlosa
                       INNER JOIN R_Lote L ON L.L_Id = LG.LG_IdLote
                       INNER JOIN R_ItensLote IL ON IL.IL_IdLote = L.L_Id AND IL.IL_IdGuiaAtendimento = ILG.ILG_IdGuiaAtendimento
                       INNER JOIN R_GuiaAtendimento GA ON GA.GA_Id = IL.IL_IdGuiaAtendimento
                       INNER JOIN R_GuiaDemonstrativo GD ON GD.GD_IdGuia = GA.GA_Id
                       INNER JOIN R_ProcGuiaDemonstrativo PGD ON PGD.PGD_IdGuiaDemonstrativo = GD.GD_Id
                       INNER JOIN R_StausProcGuiaDemonstrativo SPGD ON SPGD.SPGD_Id = PGD.PGD_IdStatusProcGuiaDemonstrativo AND SPGD.SPGD_Enum = 5
                       INNER JOIN R_JustificativaGlosa JG ON JG.JG_Id = PGD.PGD_IdJustificativa";

      return Contexto.Database.SqlQuery<XMLLoteGlosa>(query, new SqlParameter("@IdLoteGlosa", IdLoteGlosa)).ToList();
    }

    public List<RelatorioLoteGlosa> GetRelatorioLoteGlosa(int IdLoteGlosa)
    {
      string query = @"SELECT
                        GD.GD_NumeroCarteirinha [NumeroCarteirinha],
                        GD.GD_NumeroAtendimento [NumeroAtendimento],
                        PGD.PGD_CodigoProcedimento [CodigoProcedimento],
                        PGD.PGD_DescricaoProcedimento [DescricaoProcedimento],
                        GD.GD_DataAtendimento [DataAtendimento],
                        PGD.PGD_TotalApresentado [TotalApresentado],
                        PGD.PGD_TotalGlosado [TotalGlosado],
                        PGD.PGD_TotalPago [TotalPago],
                        PGD.PGD_ObservacoesGerais [ObservacoesGerais],
                        PGD.PGD_CodigoGlosa [CodigoGlosa],
                        PGD.PGD_DescricaoGlosa [DescricaoGlosa],
                        JG.JG_Codigo [CodigoJustificativa],
                        JG.JG_Descricao [DescricaoJustificativa],
                        PGD.PGD_Comentario [Comentario]
                       FROM R_LoteGlosa LG
                       INNER JOIN R_ItensLoteGlosa ILG ON ILG.ILG_IdLoteGlosa = LG.LG_Id AND LG.LG_Id = @IdLoteGlosa
                       INNER JOIN R_Lote L ON L.L_Id = LG.LG_IdLote
                       INNER JOIN R_ItensLote IL ON IL.IL_IdLote = L.L_Id AND IL.IL_IdGuiaAtendimento = ILG.ILG_IdGuiaAtendimento
                       INNER JOIN R_GuiaAtendimento GA ON GA.GA_Id = IL.IL_IdGuiaAtendimento
                       INNER JOIN R_GuiaDemonstrativo GD ON GD.GD_IdGuia = GA.GA_Id
                       INNER JOIN R_ProcGuiaDemonstrativo PGD ON PGD.PGD_IdGuiaDemonstrativo = GD.GD_Id
                       INNER JOIN R_StausProcGuiaDemonstrativo SPGD ON SPGD.SPGD_Id = PGD.PGD_IdStatusProcGuiaDemonstrativo AND SPGD.SPGD_Enum = 5
                       INNER JOIN R_JustificativaGlosa JG ON JG.JG_Id = PGD.PGD_IdJustificativa";

      return Contexto.Database.SqlQuery<RelatorioLoteGlosa>(query, new SqlParameter("@IdLoteGlosa", IdLoteGlosa)).ToList();
    }


    public string GetCodigoLote(int IdLoteGlosa)
    {
      string query = @"SELECT
                        LG_Codigo
                       FROM R_LoteGlosa
                       WHERE LG_Id = @IdLoteGlosa";

      return Contexto.Database.SqlQuery<string>(query, new SqlParameter("@IdLoteGlosa", IdLoteGlosa)).FirstOrDefault();
    }
    #endregion

    public void ProcessarGlosa(int IdRepasse, DateTime DataCreditoFatura)
    {
      RepasseService repasseService = new RepasseService(Contexto);
      R_Repasse repasse = repasseService.GetById(IdRepasse);

      StatusRepasseServices statusRepasseServices = new StatusRepasseServices();
      int IdStatusProcessado = statusRepasseServices.GetIdByEnum(EnumStatusRepasse.PROCESSADO);

      if (repasse.R_IdStatusRepasse != IdStatusProcessado)
        throw new CustomException("Só pode processar glosa em Repasses processados");

      StatusItensLoteServices statusItensLoteServices = new StatusItensLoteServices();
      List<R_StatusItensLote> ListStatusItensLote = statusItensLoteServices.Getall();

      StatusGlosaServices statusGlosaServices = new StatusGlosaServices();
      int IdStatusGlosaGeradas = statusGlosaServices.GetIdByEnum(StatusGlosaEnum.GlosasGeradas);

      TipoLoteGlosaServices tipoLoteGlosaServices = new TipoLoteGlosaServices();
      List<R_TipoLoteGlosa> ListTipoLoteGlosa = tipoLoteGlosaServices.Getall();

      List<LoteGlosaProcessaCabecalho> ListLoteGlosaProcessaCabecalho = GetListLoteGlosaProcessaCabecalho(IdRepasse);

      foreach (LoteGlosaProcessaCabecalho cabecalho in ListLoteGlosaProcessaCabecalho)
      {
        if (!cabecalho.VencimentoGlosa.HasValue)
          throw new CustomException("Paramêtro de vencimento de glosa não definido, gentileza cadastrar o parâmetro para o convênio.");

        int idTipoLoteGlosa = cabecalho.statusItensLoteEnum == StatusItensLoteEnum.Glosado ? ListTipoLoteGlosa.Where(a => a.TLG_Enum == (int)TipoLoteGlosaEnum.Reapresentacao).Select(a => a.TLG_Id).FirstOrDefault()
                       : cabecalho.statusItensLoteEnum == StatusItensLoteEnum.Reapresentacao ? ListTipoLoteGlosa.Where(a => a.TLG_Enum == (int)TipoLoteGlosaEnum.Contestacao).Select(a => a.TLG_Id).FirstOrDefault()
                       : cabecalho.statusItensLoteEnum == StatusItensLoteEnum.Contestacao ? ListTipoLoteGlosa.Where(a => a.TLG_Enum == (int)TipoLoteGlosaEnum.Treplica).Select(a => a.TLG_Id).FirstOrDefault()
                       : cabecalho.statusItensLoteEnum == StatusItensLoteEnum.Treplica ? ListTipoLoteGlosa.Where(a => a.TLG_Enum == (int)TipoLoteGlosaEnum.Treplica).Select(a => a.TLG_Id).FirstOrDefault()
                       : 0;

        string Codigo = GerarCodigo(cabecalho.statusItensLoteEnum, idTipoLoteGlosa, cabecalho.IdLote, cabecalho.NumeroLote);
        R_LoteGlosa loteGlosa = cabecalho.ModelToEntityCreate(IdStatusGlosaGeradas, idTipoLoteGlosa, DataCreditoFatura.AddDays(cabecalho.VencimentoGlosa.Value), Codigo, IdRepasse, cabecalho.IdLote, User.IdUsuario, DataCreditoFatura);

        using (TransactionScope scope = new TransactionScope(TransactionScopeOption.RequiresNew))
        {
          Create(loteGlosa);

          new LogLoteGlosaService().Create(EnumTipoLogLoteGlosa.ResponsavelAssumiuLote, loteGlosa.LG_Id, string.Format("Usuário de CPF {0} criou o lote {1}.", ContextoUsuario.UserLogged.CPF, loteGlosa.LG_Codigo));

          foreach (LoteGlosaProcessa loteGlosaProcessa in cabecalho.ListaLoteGlosaProcessa)
          {
            R_ItensLoteGlosa itensLoteGlosa = loteGlosaProcessa.ModelToEntityCreate(loteGlosa.LG_Id, IdRepasse);
            Create(itensLoteGlosa);
          }

          int idStatusItensLote = cabecalho.statusItensLoteEnum == StatusItensLoteEnum.Glosado ? ListStatusItensLote.Where(a => a.SIL_Enum == (int)StatusItensLoteEnum.Reapresentacao).Select(a => a.SIL_Id).FirstOrDefault()
               : cabecalho.statusItensLoteEnum == StatusItensLoteEnum.Reapresentacao ? ListStatusItensLote.Where(a => a.SIL_Enum == (int)StatusItensLoteEnum.Contestacao).Select(a => a.SIL_Id).FirstOrDefault()
               : cabecalho.statusItensLoteEnum == StatusItensLoteEnum.Contestacao ? ListStatusItensLote.Where(a => a.SIL_Enum == (int)StatusItensLoteEnum.Treplica).Select(a => a.SIL_Id).FirstOrDefault()
               : cabecalho.statusItensLoteEnum == StatusItensLoteEnum.Treplica ? ListStatusItensLote.Where(a => a.SIL_Enum == (int)StatusItensLoteEnum.Treplica).Select(a => a.SIL_Id).FirstOrDefault()
               : 0;

          string update = @"UPDATE R_ItensLote SET IL_IdStatusItensLote = @idStatusItensLote WHERE IL_Id IN (#Ids#)";
          update = update.Replace("#Ids#", string.Join(",", cabecalho.ListaLoteGlosaProcessa.Select(a => a.IdItensLote).ToArray()));
          Contexto.Database.ExecuteSqlCommand(update, new SqlParameter("@idStatusItensLote", idStatusItensLote));

          int IdStatusRepasseProcessadoGlosa = statusRepasseServices.GetIdByEnum(EnumStatusRepasse.ProcessadoGlosado);
          repasse.R_IdStatusRepasse = IdStatusRepasseProcessadoGlosa;
          Edit(repasse);

          scope.Complete();
        }
      }
    }

    public string GerarCodigo(StatusItensLoteEnum statusItensLoteEnum, int idTipoLoteGlosa, int IdLote, string NumeroLote)
    {
      string Codigo = string.Empty;

      string prefixo = statusItensLoteEnum == StatusItensLoteEnum.Glosado ? "R"
                : statusItensLoteEnum == StatusItensLoteEnum.Reapresentacao ? "C"
                : statusItensLoteEnum == StatusItensLoteEnum.Contestacao ? "T"
                : statusItensLoteEnum == StatusItensLoteEnum.Treplica ? "T"
                : string.Empty;

      UltimoRegistro ultimoRegistro = GetUltimoRegistro(IdLote, prefixo);

      if (ultimoRegistro == null)
        Codigo = string.Format("{0}1{1}", prefixo, NumeroLote.ToString().PadLeft(10, '0'));
      else
      {
        Codigo = GerarCodigoAuxiliar(prefixo, ultimoRegistro, true, NumeroLote);
      }

      return Codigo;
    }

    public string GerarCodigoAuxiliar(string prefixo, UltimoRegistro ultimoRegistro, bool IncrementVersao, string NumeroLote)
    {
      string sequencial = ultimoRegistro.CodigoCompleto.Substring(ultimoRegistro.CodigoCompleto.Length - 10);
      string numeroVersao = String.Join("", Regex.Split(ultimoRegistro.CodigoCompleto.Replace(sequencial, ""), @"[^\d]"));
      int numeroVersaoIncrement = Convert.ToInt32(numeroVersao) + 1;

      string codigo = string.Empty;
      if (IncrementVersao)
        codigo = string.Format("{0}{1}{2}", prefixo, numeroVersaoIncrement, NumeroLote.PadLeft(10, '0'));
      else if (!IncrementVersao)
        codigo = string.Format("{0}{1}{2}", prefixo, 1, NumeroLote.PadLeft(10, '0'));

      return codigo;
    }

    public void CancelarProcessamentoRepasse(int IdRepasse)
    {
      List<LoteGlosaToRemoveCabecalho> ListLoteGlosaToRemoveCabecalho = GetLoteGlosaToRemove(IdRepasse);
      if (ListLoteGlosaToRemoveCabecalho.Where(a => a.statusGlosaEnum != StatusGlosaEnum.GlosasGeradas).Count() > 0)
        throw new CustomException("Já existe lotes em processamento, repasse não pode ser mais cancelado.");
      else
      {
        if (ListLoteGlosaToRemoveCabecalho.Count() > 0)
        {
          string DeleteItensLoteGlosa = @"DELETE FROM R_ItensLoteGlosa WHERE ILG_IdLoteGlosa IN (#ids#)";
          DeleteItensLoteGlosa = DeleteItensLoteGlosa.Replace("#ids#", string.Join(",", ListLoteGlosaToRemoveCabecalho.Select(a => a.IdLote).ToArray()));
          Contexto.Database.ExecuteSqlCommand(DeleteItensLoteGlosa);

          string DeleteLogLoteGlosa = @"DELETE FROM R_LogLoteGlosa WHERE LLG_IdLoteGlosa IN (#ids#)".Replace("#ids#", string.Join(",", ListLoteGlosaToRemoveCabecalho.Select(a => a.IdLote).ToArray()));
          Contexto.Database.ExecuteSqlCommand(DeleteLogLoteGlosa, new SqlParameter("@IdRepasse", IdRepasse));

          string DeleteLoteGlosa = @"DELETE FROM R_LoteGlosa WHERE LG_IdRepasse = @IdRepasse";
          Contexto.Database.ExecuteSqlCommand(DeleteLoteGlosa, new SqlParameter("@IdRepasse", IdRepasse));

          RepasseService repasseService = new RepasseService();
          StatusItensLoteServices statusItensLoteServices = new StatusItensLoteServices();
          foreach (var item in ListLoteGlosaToRemoveCabecalho)
          {
            List<RepassesStatus> ListaRepassesStatus = repasseService.GetRepassesStatus(item.ListLoteGlosaToRemove.Select(a => a.IdGuiaAtendimento).ToList());
            if (ListaRepassesStatus.Count() > 0)
              if (ListaRepassesStatus.Where(a => a.EnumStatusRepasse == EnumStatusRepasse.PROCESSADO).Count() == 0
               && ListaRepassesStatus.Where(a => a.EnumStatusRepasse == EnumStatusRepasse.ProcessadoGlosado).Count() == 0)
              {
                int IdStatusItemGlosado = statusItensLoteServices.GetIdByEnum(StatusItensLoteEnum.Glosado);
                string UpdateItensLote = @"UPDATE R_ItensLote SET IL_IdStatusItensLote = @IdStatusItemGlosado WHERE IL_IdGuiaAtendimento IN (#ids#)";
                UpdateItensLote = UpdateItensLote.Replace("#ids#", string.Join(",", item.ListLoteGlosaToRemove.Select(a => a.IdGuiaAtendimento).ToArray()));
                Contexto.Database.ExecuteSqlCommand(UpdateItensLote, new SqlParameter("@IdStatusItemGlosado", IdStatusItemGlosado));
              }
              else if (ListaRepassesStatus.Where(a => a.EnumStatusRepasse == EnumStatusRepasse.ProcessadoGlosado).Count() == 1)
              {
                int IdStatusItemGlosado = statusItensLoteServices.GetIdByEnum(StatusItensLoteEnum.Reapresentacao);
                string UpdateItensLote = @"UPDATE R_ItensLote SET IL_IdStatusItensLote = @IdStatusItemGlosado WHERE IL_IdGuiaAtendimento IN (#ids#)";
                UpdateItensLote = UpdateItensLote.Replace("#ids#", string.Join(",", item.ListLoteGlosaToRemove.Select(a => a.IdGuiaAtendimento).ToArray()));
                Contexto.Database.ExecuteSqlCommand(UpdateItensLote, new SqlParameter("@IdStatusItemGlosado", IdStatusItemGlosado));
              }
              else if (ListaRepassesStatus.Where(a => a.EnumStatusRepasse == EnumStatusRepasse.ProcessadoGlosado).Count() == 2)
              {
                int IdStatusItemGlosado = statusItensLoteServices.GetIdByEnum(StatusItensLoteEnum.Contestacao);
                string UpdateItensLote = @"UPDATE R_ItensLote SET IL_IdStatusItensLote = @IdStatusItemGlosado WHERE IL_IdGuiaAtendimento IN (#ids#)";
                UpdateItensLote = UpdateItensLote.Replace("#ids#", string.Join(",", item.ListLoteGlosaToRemove.Select(a => a.IdGuiaAtendimento).ToArray()));
                Contexto.Database.ExecuteSqlCommand(UpdateItensLote, new SqlParameter("@IdStatusItemGlosado", IdStatusItemGlosado));
              }
              else if (ListaRepassesStatus.Where(a => a.EnumStatusRepasse == EnumStatusRepasse.ProcessadoGlosado).Count() > 2)
              {
                int IdStatusItemGlosado = statusItensLoteServices.GetIdByEnum(StatusItensLoteEnum.Treplica);
                string UpdateItensLote = @"UPDATE R_ItensLote SET IL_IdStatusItensLote = @IdStatusItemGlosado WHERE IL_IdGuiaAtendimento IN (#ids#)";
                UpdateItensLote = UpdateItensLote.Replace("#ids#", string.Join(",", item.ListLoteGlosaToRemove.Select(a => a.IdGuiaAtendimento).ToArray()));
                Contexto.Database.ExecuteSqlCommand(UpdateItensLote, new SqlParameter("@IdStatusItemGlosado", IdStatusItemGlosado));
              }
          }
        }
      }
    }

    public void AssumirLote(int IdLote)
    {
      StatusGlosaServices statusGlosaServices = new StatusGlosaServices();

      int IdStatusEmAnaliseUnicooper = statusGlosaServices.GetIdByEnum(StatusGlosaEnum.EmAnaliseUnicooper);

      R_LoteGlosa loteGlosa = GetById(IdLote);
      if (User.ConveniosResponsavel.Where(a => a.IdConvenio == loteGlosa.LG_IdConvenio).Count() == 0)
        throw new CustomException("Você não pode Assumir este lote.");

      if (loteGlosa.LG_IdResponsavelConvenio.HasValue)
        throw new CustomException("Já existe responsável para esse lote.");

      ItensLoteServices itensLoteServices = new ItensLoteServices();
      ItensLoteGlosaServices itensLoteGlosaServices = new ItensLoteGlosaServices();
      using (TransactionScope scope = new TransactionScope(TransactionScopeOption.RequiresNew))
      {
        loteGlosa.LG_IdResponsavelConvenio = User.ConveniosResponsavel.Where(a => a.IdConvenio == loteGlosa.LG_IdConvenio).Select(a => a.IdResponsavelConvenio).FirstOrDefault();
        loteGlosa.LG_IdStatusGlosa = IdStatusEmAnaliseUnicooper;
        Edit(loteGlosa);

        List<int> IdsGuiaAtendimento = itensLoteGlosaServices.ListaIdsGuiaAtendimento(loteGlosa.LG_Id);
        itensLoteServices.UpdateStatus(IdsGuiaAtendimento, SituacaoItensLoteEnum.EmAnaliseUnicooper);

        new LogLoteGlosaService().Create(EnumTipoLogLoteGlosa.ResponsavelAssumiuLote, IdLote, string.Format("Responsável de CPF {0} assumiu o lote {1}.", ContextoUsuario.UserLogged.CPF, loteGlosa.LG_Codigo));
        scope.Complete();
      }
    }

    public void TransferirResponsavel(int IdLote, int IdResponsavel)
    {
      R_LoteGlosa loteGlosa = GetById(IdLote);

      StatusGlosaServices statusGlosaServices = new StatusGlosaServices();
      int IdStatusEmAnaliseConvenio = statusGlosaServices.GetIdByEnum(StatusGlosaEnum.EmAnaliseConvenio);

      if (loteGlosa.LG_IdStatusGlosa == IdStatusEmAnaliseConvenio)
        throw new CustomException("Lote já está sendo analisado pelo convênio, não pode ser mais modificado.");

      ResponsavelConvenioServices responsavelConvenioServices = new ResponsavelConvenioServices();
      List<R_RelacaoResponsavelConvenio> ListaRelacaoResponsavelConvenio = responsavelConvenioServices.GetListRelacaoResponsavelConvenio(IdResponsavel);

      if (ListaRelacaoResponsavelConvenio.Where(a => a.RRC_IdConvenio == loteGlosa.LG_IdConvenio).Count() == 0)
        throw new CustomException("Não pode ser transferido para o Responsável selecionado.");

      using (TransactionScope scope = new TransactionScope(TransactionScopeOption.RequiresNew))
      {

        loteGlosa.LG_IdResponsavelConvenio = IdResponsavel;
        Edit(loteGlosa);

        R_ResponsavelConvenio novoResponsavel = responsavelConvenioServices.GetById(IdResponsavel);
        new LogLoteGlosaService().Create(EnumTipoLogLoteGlosa.AlteracaoResponsavel, IdLote, string.Format("Responsável de CPF {0} transferiu o lote {1} para o responsável de CPF {2}."
          , ContextoUsuario.UserLogged.CPF, loteGlosa.LG_Codigo, novoResponsavel.RC_CPF));
        scope.Complete();
      }
    }

    public void GeraGlosaNaoPago(DateTime DataInicial, List<int> IdItemLote, int IdRepasse, int IdMotivo, string DescMotivo)
    {
      string Query = string.Format(@"SELECT 
							                          C.C_Id [CodigoConvenio],
							                          IL.IL_Id [CodigoItemLote],
							                          C.C_RazaoSocial [Nome],
							                          C.C_VencLoteGlosa [DiasVencimento],
	                                      L.L_Id [IdLote],
	                                      L.L_NumeroLote [NumeroLote]
						                          FROM R_ItensLote IL 
						                          INNER JOIN R_GuiaAtendimento GA ON GA.GA_Id=IL.IL_IdGuiaAtendimento AND IL.IL_Id IN ({0})
						                          INNER JOIN R_Convenio C ON C.C_Id=GA.GA_IdConvenio
                                      INNER JOIN R_Lote L ON L.L_Id = IL.IL_IdLote", string.Join(", ", IdItemLote));

      List<ConvenioVencimento> conveniosVencimentos = Contexto.Database.SqlQuery<ConvenioVencimento>(Query).ToList();

      if (conveniosVencimentos.Where(a => !a.DiasVencimento.HasValue).Count() > 0)
        throw new CustomException("É necessário que os convênios utilizados pelos itens tenham os dias de vencimento de glosa cadastrados para gerar uma glosa.");

      if (string.IsNullOrEmpty(DescMotivo))
        throw new CustomException("É necessário que selecionar um motivo para gerar uma glosa.");

      foreach (int id in IdItemLote)
      {
        Query = @"INSERT INTO 
                R_GuiaDemonstrativo (
	                GD_IdGuia,
	                GD_IdDemonstrativoConvenio,
	                GD_NumeroGuia,
	                GD_NumeroCarteirinha,
	                GD_NumeroAtendimento,
	                GD_DataAtendimento,
	                GD_TotalFaturado,
	                GD_TotalGlosado,
	                GD_TotalPago,
	                GD_TotalApresentado,
	                DC_IdStatusGuiaDemonstrativoConvenio,
	                GD_InfoProcessamento,
	                GD_DataProcessamento
                )
                SELECT
	                GA.GA_Id [GD_IdGuia],
	                NULL [GD_IdDemonstrativoConvenio],
	                GA.GA_NroUnicooper [GD_NumeroGuia],
	                GA.GA_CodCarteiraPaciente [GD_NumeroCarteirinha],
	                GA.GA_AtendimentoHospital [GD_NumeroAtendimento],
	                GA.GA_DataAtendimento [GD_DataAtendimento],
	                GA.GA_ValorFaturado [GD_TotalFaturado],
	                GA.GA_ValorFaturado [GD_TotalGlosado],
	                0 [GD_TotalPago],
	                0 [GD_TotalApresentado],
	                2 [DC_IdStatusGuiaDemonstrativoConvenio],
	                'Processamento concluído.' [GD_InfoProcessamento],
	                GETDATE() [GD_DataProcessamento]
                FROM R_ItensLote IL
                INNER JOIN R_GuiaAtendimento GA ON GA.GA_Id=IL.IL_IdGuiaAtendimento
                WHERE IL.IL_Id = @Id";

        Contexto.Database.ExecuteSqlCommand(Query, new SqlParameter("@Id", id));

        Query = @"INSERT INTO
                    R_ProcGuiaDemonstrativo 
                    (
	                    PGD_CodigoProcedimento,
	                    PGD_DescricaoProcedimento,
	                    PGD_IdGuiaDemonstrativo,
	                    PGD_TotalFaturado,
	                    PGD_TotalGlosado,
	                    PGD_TotalPago,
	                    PGD_TotalApresentado,
	                    PGD_CodigoGlosa,
	                    PGD_DescricaoGlosa,
	                    PGD_ObservacoesGerais,
	                    PGD_IdJustificativa,
	                    PGD_Comentario
                    )
                    SELECT
	                    PGA.PGA_CodProcedimento [PGD_CodigoProcedimento],
	                    PGA.PGA_DescProcedimento [PGD_DescricaoProcedimento],
	                    GD.GD_Id [PGD_IdGuiaDemonstrativo],
	                    PGA.GA_ValorFaturado [PGD_TotalFaturado],
	                    PGA.GA_ValorFaturado [PGD_TotalGlosado],
	                    0 [PGD_TotalPago],
	                    0 [PGD_TotalApresentado],
	                    @IdMotivoGlosa [PGD_CodigoGlosa],
	                    @DescMotivoGlosa [PGD_DescricaoGlosa],
	                    NULL [PGD_ObservacoesGerais],
	                    NULL [PGD_IdJustificativa],
	                    NULL [PGD_Comentario]
                    FROM R_ItensLote IL
                    INNER JOIN R_ProcGuiaAtendimento PGA ON PGA.PGA_IdGuiaAtendimento=IL.IL_IdGuiaAtendimento
                    INNER JOIN R_GuiaDemonstrativo GD ON GD.GD_IdGuia=IL.IL_IdGuiaAtendimento
                    WHERE IL.IL_Id = @Id";

        Contexto.Database.ExecuteSqlCommand(Query, new SqlParameter("@Id", id), new SqlParameter("@IdMotivoGlosa", IdMotivo), new SqlParameter("@DescMotivoGlosa", DescMotivo));
      }

      int idTipoLoteGlosa = new TipoLoteGlosaServices().GetIdByEnum(TipoLoteGlosaEnum.Reapresentacao);
      List<R_LoteGlosa> LotesGeradosConvenio = new List<R_LoteGlosa>();

      foreach (ConvenioVencimento conv in conveniosVencimentos)
      {
        int IdLote = LotesGeradosConvenio.Where(a => a.LG_IdConvenio == conv.CodigoConvenio).Select(a => a.LG_Id).FirstOrDefault();

        if (IdLote == 0)
        {
          R_LoteGlosa loteGlosa = new R_LoteGlosa()
          {
            LG_Codigo = GerarCodigo(StatusItensLoteEnum.Glosado, idTipoLoteGlosa, conv.IdLote, conv.NumeroLote),
            LG_DataCriacao = DateTime.Now,
            LG_DataReapresentacao = null,
            LG_DataVencimentoAnalise = DataInicial.AddDays(conv.DiasVencimento.Value),
            LG_IdConvenio = conv.CodigoConvenio,
            LG_IdStatusGlosa = new StatusGlosaServices().GetIdByEnum(StatusGlosaEnum.GlosasGeradas),
            LG_IdTipoLoteGlosa = idTipoLoteGlosa,
            LG_IdRepasse = IdRepasse,
            LG_IdUsuarioCriacao = User.IdUsuario,
            LG_DataInicio = DataInicial
          };

          Create(loteGlosa);

          new LogLoteGlosaService().Create(EnumTipoLogLoteGlosa.ResponsavelAssumiuLote, loteGlosa.LG_Id, string.Format("Usuário de CPF {0} criou o lote {1}.", ContextoUsuario.UserLogged.CPF, loteGlosa.LG_Codigo));

          LotesGeradosConvenio.Add(loteGlosa);
          IdLote = loteGlosa.LG_Id;
        }

        Query = @"INSERT INTO 
                  R_ItensLoteGlosa
                  (
	                  ILG_IdLoteGlosa,
	                  ILG_IdGuiaAtendimento,
	                  ILG_IdGuiaDemonstrativo,
	                  ILG_IdRepasse
                  )
                  SELECT
	                  @IdLoteGlosa [ILG_IdLoteGlosa],
	                  IL.IL_IdGuiaAtendimento [ILG_IdGuiaAtendimento],
	                  GD.GD_Id [ILG_IdGuiaDemonstrativo],
	                  @IdRepasse [ILG_IdRepasse]
                  FROM R_ItensLote IL
                  INNER JOIN R_StatusItensLote SIL ON SIL.SIL_Id = IL.IL_IdStatusItensLote 
                  INNER JOIN R_GuiaAtendimento GA ON GA.GA_Id = IL.IL_IdGuiaAtendimento
                  INNER JOIN R_GuiaDemonstrativo GD ON GD_IdGuia=IL.IL_IdGuiaAtendimento
                  WHERE IL.IL_Id = @IdItemLote";

        Contexto.Database.ExecuteSqlCommand(Query, new SqlParameter("@IdLoteGlosa", IdLote), new SqlParameter("@IdConvenio", conv.CodigoConvenio)
          , new SqlParameter("@IdRepasse", IdRepasse), new SqlParameter("@IdItemLote", conv.CodigoItemLote));

        string CodLoteGlosa = LotesGeradosConvenio.Where(a => a.LG_Id == IdLote).Select(a => a.LG_Codigo).FirstOrDefault();
        string NroGuiaItem = Contexto.R_ItensLote.Where(a => a.IL_Id == conv.CodigoItemLote).Select(a => a.IL_NumeroGuia).FirstOrDefault();
        new LogLoteGlosaService().Create(EnumTipoLogLoteGlosa.ItemInseridoNoLote, IdLote, string.Format("Item de lote de guia número {0} inserido no lote de glosa {1}.", NroGuiaItem, CodLoteGlosa));
      }

      Query = string.Format("UPDATE R_ItensLote SET IL_IdStatusItensLote = (SELECT SIL_Id FROM R_StatusItensLote WHERE SIL_Enum = 3) WHERE IL_Id IN ({0})", string.Join(", ", IdItemLote));
      Contexto.Database.ExecuteSqlCommand(Query);
    }

    public void JustificaProcsGlosa(List<int> IdsItens, int? IdJustificativa, string Comentario, int IdLoteGlosa)
    {
      R_LoteGlosa loteGlosa = GetById(IdLoteGlosa);
      StatusGlosaServices statusGlosaServices = new StatusGlosaServices();
      int IdStatusEmAnaliseConvenio = statusGlosaServices.GetIdByEnum(StatusGlosaEnum.EmAnaliseConvenio);

      if (loteGlosa.LG_IdStatusGlosa == IdStatusEmAnaliseConvenio)
        throw new CustomException("Lote já está sendo analisado pelo convênio, não pode ser mais modificado.");

      using (TransactionScope scope = new TransactionScope(TransactionScopeOption.RequiresNew))
      {
        StausProcGuiaDemonstrativoServices stausProcGuiaDemonstrativoServices = new StausProcGuiaDemonstrativoServices();
        int IdStatusJustificada = stausProcGuiaDemonstrativoServices.GetIdByEnum(StatusProcGuiaDemonstrativoEnum.Justificada);
        string Query = string.Format(@"UPDATE R_ProcGuiaDemonstrativo 
                                       SET PGD_IdJustificativa = @IdJustificativa, PGD_IdStatusProcGuiaDemonstrativo = @IdStatusJustificada 
                                       WHERE PGD_Id IN ({0})", string.Join(", ", IdsItens));
        Contexto.Database.ExecuteSqlCommand(Query, new SqlParameter("@IdJustificativa", IdJustificativa)
                                                 , new SqlParameter("@IdStatusJustificada", IdStatusJustificada));

        if (IdJustificativa.HasValue)
        {
          Query = @"INSERT INTO 
                R_LogGuiaAtendimento 
                (LGA_DataCriacao, LGA_IdUsuarioCriacao, LGA_Descricao) 
                VALUES 
	              (
		              @DataAtual, 
		              @IdUsuario, 
		              CONCAT
		              (
			              'Inclusão da justificativa de glosa no procedimento ', 
			              (SELECT PGD.PGD_CodigoProcedimento FROM R_ProcGuiaDemonstrativo PGD WHERE PGD.PGD_Id = @IdItem),
			              ' da guia ',                           
			              (SELECT GD.GD_NumeroGuia FROM R_ProcGuiaDemonstrativo PGD INNER JOIN R_GuiaDemonstrativo GD ON GD.GD_Id = PGD.PGD_IdGuiaDemonstrativo WHERE PGD.PGD_Id = @IdItem),
			              ' do lote ',
			              (SELECT 
							      TOP(1) LG.LG_Codigo
							      FROM R_ItensLoteGlosa ILG
							      INNER JOIN R_GuiaDemonstrativo GD ON GD.GD_Id = ILG.ILG_IdGuiaDemonstrativo
							      INNER JOIN R_ProcGuiaDemonstrativo PGD ON PGD.PGD_IdGuiaDemonstrativo = GD.GD_Id
							      INNER JOIN R_LoteGlosa LG ON LG.LG_Id = ILG.ILG_IdLoteGlosa
							      WHERE PGD.PGD_Id = @IdItem),
			              ' - ',
						        (SELECT JG.JG_Descricao FROM R_ProcGuiaDemonstrativo PGD INNER JOIN R_JustificativaGlosa JG ON JG.JG_Id = PGD.PGD_IdJustificativa WHERE PGD.PGD_Id = @IdItem)
		              )
	              )";

          foreach (int idItem in IdsItens)
            Contexto.Database.ExecuteSqlCommand(Query, new SqlParameter("@IdUsuario", ContextoUsuario.UserLogged.IdUsuario)
                                                     , new SqlParameter("@IdItem", idItem)
                                                     , new SqlParameter("@DataAtual", DateTime.Now));

          R_JustificativaGlosa justificativa = new JustificativaGlosaService().GetById(IdJustificativa.Value);
          new LogLoteGlosaService().Create(EnumTipoLogLoteGlosa.ItemJustificadoGlosa, IdLoteGlosa, string.Format("Procedimentos do lote de glosa {0} justificado - {1}.", loteGlosa.LG_Codigo, justificativa.JG_Descricao));
        }

        if (!string.IsNullOrEmpty(Comentario))
        {
          Query = string.Format(@"UPDATE R_ProcGuiaDemonstrativo SET PGD_Comentario = @Comentario WHERE PGD_Id IN ({0})", string.Join(", ", IdsItens));
          Contexto.Database.ExecuteSqlCommand(Query, new SqlParameter("@Comentario", Comentario));
        }

        MotivoGlosaJustificativaServices motivoGlosaJustificativaServices = new MotivoGlosaJustificativaServices();
        motivoGlosaJustificativaServices.Create(IdsItens, IdLoteGlosa);

        AtualizarStatus(IdLoteGlosa);

        ItensLoteServices itensLoteServices = new ItensLoteServices();
        ItensLoteGlosaServices itensLoteGlosaServices = new ItensLoteGlosaServices();
        List<int> IdsGuiaAtendimento = itensLoteGlosaServices.ListaIdsGuiaAtendimento(IdsItens);
        itensLoteServices.UpdateStatus(IdsGuiaAtendimento, SituacaoItensLoteEnum.Justificada);
        scope.Complete();
      }
    }

    public void EnviarConvenio(int IdLoteGlosa)
    {
      R_LoteGlosa loteGlosa = GetById(IdLoteGlosa);
      StatusGlosaServices statusGlosaServices = new StatusGlosaServices();
      int IdStatusEmAnaliseConvenio = statusGlosaServices.GetIdByEnum(StatusGlosaEnum.EmAnaliseConvenio);
      int IdStatusDisponivelEnviarConvenio = statusGlosaServices.GetIdByEnum(StatusGlosaEnum.DisponivelEnviarConvenio);

      if (loteGlosa.LG_IdStatusGlosa == IdStatusEmAnaliseConvenio)
        throw new CustomException("Lote já está sendo analisado pelo convênio");

      using (TransactionScope scope = new TransactionScope(TransactionScopeOption.RequiresNew))
      {
        if (loteGlosa.LG_IdStatusGlosa == IdStatusDisponivelEnviarConvenio)
        {
          ItensLoteServices itensLoteServices = new ItensLoteServices();
          ItensLoteGlosaServices itensLoteGlosaServices = new ItensLoteGlosaServices();

          loteGlosa.LG_IdStatusGlosa = IdStatusEmAnaliseConvenio;
          Edit(loteGlosa);

          List<int> IdsGuiaAtendimento = itensLoteGlosaServices.ListaIdsGuiaAtendimento(loteGlosa.LG_Id);
          itensLoteServices.UpdateStatus(IdsGuiaAtendimento, SituacaoItensLoteEnum.EmAnaliseConvenio);
        }
        else
          throw new CustomException("Lote indisponível para envio ao convênio, verifique se todos os procedimentos já foram justificados.");

        new LogLoteGlosaService().Create(EnumTipoLogLoteGlosa.EnviadoConvenio, IdLoteGlosa, string.Format("Lote de glosa {0} enviado ao convênio.", loteGlosa.LG_Codigo));
        scope.Complete();
      }
    }

    public void AceitarGlosa(List<int> IdsProcGuiaDemonstrativo, int CodigoGlosa, string Comentario, int CodJustificativa)
    {
      StausProcGuiaDemonstrativoServices stausProcGuiaDemonstrativoServices = new StausProcGuiaDemonstrativoServices();
      int IdStatusGlosaAceita = stausProcGuiaDemonstrativoServices.GetIdByEnum(StatusProcGuiaDemonstrativoEnum.GlosaAceita);

      StatusGlosaServices statusGlosaServices = new StatusGlosaServices();
      int IdStatusEmAnaliseConvenio = statusGlosaServices.GetIdByEnum(StatusGlosaEnum.EmAnaliseConvenio);

      R_LoteGlosa loteGlosa = GetById(CodigoGlosa);

      if (loteGlosa.LG_IdStatusGlosa == IdStatusEmAnaliseConvenio)
        throw new CustomException("Lote já está sendo analisado pelo convênio");
      using (TransactionScope scope = new TransactionScope(TransactionScopeOption.RequiresNew))
      {
        string query = @"UPDATE R_ProcGuiaDemonstrativo SET PGD_IdStatusProcGuiaDemonstrativo = @IdStatusGlosaAceita, PGD_IdJustificativa = @CodJustificativa, PGD_Comentario = @Comentario WHERE PGD_Id IN (#ids#)";
        query = query.Replace("#ids#", string.Join(",", IdsProcGuiaDemonstrativo));
        Contexto.Database.ExecuteSqlCommand(query, new SqlParameter("@IdStatusGlosaAceita", IdStatusGlosaAceita)
                                                 , new SqlParameter("@CodJustificativa", CodJustificativa)
                                                 , new SqlParameter("@Comentario", Comentario));

        new LogLoteGlosaService().Create(EnumTipoLogLoteGlosa.ItemAceitoGlosa, CodigoGlosa, string.Format("Procedimentos {0} aceito glosa.", string.Join(",", IdsProcGuiaDemonstrativo.ToArray())));
        AtualizarStatus(CodigoGlosa);

        ItensLoteServices itensLoteServices = new ItensLoteServices();
        ItensLoteGlosaServices itensLoteGlosaServices = new ItensLoteGlosaServices();
        List<int> IdsGuiaAtendimento = itensLoteGlosaServices.ListaIdsGuiaAtendimento(IdsProcGuiaDemonstrativo);
        itensLoteServices.UpdateStatus(IdsGuiaAtendimento, SituacaoItensLoteEnum.GlosaDevolvida);
        scope.Complete();
      }
    }

    public void AtualizarStatus(int IdLoteGlosa)
    {
      R_LoteGlosa loteGlosa = GetById(IdLoteGlosa);

      ItensLoteGlosaServices itensLoteGlosaServices = new ItensLoteGlosaServices(User);
      List<StatusProcGuiaDemonstrativoEnum> ListStatusProcGuiaDemonstrativoEnum = itensLoteGlosaServices.GetStatusProcGuiaDemonstrativo(IdLoteGlosa);

      StatusGlosaServices statusGlosaServices = new StatusGlosaServices();
      List<R_StatusGlosa> ListaStatusGlosa = statusGlosaServices.Getall();

      int QuantidadeGlosadaJustificada = ListStatusProcGuiaDemonstrativoEnum.Where(a => a.Equals(StatusProcGuiaDemonstrativoEnum.Glosado)).Count() +
       ListStatusProcGuiaDemonstrativoEnum.Where(a => a.Equals(StatusProcGuiaDemonstrativoEnum.Justificada)).Count();

      if (ListStatusProcGuiaDemonstrativoEnum.Where(a => a.Equals(StatusProcGuiaDemonstrativoEnum.Glosado)).Count() == 0
      && ListStatusProcGuiaDemonstrativoEnum.Where(a => a.Equals(StatusProcGuiaDemonstrativoEnum.Justificada)).Count() == 0
      && ListStatusProcGuiaDemonstrativoEnum.Where(a => a.Equals(StatusProcGuiaDemonstrativoEnum.GlosaAceita)).Count() > 0
          )
        loteGlosa.LG_IdStatusGlosa = ListaStatusGlosa.Where(a => a.SG_Enum == (int)StatusGlosaEnum.GlosasAceitas).Select(a => a.SG_Id).FirstOrDefault();

      if (ListStatusProcGuiaDemonstrativoEnum.Where(a => a.Equals(StatusProcGuiaDemonstrativoEnum.Glosado)).Count() == 0
       && ListStatusProcGuiaDemonstrativoEnum.Where(a => a.Equals(StatusProcGuiaDemonstrativoEnum.Justificada)).Count() > 0
          )
        loteGlosa.LG_IdStatusGlosa = ListaStatusGlosa.Where(a => a.SG_Enum == (int)StatusGlosaEnum.DisponivelEnviarConvenio).Select(a => a.SG_Id).FirstOrDefault();

      Edit(loteGlosa);

      string DescricaoNovoStatus = ListaStatusGlosa.Where(a => a.SG_Id == loteGlosa.LG_IdStatusGlosa).Select(a => a.SG_Descricao).FirstOrDefault();
      new LogLoteGlosaService().Create(EnumTipoLogLoteGlosa.ModificadoStatusGlosa, IdLoteGlosa, string.Format("Atualizado Status para {0}.", DescricaoNovoStatus));

    }

    public byte[] GerarXML(int IdLoteGlosa)
    {
      List<XMLLoteGlosa> ListaXmlLoteGlosa = GetXMLLoteGlosa(IdLoteGlosa);
      System.Xml.Serialization.XmlSerializer Serializer = new System.Xml.Serialization.XmlSerializer(typeof(List<XMLLoteGlosa>));
      byte[] teste;
      using (var ms = new MemoryStream())
      {
        Serializer.Serialize(ms, ListaXmlLoteGlosa);
        teste = ms.ToArray();
      }
      return teste;
    }

    public byte[] GerarRelatorio(int IdLoteGlosa)
    {
      ReportViewer ReportViewer = new ReportViewer();
      ReportViewer.LocalReport.ReportPath = Path.Combine(ConfigurationManager.AppSettings["PathReports"] + "RelatorioLoteGlosa.rdlc");

      string layoutTipe = "EXCELOPENXML";
      string mimeType, encoding, filenameExtension;
      string[] streamids;
      Warning[] warnings;

      List<RelatorioLoteGlosa> ListaRelatorioLoteGlosa = GetRelatorioLoteGlosa(IdLoteGlosa);
      ReportDataSource RelatorioGlosaRDLC = new ReportDataSource("RelatorioGlosaRDLC", ListaRelatorioLoteGlosa);
      ReportViewer.LocalReport.DataSources.Add(RelatorioGlosaRDLC);
      Byte[] Arquivo = ReportViewer.LocalReport.Render(layoutTipe, null, out mimeType, out encoding, out filenameExtension, out streamids, out warnings);
      return Arquivo;
    }

    public List<LotesGlosaVencendoCabecalho> GetLotesGlosaVencendo(DateTime DataVencimento)
    {
      string query = @"SELECT 
                        LG.LG_Id [Codigo],
                        LG.LG_Codigo [CodigoLote],
                        SG.SG_Descricao [StatusDescricao],
                        TLG.TLG_Descricao [StatusTipoGlosa],
                        LG.LG_DataCriacao [DataInicio],
                        LG.LG_DataVencimentoAnalise [DataVencimento],
                        C.C_RazaoSocial [Convenio],
                        RC.RC_Email [EmailResponsavel]
                       FROM R_LoteGlosa LG
                       INNER JOIN R_StatusGlosa SG ON SG.SG_Id = LG.LG_IdStatusGlosa AND LG_DataVencimentoAnalise BETWEEN CONVERT(date, @DataAtual) AND CONVERT(date, @DataVencimento) AND  LG_IdResponsavelConvenio IS NOT NULL AND (LG_EnviadoEmail = @CondicaoFalse OR LG_EnviadoEmail IS NULL)
                       INNER JOIN R_TipoLoteGlosa TLG ON TLG.TLG_Id = LG.LG_IdTipoLoteGlosa
                       INNER JOIN R_Repasse R ON R.R_Id = LG.LG_IdRepasse
                       INNER JOIN R_Convenio C ON C.C_Id = R.R_IdConvenio
                       INNER JOIN R_ResponsavelConvenio RC ON LG.LG_IdResponsavelConvenio = RC.RC_Id";

      List<LotesGlosaVencendo> ListaLoteGlosaVencendo = Contexto.Database.SqlQuery<LotesGlosaVencendo>(query, new SqlParameter("@DataAtual", DateTime.Now)
                                                                                                            , new SqlParameter("@DataVencimento", DataVencimento)
                                                                                                            , new SqlParameter("@CondicaoFalse", false)).ToList();

      var Groupby = ListaLoteGlosaVencendo.GroupBy(a => new { a.EmailResponsavel });

      return Groupby.Select(a => new LotesGlosaVencendoCabecalho()
      {
        EmailResponsavel = a.Key.EmailResponsavel,
        ListaLotesGlosaVencendo = a.ToList()
      }).ToList();
    }

    public string GetTableLoteGlosaVencendo(List<LotesGlosaVencendo> ListaLotesGlosaVencendo)
    {
      TagBuilder table = new TagBuilder("table");
      TagBuilder thead = new TagBuilder("thead");
      TagBuilder tbody = new TagBuilder("tbody");
      TagBuilder tr = new TagBuilder("tr");
      TagBuilder th = new TagBuilder("th");
      TagBuilder td = new TagBuilder("td");

      th.InnerHtml = "Código";
      th.Attributes.Add("style", "border: 1px solid black;");
      tr.InnerHtml += th.ToString();
      th.InnerHtml = "Status";
      tr.InnerHtml += th.ToString();
      th.InnerHtml = "Situação";
      tr.InnerHtml += th.ToString();
      th.InnerHtml = "Início";
      tr.InnerHtml += th.ToString();
      th.InnerHtml = "Vencimento";
      tr.InnerHtml += th.ToString();
      th.InnerHtml = "Convênio";
      tr.InnerHtml += th.ToString();
      thead.InnerHtml = tr.ToString();
      table.InnerHtml = thead.ToString();

      td.Attributes.Add("style", "border: 1px solid black;");
      foreach (var item in ListaLotesGlosaVencendo)
      {
        tr = new TagBuilder("tr");
        td.InnerHtml = item.CodigoLote;
        tr.InnerHtml += td.ToString();
        td.InnerHtml = item.StatusDescricao;
        tr.InnerHtml += td.ToString();
        td.InnerHtml = item.StatusTipoGlosa;
        tr.InnerHtml += td.ToString();
        td.InnerHtml = item.DataInicio.ToString("dd/MM/yyyy");
        tr.InnerHtml += td.ToString();
        td.InnerHtml = item.DataVencimento.ToString("dd/MM/yyyy");
        tr.InnerHtml += td.ToString();
        td.InnerHtml = item.Convenio;
        tr.InnerHtml += td.ToString();
        tbody.InnerHtml = tr.ToString();
      }

      table.InnerHtml += tbody.ToString();
      table.AddCssClass("table table-sm table-striped table-hover text-nowrap");

      table.Attributes.Add("style", "width: 100%; padding-right: 30px;");
      return table.ToString();
    }
  }
}
﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Models;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace RepasseConvenio.Controllers
{
  [Authorize]
  public class ProcedimentoController : LibController
  {

    private ProcedimentoServices ProcedimentoServices
    {
      get
      {
        if (_ProcedimentoServices == null)
          _ProcedimentoServices = new ProcedimentoServices(ContextoUsuario.UserLogged);

        return _ProcedimentoServices;
      }
    }
    private ProcedimentoServices _ProcedimentoServices;

    public ActionResult Index(int? codConvenio)
    {
      try
      {
        if (!codConvenio.HasValue || codConvenio == 0)
          throw new Exception("Nenhum Convênio selecionado.");

        List<ProcedimentoIndex> Procedimento = ProcedimentoServices.Get(codConvenio.Value, 0);
        return View(Procedimento);
      }
      catch (Exception)
      {
        throw;
      }
    }

    //[HttpPost]
    //public JsonResult IntegraProcedimento(string ConnectionId)
    //{
    //  try
    //  {
    //    ConvenioServices convenioServices = new ConvenioServices();
    //    convenioServices.IntegraConvenio(ConnectionId);
    //    return Json("");
    //  }
    //  catch (Exception ex)
    //  {
    //    throw;
    //  }
    //}

  }
}
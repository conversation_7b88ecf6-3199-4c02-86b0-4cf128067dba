﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using PagedList;
using RepasseConvenio.Domain.Infrastructure.Helpers;
using RepasseConvenio.Infrastructure;
using RepasseConvenio.Models;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;

namespace RepasseConvenio.Services
{
  public class ResponsavelConvenioServices : ServiceBase
  {
    public ResponsavelConvenioServices()
   : base()
    { }
    public ResponsavelConvenioServices(RepasseEntities Contexto)
       : base(Contexto)
    { }

    public ResponsavelConvenioServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public ResponsavelConvenioServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public IPagedList<ResponsavelConvenioModel> GetAllByIdConvenio(int IdConvenio, int Pagina)
    {
      string query = @"SELECT
	                        RC.RC_Id [Codigo],
	                        RC.RC_Nome [NomeResponsavel],
	                        RC.RC_CPF [CPF],
	                        RC.RC_Email [Email],
	                        RRC.RRC_IdConvenio [CodigoConvenio],
	                        RRC.RRC_Id [CodigoRelacao]
                        FROM R_ResponsavelConvenio RC
                        INNER JOIN R_RelacaoResponsavelConvenio RRC ON RRC.RRC_IdResponsavel=RC.RC_Id
                        WHERE RRC.RRC_IdConvenio=@IdConvenio";

      return Contexto.Database.SqlQuery<ResponsavelConvenioModel>(query, new SqlParameter("@IdConvenio", IdConvenio)).ToList().ToPagedList(Pagina, PageSize);
    }

    public ResponsavelConvenioModel GetByIdModel(int id)
    {
      string Query = @"SELECT
	                        RC.RC_Id [Codigo],
	                        RC.RC_Nome [NomeResponsavel],
	                        RC.RC_CPF [CPF],
	                        RC.RC_Email [Email],
	                        RRC.RRC_IdConvenio [CodigoConvenio],
	                        RRC.RRC_Id [CodigoRelacao]
                        FROM R_ResponsavelConvenio RC
                        INNER JOIN R_RelacaoResponsavelConvenio RRC ON RRC.RRC_IdResponsavel=RC.RC_Id
                        WHERE RC.RC_Id=@Id";

      return Contexto.Database.SqlQuery<ResponsavelConvenioModel>(Query, new SqlParameter("@Id", id)).FirstOrDefault();
    }

    public R_ResponsavelConvenio GetById(int? id)
    {
      return Contexto.R_ResponsavelConvenio.Where(a => a.RC_Id == id).FirstOrDefault();
    }

    public R_ResponsavelConvenio GetByCPF(string CPF)
    {
      return Contexto.R_ResponsavelConvenio.Where(a => a.RC_CPF == CPF).FirstOrDefault();
    }

    public int? GetIdByCPF(string CPF)
    {
      return Contexto.R_ResponsavelConvenio.Where(a => a.RC_CPF == CPF).Select(a => a.RC_Id).FirstOrDefault();
    }

    public List<ResponsavalConvenioUserLogged> GetIdsConvenioByCPF(string CPF)
    {
      string Query = @"SELECT
	                       RRC.RRC_IdConvenio [IdConvenio],
	                       RC.RC_Id [IdResponsavelConvenio]
                       FROM R_RelacaoResponsavelConvenio RRC
                       INNER JOIN R_ResponsavelConvenio RC ON RC.RC_Id=RRC.RRC_IdResponsavel
                       WHERE RC.RC_CPF=@CPF";

      return Contexto.Database.SqlQuery<ResponsavalConvenioUserLogged>(Query, new SqlParameter("@CPF", CPF)).ToList();
    }

    public List<R_RelacaoResponsavelConvenio> GetListRelacaoResponsavelConvenio(int IdResponsavel)
    {
      string Query = @"SELECT
                        *
                       FROM R_RelacaoResponsavelConvenio RRC
                       WHERE RRC.RRC_IdResponsavel = @IdResponsavel";

      return Contexto.Database.SqlQuery<R_RelacaoResponsavelConvenio>(Query, new SqlParameter("@IdResponsavel", IdResponsavel)).ToList();
    }

    public void Create(ResponsavelConvenioModel model)
    {
      if (!model.ToCadastroValido())
        throw new CustomException("Gentileza preencher todos os campos.");

      ValidadorHelper.ValidaCPF(model.CPF);

      R_ResponsavelConvenio responsavel = GetByCPF(model.CPF);

      if (responsavel == null)
      {
        responsavel = model.ModelToEntityCreate();
        Create(responsavel);
      }

      R_RelacaoResponsavelConvenio relacao = Contexto.R_RelacaoResponsavelConvenio.Where(a => a.RRC_IdResponsavel == responsavel.RC_Id && a.RRC_IdConvenio == model.CodigoConvenio).FirstOrDefault();

      if (relacao != null)
        throw new CustomException("Este responsável já está associado a este convênio.");

      relacao = new R_RelacaoResponsavelConvenio()
      {
        RRC_IdConvenio = model.CodigoConvenio,
        RRC_IdResponsavel = responsavel.RC_Id
      };

      Create(relacao);
    }

    public void Edit(ResponsavelConvenioModel model)
    {
      if (!model.ToCadastroValido())
        throw new CustomException("Gentileza preencher todos os campos.");

      ValidadorHelper.ValidaCPF(model.CPF);

      R_ResponsavelConvenio entity = GetByCPF(model.CPF);

      if (entity != null && entity.RC_Id != model.Codigo)
        throw new CustomException("Este CPF pertence à outro responsável, não é possível utilizá-lo.");

      entity = GetById(model.Codigo);

      if (entity != null)
      {
        entity.RC_Nome = model.NomeResponsavel;
        entity.RC_Email = model.Email;
        entity.RC_CPF = model.CPF;

        Edit(entity);
      }
      else
        throw new CustomException("Responsável não encontrado.");
    }

    public void Delete(int id)
    {
      if (HaveLoteResponsavel(id))
        throw new CustomException("Este responsável está atribuido à algum lote de glosa, não pode ser desvinculado.");

      R_RelacaoResponsavelConvenio entity = Contexto.R_RelacaoResponsavelConvenio.Where(a => a.RRC_Id == id).FirstOrDefault();

      if (entity != null)
        Delete(entity);
    }

    public bool HaveLoteResponsavel(int IdRelacaoResponsavelConvenio)
    {
      RelacaoResponsavelConvenioServices relacaoResponsavelConvenioServices = new RelacaoResponsavelConvenioServices();
      R_RelacaoResponsavelConvenio relacaoResponsavelConvenio = relacaoResponsavelConvenioServices.GetById(IdRelacaoResponsavelConvenio);

      string query = @"SELECT
                        COUNT(1)
                       FROM R_LoteGlosa LG
                       INNER JOIN R_ResponsavelConvenio RC ON RC.RC_Id = LG.LG_IdResponsavelConvenio AND LG_IdResponsavelConvenio = @IdResponsavelConvenio
                       INNER JOIN R_StatusGlosa SG ON SG.SG_Id = LG.LG_IdStatusGlosa AND SG.SG_Enum != @EnumGlosaGerada";

      int QuantidadeLotes = Contexto.Database.SqlQuery<int>(query, new SqlParameter("@EnumGlosaGerada", (int)StatusGlosaEnum.GlosasGeradas)
                                                                 , new SqlParameter("@IdResponsavelConvenio", relacaoResponsavelConvenio.RRC_IdResponsavel)).FirstOrDefault();

      if (QuantidadeLotes == 0)
        return false;
      else
        return true;
    }
  }
}
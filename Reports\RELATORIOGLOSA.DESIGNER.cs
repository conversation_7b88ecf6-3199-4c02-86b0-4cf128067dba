﻿//------------------------------------------------------------------------------
// <auto-generated>
//     O código foi gerado por uma ferramenta.
//     Versão de Tempo de Execução:4.0.30319.42000
//
//     As alterações ao arquivo poderão causar comportamento incorreto e serão perdidas se
//     o código for gerado novamente.
// </auto-generated>
//------------------------------------------------------------------------------

#pragma warning disable 1591

namespace RepasseConvenio.Reports {
    
    
    /// <summary>
    ///Represents a strongly typed in-memory cache of data.
    ///</summary>
    [global::System.Serializable()]
    [global::System.ComponentModel.DesignerCategoryAttribute("code")]
    [global::System.ComponentModel.ToolboxItem(true)]
    [global::System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedDataSetSchema")]
    [global::System.Xml.Serialization.XmlRootAttribute("RelatorioGlosa")]
    [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.DataSet")]
    public partial class RelatorioGlosa : global::System.Data.DataSet {
        
        private RelatorioGlosaRDLCDataTable tableRelatorioGlosaRDLC;
        
        private global::System.Data.SchemaSerializationMode _schemaSerializationMode = global::System.Data.SchemaSerializationMode.IncludeSchema;
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        public RelatorioGlosa() {
            this.BeginInit();
            this.InitClass();
            global::System.ComponentModel.CollectionChangeEventHandler schemaChangedHandler = new global::System.ComponentModel.CollectionChangeEventHandler(this.SchemaChanged);
            base.Tables.CollectionChanged += schemaChangedHandler;
            base.Relations.CollectionChanged += schemaChangedHandler;
            this.EndInit();
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        protected RelatorioGlosa(global::System.Runtime.Serialization.SerializationInfo info, global::System.Runtime.Serialization.StreamingContext context) : 
                base(info, context, false) {
            if ((this.IsBinarySerialized(info, context) == true)) {
                this.InitVars(false);
                global::System.ComponentModel.CollectionChangeEventHandler schemaChangedHandler1 = new global::System.ComponentModel.CollectionChangeEventHandler(this.SchemaChanged);
                this.Tables.CollectionChanged += schemaChangedHandler1;
                this.Relations.CollectionChanged += schemaChangedHandler1;
                return;
            }
            string strSchema = ((string)(info.GetValue("XmlSchema", typeof(string))));
            if ((this.DetermineSchemaSerializationMode(info, context) == global::System.Data.SchemaSerializationMode.IncludeSchema)) {
                global::System.Data.DataSet ds = new global::System.Data.DataSet();
                ds.ReadXmlSchema(new global::System.Xml.XmlTextReader(new global::System.IO.StringReader(strSchema)));
                if ((ds.Tables["RelatorioGlosaRDLC"] != null)) {
                    base.Tables.Add(new RelatorioGlosaRDLCDataTable(ds.Tables["RelatorioGlosaRDLC"]));
                }
                this.DataSetName = ds.DataSetName;
                this.Prefix = ds.Prefix;
                this.Namespace = ds.Namespace;
                this.Locale = ds.Locale;
                this.CaseSensitive = ds.CaseSensitive;
                this.EnforceConstraints = ds.EnforceConstraints;
                this.Merge(ds, false, global::System.Data.MissingSchemaAction.Add);
                this.InitVars();
            }
            else {
                this.ReadXmlSchema(new global::System.Xml.XmlTextReader(new global::System.IO.StringReader(strSchema)));
            }
            this.GetSerializationData(info, context);
            global::System.ComponentModel.CollectionChangeEventHandler schemaChangedHandler = new global::System.ComponentModel.CollectionChangeEventHandler(this.SchemaChanged);
            base.Tables.CollectionChanged += schemaChangedHandler;
            this.Relations.CollectionChanged += schemaChangedHandler;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        [global::System.ComponentModel.Browsable(false)]
        [global::System.ComponentModel.DesignerSerializationVisibility(global::System.ComponentModel.DesignerSerializationVisibility.Content)]
        public RelatorioGlosaRDLCDataTable RelatorioGlosaRDLC {
            get {
                return this.tableRelatorioGlosaRDLC;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        [global::System.ComponentModel.BrowsableAttribute(true)]
        [global::System.ComponentModel.DesignerSerializationVisibilityAttribute(global::System.ComponentModel.DesignerSerializationVisibility.Visible)]
        public override global::System.Data.SchemaSerializationMode SchemaSerializationMode {
            get {
                return this._schemaSerializationMode;
            }
            set {
                this._schemaSerializationMode = value;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        [global::System.ComponentModel.DesignerSerializationVisibilityAttribute(global::System.ComponentModel.DesignerSerializationVisibility.Hidden)]
        public new global::System.Data.DataTableCollection Tables {
            get {
                return base.Tables;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        [global::System.ComponentModel.DesignerSerializationVisibilityAttribute(global::System.ComponentModel.DesignerSerializationVisibility.Hidden)]
        public new global::System.Data.DataRelationCollection Relations {
            get {
                return base.Relations;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        protected override void InitializeDerivedDataSet() {
            this.BeginInit();
            this.InitClass();
            this.EndInit();
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        public override global::System.Data.DataSet Clone() {
            RelatorioGlosa cln = ((RelatorioGlosa)(base.Clone()));
            cln.InitVars();
            cln.SchemaSerializationMode = this.SchemaSerializationMode;
            return cln;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        protected override bool ShouldSerializeTables() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        protected override bool ShouldSerializeRelations() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        protected override void ReadXmlSerializable(global::System.Xml.XmlReader reader) {
            if ((this.DetermineSchemaSerializationMode(reader) == global::System.Data.SchemaSerializationMode.IncludeSchema)) {
                this.Reset();
                global::System.Data.DataSet ds = new global::System.Data.DataSet();
                ds.ReadXml(reader);
                if ((ds.Tables["RelatorioGlosaRDLC"] != null)) {
                    base.Tables.Add(new RelatorioGlosaRDLCDataTable(ds.Tables["RelatorioGlosaRDLC"]));
                }
                this.DataSetName = ds.DataSetName;
                this.Prefix = ds.Prefix;
                this.Namespace = ds.Namespace;
                this.Locale = ds.Locale;
                this.CaseSensitive = ds.CaseSensitive;
                this.EnforceConstraints = ds.EnforceConstraints;
                this.Merge(ds, false, global::System.Data.MissingSchemaAction.Add);
                this.InitVars();
            }
            else {
                this.ReadXml(reader);
                this.InitVars();
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        protected override global::System.Xml.Schema.XmlSchema GetSchemaSerializable() {
            global::System.IO.MemoryStream stream = new global::System.IO.MemoryStream();
            this.WriteXmlSchema(new global::System.Xml.XmlTextWriter(stream, null));
            stream.Position = 0;
            return global::System.Xml.Schema.XmlSchema.Read(new global::System.Xml.XmlTextReader(stream), null);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        internal void InitVars() {
            this.InitVars(true);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        internal void InitVars(bool initTable) {
            this.tableRelatorioGlosaRDLC = ((RelatorioGlosaRDLCDataTable)(base.Tables["RelatorioGlosaRDLC"]));
            if ((initTable == true)) {
                if ((this.tableRelatorioGlosaRDLC != null)) {
                    this.tableRelatorioGlosaRDLC.InitVars();
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        private void InitClass() {
            this.DataSetName = "RelatorioGlosa";
            this.Prefix = "";
            this.Namespace = "http://tempuri.org/RelatorioGlosa.xsd";
            this.EnforceConstraints = true;
            this.SchemaSerializationMode = global::System.Data.SchemaSerializationMode.IncludeSchema;
            this.tableRelatorioGlosaRDLC = new RelatorioGlosaRDLCDataTable();
            base.Tables.Add(this.tableRelatorioGlosaRDLC);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        private bool ShouldSerializeRelatorioGlosaRDLC() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        private void SchemaChanged(object sender, global::System.ComponentModel.CollectionChangeEventArgs e) {
            if ((e.Action == global::System.ComponentModel.CollectionChangeAction.Remove)) {
                this.InitVars();
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        public static global::System.Xml.Schema.XmlSchemaComplexType GetTypedDataSetSchema(global::System.Xml.Schema.XmlSchemaSet xs) {
            RelatorioGlosa ds = new RelatorioGlosa();
            global::System.Xml.Schema.XmlSchemaComplexType type = new global::System.Xml.Schema.XmlSchemaComplexType();
            global::System.Xml.Schema.XmlSchemaSequence sequence = new global::System.Xml.Schema.XmlSchemaSequence();
            global::System.Xml.Schema.XmlSchemaAny any = new global::System.Xml.Schema.XmlSchemaAny();
            any.Namespace = ds.Namespace;
            sequence.Items.Add(any);
            type.Particle = sequence;
            global::System.Xml.Schema.XmlSchema dsSchema = ds.GetSchemaSerializable();
            if (xs.Contains(dsSchema.TargetNamespace)) {
                global::System.IO.MemoryStream s1 = new global::System.IO.MemoryStream();
                global::System.IO.MemoryStream s2 = new global::System.IO.MemoryStream();
                try {
                    global::System.Xml.Schema.XmlSchema schema = null;
                    dsSchema.Write(s1);
                    for (global::System.Collections.IEnumerator schemas = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator(); schemas.MoveNext(); ) {
                        schema = ((global::System.Xml.Schema.XmlSchema)(schemas.Current));
                        s2.SetLength(0);
                        schema.Write(s2);
                        if ((s1.Length == s2.Length)) {
                            s1.Position = 0;
                            s2.Position = 0;
                            for (; ((s1.Position != s1.Length) 
                                        && (s1.ReadByte() == s2.ReadByte())); ) {
                                ;
                            }
                            if ((s1.Position == s1.Length)) {
                                return type;
                            }
                        }
                    }
                }
                finally {
                    if ((s1 != null)) {
                        s1.Close();
                    }
                    if ((s2 != null)) {
                        s2.Close();
                    }
                }
            }
            xs.Add(dsSchema);
            return type;
        }
        
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        public delegate void RelatorioGlosaRDLCRowChangeEventHandler(object sender, RelatorioGlosaRDLCRowChangeEvent e);
        
        /// <summary>
        ///Represents the strongly named DataTable class.
        ///</summary>
        [global::System.Serializable()]
        [global::System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")]
        public partial class RelatorioGlosaRDLCDataTable : global::System.Data.TypedTableBase<RelatorioGlosaRDLCRow> {
            
            private global::System.Data.DataColumn columnNumeroCarteirinha;
            
            private global::System.Data.DataColumn columnNumeroAtendimento;
            
            private global::System.Data.DataColumn columnCodigoProcedimento;
            
            private global::System.Data.DataColumn columnDescricaoProcedimento;
            
            private global::System.Data.DataColumn columnDataAtendimento;
            
            private global::System.Data.DataColumn columnTotalApresentado;
            
            private global::System.Data.DataColumn columnTotalGlosado;
            
            private global::System.Data.DataColumn columnTotalPago;
            
            private global::System.Data.DataColumn columnObservacoesGerais;
            
            private global::System.Data.DataColumn columnCodigoGlosa;
            
            private global::System.Data.DataColumn columnDescricaoGlosa;
            
            private global::System.Data.DataColumn columnCodigoJustificativa;
            
            private global::System.Data.DataColumn columnDescricaoJustificativa;
            
            private global::System.Data.DataColumn columnComentario;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public RelatorioGlosaRDLCDataTable() {
                this.TableName = "RelatorioGlosaRDLC";
                this.BeginInit();
                this.InitClass();
                this.EndInit();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            internal RelatorioGlosaRDLCDataTable(global::System.Data.DataTable table) {
                this.TableName = table.TableName;
                if ((table.CaseSensitive != table.DataSet.CaseSensitive)) {
                    this.CaseSensitive = table.CaseSensitive;
                }
                if ((table.Locale.ToString() != table.DataSet.Locale.ToString())) {
                    this.Locale = table.Locale;
                }
                if ((table.Namespace != table.DataSet.Namespace)) {
                    this.Namespace = table.Namespace;
                }
                this.Prefix = table.Prefix;
                this.MinimumCapacity = table.MinimumCapacity;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            protected RelatorioGlosaRDLCDataTable(global::System.Runtime.Serialization.SerializationInfo info, global::System.Runtime.Serialization.StreamingContext context) : 
                    base(info, context) {
                this.InitVars();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn NumeroCarteirinhaColumn {
                get {
                    return this.columnNumeroCarteirinha;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn NumeroAtendimentoColumn {
                get {
                    return this.columnNumeroAtendimento;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn CodigoProcedimentoColumn {
                get {
                    return this.columnCodigoProcedimento;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn DescricaoProcedimentoColumn {
                get {
                    return this.columnDescricaoProcedimento;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn DataAtendimentoColumn {
                get {
                    return this.columnDataAtendimento;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn TotalApresentadoColumn {
                get {
                    return this.columnTotalApresentado;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn TotalGlosadoColumn {
                get {
                    return this.columnTotalGlosado;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn TotalPagoColumn {
                get {
                    return this.columnTotalPago;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn ObservacoesGeraisColumn {
                get {
                    return this.columnObservacoesGerais;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn CodigoGlosaColumn {
                get {
                    return this.columnCodigoGlosa;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn DescricaoGlosaColumn {
                get {
                    return this.columnDescricaoGlosa;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn CodigoJustificativaColumn {
                get {
                    return this.columnCodigoJustificativa;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn DescricaoJustificativaColumn {
                get {
                    return this.columnDescricaoJustificativa;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataColumn ComentarioColumn {
                get {
                    return this.columnComentario;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            [global::System.ComponentModel.Browsable(false)]
            public int Count {
                get {
                    return this.Rows.Count;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public RelatorioGlosaRDLCRow this[int index] {
                get {
                    return ((RelatorioGlosaRDLCRow)(this.Rows[index]));
                }
            }
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public event RelatorioGlosaRDLCRowChangeEventHandler RelatorioGlosaRDLCRowChanging;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public event RelatorioGlosaRDLCRowChangeEventHandler RelatorioGlosaRDLCRowChanged;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public event RelatorioGlosaRDLCRowChangeEventHandler RelatorioGlosaRDLCRowDeleting;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public event RelatorioGlosaRDLCRowChangeEventHandler RelatorioGlosaRDLCRowDeleted;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void AddRelatorioGlosaRDLCRow(RelatorioGlosaRDLCRow row) {
                this.Rows.Add(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public RelatorioGlosaRDLCRow AddRelatorioGlosaRDLCRow(string NumeroCarteirinha, string NumeroAtendimento, string CodigoProcedimento, string DescricaoProcedimento, System.DateTime DataAtendimento, decimal TotalApresentado, decimal TotalGlosado, decimal TotalPago, string ObservacoesGerais, string CodigoGlosa, string DescricaoGlosa, string CodigoJustificativa, string DescricaoJustificativa, string Comentario) {
                RelatorioGlosaRDLCRow rowRelatorioGlosaRDLCRow = ((RelatorioGlosaRDLCRow)(this.NewRow()));
                object[] columnValuesArray = new object[] {
                        NumeroCarteirinha,
                        NumeroAtendimento,
                        CodigoProcedimento,
                        DescricaoProcedimento,
                        DataAtendimento,
                        TotalApresentado,
                        TotalGlosado,
                        TotalPago,
                        ObservacoesGerais,
                        CodigoGlosa,
                        DescricaoGlosa,
                        CodigoJustificativa,
                        DescricaoJustificativa,
                        Comentario};
                rowRelatorioGlosaRDLCRow.ItemArray = columnValuesArray;
                this.Rows.Add(rowRelatorioGlosaRDLCRow);
                return rowRelatorioGlosaRDLCRow;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public override global::System.Data.DataTable Clone() {
                RelatorioGlosaRDLCDataTable cln = ((RelatorioGlosaRDLCDataTable)(base.Clone()));
                cln.InitVars();
                return cln;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            protected override global::System.Data.DataTable CreateInstance() {
                return new RelatorioGlosaRDLCDataTable();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            internal void InitVars() {
                this.columnNumeroCarteirinha = base.Columns["NumeroCarteirinha"];
                this.columnNumeroAtendimento = base.Columns["NumeroAtendimento"];
                this.columnCodigoProcedimento = base.Columns["CodigoProcedimento"];
                this.columnDescricaoProcedimento = base.Columns["DescricaoProcedimento"];
                this.columnDataAtendimento = base.Columns["DataAtendimento"];
                this.columnTotalApresentado = base.Columns["TotalApresentado"];
                this.columnTotalGlosado = base.Columns["TotalGlosado"];
                this.columnTotalPago = base.Columns["TotalPago"];
                this.columnObservacoesGerais = base.Columns["ObservacoesGerais"];
                this.columnCodigoGlosa = base.Columns["CodigoGlosa"];
                this.columnDescricaoGlosa = base.Columns["DescricaoGlosa"];
                this.columnCodigoJustificativa = base.Columns["CodigoJustificativa"];
                this.columnDescricaoJustificativa = base.Columns["DescricaoJustificativa"];
                this.columnComentario = base.Columns["Comentario"];
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            private void InitClass() {
                this.columnNumeroCarteirinha = new global::System.Data.DataColumn("NumeroCarteirinha", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnNumeroCarteirinha);
                this.columnNumeroAtendimento = new global::System.Data.DataColumn("NumeroAtendimento", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnNumeroAtendimento);
                this.columnCodigoProcedimento = new global::System.Data.DataColumn("CodigoProcedimento", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnCodigoProcedimento);
                this.columnDescricaoProcedimento = new global::System.Data.DataColumn("DescricaoProcedimento", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnDescricaoProcedimento);
                this.columnDataAtendimento = new global::System.Data.DataColumn("DataAtendimento", typeof(global::System.DateTime), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnDataAtendimento);
                this.columnTotalApresentado = new global::System.Data.DataColumn("TotalApresentado", typeof(decimal), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnTotalApresentado);
                this.columnTotalGlosado = new global::System.Data.DataColumn("TotalGlosado", typeof(decimal), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnTotalGlosado);
                this.columnTotalPago = new global::System.Data.DataColumn("TotalPago", typeof(decimal), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnTotalPago);
                this.columnObservacoesGerais = new global::System.Data.DataColumn("ObservacoesGerais", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnObservacoesGerais);
                this.columnCodigoGlosa = new global::System.Data.DataColumn("CodigoGlosa", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnCodigoGlosa);
                this.columnDescricaoGlosa = new global::System.Data.DataColumn("DescricaoGlosa", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnDescricaoGlosa);
                this.columnCodigoJustificativa = new global::System.Data.DataColumn("CodigoJustificativa", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnCodigoJustificativa);
                this.columnDescricaoJustificativa = new global::System.Data.DataColumn("DescricaoJustificativa", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnDescricaoJustificativa);
                this.columnComentario = new global::System.Data.DataColumn("Comentario", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComentario);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public RelatorioGlosaRDLCRow NewRelatorioGlosaRDLCRow() {
                return ((RelatorioGlosaRDLCRow)(this.NewRow()));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            protected override global::System.Data.DataRow NewRowFromBuilder(global::System.Data.DataRowBuilder builder) {
                return new RelatorioGlosaRDLCRow(builder);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            protected override global::System.Type GetRowType() {
                return typeof(RelatorioGlosaRDLCRow);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            protected override void OnRowChanged(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanged(e);
                if ((this.RelatorioGlosaRDLCRowChanged != null)) {
                    this.RelatorioGlosaRDLCRowChanged(this, new RelatorioGlosaRDLCRowChangeEvent(((RelatorioGlosaRDLCRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            protected override void OnRowChanging(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanging(e);
                if ((this.RelatorioGlosaRDLCRowChanging != null)) {
                    this.RelatorioGlosaRDLCRowChanging(this, new RelatorioGlosaRDLCRowChangeEvent(((RelatorioGlosaRDLCRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            protected override void OnRowDeleted(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleted(e);
                if ((this.RelatorioGlosaRDLCRowDeleted != null)) {
                    this.RelatorioGlosaRDLCRowDeleted(this, new RelatorioGlosaRDLCRowChangeEvent(((RelatorioGlosaRDLCRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            protected override void OnRowDeleting(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleting(e);
                if ((this.RelatorioGlosaRDLCRowDeleting != null)) {
                    this.RelatorioGlosaRDLCRowDeleting(this, new RelatorioGlosaRDLCRowChangeEvent(((RelatorioGlosaRDLCRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void RemoveRelatorioGlosaRDLCRow(RelatorioGlosaRDLCRow row) {
                this.Rows.Remove(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public static global::System.Xml.Schema.XmlSchemaComplexType GetTypedTableSchema(global::System.Xml.Schema.XmlSchemaSet xs) {
                global::System.Xml.Schema.XmlSchemaComplexType type = new global::System.Xml.Schema.XmlSchemaComplexType();
                global::System.Xml.Schema.XmlSchemaSequence sequence = new global::System.Xml.Schema.XmlSchemaSequence();
                RelatorioGlosa ds = new RelatorioGlosa();
                global::System.Xml.Schema.XmlSchemaAny any1 = new global::System.Xml.Schema.XmlSchemaAny();
                any1.Namespace = "http://www.w3.org/2001/XMLSchema";
                any1.MinOccurs = new decimal(0);
                any1.MaxOccurs = decimal.MaxValue;
                any1.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any1);
                global::System.Xml.Schema.XmlSchemaAny any2 = new global::System.Xml.Schema.XmlSchemaAny();
                any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1";
                any2.MinOccurs = new decimal(1);
                any2.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any2);
                global::System.Xml.Schema.XmlSchemaAttribute attribute1 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute1.Name = "namespace";
                attribute1.FixedValue = ds.Namespace;
                type.Attributes.Add(attribute1);
                global::System.Xml.Schema.XmlSchemaAttribute attribute2 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute2.Name = "tableTypeName";
                attribute2.FixedValue = "RelatorioGlosaRDLCDataTable";
                type.Attributes.Add(attribute2);
                type.Particle = sequence;
                global::System.Xml.Schema.XmlSchema dsSchema = ds.GetSchemaSerializable();
                if (xs.Contains(dsSchema.TargetNamespace)) {
                    global::System.IO.MemoryStream s1 = new global::System.IO.MemoryStream();
                    global::System.IO.MemoryStream s2 = new global::System.IO.MemoryStream();
                    try {
                        global::System.Xml.Schema.XmlSchema schema = null;
                        dsSchema.Write(s1);
                        for (global::System.Collections.IEnumerator schemas = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator(); schemas.MoveNext(); ) {
                            schema = ((global::System.Xml.Schema.XmlSchema)(schemas.Current));
                            s2.SetLength(0);
                            schema.Write(s2);
                            if ((s1.Length == s2.Length)) {
                                s1.Position = 0;
                                s2.Position = 0;
                                for (; ((s1.Position != s1.Length) 
                                            && (s1.ReadByte() == s2.ReadByte())); ) {
                                    ;
                                }
                                if ((s1.Position == s1.Length)) {
                                    return type;
                                }
                            }
                        }
                    }
                    finally {
                        if ((s1 != null)) {
                            s1.Close();
                        }
                        if ((s2 != null)) {
                            s2.Close();
                        }
                    }
                }
                xs.Add(dsSchema);
                return type;
            }
        }
        
        /// <summary>
        ///Represents strongly named DataRow class.
        ///</summary>
        public partial class RelatorioGlosaRDLCRow : global::System.Data.DataRow {
            
            private RelatorioGlosaRDLCDataTable tableRelatorioGlosaRDLC;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            internal RelatorioGlosaRDLCRow(global::System.Data.DataRowBuilder rb) : 
                    base(rb) {
                this.tableRelatorioGlosaRDLC = ((RelatorioGlosaRDLCDataTable)(this.Table));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public string NumeroCarteirinha {
                get {
                    try {
                        return ((string)(this[this.tableRelatorioGlosaRDLC.NumeroCarteirinhaColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("O valor da coluna \'NumeroCarteirinha\' na tabela \'RelatorioGlosaRDLC\' é DBNull.", e);
                    }
                }
                set {
                    this[this.tableRelatorioGlosaRDLC.NumeroCarteirinhaColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public string NumeroAtendimento {
                get {
                    try {
                        return ((string)(this[this.tableRelatorioGlosaRDLC.NumeroAtendimentoColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("O valor da coluna \'NumeroAtendimento\' na tabela \'RelatorioGlosaRDLC\' é DBNull.", e);
                    }
                }
                set {
                    this[this.tableRelatorioGlosaRDLC.NumeroAtendimentoColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public string CodigoProcedimento {
                get {
                    try {
                        return ((string)(this[this.tableRelatorioGlosaRDLC.CodigoProcedimentoColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("O valor da coluna \'CodigoProcedimento\' na tabela \'RelatorioGlosaRDLC\' é DBNull.", e);
                    }
                }
                set {
                    this[this.tableRelatorioGlosaRDLC.CodigoProcedimentoColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public string DescricaoProcedimento {
                get {
                    try {
                        return ((string)(this[this.tableRelatorioGlosaRDLC.DescricaoProcedimentoColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("O valor da coluna \'DescricaoProcedimento\' na tabela \'RelatorioGlosaRDLC\' é DBNull" +
                                ".", e);
                    }
                }
                set {
                    this[this.tableRelatorioGlosaRDLC.DescricaoProcedimentoColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public System.DateTime DataAtendimento {
                get {
                    try {
                        return ((global::System.DateTime)(this[this.tableRelatorioGlosaRDLC.DataAtendimentoColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("O valor da coluna \'DataAtendimento\' na tabela \'RelatorioGlosaRDLC\' é DBNull.", e);
                    }
                }
                set {
                    this[this.tableRelatorioGlosaRDLC.DataAtendimentoColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public decimal TotalApresentado {
                get {
                    try {
                        return ((decimal)(this[this.tableRelatorioGlosaRDLC.TotalApresentadoColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("O valor da coluna \'TotalApresentado\' na tabela \'RelatorioGlosaRDLC\' é DBNull.", e);
                    }
                }
                set {
                    this[this.tableRelatorioGlosaRDLC.TotalApresentadoColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public decimal TotalGlosado {
                get {
                    try {
                        return ((decimal)(this[this.tableRelatorioGlosaRDLC.TotalGlosadoColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("O valor da coluna \'TotalGlosado\' na tabela \'RelatorioGlosaRDLC\' é DBNull.", e);
                    }
                }
                set {
                    this[this.tableRelatorioGlosaRDLC.TotalGlosadoColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public decimal TotalPago {
                get {
                    try {
                        return ((decimal)(this[this.tableRelatorioGlosaRDLC.TotalPagoColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("O valor da coluna \'TotalPago\' na tabela \'RelatorioGlosaRDLC\' é DBNull.", e);
                    }
                }
                set {
                    this[this.tableRelatorioGlosaRDLC.TotalPagoColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public string ObservacoesGerais {
                get {
                    try {
                        return ((string)(this[this.tableRelatorioGlosaRDLC.ObservacoesGeraisColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("O valor da coluna \'ObservacoesGerais\' na tabela \'RelatorioGlosaRDLC\' é DBNull.", e);
                    }
                }
                set {
                    this[this.tableRelatorioGlosaRDLC.ObservacoesGeraisColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public string CodigoGlosa {
                get {
                    try {
                        return ((string)(this[this.tableRelatorioGlosaRDLC.CodigoGlosaColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("O valor da coluna \'CodigoGlosa\' na tabela \'RelatorioGlosaRDLC\' é DBNull.", e);
                    }
                }
                set {
                    this[this.tableRelatorioGlosaRDLC.CodigoGlosaColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public string DescricaoGlosa {
                get {
                    try {
                        return ((string)(this[this.tableRelatorioGlosaRDLC.DescricaoGlosaColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("O valor da coluna \'DescricaoGlosa\' na tabela \'RelatorioGlosaRDLC\' é DBNull.", e);
                    }
                }
                set {
                    this[this.tableRelatorioGlosaRDLC.DescricaoGlosaColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public string CodigoJustificativa {
                get {
                    try {
                        return ((string)(this[this.tableRelatorioGlosaRDLC.CodigoJustificativaColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("O valor da coluna \'CodigoJustificativa\' na tabela \'RelatorioGlosaRDLC\' é DBNull.", e);
                    }
                }
                set {
                    this[this.tableRelatorioGlosaRDLC.CodigoJustificativaColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public string DescricaoJustificativa {
                get {
                    try {
                        return ((string)(this[this.tableRelatorioGlosaRDLC.DescricaoJustificativaColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("O valor da coluna \'DescricaoJustificativa\' na tabela \'RelatorioGlosaRDLC\' é DBNul" +
                                "l.", e);
                    }
                }
                set {
                    this[this.tableRelatorioGlosaRDLC.DescricaoJustificativaColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public string Comentario {
                get {
                    try {
                        return ((string)(this[this.tableRelatorioGlosaRDLC.ComentarioColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("O valor da coluna \'Comentario\' na tabela \'RelatorioGlosaRDLC\' é DBNull.", e);
                    }
                }
                set {
                    this[this.tableRelatorioGlosaRDLC.ComentarioColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsNumeroCarteirinhaNull() {
                return this.IsNull(this.tableRelatorioGlosaRDLC.NumeroCarteirinhaColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetNumeroCarteirinhaNull() {
                this[this.tableRelatorioGlosaRDLC.NumeroCarteirinhaColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsNumeroAtendimentoNull() {
                return this.IsNull(this.tableRelatorioGlosaRDLC.NumeroAtendimentoColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetNumeroAtendimentoNull() {
                this[this.tableRelatorioGlosaRDLC.NumeroAtendimentoColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsCodigoProcedimentoNull() {
                return this.IsNull(this.tableRelatorioGlosaRDLC.CodigoProcedimentoColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetCodigoProcedimentoNull() {
                this[this.tableRelatorioGlosaRDLC.CodigoProcedimentoColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsDescricaoProcedimentoNull() {
                return this.IsNull(this.tableRelatorioGlosaRDLC.DescricaoProcedimentoColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetDescricaoProcedimentoNull() {
                this[this.tableRelatorioGlosaRDLC.DescricaoProcedimentoColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsDataAtendimentoNull() {
                return this.IsNull(this.tableRelatorioGlosaRDLC.DataAtendimentoColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetDataAtendimentoNull() {
                this[this.tableRelatorioGlosaRDLC.DataAtendimentoColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsTotalApresentadoNull() {
                return this.IsNull(this.tableRelatorioGlosaRDLC.TotalApresentadoColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetTotalApresentadoNull() {
                this[this.tableRelatorioGlosaRDLC.TotalApresentadoColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsTotalGlosadoNull() {
                return this.IsNull(this.tableRelatorioGlosaRDLC.TotalGlosadoColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetTotalGlosadoNull() {
                this[this.tableRelatorioGlosaRDLC.TotalGlosadoColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsTotalPagoNull() {
                return this.IsNull(this.tableRelatorioGlosaRDLC.TotalPagoColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetTotalPagoNull() {
                this[this.tableRelatorioGlosaRDLC.TotalPagoColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsObservacoesGeraisNull() {
                return this.IsNull(this.tableRelatorioGlosaRDLC.ObservacoesGeraisColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetObservacoesGeraisNull() {
                this[this.tableRelatorioGlosaRDLC.ObservacoesGeraisColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsCodigoGlosaNull() {
                return this.IsNull(this.tableRelatorioGlosaRDLC.CodigoGlosaColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetCodigoGlosaNull() {
                this[this.tableRelatorioGlosaRDLC.CodigoGlosaColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsDescricaoGlosaNull() {
                return this.IsNull(this.tableRelatorioGlosaRDLC.DescricaoGlosaColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetDescricaoGlosaNull() {
                this[this.tableRelatorioGlosaRDLC.DescricaoGlosaColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsCodigoJustificativaNull() {
                return this.IsNull(this.tableRelatorioGlosaRDLC.CodigoJustificativaColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetCodigoJustificativaNull() {
                this[this.tableRelatorioGlosaRDLC.CodigoJustificativaColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsDescricaoJustificativaNull() {
                return this.IsNull(this.tableRelatorioGlosaRDLC.DescricaoJustificativaColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetDescricaoJustificativaNull() {
                this[this.tableRelatorioGlosaRDLC.DescricaoJustificativaColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public bool IsComentarioNull() {
                return this.IsNull(this.tableRelatorioGlosaRDLC.ComentarioColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public void SetComentarioNull() {
                this[this.tableRelatorioGlosaRDLC.ComentarioColumn] = global::System.Convert.DBNull;
            }
        }
        
        /// <summary>
        ///Row event argument class
        ///</summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
        public class RelatorioGlosaRDLCRowChangeEvent : global::System.EventArgs {
            
            private RelatorioGlosaRDLCRow eventRow;
            
            private global::System.Data.DataRowAction eventAction;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public RelatorioGlosaRDLCRowChangeEvent(RelatorioGlosaRDLCRow row, global::System.Data.DataRowAction action) {
                this.eventRow = row;
                this.eventAction = action;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public RelatorioGlosaRDLCRow Row {
                get {
                    return this.eventRow;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "16.0.0.0")]
            public global::System.Data.DataRowAction Action {
                get {
                    return this.eventAction;
                }
            }
        }
    }
}

#pragma warning restore 1591
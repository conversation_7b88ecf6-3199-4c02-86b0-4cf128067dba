﻿@using RepasseConvenio.Models
@using PagedList
@using PagedList.Mvc;
@model IPagedList<ResponsavelConvenioModel>

@{
  ViewBag.Title = "Responsáveis do Convênio";
  Layout = "~/Views/Shared/_Layout.cshtml";
}

@Scripts.Render("~/Views/ResponsavelConvenio/ResponsavelConvenio.js?ada=ada")

<div class="row">
  <section class="col-lg-12 connectedSortable ui-sortable">
    <div class="card">
      <div class="card-header ui-sortable-handle">
        <div class="row" style="justify-content:space-between;">
          <h3 class="card-title">
          </h3>
          <div class="card-tools justify-content-end">
            <button type="button" class="btn btn-outline-primary" data-toggle="modal" data-target="#ModalNovoResponsavel">Novo Responsável</button>
            <button type="button" class="btn btn-outline-secondary" id="EditarResponsavel">Editar Responsável</button>
            <button type="button" class="btn btn-outline-danger" id="RemoverResponsavel">Desvincular Responsável</button>
          </div>
        </div>
      </div>
      <input type="hidden" value="@ViewBag.CodigoConvenio" id="CodigoConvenio" />
      <div class="card-body" id="GridResponsaveis">
        @Html.Partial("_Grid", Model)
      </div>
      <div class="box-footer with-border" style="padding: 0 10pt;">
        <div class="row">
          <div class="col-md-8 ">
            @Html.PagedListPager(Model, pagina => Url.Action("Index", new { pagina }))
          </div>
        </div>
      </div>
    </div>
  </section>
</div>

@Html.Partial("_ModalNovo", new ResponsavelConvenioModel())

<style>
  .TrSelecionavel {
    cursor: pointer;
  }

  .Selected {
    background-color: #d6101085 !important;
    transition: all 100ms;
  }

  .UnSelected {
    transition: all 100ms;
  }
</style>
﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using PagedList;
using RepasseConvenio.Infrastructure;
using RepasseConvenio.Models;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;

namespace RepasseConvenio.Services
{
  public class ValoresTabelaProgressivaINSSServices : ServiceBase
  {
    public ValoresTabelaProgressivaINSSServices(RepasseEntities Contexto)
        : base(Contexto)
    { }
    public ValoresTabelaProgressivaINSSServices()
       : base()
    { }

    public ValoresTabelaProgressivaINSSServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public ValoresTabelaProgressivaINSSServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }
    public IPagedList<ValoresTabelaProgressivaINSSModel> Get(int pageNumber, string filtro)
    {
      string query = String.Format(@"SELECT 
	                                    V.V_Id AS Codigo,
	                                    V.V_ValorInicial AS ValorInicial,
	                                    V.V_ValorFinal AS ValorFinal,
	                                    V.V_AliquotaProgressiva AS AliquotaProgressiva
                                    FROM R_ValoresTabelaProgressivaINSS V
                                    ");

      return Contexto.Database.SqlQuery<ValoresTabelaProgressivaINSSModel>(query)
             .ToList().OrderBy(a => a.AliquotaProgressiva).ToPagedList(pageNumber, PageSize);
    }
    public R_ValoresTabelaProgressivaINSS GetById(int Id)
    {
      return Contexto.R_ValoresTabelaProgressivaINSS.Where(a => a.V_Id == Id).FirstOrDefault();
    }
    public List<R_ValoresTabelaProgressivaINSS> GetByIdTabelaINSS(int IdTabela)
    {
      return Contexto.R_ValoresTabelaProgressivaINSS.Where(a => a.V_IdTabelaProgressivaINSS == IdTabela).ToList();
    }
    public int GetQtdItensByIdTabelaINSS(int IdTabela)
    {
      string query = String.Format(@"SELECT 
	                                    COUNT(*)
                                    FROM R_ValoresTabelaProgressivaINSS
                                    WHERE V_IdTabelaProgressivaINSS = @IdTabela
                                    ");

      return Contexto.Database.SqlQuery<int>(query, new SqlParameter("@IdTabela", IdTabela)).FirstOrDefault();

    }
    public R_ValoresTabelaProgressivaINSS GetValorTabelaProgressivaINSS(DateTime DataVigencia, decimal ProLabore)
    {
      string query = @"SELECT 
                      V.* 
                      FROM R_TabelaProgressivaINSS T
                      INNER JOIN R_ValoresTabelaProgressivaINSS V ON T.T_Id = V.V_IdTabelaProgressivaINSS
                      WHERE
                      @Data BETWEEN T.T_DtInicioVigencia AND T.T_DtFimVigencia
                      AND
                      @Valor BETWEEN V.V_ValorInicial AND V.V_ValorFinal";

      return Contexto.Database.SqlQuery<R_ValoresTabelaProgressivaINSS>(query
                                                                            , new SqlParameter("@Data", DataVigencia)
                                                                            , new SqlParameter("@Valor", ProLabore)
                                                                            ).FirstOrDefault();

    }
    public R_ValoresTabelaProgressivaIRPF GetValorTabelaProgressivaIRPF(DateTime DataVigencia, decimal ProLabore)
    {
      string query = @"SELECT 
                      V.* 
                      FROM R_TabelaProgressivaIRPF T
                      INNER JOIN R_ValoresTabelaProgressivaIRPF V ON T.T_Id = V.V_IdTabelaProgressivaIRPF
                      WHERE
                      @Data BETWEEN T.T_DtInicioVigencia AND T.T_DtFimVigencia
                      AND
                      @Valor BETWEEN V.V_ValorInicial AND V.V_ValorFinal";

      return Contexto.Database.SqlQuery<R_ValoresTabelaProgressivaIRPF>(query
                                                                            , new SqlParameter("@Data", DataVigencia)
                                                                            , new SqlParameter("@Valor", ProLabore)
                                                                            ).FirstOrDefault();

    }
    public void ValidaModel(ValoresTabelaProgressivaINSSModel model)
    {
      if (model.ValorFinal <= 0)
        throw new CustomException("Campo Valor Final é obrigatório");

      if (model.ValorInicial <= 0)
        throw new CustomException("Campo Valor Inicial é obrigatório");

      if (model.AliquotaProgressiva <= 0)
        throw new CustomException("Campo Aliquota Progressiva é obrigatório");
    }
    public void Create(ValoresTabelaProgressivaINSSModel model)
    {
      ValidaModel(model);
      R_ValoresTabelaProgressivaINSS entity = model.ToEntityCreate();
      Create(entity);
    }
    public void Edit(ValoresTabelaProgressivaINSSModel model)
    {
      ValidaModel(model);
      R_ValoresTabelaProgressivaINSS valor = GetById(model.Codigo);
      valor.ToEntityEdit(model);
      Edit(valor);
    }
    public void Delete(int Id)
    {
      R_ValoresTabelaProgressivaINSS R_ValoresTabelaProgressivaINSSs = GetById(Id);

      Contexto.R_ValoresTabelaProgressivaINSS.Remove(R_ValoresTabelaProgressivaINSSs);
      Contexto.SaveChanges();
    }

  }
}


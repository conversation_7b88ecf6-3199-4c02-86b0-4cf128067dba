﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace RepasseConvenio.Models
{
  public class DepositoRepasseModel
  {
    public int Codigo { get; set; }
    public int IdBancoUnicooper { get; set; }
    public DateTime DataDeposito { get; set; }
    public decimal ValorDeposito { get; set; }
    public string NumeroDocumento { get; set; }
    public decimal ValorUtilizado { get; set; }
    public int IdUsuarioCriacao { get; set; }
    public DateTime DataCriacao { get; set; }
    
  }

  public class ImportPlanilhaDeposito
  {
    public int IdDepositoRepasse { get; set; }

    public decimal ValorDeposito { get; set; }

    public decimal ValorUtilizado { get; set; }
  }

  public static class DepositoReapsseModelConversion
  {
    public static R_DepositoRepasse toEditRepasseModel(this DepositoRepasseModel model)
    {
      R_DepositoRepasse entity = new R_DepositoRepasse();
      entity.DR_Id = model.Codigo;
      entity.DR_DataDeposito = model.DataDeposito;
      entity.DR_DataCriacao = model.DataCriacao;
      entity.DR_IdBancosUnicooper = model.IdBancoUnicooper;
      entity.DR_IdUsuarioCriacao = model.IdUsuarioCriacao;
      entity.DR_NumeroDocumento = model.NumeroDocumento;
      entity.DR_ValorUtilizado = model.ValorUtilizado;



      return entity;
    }
  }

 
}

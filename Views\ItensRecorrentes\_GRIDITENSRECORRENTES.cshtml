﻿@using RepasseConvenio.Models
@model List<ItensRecorrentesIndex>
<div class="card-body">
  <div class="card-acoes" style=" display: flex; flex-wrap: wrap; flex-direction: row; align-items: baseline; margin-bottom: 1%;">
    <a class="btn btn-block btn-outline-warning disabled" id="Editar" style="width: 80px;"> Editar </a>
    <a class="btn btn-block btn-outline-danger disabled" id="Deletar" style="width: 80px; margin-left: 1%;"> Deletar </a>
  </div>
  <table class="table table-sm table-striped table-hover text-nowrap">
    <thead>
      <tr>
        <th>
          Início
        </th>
        <th>
          Fim
        </th>
        <th>
          Última Exclusão
        </th>
        <th>
          Valor
        </th>
        <th>
          Classificação
        </th>
        <th>
          Tipo Lançamento
        </th>
        <th>
          CPF/CNPJ
        </th>
        @*<th>
        </th>*@
      </tr>
    </thead>
    <tbody>
      @foreach (ItensRecorrentesIndex item in Model)
      {
      <tr class="TrSelecionavel" data-codigoitem="@item.Codigo" data-codmedico="@item.CodigoMedico">
        <td>
          @item.DataRecorrencia.ToString("dd/MM/yyyy")
        </td>
        <td>
          @if (item.DataFim.HasValue)
          {
            <text>
              @item.DataFim.Value.ToString("dd/MM/yyyy")
            </text>
          }
          else
          {
            <text>
            </text>
          }
        </td>
        <td>
          @if (item.DateLastExcute.HasValue)
          {
            <text>
              @item.DateLastExcute.Value.ToString("dd/MM/yyyy")
            </text>
          }
          else
          {
            <text>
            </text>
          }
        </td>
        <td>
          R$ @item.Valor
        </td>
        <td>
          @item.DescricaoClassificacao
        </td>
        <td>
          @item.TipoLancamento
        </td>

        <td class="cnpj">
          @item.CPFCNPJ
        </td>
        @*  <td style="display: flex; flex-wrap: wrap;">
                <div class="btn-group">
                   <a href="@Url.Action("Edit","ItensRecorrentes", new { id = item.Codigo })" class="btn btn-info"><i class="fas fa-edit"></i></a>
        @if (string.IsNullOrEmpty(item.CodigoRepasse))
          {
            <a class="btn btn-danger" id="RemoverExtrato" data-codigoclassificacao="@item.CodigoClassificacao" data-idextrato="@item.Codigo" data-codigomedico="@item.CodigoMedico"><i class="fas fa-trash"></i></a>
          }
    </div>
              </td>*@
      </tr>
      }
    </tbody>
  </table>
 </div>
<style>
  .card-acoes {
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;
    margin: 5px 0;
}
  .Selected {
  transform: scale(1.007);
  background-color: #d6101085 !important;
  transition: all 100ms;
  /*border: 2px solid red;*/
}

.UnSelected {
  transform: scale(1);
  /*background-color: #17a2b8 !important;*/
  transition: all 100ms;
}
</style>
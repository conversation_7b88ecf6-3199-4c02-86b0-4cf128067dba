﻿@using RepasseConvenio.Models
@model List<ProcedimentoIndex>

@{
  ViewBag.Title = "Lista Procedimento";
}

<div class="row">
  <section class="col-lg-12 connectedSortable ui-sortable">
    <div class="card">
      <div class="card-header ui-sortable-handle">
        <h3 class="card-title">
          <i class="fas fa-user mr-1"></i>
         
        </h3>
        <div class="card-tools">
          @*<button type="button" class="btn btn-block btn-outline-primary" id="IntegraConvenio" > Integrar Convenio </button>*@
          <a class="nav-link active"></a>
        </div>
        <div class="card-tools">
        </div>
      </div><!-- /.card-header -->
      <div class="card-body">
        <div class="tab-content p-0">
          <div class="box-body">
            <div class="row">
              <div class="col-md-12 table-responsive p-0">
                <table class="table table-sm table-striped table-hover text-nowrap">
                  <thead>
                    <tr>
                      <th>
                        Razão Social
                      </th>
                      <th>
                        CNPJ
                      </th>
                      <th>
                        Código ANS
                      </th>
                      <th>
                        Código Procedimento
                      </th>
                      <th>
                        Descrição Procedimento
                      </th>
                      <th>
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    @foreach (ProcedimentoIndex item in Model)
                    {
                    <tr>
                      <td>
                        @item.RazaoSocial
                      </td>
                      <td>
                        @item.CNPJ
                      </td>
                      <td>
                        @item.CodANS
                      </td>
                      <td>
                        @item.CodProcedimento
                      </td>
                      <td>
                        @item.Descricao
                      </td>
                      <td>
                      </td>
                    </tr>
                    }
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>

﻿@using RepasseConvenio.Models
@using PagedList
@using PagedList.Mvc;
@model MedicoModel

@{
  ViewBag.Title = "Detalhes Médico";
}

@using (Html.BeginForm("Detalhes", "Medico", FormMethod.Post))
{
  @Html.HiddenFor(a => a.<PERSON>)

  <div class="row">
    <section class="col-lg-12 connectedSortable ui-sortable">
      <div class="card">
        <div class="card-header ui-sortable-handle">
          <h3 class="card-title">
            <i class="fas fa-user mr-1"></i>
            Detalhes Médico
          </h3>
          <div class="card-tools">
          </div>
        </div>
        <div class="card-body">
            <div class="row">
              <div class="col-md-4">
                <div class="form-group">

                  @Html.LabelFor(a => a.Nome)
                  @Html.TextBoxFor(a => a.Nome, new { @class = "form-control", disabled = true })
                </div>
              </div>
              <div class="col-md-2">
                <div class="form-group">
                  @Html.LabelFor(a => a.CPF)
                  @Html.TextBoxFor(a => a.CPF, new { @class = "form-control CPF", disabled = true })
                </div>
              </div>
              <div class="col-md-2">
                <div class="form-group">
                  @Html.LabelFor(a => a.CRM)
                  @Html.TextBoxFor(a => a.CRM, new { @class = "form-control", disabled = true })
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  @Html.LabelFor(a => a.Email)
                  @Html.TextBoxFor(a => a.Email, new { @class = "form-control", disabled = true })
                </div>
              </div>
            </div>

            <div class="row" style="align-items: flex-end;">
              <div class="col-md-3">
                <div class="form-group">
                  @Html.LabelFor(a => a.BancoMedico)
                  @Html.TextBoxFor(a => a.BancoMedico, new { @class = "form-control", disabled = true })
                </div>
              </div>
              <div class="col-md-2">
                <div class="form-group">
                  @Html.LabelFor(a => a.AgenciaMedico)
                  @Html.TextBoxFor(a => a.AgenciaMedico, new { @class = "form-control", disabled = true })
                </div>
              </div>
              <div class="col-md-2">
                <div class="form-group">
                  @Html.LabelFor(a => a.ContaMedico)
                  @Html.TextBoxFor(a => a.ContaMedico, new { @class = "form-control", disabled = true })
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  @Html.LabelFor(a => a.ValorProLabore)
                  @Html.TextBoxFor(a => a.ValorProLabore, new { @class = "form-control  money" })
                </div>
              </div>

              <div class="col-md-3" style=" margin-bottom: 1rem;">
                <div class=" col-md-12 form-group" style="margin-bottom: 0 !important;">
                  <label>Diretor</label>
                </div>
                <div class="col-md-12">
                  @Html.RadioButtonFor(a => a.Diretor, "true")  Sim
                  @Html.RadioButtonFor(a => a.Diretor, "false")  Não
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  @Html.CheckBoxFor(a => a.Ativo)
                  @Html.LabelFor(a => a.Ativo)
                </div>
              </div>
            </div>
        </div>
        <div class="card-footer">
          <button type="button" value="Voltar" class="btn btn-secondary" onclick="window.history.back();">Voltar</button>

          <button type="submit" class="btn btn-info pull-rigth">Salvar</button>
        </div>
      </div>
    </section>
  </div>
}
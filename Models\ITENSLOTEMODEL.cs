﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Services;
using RepasseConvenio.WSPortalCooperado;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Web;

namespace RepasseConvenio.Models
{
  public class ItensLoteModel
  {
    public int Codigo { get; set; }
    [DisplayName("Número da guia ")]
    public string NumeroGuia { get; set; }
    public int IdLote { get; set; }
    public int IdGuiaAtendimento { get; set; }

    [DisplayName("Data do atendimento")]
    public DateTime DataAtendimento { get; set; }
    [DisplayName("Valor faturado")]
    public  decimal? ValorFaturado { get; set; }
    [DisplayName("Valor glosado")]
    public decimal? ValorGlosado { get; set; }
    [DisplayName("Valor Total")]
    public decimal?  ValorTotal { get; set; }

    [DisplayName("Status")]
    public string StatusLote { get; set; }
    public string SituacaoLote { get; set; }

  }


  public class ItensLoteModal
  {
    public int Codigo { get; set; }
    public int CodigoLote { get; set; }
    public int CodigoGuiaAtendimento { get; set; }
    public string NumeroGuia { get; set; }
    public string DescricaoStatus { get; set; }

  }
  public static class ItensLoteConversions
  {
    public static R_ItensLote Create(this R_ItensLote itensLote
                                    , int IdLote
                                    , int IdGuiaAtendimento
                                    , string NumeroGuia
                                    , int IdStatusNaoProcessado
                                    , int IdSituacaoNaoProcessado)
    {
      return new R_ItensLote()
      {
        IL_IdLote = IdLote,
        IL_IdGuiaAtendimento = IdGuiaAtendimento,
        IL_NumeroGuia = NumeroGuia,
        IL_IdStatusItensLote = IdStatusNaoProcessado,
        IL_IdSituacaoItensLote = IdSituacaoNaoProcessado
      };
    }
  }

}

  

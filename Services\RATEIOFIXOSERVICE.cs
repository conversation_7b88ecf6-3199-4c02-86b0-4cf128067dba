﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Models;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Web;

namespace RepasseConvenio.Services
{
  public class RateioFixoService : ServiceBase
  {
    public RateioFixoService()
   : base()
    { }
    public RateioFixoService(RepasseEntities Contexto)
       : base(Contexto)
    { }

    public RateioFixoService(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public RateioFixoService(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public List<RateioFixoModel> Get()
    {
      string query = @"SELECT
                          RF_Funcao [Funcao]
                        , RF_Percentual [Percentual]
                        , RF_IdRateioMedico [CodigoRateio]
                        , RF_IdMedico [CodigoMedico]
                        , RF_Id [Codigo]
                     FROM R_RateioFixo";

      return Contexto.Database.SqlQuery<RateioFixoModel>(query).ToList();
    }

    public List<RateioFixoIndex> GettoIndex(int id)
    {
      string query = @"SELECT
						              RF.RF_Id [Codigo]
						            , RM.RM_CodigoProcedimento + ' - ' + RM.RM_DescricaoProcedimento [Procedimento]
						            , H.H_Nome [Hospital]
						            , M.M_Nome [NomeMedico]
						            , M.M_CRM [CRMMedico]
                        , RF.RF_Funcao [Funcao]
                        , RF.RF_Percentual [Percentual]
                     FROM R_RateioFixo RF
                       INNER JOIN R_RateioMedico RM ON RF.RF_IdRateioMedico = RM.RM_Id
                       INNER JOIN R_Medico M ON M.M_Id = RF.RF_IdMedico
					             LEFT JOIN R_Hospital H ON H.H_Id = RM.RM_IdHospital
                      WHERE RF.RF_IdRateioMedico = @id";

      return Contexto.Database.SqlQuery<RateioFixoIndex>(query, new SqlParameter("@id",id)).ToList();
    }

    public RateioFixoModel GetbyId(int id)
    {
      string query = @"SELECT
                          RF_Funcao [Funcao]
                        , RF_Percentual [Percentual]
                        , RF_IdRateioMedico [CodigoRateio]
                        , RF_IdMedico [CodigoMedico]
                        , RF_Id [Codigo]
                     FROM R_RateioFixo 
                      WHERE RF_Id = @id";

      return Contexto.Database.SqlQuery<RateioFixoModel>(query, new SqlParameter("@id", id)).FirstOrDefault();
    }

    public bool Exist(int rateio, int Medico, string funcao)
    {
      R_RateioFixo rateioMedico = Contexto.R_RateioFixo.Where(a => a.RF_IdMedico == Medico && a.RF_Funcao.ToUpper().Equals(funcao.ToUpper()) && a.RF_IdRateioMedico == rateio).FirstOrDefault();

      if (rateioMedico == null)
        return false;
      else
        return true;
    }

    public void Create(RateioFixoModel model)
    {
      try
      {
        R_RateioFixo rateio = model.toRateioFixoCreate();
        Create(rateio);
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }

    public void Edit(RateioFixoModel model)
    {
      R_RateioFixo rateio = model.toRateioFixoEdit();
      Edit(rateio);
    }
  }
}
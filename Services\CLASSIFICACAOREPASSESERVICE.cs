﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using PagedList;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RepasseConvenio.Services
{
  public class ClassificacaoRepasseServices : ServiceBase
  {
    public ClassificacaoRepasseServices(RepasseEntities Contexto)
        : base(Contexto)
    { }
    public ClassificacaoRepasseServices()
       : base()
    { }

    public ClassificacaoRepasseServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public ClassificacaoRepasseServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public R_ClassificacaoRepasse GetByEnum(EnumClassificacaoRepasse Enum)
    {
      return Contexto.R_ClassificacaoRepasse.Where(a => a.CR_Enum == (int)Enum).FirstOrDefault();
    }
    public int GetIdByEnum(EnumClassificacaoRepasse Enum)
    {
      return Contexto.R_ClassificacaoRepasse.Where(a => a.CR_Enum == (int)Enum).Select(a => a.CR_Id).FirstOrDefault();
    }

    public R_ClassificacaoRepasse GetById(int? Id)
    {
      return Contexto.R_ClassificacaoRepasse.Where(a => a.CR_Id == Id).FirstOrDefault();
    }

    public IPagedList<R_ClassificacaoRepasse> GetAll(int? page)
    {
      page = page ?? 1;
      string query = @"SELECT * FROM R_ClassificacaoRepasse ORDER BY CR_Enum";
      return Contexto.Database.SqlQuery<R_ClassificacaoRepasse>(query).ToList().ToPagedList((int)page, GetQtdeRegistroPagina());
    }



    public void Edit(ClassificacaoRepasseModel model)
    {
      int comprar = Contexto.R_ClassificacaoRepasse
        .Where(x => x.CR_Enum == model.Enum && x.CR_Id != model.Id).ToList().Count();
      if (comprar > 0)
        throw new CustomException("Já existe uma classificação com este mesmo código com identificações diferentes!");
      Edit(model.ToDB());  
    }


    public void Create(ClassificacaoRepasseModel model)
    {
      int comprar = Contexto.R_ClassificacaoRepasse.Where(x => x.CR_Enum == model.Enum && x.CR_Id != model.Id).ToList().Count();
      if (comprar > 0)
        throw new CustomException("Já existe uma classificação com este mesmo código com identificações diferentes!");
      Create(model.ToDB());
    }


    public List<R_ClassificacaoRepasse> GetByTerm(string term)
    {
      if (string.IsNullOrEmpty(term))
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_ClassificacaoRepasse");

        return Contexto.Database.SqlQuery<R_ClassificacaoRepasse>(query).ToList();

      }
      else
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_ClassificacaoRepasse
                                       WHERE CR_Descricao LIKE @termo");

        return Contexto.Database.SqlQuery<R_ClassificacaoRepasse>(query, new SqlParameter("@termo", string.Format("%{0}%", term))).ToList();

      }
    }
  }
}


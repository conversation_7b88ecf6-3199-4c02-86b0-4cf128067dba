﻿@using RepasseConvenio.Models
@model List<LoteRepassaeModal>

<table class="table table-hover table-striped">
  <thead>
    <tr>
      <th>

      </th>
      <th>
        Número Lote
      </th>
      <th>
        Criado
      </th>
      <th>
        Enviado
      </th>
      <th>
        Vencimento
      </th>
      <th>
        Status
      </th>
    </tr>
  </thead>
  <tbody>
    @foreach (LoteRepassaeModal item in Model)
    {
    <tr>
      <td>
        <a class="btn btn-outline-warning btn-circle btn-sm VisualizarItensLoteRepasse" data-codigolote="@item.Codigo" title="Visualizar">
          Visualizar
        </a>
      </td>
      <td>
        @item.NumeroLote
      </td>
      <td>
        @item.DataCriacao.ToString("dd/MM/yyyy")
      </td>
      <td>
        @item.DataEnvio.ToString("dd/MM/yyyy")
      </td>
      <td>
        @if (item.DataVencimento.HasValue)
        {
          <text>
            @item.DataVencimento.Value.ToString("dd/MM/yyyy")
          </text>
        }
        else
        {
          <text> - </text>
        }
      </td>
      <td>
        @item.DescricaoStatus
      </td>
    </tr>
    }
  </tbody>
</table>

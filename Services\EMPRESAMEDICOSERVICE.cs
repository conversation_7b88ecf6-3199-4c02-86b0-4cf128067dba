﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using Microsoft.AspNet.SignalR;
using RepasseConvenio.Infrastructure.Hubs;
using RepasseConvenio.Models;
using RepasseConvenio.WSPortalCooperado;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Web;

namespace RepasseConvenio.Services
{
  public class EmpresaMedicoService : ServiceBase
  {
    public EmpresaMedicoService()
   : base()
    { }
    public EmpresaMedicoService(RepasseEntities Contexto)
       : base(Contexto)
    { }

    public EmpresaMedicoService(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public EmpresaMedicoService(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public R_EmpresaMedico GetById(int? IdMedico)
    {
      string query = @"SELECT * FROM R_EmpresaMedico WHERE EM_Id = @IdMedico";
      return Contexto.Database.SqlQuery<R_EmpresaMedico>(query, new SqlParameter("@IdMedico", IdMedico.HasValue ? (object)IdMedico : DBNull.Value)).FirstOrDefault();
    }

    public R_EmpresaMedico GetByCNPJ(string cnpj)
    {
      return Contexto.R_EmpresaMedico.Where(a => a.EM_CNPJ.Equals(cnpj)).FirstOrDefault();
    }
    public R_EmpresaMedico GetById(int codMedico)
    {
      return Contexto.R_EmpresaMedico.Where(a => a.EM_IdMedico == codMedico).FirstOrDefault();
    }

    public List<EmpresaMedicoModel> GetbyIdMedico(int codMedico)
    {
      try
      {
        string query = @"SELECT 
                           EM_Id [Codigo]
                         , EM_IdMedico [CodigoMedico]
                         , EM_RazaoSocial [RazaoSocial]
                         , EM_CNPJ [CNPJ]
                         , EM_AgenciaEmpresa [Agencia]
                         , EM_ContaEmpresa [Conta]
                         , EM_BancoEmpresa [Banco]
                        FROM R_EmpresaMedico 
                        WHERE EM_IdMEdico = @medico";

        return Contexto.Database.SqlQuery<EmpresaMedicoModel>(query, new SqlParameter("@medico", codMedico)).ToList();

      }
      catch (Exception)
      {
        throw;
      }
    }

    public List<R_EmpresaMedico> GetByTerm(string term, int id)
    {
      if (string.IsNullOrEmpty(term))
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_EmpresaMedico WHERE EM_IdMedico = @id");

        //return Contexto.Database.SqlQuery<R_EmpresaMedico>(query).ToList();
        return Contexto.Database.SqlQuery<R_EmpresaMedico>(query, new SqlParameter("@id", id)).ToList();
      }
      else
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_EmpresaMedico
                                       WHERE EM_RazaoSocial LIKE @termo OR EM_CNPJ LIKE @termo");

        return Contexto.Database.SqlQuery<R_EmpresaMedico>(query, new SqlParameter("@termo", string.Format("%{0}%", term))).ToList();

      }
    }

    public bool IfExistByAgenciaContaBanco(string agencia, string conta, string banco, int IdMedico)
    {
      string query = @"SELECT
                        COUNT(1)
                       FROM R_EmpresaMedico
                       WHERE EM_IdMedico = @IdMedico
                       AND EM_AgenciaEmpresa = @agencia
                       AND EM_BancoEmpresa = @banco
                       AND EM_ContaEmpresa = @conta";
      int quantidade = Contexto.Database.SqlQuery<int>(query, new SqlParameter("@IdMedico", IdMedico)
                                                            , new SqlParameter("@agencia", agencia)
                                                            , new SqlParameter("@banco", banco)
                                                            , new SqlParameter("@conta", conta)).FirstOrDefault();
      if (quantidade == 0)
        return false;
      else
        return true;
    }

    public void CreateEmpresaMedico(EmpresaMedicoRepasse empresaMedicoRepasse, int IdMedico)
    {
      R_EmpresaMedico empresaMedico = new R_EmpresaMedico();
      empresaMedico = empresaMedico.EmpresaMedicoRepasseToEntityCreate(empresaMedicoRepasse, IdMedico);
      Create(empresaMedico);
    }

    public void IntegraEmpresaMedico(string connectionId, int IdMedico, int IdMedicoExterno, string NomeMedico, int MedicoAtual, int QuantidadeTotal)
    {
      SendSignal sendSignal = new SendSignal();
      sendSignal.openModalRetornoUsuario(connectionId, "Integrando Empresas");
      sendSignal.creatPStrong(connectionId, string.Format("Procurando Empresas Medico(a): {0} ... {1} de {2}", NomeMedico, MedicoAtual, QuantidadeTotal));

      IntegraRepasse integraRepasse = new IntegraRepasse();
      List<EmpresaMedicoRepasse> ListaEmpresaMedicos = integraRepasse.GetEmpresasMedico("8F6AB82E-D215-4477-B07F-F3C17B58F60E", IdMedicoExterno).ToList();
      sendSignal.creatPSucess(connectionId, string.Format("{0} Empresa(s) Encontrada(s)", ListaEmpresaMedicos.Count()));
      sendSignal.creatPSucess(connectionId, "Sincronizando... Aguarde");
      int i = 0;
      int z = 0;
      int quantidadePular = ListaEmpresaMedicos.Count() / 100;
      int quantidade = 0;
      if (ListaEmpresaMedicos.Count() == 0)
        sendSignal.setPorcentagemProgress(connectionId, (100).ToString());

      foreach (EmpresaMedicoRepasse empresaMedicoRepasse in ListaEmpresaMedicos)
      {
        if (i == quantidade)
        {
          quantidade += quantidadePular;
          sendSignal.setPorcentagemProgress(connectionId, (z + 1).ToString());
          z++;
        }

        bool ifExistByAgenciaContaBanco = IfExistByAgenciaContaBanco(empresaMedicoRepasse.AgenciaEmpresa, empresaMedicoRepasse.ContaEmpresa, empresaMedicoRepasse.BancoEmpresa, IdMedico);
        if (!ifExistByAgenciaContaBanco)
          CreateEmpresaMedico(empresaMedicoRepasse, IdMedico);
        i++;
      }
    }

    public List<EmpresaMedicoRegra> GetListaEmpresaMedicoRegra(int IdMedico)
    {
      string query = @"SELECT
                        RCC.RCC_QuerySql [Query],
                        EM.EM_Id [IdEmpresa],
                        EM.EM_CNPJ [CNPJ]
                       FROM R_RegraCartaConversao RCC
                       INNER JOIN R_EmpresaMedico EM ON EM.EM_Id = RCC.RCC_IdEmpresaMedico AND EM.EM_IdMedico = @IdMedico
                       where RCC.RCC_Valida = 1";

      return Contexto.Database.SqlQuery<EmpresaMedicoRegra>(query, new SqlParameter("@IdMedico", IdMedico)).ToList();
    }

    public List<R_EmpresaMedico> GetByTermAndIdMedico(string term, int IdMedico)
    {
      if (string.IsNullOrEmpty(term))
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM R_EmpresaMedico
                                       WHERE EM_IdMedico = @IdMedico");

        return Contexto.Database.SqlQuery<R_EmpresaMedico>(query, new SqlParameter("@IdMedico", IdMedico)).ToList();

      }
      else
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_EmpresaMedico
                                       WHERE EM_IdMedico = @IdMedico AND (EM_RazaoSocial LIKE @termo OR EM_CNPJ LIKE @termo)");

        return Contexto.Database.SqlQuery<R_EmpresaMedico>(query, new SqlParameter("@termo", string.Format("%{0}%", term))
                                                                , new SqlParameter("@IdMedico", IdMedico)).ToList();

      }
    }

  }
}
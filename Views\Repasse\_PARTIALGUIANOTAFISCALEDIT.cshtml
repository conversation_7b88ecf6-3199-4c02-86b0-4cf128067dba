﻿@using RepasseConvenio.Infrastructure.Controls
@using RepasseConvenio.Models
@model List<GuiaNotaFiscalEdit>

@if (Model.Count() == 0)
{
  <p style="width: 100%; display: flex; align-items: center; justify-content: center;"> Nenhuma Guia Adicionada</p>
}

else
{

  decimal vlrPago = Model.Sum(a => a.ValorPago);
  decimal vlrApr = Model.Sum(a => a.Valor<PERSON>presentado);
  <div class="row" style="width: 100%;">
    <div class="col-md-12 table-responsive p-0 ">
      <table class="table table-sm table-striped table-hover text-nowrap">
        <thead>
          <tr>
            <th colspan="7" style="text-align: end;border-top: 0 !important;">
              Total Apresentado: @vlrApr
            </th>
            <th style="text-align: end;padding-right: 10px;border-top: 0 !important;">
              Total Pago: @vlrPago
            </th>
          </tr>
          <tr>
            <th>
            </th>
            <th>
              Apresentado
            </th>
            <th>
              Pago
            </th>
            <th>
              Faturado
            </th>
            <th>
              Glosado
            </th>
            <th>
              Nro Unicooper
            </th>
            <th>
              Procedimento
            </th>
            <th>
              Descrição
            </th>
          </tr>
        </thead>
        <tbody>
          @for (int i = 0; i < Model.Count(); i++)
          {
            <tr>
              <td>
                <a class="btn btn-outline-warning btn-circle btn-sm btnEditGuiaNotaFiscal" data-linha="@i" data-idguianotafiscal="@Model[i].Id" title="Editar">
                  Salvar
                </a>
                <a class="btn btn-outline-danger btn-circle btn-sm btnExcluirGuiaNotaFiscal" data-linha="@i" data-idguianotafiscal="@Model[i].Id" title="Editar">
                  Excluir
                </a>
              </td>
              <td>
                @Html.EditorFor(model => Model[i].ValorApresentado, new { htmlAttributes = new { @class = "form-control moneyWithOutPrefix", @style = " width: 105px;" } })
              </td>
              <td>
                @Html.EditorFor(model => Model[i].ValorPago, new { htmlAttributes = new { @class = "form-control moneyWithOutPrefix", @style = " width: 100px;" } })
              </td>
              <td>
                @Model[i].ValorFaturado
              </td>
              <td>
                @Model[i].ValorGlosado
              </td>
              <td>
                @Model[i].NroUnicooper
              </td>
              <td>
                @Model[i].CodProcedimento
              </td>
              <td>
                @Model[i].DescProcedimento
              </td>

            </tr>
          }
        </tbody>
      </table>
    </div>
  </div>
}


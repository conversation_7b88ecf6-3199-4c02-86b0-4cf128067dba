﻿var ExtratoMedico;

ExtratoMedico = function () {
};

ExtratoMedico.init = function () {
  $(document).on("click", ".TrSelecionavel", function () {
    var IdExtrato = $(this).data("codigoextrato");
    var IdMedico = $(this).data("codigomedico");
    var IdClassificacao = $(this).data("codigoclassificacao");
    var urlEditarExtrato = GetURLBaseComplete() + "/ExtratoMedico/Edit/" + IdExtrato;

    if (!$(this).hasClass("Selected")) {
      $('.Selected').each(function (index, element) {
        $(element).removeClass("Selected")
        $(element).addClass("UnSelected")
      });

      if ($(this).hasClass("Selected")) {
        $(this).removeClass("Selected")
        $(this).addClass("UnSelected")
      }
      else {
        $(this).addClass("Selected")
        $(this).removeClass("UnSelected")
      }
    }
    else {
      $(this).removeClass("Selected")
      $(this).addClass("UnSelected")
    }

    var countSelected = 0;
    $('.Selected').each(function (index, element) {
      if ($(element).hasClass("Selected")) {
        $('#Editar').attr("href", urlEditarExtrato);
        $('#Log').data("idextrato", IdExtrato);
        $('#Remover').data("idextrato", IdExtrato);
        $('#Remover').data("idmedico", IdMedico);
        $('#Remover').data("codigoclassificacao", IdClassificacao);
        countSelected++;
      }
    });

    if (countSelected == 0) {
      $('#Editar').removeAttr("href");
      $('#Log').removeAttr("href");
      $('#Remover').removeAttr("href");

      $('#Editar').attr("disabled", true);
      $('#Log').attr("disabled", true);
      $('#Remover').attr("disabled", true);

      $('#Editar').addClass("disabled");
      $('#Log').addClass("disabled");
      $('#Remover').addClass("disabled");

      $('#Remover').data("idextrato", "");
      $('#Remover').data("idmedico", "");
      $('#Remover').data("codigoclassificacao", "");

      $('#Log').data("idextrato", "");
    }
    else {
      $('#Editar').attr("disabled", false);
      $('#Log').attr("disabled", false);
      $('#Remover').attr("disabled", false);

      $('#Editar').removeClass("disabled");
      $('#Log').removeClass("disabled");
      $('#Remover').removeClass("disabled");

      $('#Remover').data("idextrato", IdExtrato);
      $('#Remover').data("idmedico", IdMedico);
      $('#Remover').data("codigoclassificacao", IdClassificacao);

      $('#Log').data("idextrato", IdExtrato);
    }
  });

  $(document).on('change', '#TipoContaEnum', function () {

    var tipo = $(this).val();
    if (tipo == "PJ") {
      $("#EmpresaCNPJ").removeAttr("hidden", "hidden");
    }
    else {
      $("#EmpresaCNPJ").attr("hidden", "hidden");
    }
  });

  $(document).on('click', '#Remover', function () {
    var IdExtrato = $(this).data("idextrato");
    var IdMedico = $(this).data("idmedico");
    var Codigoclassificacao = $(this).data("codigoclassificacao");

    ExtratoMedico.ConfirmarDelete(IdExtrato, IdMedico, Codigoclassificacao);
  });

  $(document).on('click', '#Log', function () {
    var IdExtrato = $(this).data("idextrato");
    var data = {
      id: IdExtrato
    };

    $.ajax({
      type: 'GET',
      url: GetURLBaseComplete() + '/ExtratoMedico/_GridLogs',
      data: data,
      dataType: 'html',
      success: function (data) {
        $('#GridLogs').html(data);
        $('#ModalLogs').modal("show");

      },
      error: function (err) {
      }
    });
  });

  $(document).on('click', '.MaisDetalhesLog', function () {
    var IdLog = $(this).data("codigolog");
    var data = {
      id: IdLog
    };

    $.ajax({
      type: 'GET',
      url: GetURLBaseComplete() + '/ExtratoMedico/_GridLogsDetalhes',
      data: data,
      dataType: 'html',
      success: function (data) {
        $('#GridLogsDetalhes').html(data);
        $('#ModalLogsDetalhes').modal("show");

      },
      error: function (err) {
        AlertaToast("Erro", "Log sem detalhes adicionais.", "error", 3000, "#ff9494");
      }
    });
  });

}

ExtratoMedico.ConfirmarDelete = function (IdExtrato, IdMedico, CodicoClassificacao) {
  if (CodicoClassificacao == 5) {
    Swal.fire({
      title: "Deseja continuar a exclusão?",
      html: "Este Extrato é um Processamento de Repasse, irá excluir todos os demais do mesmo repasse, deseja continuar?",
      icon: "warning",
      confirmButtonText: "Excluir",
      showCancelButton: "Cancelar",
      showLoaderOnConfirm: true,
      dangerMode: true,
    })
      .then((willDelete) => {
        if (willDelete.isConfirmed) {
          ExtratoMedico.AjaxDelete(IdExtrato, IdMedico);
        } else {
        }
      });
  }
  else {
    Swal.fire({
      title: "Deseja continuar a exclusão?",
      html: "",
      icon: "warning",
      confirmButtonText: "Excluir",
      showCancelButton: "Cancelar",
      showLoaderOnConfirm: true,
      dangerMode: true,
    })
      .then((willDelete) => {
        if (willDelete.isConfirmed) {
          ExtratoMedico.AjaxDelete(IdExtrato, IdMedico);
        } else {
        }
      });
  }
}

ExtratoMedico.AjaxDelete = function (IdExtrato, IdMedico) {
  var data = {
    IdExtrato: IdExtrato
  };

  $.ajax({
    type: 'POST',
    url: GetURLBaseComplete() + '/ExtratoMedico/Delete',
    data: data,
    dataType: 'json',
    success: function (data) {
      if (!data.Erro) {
        Alerta(data.Titulo, data.Mensagem, "success", "Fechar");
        ExtratoMedico.GridExtratos(IdMedico);
      }
      else
        Alerta(data.Titulo, data.Mensagem, "error", "Fechar");
    },
    error: function (err) {
      AlertaSwal("Erro", "Gentileza tentar mais tarde.", "error", "Fechar");
    }
  });
}

ExtratoMedico.GridExtratos = function (IdMedico) {
  var data = {
    id: IdMedico
  };

  $.ajax({
    type: 'GET',
    url: GetURLBaseComplete() + '/ExtratoMedico/_GridExtratos',
    data: data,
    dataType: 'html',
    success: function (data) {
      $('#GridExtratos').html(data);
    },
    error: function (err) {
    }
  });
}

$(document).ready(function () {
  ExtratoMedico.init();
});


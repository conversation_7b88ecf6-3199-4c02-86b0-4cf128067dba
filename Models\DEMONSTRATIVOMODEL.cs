﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace RepasseConvenio.Models
{
  public class DemonstrativoConvenioCreate
  {
    public string NomePlanilha { get; set; }

    public decimal ValorFaturado { get; set; }

    public decimal ValorApresentado { get; set; }

    public decimal ValorGlosado { get; set; }

    public decimal ValorPago { get; set; }

    public DateTime DataCriacao { get; set; }

    public int IdRepasse { get; set; }

    public string InfoProcessamento { get; set; }

  }

  public class DemonstrativoConvenioGrid
  {
    public int Codigo { get; set; }

    public int CodigoRepasse { get; set; }

    public string NomePlanilha { get; set; }

    public decimal TotalFaturado { get; set; }

    public decimal ValorApresentado { get; set; }

    public decimal TotalGlosado { get; set; }

    public decimal TotalPago { get; set; }

    public bool Processado { get; set; }

    [NotMapped]
    public bool isSelected { get; set; }
  }

  public class DemonstrativoUpdateValores
  {
    public decimal TotalFaturado { get; set; }

    public decimal TotalPago { get; set; }

    public decimal TotalApresentado { get; set; }

    public decimal TotalGlosado { get; set; }

  }

  public static class DemonstrativoConversions
  {
    public static R_DemonstrativoConvenio ModelToEntityCreate(this DemonstrativoConvenioCreate demonstrativoConvenioCreate
                                                             , int IdUsuarioCriacao
                                                             , int IdStatusDemonstrativo)
    {
      return new R_DemonstrativoConvenio()
      {
        DC_IdRepasse = demonstrativoConvenioCreate.IdRepasse,
        DC_IdUsuarioCriacao = IdUsuarioCriacao,
        DC_InfoProcessamento = demonstrativoConvenioCreate.InfoProcessamento,
        DC_NomePlanilha = demonstrativoConvenioCreate.NomePlanilha,
        DC_TotalApresentado = demonstrativoConvenioCreate.ValorApresentado,
        DC_TotalFaturado = demonstrativoConvenioCreate.ValorFaturado,
        DC_TotalGlosado = demonstrativoConvenioCreate.ValorGlosado,
        DC_TotalPago = demonstrativoConvenioCreate.ValorPago,
        DC_DataCriacao = DateTime.Now,
        DC_IdStatusDemonstrativoConvenio = IdStatusDemonstrativo
      };
    }

    public static DemonstrativoConvenioCreate ListaGuiaDemonstrativoIntegraToDemonstrativoConvenioCreate(this List<GuiaDemonstrativoIntegraCabecalho> ListaGuiaDemonstrativoIntegraCabecalho, string NomePlanilha, int IdRepasse)
    {
      return new DemonstrativoConvenioCreate()
      {
        ValorFaturado = ListaGuiaDemonstrativoIntegraCabecalho.Select(a => a.ListaGuiaDemonstrativoIntegra).Sum(a => a.Sum(b => b.ValorFaturado)),
        ValorApresentado = ListaGuiaDemonstrativoIntegraCabecalho.Select(a => a.ListaGuiaDemonstrativoIntegra).Sum(a => a.Sum(b => b.ValorApresentado.Value)),
        ValorGlosado = ListaGuiaDemonstrativoIntegraCabecalho.Select(a => a.ListaGuiaDemonstrativoIntegra).Sum(a => a.Sum(b => b.ValorGlosado)),
        ValorPago = ListaGuiaDemonstrativoIntegraCabecalho.Select(a => a.ListaGuiaDemonstrativoIntegra).Sum(a => a.Sum(b => b.ValorPago)),
        IdRepasse = IdRepasse,
        NomePlanilha = NomePlanilha
      };
    }
  }
}
﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using PagedList;
using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Controls;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace RepasseConvenio.Controllers
{
  [Authorize]
  [Security("67E2299B-BD67-4EEE-A425-FC2B5521E84B")]

  public class ConvenioController : LibController
  {

    private ConvenioServices ConvenioServices
    {
      get
      {
        if (_ConvenioServices == null)
          _ConvenioServices = new ConvenioServices(ContextoUsuario.UserLogged);

        return _ConvenioServices;
      }
    }
    private ConvenioServices _ConvenioServices;
  [Security("C42320B0-B6D6-4053-8150-AE24C943D394")]
    public ActionResult Index(int? Page)
    {
      try
      {

        int numpag = Page ?? 1;
        ConvenioIndex Convenio = ConvenioServices.Get(numpag);

        return View(Convenio);
      }
      catch (Exception)
      {
        throw;
      }
    }
    [Security("8E64CB74-C79E-4A33-A129-8D44B7F133F5")]

    public ActionResult Details(int codigoConvenio, int? Page)
    {
      try
      {
        int numpag = Page ?? 1;
        ConvenioModel convenio = ConvenioServices.GetByIdConvenio(codigoConvenio);
        convenio.ListProcedimentos = new ProcedimentoServices().Get(codigoConvenio, numpag);
        return View(convenio);
      }
      catch (Exception)
      {
        throw;
      }
    }
    [HttpGet]
    [Security("F764A832-1753-414B-8639-6B55372AD3E4")]

    public ActionResult Edit(int codigoConvenio)
    {
      try
      {
        ConvenioModel convenio = ConvenioServices.GetByIdConvenio(codigoConvenio);
        return View(convenio);
      }
      catch (Exception)
      {
        throw;
      }
    }
    [HttpPost]
    [Security("F764A832-1753-414B-8639-6B55372AD3E4")]
    public ActionResult Edit(ConvenioModel model)
    {
      try
      {
        if (ModelState.IsValid)
        {
          ConvenioServices.EditConvenio(model);
          MessageListToast.Add(new Message(MessageType.Success, LibMessageToast.MessageEditSucess));
          return RedirectToAction("Index");
        }
        return View(model);
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return View(model);
      }
    }


    [HttpPost]
    [Security("D7BD9FE1-8E34-479B-9A4F-19953E9D9BDA")]

    public JsonResult IntegraConvenio(string ConnectionId)
    {
      try
      {
        ConvenioServices convenioServices = new ConvenioServices();
        convenioServices.IntegraConvenio(ConnectionId);
        return Json("");
      }
      catch (Exception ex)
      {
        throw;
      }
    }

    [Security("CE59402D-4FD1-4639-9022-8A0F62869E62")]
    [HttpGet]
    public PartialViewResult _ProcedimentoPartial(List<ProcedimentoIndex> list)
    {
      try
      {
        return PartialView("_ProcedimentoPartial", list);
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }
  }
}
﻿@using RepasseConvenio.Models
@model List<PlanilhaRecebimentoModel>

@{
  ViewBag.Title = "Repasse";
}

<div class="col-md-12 table-responsive p-0 ">
  <table class="table table-sm table-striped table-hover text-nowrap">
    <thead>
      <tr>
        <th>
          @Html.CheckBox("CheckAllPlanRecebimento")
        </th>
        <th>
          Nome
        </th>
        <th>
          Número Guia
        </th>
        <th>
          Total Faturado
        </th>
        <th>
          Valor Apresentado
        </th>
        <th>
          Total Glosado
        </th>
        <th>
          Total Pago
        </th>
        <th>

        </th>
      </tr>
    </thead>
    <tbody>
      @foreach (PlanilhaRecebimentoModel item in Model)
      {
        <tr>
          <td>
            <input class="isSelected" data-codigoplanilha="@item.PRM_Codigo" data-val="true" data-val-required="O campo isSelected é obrigatório." name="item.isSelected" type="checkbox" value="true">
          </td>
          <td>
            @item.PRM_Nome
          </td>
          <td>
            @item.PRM_NumeroGuia
          </td>
          <td>
            @item.PRM_TotalFaturado
          </td>
          <td>
            @item.PRM_ValorApresentado
          </td>
          <td>
            @item.PRM_TotalGlosado
          </td>
          <td>
            @item.PRM_TotalPago
          </td>
          <td>
            @if (item.PRM_Processado != true)
            {
              <a data-url="@Url.Action("Edit", "PlanilhaRecebimento", new { id = item.PRM_Codigo })" data-idplan="@item.PRM_Codigo" data-idrepasse="@item.PRM_CodigoRepasse" class="btn btn-outline-warning btn-circle btn-sm btnEditPlanRecebimento" title="Editar">
                Editar
              </a>
            }
            <a data-idplan="@item.PRM_Codigo" class="btn btn-outline-danger btn-circle btn-sm btnRemoverPlanRecebimento" title="Excluir">
              Excluir
            </a>
          </td>
        </tr>
      }
    </tbody>
  </table>
</div>

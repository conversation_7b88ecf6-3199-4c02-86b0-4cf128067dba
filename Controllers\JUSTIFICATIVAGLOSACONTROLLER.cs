﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Controls;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using RepasseConvenio.Services;
using System;
using System.Web.Mvc;

namespace RepasseConvenio.Controllers
{
  [Authorize]
  [Security("A4B40E6E-1FF5-4EAB-9F04-8583E4CF0D21")]

  public class JustificativaGlosaController : LibController
  {
    private JustificativaGlosaService justificativaGlosaService
    {
      get
      {
        if (_JustificativaGlosaService == null)
          _JustificativaGlosaService = new JustificativaGlosaService(ContextoUsuario.UserLogged);

        return _JustificativaGlosaService;
      }
    }

    private JustificativaGlosaService _JustificativaGlosaService;
    [Security("C18A852E-7FBD-4FD3-AA87-C7269324EC1B")]

    public ActionResult Index(int? page, string filtro)
    {
      try
      {
        int pageNumber = page ?? 1;
        ViewBag.page = pageNumber;
        ViewBag.filtro = filtro;

        var list = justificativaGlosaService.Get(pageNumber);
        return View(list);
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return View();
      }
    }
    [Security("A8DC5D34-487E-485D-B37D-3A13E4CC1FFD")]
    [HttpGet]
    public ActionResult Create()
    {
      return View();
    }
    [Security("A8DC5D34-487E-485D-B37D-3A13E4CC1FFD")]
    [HttpPost]
    public ActionResult Create(JustificativaGlosaModel model)
    {
      try
      {
        if (ModelState.IsValid)
        {
          justificativaGlosaService.Create(model);

          MessageListToast.Add(new Message(MessageType.Success, "Extrato salvo com sucesso."));
          return RedirectToAction("Index");
        }
        MessageListToast.Add(new Message(MessageType.Error, "Favor verificar os campos preenchidos."));
        return View(model);
      }
      catch (CustomException ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return View(model);
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, "Favor verificar os campos preenchidos."));
        return View(model);
      }
    }
    [Security("3B7644BB-402F-45E7-941C-182DE1B1C9BB")]

    [HttpGet]
    public ActionResult Edit(int codigo)
    {
      try
      {
        JustificativaGlosaModel model = justificativaGlosaService.GetById(codigo).OffEntityIRToModel();
        return View(model);
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return RedirectToAction("Index", "JustificativaGlosa");
      }
    }
    [Security("3B7644BB-402F-45E7-941C-182DE1B1C9BB")]
    [HttpPost]
    public ActionResult Edit(JustificativaGlosaModel model)
    {
      try
      {
        justificativaGlosaService.Edit(model);
        MessageListToast.Add(new Message(MessageType.Success, LibMessageToast.MessageEditSucess));
        return RedirectToAction("Index", "JustificativaGlosa");
      }
      catch (CustomException ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return View(model);
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return View(model);
      }
    }
    [Security("8A468F0B-E533-4560-BF0C-1B657F31ED55")]

    public ActionResult Delete(int codigo)
    {
      try
      {
        justificativaGlosaService.Delete(codigo);

        MessageListToast.Add(new Message(MessageType.Success, LibMessageToast.MessageDeleteSucess));
        return RedirectToAction("Index", "JustificativaGlosa");
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return RedirectToAction("Index", "JustificativaGlosa");
      }
    }
  }
}
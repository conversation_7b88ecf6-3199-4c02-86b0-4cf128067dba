﻿@using RepasseConvenio.Models
@model List<RateioMedicoIndex>

@{
  ViewBag.Title = "Lista Médico";
}


<div class="row">
  <section class="col-lg-12 connectedSortable ui-sortable">
    <div class="card">
      <div class="card-header ui-sortable-handle card-header-sm">
        <h3 class="card-title">
          <i class="fas fa-paperclip mr-1"></i>
          Regra de Rateio
        </h3>
        <div class="card-tools">
          <button type="button" style="padding: 0.2rem;" value="Create" class="btn btn-primary btn-sm-header" onclick="location.href = '@Url.Action("Create", "RateioMedico", new {codMedico = @ViewBag.CodigoMedico})'">Nova regra</button>
        </div>
      </div><!-- /.card-header -->
      <div class="card-body">
        <div class="tab-content p-0">
          <div class="box-body">
            <div class="row">
              <div class="col-md-12">
                <table class="table table-hover table-striped">
                  <thead>
                    <tr>
                      <th>
                        Hospital
                      </th>
                      <th>
                        CNPJ
                      </th>
                      <th>
                        Cod. Procedimento
                      </th>
                      <th>
                        Descrição Procedimento
                      </th>
                      <th>
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    @foreach (RateioMedicoIndex item in Model)
                    {
                      <tr>
                        <td>
                          @item.NomeHospital
                        </td>
                        <td>
                          @item.CNPJHospital
                        </td>
                        <td>
                          @item.CodigoProcedimento
                        </td>
                        <td>
                          @item.DescricaoProcedimento
                        </td>
                        <td>
                          <a href="@Url.Action("Index", "RateioFixo", new { codRateio = item.Codigo })" class="btn btn-warning" title="Rateio Fixo">
                            Rateio Fixo
                          </a>
                          <a href="@Url.Action("Index", "RateioGrauParticipacao", new { codRateio = item.Codigo })" class="btn btn-warning" title=" Rateio Grau Participação">
                            Rateio Grau Participação
                          </a>
                        </td>
                      </tr>
                    }
                  </tbody>
                </table>
              </div>
            </div>
            <hr />
            <div class="row">
              <div class="col-md-3">
                <button type="button" value="Voltar" class="btn btn-secondary" onclick="location.href='@Url.Action("Index", "Medico")'">Voltar</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>
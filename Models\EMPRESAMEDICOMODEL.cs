﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.WSPortalCooperado;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Permissions;
using System.Web;
using System.Web.Script.Serialization;

namespace RepasseConvenio.Models
{
  public class EmpresaMedicoModel
  {
    public int Codigo { get; set; }
    public string RazaoSocial { get; set; }
    public string CNPJ { get; set; }
    public string Agencia { get; set; }
    public string Banco { get; set; }
    public string Conta { get; set; }
    public int CodigoMedico { get; set; }
  }

  public class EmpresaMedicoRegra
  {
    public int IdEmpresa { get; set; }

    public string CNPJ { get; set; }

    public string Query { get; set; }

    public int Prioridade { get; set; }
  }

  public static class EmpresaMedicoModelConversion
  {
    public static R_EmpresaMedico EmpresaMedicoRepasseToEntityCreate(this R_EmpresaMedico empresaMedico, EmpresaMedicoRepasse empresaMedicoRepasse, int idMedico)
    {
      return new R_EmpresaMedico()
      {
        EM_RazaoSocial = empresaMedicoRepasse.RazaoSocial,
        EM_ContaEmpresa = empresaMedicoRepasse.ContaEmpresa,
        EM_AgenciaEmpresa = empresaMedicoRepasse.AgenciaEmpresa,
        EM_BancoEmpresa = empresaMedicoRepasse.BancoEmpresa,
        EM_CNPJ = empresaMedicoRepasse.CNPJ,
        EM_IdMedico = idMedico
      };
    }
  }
}
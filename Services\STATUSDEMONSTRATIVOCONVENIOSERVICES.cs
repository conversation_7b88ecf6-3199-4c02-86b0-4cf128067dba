﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using PagedList;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RepasseConvenio.Services
{
  public class StatusDemonstrativoConvenioServices : ServiceBase
  {
    public StatusDemonstrativoConvenioServices(RepasseEntities Contexto)
        : base(Contexto)
    { }
    public StatusDemonstrativoConvenioServices()
       : base()
    { }

    public StatusDemonstrativoConvenioServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public StatusDemonstrativoConvenioServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public int GetIdByEnum(StatusDemonstrativoEnum statusDemonstrativoEnum)
    {
      return Contexto.R_StatusDemonstrativoConvenio.Where(a => a.SDC_Enum == (int)statusDemonstrativoEnum).Select(a => a.SDC_Id).FirstOrDefault();
    }
    public R_StatusDemonstrativoConvenio GetByEnum(StatusDemonstrativoEnum statusDemonstrativoEnum)
    {
      return Contexto.R_StatusDemonstrativoConvenio.Where(a => a.SDC_Enum == (int)statusDemonstrativoEnum).FirstOrDefault();
    }

    public R_StatusDemonstrativoConvenio GetById(int Id)
    {
      return Contexto.R_StatusDemonstrativoConvenio.Where(a => a.SDC_Id == Id).FirstOrDefault();
    }

    public List<R_StatusDemonstrativoConvenio> GetAll()
    {
      string query = @"SELECT * FROM R_StatusDemonstrativoConvenio";
      return Contexto.Database.SqlQuery<R_StatusDemonstrativoConvenio>(query).ToList();
    }
  }
}


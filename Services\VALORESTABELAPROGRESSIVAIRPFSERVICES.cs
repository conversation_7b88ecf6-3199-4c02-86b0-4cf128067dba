﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using PagedList;
using RepasseConvenio.Infrastructure;
using RepasseConvenio.Models;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;

namespace RepasseConvenio.Services
{
  public class ValoresTabelaProgressivaIRPFServices : ServiceBase
  {

    public ValoresTabelaProgressivaIRPFServices(RepasseEntities Contexto)
       : base(Contexto)
    { }
    public ValoresTabelaProgressivaIRPFServices()
       : base()
    { }

    public ValoresTabelaProgressivaIRPFServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }
    public ValoresTabelaProgressivaIRPFServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
          : base(UsuarioLogado, Contexto)
    { }

    public IPagedList<ValoresTabelaProgressivaIRPFModel> Get(int pageNumber, string filtro)
    {
      string query = String.Format(@" SELECT 
	                                    V.V_Id AS Codigo,
	                                    V.V_ValorInicial AS ValorInicial,
	                                    V.V_ValorFinal AS ValorFinal,
	                                    V.V_AliquotaProgressiva AS AliquotaProgressiva
                                    FROM R_ValoresTabelaProgressivaIRPF V
                                    ");

      return Contexto.Database.SqlQuery<ValoresTabelaProgressivaIRPFModel>(query)
             .ToList().OrderBy(a => a.AliquotaProgressiva).ToPagedList(pageNumber, PageSize);
    }

    public void Create(ValoresTabelaProgressivaIRPFModel model)
    {
      ValidaModel(model);
      R_ValoresTabelaProgressivaIRPF entity = model.ToEntityIRCreate();
      Create(entity);
    }

    public List<R_ValoresTabelaProgressivaIRPF> GetByIdTabelaIRPF(int IdTabela)
    {
      return Contexto.R_ValoresTabelaProgressivaIRPF.Where(a => a.V_IdTabelaProgressivaIRPF == IdTabela).ToList();
    }


    public R_ValoresTabelaProgressivaIRPF GetById(int Id)
    {
      return Contexto.R_ValoresTabelaProgressivaIRPF.Where(a => a.V_Id == Id).FirstOrDefault();
    }

    public int GetQtdItensByIdTabelaIRPF(int IdTabela)
    {
      string query = String.Format(@"SELECT 
	                                    COUNT(*)
                                    FROM R_ValoresTabelaProgressivaIRPF
                                    WHERE V_IdTabelaProgressivaIRPF = @IdTabela
                                    ");

      return Contexto.Database.SqlQuery<int>(query, new SqlParameter("@IdTabela", IdTabela)).FirstOrDefault();

    }

    public void Delete(int Id)
    {
      R_ValoresTabelaProgressivaIRPF R_ValoresTabelaProgressivaIRPFf = GetById(Id);

      Contexto.R_ValoresTabelaProgressivaIRPF.Remove(R_ValoresTabelaProgressivaIRPFf);
      Contexto.SaveChanges();
    }

    public void ValidaModel(ValoresTabelaProgressivaIRPFModel model)
    {
      if (model.ValorFinal <= 0)
        throw new CustomException("Campo Valor Final é obrigatório");

      if (model.ValorInicial <= 0)
        throw new CustomException("Campo Valor Inicial é obrigatório");

      if (model.AliquotaProgressiva <= 0)
        throw new CustomException("Campo Aliquota Progressiva é obrigatório");
    }

  }
}
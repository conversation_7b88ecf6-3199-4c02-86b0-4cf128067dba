﻿@using RepasseConvenio.Models
@using RepasseConvenio.Infrastructure.Controls
@model LoteGlosaTransResponGrid

<div class="row" style="width: 100%;">
  <div class="col-md-12">
    <div class="form-group" style="display: flex; flex-direction: column;">
      @Html.LabelFor(m => m.ResponsavelSelect)
      @Html.LibSelect2For(m => m.ResponsavelSelect, new { @class = "form-control", IdPai = "CodigoLoteGlosa" })
    </div>
  </div>
</div>

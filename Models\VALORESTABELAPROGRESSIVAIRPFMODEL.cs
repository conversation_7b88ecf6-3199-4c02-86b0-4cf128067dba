﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace RepasseConvenio.Models
{

  public class ValoresTabelaProgressivaIRPFModel
  {
    //vvv
    public ValoresTabelaProgressivaIRPFModel()
    {
    }
    public ValoresTabelaProgressivaIRPFModel(int codigoTabela)
    {
      CodigoTabela = codigoTabela;
    }

    public int Codigo { get; set; }
    public int CodigoTabela { get; set; }

    [Required]
    [DisplayName("Valor Inicial")]
    public decimal ValorInicial { get; set; }

    [Required]
    [DisplayName("Valor Final")]
    public decimal ValorFinal { get; set; }

    [DisplayName("Valor Inicial")]
    public string ValorInicialText
    {
      get
      {
        return ValorInicial.ToString();
      }
      set
      {
        string valText = string.IsNullOrEmpty(value) ? "0" : value.Replace("R$", "").Replace(" ", "");
        decimal val = 0;
        decimal.TryParse(valText, out val);
        ValorInicial = val;
      }
    }
    [DisplayName("Valor Final")]
    public string ValorFinalText
    {
      get
      {
        return ValorFinal.ToString();
      }
      set
      {
        string valText = string.IsNullOrEmpty(value) ? "0" : value.Replace("R$", "").Replace(" ", "");
        decimal val = 0;
        decimal.TryParse(valText, out val);
        ValorFinal = val;
      }
    }

    [Required]
    [DisplayName("Aliquota Progressiva %")]
    public decimal AliquotaProgressiva { get; set; }

  }
  public static class ValoresTabelaProgressivaIRModelConversion
  {
    public static R_ValoresTabelaProgressivaIRPF ToEntityIREdit(this R_ValoresTabelaProgressivaIRPF entity, ValoresTabelaProgressivaIRPFModel model)
    {
      //entity.V_IdTabelaProgressivaINSS = model.CodigoTabela;
      entity.V_ValorInicial = model.ValorInicial;
      entity.V_ValorFinal = model.ValorFinal;
      entity.V_AliquotaProgressiva = model.AliquotaProgressiva;

      return entity;
    }
    public static R_ValoresTabelaProgressivaIRPF ToEntityIRCreate(this ValoresTabelaProgressivaIRPFModel model)
    {
      R_ValoresTabelaProgressivaIRPF entity = new R_ValoresTabelaProgressivaIRPF();

      entity.V_IdTabelaProgressivaIRPF = model.CodigoTabela;
      entity.V_ValorInicial = model.ValorInicial;
      entity.V_ValorFinal = model.ValorFinal;
      entity.V_AliquotaProgressiva = model.AliquotaProgressiva;

      return entity;
    }
    public static ValoresTabelaProgressivaIRPFModel OffEntityIRToModel(this R_ValoresTabelaProgressivaIRPF entity)
    {
      ValoresTabelaProgressivaIRPFModel model = new ValoresTabelaProgressivaIRPFModel();
      model.Codigo = entity.V_Id;
      model.CodigoTabela = entity.V_IdTabelaProgressivaIRPF;
      model.ValorInicial = entity.V_ValorInicial;
      model.ValorFinal = entity.V_ValorFinal;
      model.AliquotaProgressiva = entity.V_AliquotaProgressiva;

      return model;
    }
  }
}
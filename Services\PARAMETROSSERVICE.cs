﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Models;
using RepasseConvenio.WSPortalCooperado;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.Entity;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Text;
using System.Transactions;
using System.Web;
using System.Data.OleDb;
using RepasseConvenio.Infrastructure;
using Microsoft.Ajax.Utilities;
using System.Security.Cryptography.Xml;

namespace RepasseConvenio.Services
{
  public class ParametrosService : ServiceBase
  {
    public ParametrosService()
   : base()
    { }
    public ParametrosService(RepasseEntities Contexto)
       : base(Contexto)
    { }

    public ParametrosService(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public ParametrosService(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public R_Parametros GetByEnum(ParametrosEnum parametrosEnum)
    {
      string Query = @"SELECT * FROM R_Parametros where P_Enum = @Enum";
      return Contexto.Database.SqlQuery<R_Parametros>(Query, new SqlParameter("@Enum", (int)parametrosEnum)).FirstOrDefault();
    }

  }
}
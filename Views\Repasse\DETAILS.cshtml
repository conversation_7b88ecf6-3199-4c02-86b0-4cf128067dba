﻿@using RepasseConvenio.Infrastructure.Controls
@using RepasseConvenio.Models
@using System.Globalization;
@model R_RepasseModel

@{
  ViewBag.Title = "Detalhes Repasse";
  ViewBag.DescricaoTela = "Repasse";
  ViewBag.ResumoTela = "Detalhes Repasse";
  Layout = "~/Views/Shared/_Layout.cshtml";
}
@Scripts.Render("~/Views/Repasse/Repasse.js?u=u")
@Styles.Render("~/Views/Repasse/Repasse.css?lp=tgb")

@Html.AntiForgeryToken()
@Html.ValidationSummary(true, "", new { @class = "text-danger" })
@Html.HiddenFor(a => a.Codigo)

<div class="row">
  <section class="col-lg-12 connectedSortable ui-sortable">
    <div class="card">
      <div class="card-header ui-sortable-handle">
        <div class="card-title">
          @{
            switch ((EnumStatusRepasse)Model.CodigoStatusRepasse)
            {
              case EnumStatusRepasse.EMCONFERENCIA:
                <div class=" emconferencia">
                </div>
                break;
              case EnumStatusRepasse.ELABORACAO:
                <div class=" emelaboracao">
                </div>
                break;
              case EnumStatusRepasse.CONCLUIDO:
                <div class=" concluido">
                </div>
                break;
              case EnumStatusRepasse.PROCESSADO:
                <div class=" processado">
                </div>
                break;
              case EnumStatusRepasse.PROCESSADOPARCIALMENTE:
                <div class=" parcProcessado">
                </div>
                break;
              case EnumStatusRepasse.ProcessadoGlosado:
                <div class=" glossado">
                </div>
                break;
            }
            @RepasseConvenio.Infrastructure.Helpers.EnumHelper.Description((EnumStatusRepasse)Model.CodigoStatusRepasse)
          }
        </div> 
        <div class="card-tools">
          @if (RepasseConvenio.Infrastructure.Helpers.EnumHelper.Description((EnumStatusRepasse)Model.CodigoStatusRepasse).Equals("Processado")
            || RepasseConvenio.Infrastructure.Helpers.EnumHelper.Description((EnumStatusRepasse)Model.CodigoStatusRepasse).Equals("Processado/Glosado"))
          {
            <button id="btnCancelarProcessamento" data-idrepasse="@Model.Codigo" type="button" class="btn btn-outline-danger" title="Cancelar Processamento">
              Cancelar Processamento
            </button>
          }
          @if (RepasseConvenio.Infrastructure.Helpers.EnumHelper.Description((EnumStatusRepasse)Model.CodigoStatusRepasse).Equals("Processado"))
          {
            <button id="ProcessarGlosa" data-idrepasse="@Model.Codigo" data-toggle="modal" type="button" class="btn btn-outline-secondary btn-newitem" title="Processar Glosa">
              Processar Glosa
            </button>
          }
          <button id="MostrarExtratos" data-idrepasse="@Model.Codigo" data-toggle="modal" type="button" class="btn btn-outline-secondary btn-newitem" title="Extratos">
            Extratos
          </button>
        </div>
      </div><!-- /.card-header -->
      <div class="card-body">
        <div class="tab-content p-0">
          <div class="row">
            <div class="col-md-2">
              <div class="form-group">
                @Html.LabelFor(model => model.Numero)
                @Html.EditorFor(model => model.Numero, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Numero, "", new { @class = "text-danger" })
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-group">
                @Html.LabelFor(model => model.DataInicio)
                @Html.EditorFor(model => model.DataInicio, new { htmlAttributes = new { @class = "form-control airDatePickerDate Data" } })
                @Html.ValidationMessageFor(model => model.DataInicio, "", new { @class = "text-danger" })
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                @Html.LabelFor(m => m.ResponsavelSelect)
                @Html.LibSelect2For(m => m.ResponsavelSelect, new { @class = "form-control" }, new Select2Configuration() { Disabled = true })
              </div>
            </div>
            @if (!Model.Valorizacao && !Model.NF && !Model.Deposito)
            {
              <div class="col-md-4">
                <div class="form-group">
                  @Html.LabelFor(m => m.ConvenioSelect)
                  @Html.LibSelect2For(m => m.ConvenioSelect, new { @class = "form-control" })
                </div>
              </div>
            }
            else
            {
              @Html.HiddenFor(a => a.CodigoConvenio)
              <div class="col-md-4">
                <div class="form-group">
                  @Html.LabelFor(model => model.RazaoSocialConvenio)
                  @Html.EditorFor(model => model.RazaoSocialConvenio, new { htmlAttributes = new { @class = "form-control", @readonly = "readonly" } })
                </div>
              </div>
            }
          </div>
          <div class="row">
            <div class="col-md-2">
              <div class="form-group">
                @Html.CheckBoxFor(a => a.NF, new { disabled = true })
                @Html.LabelFor(a => a.NF)
              </div>
            </div>

            <div class="col-md-2">
              <div class="form-group">
                @Html.CheckBoxFor(a => a.Valorizacao, new { disabled = true })
                @Html.LabelFor(a => a.Valorizacao)
              </div>
            </div>

            <div class="col-md-2">
              <div class="form-group">
                @Html.CheckBoxFor(a => a.Deposito, new { disabled = true })
                @Html.LabelFor(a => a.Deposito)
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="card">
      <div class="card-header ui-sortable-handle">
        <h3 class="card-title">
          Nota Fiscal
        </h3>
        <div class="card-tools">
          <button style="float:right" id="NovaNotaFiscal" data-idrepasse="@Model.Codigo" data-toggle="modal" type="button" class="btn btn-outline-secondary btn-newitem" title="Nova Nota">
            Nova Nota
          </button>
        </div>
      </div><!-- /.card-header -->
      <div class="card-body">
        <div class="tab-content p-0">
          <div class="box-body">
            <div class="row">
              <div class="col-md-12 table-responsive p-0" id="GridNotaFiscalRepasse">
                @Html.Partial("_GridNotaFiscalRepasse", Model.ListNotaFiscalRepasse)
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="card">
      <div class="card-header ui-sortable-handle">
        <h3 class="card-title">
          Demonstrativo
        </h3>
        <div class="card-tools">
          <button id="AtdNaoPg" data-idrepasse="@Model.Codigo" type="button" class="btn btn-outline-secondary" title="Atendimentos Não Pagos">
            Atendimentos Não Pagos
          </button>
          <button id="VisuLotes" data-idrepasse="@Model.Codigo" data-toggle="modal" type="button" class="btn btn-outline-secondary btn-newitem" title="Visualizar Lotes">
            Visualizar Lotes
          </button>
        </div>
      </div>
      <div class="card-body">
        <div class="tab-content p-0">
          <div class="box-body">
            <div class="row">
              <div class="col-md-12 table-responsive p-0" id="GridPlanilhaRecebimento">
                @Html.Partial("_GridDemonstrativoConvenio", Model.ListDemonstrativoConvenioGrid)
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="card-footer" style=" display: flex; justify-content: space-between;">
        <p><b>Total Faturado: </b> @Model.ListDemonstrativoConvenioGrid.Sum(a => a.TotalFaturado).ToString("C2", CultureInfo.CreateSpecificCulture("pt-BR"))</p>
        <p><b>Total Apresentado:</b> @Model.ListDemonstrativoConvenioGrid.Sum(a => a.ValorApresentado).ToString("C2", CultureInfo.CreateSpecificCulture("pt-BR"))</p>
        <p><b>Total Glosado:</b> @Model.ListDemonstrativoConvenioGrid.Sum(a => a.TotalGlosado).ToString("C2", CultureInfo.CreateSpecificCulture("pt-BR"))</p>
        <p><b>Total Pago:</b> @Model.ListDemonstrativoConvenioGrid.Sum(a => a.TotalPago).ToString("C2", CultureInfo.CreateSpecificCulture("pt-BR"))</p>
      </div>
    </div>


    <div class="card">
      <div class="card-header ui-sortable-handle">
        <h3 class="card-title">
          Dados Bancários
        </h3>
        <div class="card-tools">
        </div>
      </div><!-- /.card-header -->
      <div class="card-body">
        <div class="tab-content p-0">
          <div class="row">
            <div class="col-md-3">
              <div class="form-group">
                @Html.LabelFor(m => m.BancoSelect)
                @Html.LibSelect2For(m => m.BancoSelect, new { @class = "form-control" }, new Select2Configuration() { Disabled = true })
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-group">
                @Html.LabelFor(model => model.AgenciaDeposito)
                @Html.EditorFor(model => model.AgenciaDeposito, new { htmlAttributes = new { @class = "form-control numeros" } })
                @Html.ValidationMessageFor(model => model.AgenciaDeposito, "", new { @class = "text-danger" })
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-group">
                @Html.LabelFor(model => model.ContaDeposito)
                @Html.EditorFor(model => model.ContaDeposito, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.ContaDeposito, "", new { @class = "text-danger" })
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                @Html.LabelFor(model => model.NumDeposito)
                @Html.EditorFor(model => model.NumDeposito, new { htmlAttributes = new { @class = "form-control numeros" } })
                @Html.ValidationMessageFor(model => model.NumDeposito, "", new { @class = "text-danger" })
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-group">
                @Html.LabelFor(model => model.DataDeposito)
                @Html.EditorFor(model => model.DataDeposito, new { htmlAttributes = new { @class = "form-control airDatePickerDate Data" } })
                @Html.ValidationMessageFor(model => model.DataDeposito, "", new { @class = "text-danger" })
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-3">
              <div class="form-group">
                @Html.LabelFor(model => model.ValorDeposito)
                @Html.EditorFor(model => model.ValorDeposito, new { htmlAttributes = new { @class = "form-control money" } })
                @Html.ValidationMessageFor(model => model.ValorDeposito, "", new { @class = "text-danger" })
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>

    <div class="card">
      <div class="card-header ui-sortable-handle">
        <h3 class="card-title">
          Valores Repasse
        </h3>
        <div class="card-tools">
        </div>
      </div><!-- /.card-header -->
      <div class="card-body">
        <div class="tab-content p-0">
          <div class="row">
            <div class="col-md-2">
              <div class="form-group">
                @Html.LabelFor(model => model.Valor)
                @Html.EditorFor(model => model.Valor, new { htmlAttributes = new { @class = "form-control money" } })
                @Html.ValidationMessageFor(model => model.Valor, "", new { @class = "text-danger" })
              </div>
            </div>
            <div class="col-md-2">
              <div class="checkbox form-group">
                @Html.LabelFor(model => model.Imposto)
                @Html.EditorFor(model => model.Imposto, new { htmlAttributes = new { @class = "form-control money" } })
                @Html.ValidationMessageFor(model => model.Imposto, "", new { @class = "text-danger" })
              </div>
            </div>
            <div class="col-md-2">
              <div class="checkbox form-group">
                @Html.LabelFor(model => model.TaxaAdministrativa)
                @Html.EditorFor(model => model.TaxaAdministrativa, new { htmlAttributes = new { @class = "form-control money" } })
                @Html.ValidationMessageFor(model => model.TaxaAdministrativa, "", new { @class = "text-danger" })
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>
<div class="box box-primary">
  <div class="box-footer">
    <button type="button" value="Voltar" class="btn btn-secondary" onclick="location.href='@Url.Action("Index", "Repasse")'">Voltar</button>
  </div>
</div>
<br />

<script>
  $(document).ready(function () {
    $("input, select, textarea").attr("readonly", true);
    $("#NomeMedico").removeAttr("readonly");
  });
</script>

@Html.Partial("_ModalNotaFiscal")
@Html.Partial("_ModalProcedimentosDemonstrativo")
@Html.Partial("_ModalGuiasDemonstrativo")
@Html.Partial("_ModalGuiaAtendimento")
@Html.Partial("_ModalLotes")
@Html.Partial("_ModalItensLote")
@Html.Partial("_ModalProcessarLoteGlosa")
@Html.Partial("_ModalAtdNaoPago", new GlosarNaoPago())
@Html.Partial("_ModalExtratos", new ExtratoRepasseModal() { IdRepasseExtrato = Model.Codigo })

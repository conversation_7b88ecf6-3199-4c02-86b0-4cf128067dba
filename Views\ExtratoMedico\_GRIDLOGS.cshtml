﻿@using RepasseConvenio.Models
@model List<LogExtratoMedicoModal>

<table class="table table-sm table-striped table-hover text-nowrap">
  <thead>
    <tr>
      <th>
        Data
      </th>
      <th>
        Executado por
      </th>
      <th>
        Descrição
      </th>
      <th>
      </th>
    </tr>
  </thead>
  <tbody>
    @foreach (LogExtratoMedicoModal item in Model)
    {
    <tr>
      <td>
        @item.DataCriacao.ToString("dd/MM/yyyy HH:mm")
      </td>
      <td>
        @item.Usuario
      </td>
      <td>
        @item.Descricao
      </td>
      <td>
        <button class="customBtnDanger MaisDetalhesLog" data-codigolog="@item.Codigo"><i class="fas fa-info"></i></button>
      </td>
    </tr>
    }
  </tbody>
</table>

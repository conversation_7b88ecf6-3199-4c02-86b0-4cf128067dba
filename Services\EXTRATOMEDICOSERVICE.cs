﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using PagedList;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;
using RepasseConvenio.WSPortalCooperado;
using System.Globalization;

namespace RepasseConvenio.Services
{
  public class ExtratoMedicoServices : ServiceBase
  {
    public ExtratoMedicoServices(RepasseEntities Contexto)
        : base(Contexto)
    { }
    public ExtratoMedicoServices()
       : base()
    { }

    public ExtratoMedicoServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public ExtratoMedicoServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public List<ExtratoMedicoIndex> Get(int id, int page)
    {
      string query = string.Format(@"SELECT 
                                          EX_Id [Codigo]
                                        , EX_DataApuracao [DataApuracao]
                                        , EX_DataPagamento [DataPagamento]
                                        , EX_CodigoRepasse [CodigoRepasse]
                                        , EX_TipoLancamento [TipoLancamento]
                                        , EX_IdClassificacao [CodigoClassificacao]
                                        , EX_Valor [Valor]
                                        , EX_IdConvenio [CodigoConvenio]
                                        , EX_TipoConta [TipoConta]
                                        , EX_CPFCNPJDeposito [CPFCNPJ]
                                        , EX_IdMedico  [CodigoMedico]
                                        , M.M_Nome [NomeMedico]
                                        , C.C_RazaoSocial [RazaoSocial]
                                        , CR.CR_Descricao [DescricaoClassificacao] 
                                       FROM 
                                          R_ExtratoMedico EM
                                       INNER JOIN R_Medico M ON EM.EX_IdMedico = M.M_Id AND EM.EX_IdMedico = @Id
                                       INNER JOIN R_ClassificacaoRepasse CR ON CR.CR_Id = EM.EX_IdClassificacao
                                       LEFT JOIN R_Convenio C ON C.C_Id = EM.EX_IdConvenio
                                       ");
      //OFFSET (@pag - 1) * 20 ROWS
      //FETCH NEXT 20 ROWS ONLY , new SqlParameter("@pag", page)

      return Contexto.Database.SqlQuery<ExtratoMedicoIndex>(query, new SqlParameter("@Id", id)).ToList();
    }

    public List<ExtratoMedicoIndex> Get(ExtratoMedicoFiltroIndex filtro)
    {
      string query = string.Format(@"SELECT 
                                          EX_Id [Codigo]
                                        , EX_DataApuracao [DataApuracao]
                                        , EX_DataPagamento [DataPagamento]
                                        , EX_CodigoRepasse [CodigoRepasse]
                                        , EX_TipoLancamento [TipoLancamento]
                                        , EX_IdClassificacao [CodigoClassificacao]
                                        , EX_Valor [Valor]
                                        , EX_IdConvenio [CodigoConvenio]
                                        , EX_TipoConta [TipoConta]
                                        , EX_CPFCNPJDeposito [CPFCNPJ]
                                        , EX_IdMedico  [CodigoMedico]
                                        , M.M_Nome [NomeMedico]
                                        , C.C_RazaoSocial [RazaoSocial]
                                        , CR.CR_Descricao [DescricaoClassificacao] 
                                       FROM 
                                          R_ExtratoMedico EM
                                       INNER JOIN R_Medico M ON EM.EX_IdMedico = M.M_Id AND EM.EX_IdMedico = @Id
                                       INNER JOIN R_ClassificacaoRepasse CR ON CR.CR_Id = EM.EX_IdClassificacao
                                       LEFT JOIN R_Convenio C ON C.C_Id = EM.EX_IdConvenio
                                       [CONDICAO]
                                       ");


      List<string> condicao = new List<string>();
      List<SqlParameter> listparametrs = new List<SqlParameter>();

      listparametrs.Add(new SqlParameter("@Id", filtro.IdMedico));
      
      if (!string.IsNullOrEmpty(filtro.Data))
      {
        DateTime dt = Convert.ToDateTime(filtro.Data, new CultureInfo("pt-Br"));
        listparametrs.Add(new SqlParameter("@Data", dt));
        condicao.Add("EM.EX_DataPagamento = @Data");
      }

      if (condicao.Count > 0)
        query = query.Replace("[CONDICAO]", String.Format("WHERE {0}", String.Join(" AND ", condicao.ToArray())));
      else
        query = query.Replace("[CONDICAO]", "");

      return Contexto.Database.SqlQuery<ExtratoMedicoIndex>(query, listparametrs.ToArray()).ToList();
    }

    public List<ExtratoMedicoGrid> GetExtratoMedicoGrid(ExtratoRepasseModal extratoRepasseModal)
    {
      string query = string.Format(@"SELECT 
                                          EX_Id [Codigo]
                                        , EX_DataApuracao [DataApuracao]
                                        , EX_DataPagamento [DataPagamento]
                                        , EX_CodigoRepasse [CodigoRepasse]
                                        , EX_TipoLancamento [TipoLancamento]
                                        , EX_IdClassificacao [CodigoClassificacao]
                                        , EX_Valor [Valor]
                                        , EX_IdConvenio [CodigoConvenio]
                                        , EX_TipoConta [TipoConta]
                                        , EX_CPFCNPJDeposito [CPFCNPJ]
                                        , EX_IdMedico  [CodigoMedico]
                                        , M.M_Nome [NomeMedico]
                                        , C.C_RazaoSocial [RazaoSocial]
                                        , CR.CR_Descricao [DescricaoClassificacao] 
                                       FROM R_ExtratoMedico EM
                                       INNER JOIN R_Medico M ON EM.EX_IdMedico = M.M_Id AND EM.EX_CodigoRepasse = @IdRepasse
                                       INNER JOIN R_ClassificacaoRepasse CR ON CR.CR_Id = EM.EX_IdClassificacao
                                       LEFT JOIN R_Convenio C ON C.C_Id = EM.EX_IdConvenio");

      List<string> ListaCondicao = new List<string>();
      List<SqlParameter> ListaParametros = new List<SqlParameter>();
      ListaParametros.Add(new SqlParameter("@IdRepasse", extratoRepasseModal.IdRepasseExtrato));

      if (!string.IsNullOrEmpty(extratoRepasseModal.NomeMedico))
      {
        ListaCondicao.Add("M.M_Nome LIKE @NomeMedico");
        ListaParametros.Add(new SqlParameter("@NomeMedico", '%' + extratoRepasseModal.NomeMedico + '%'));
      }
      if (extratoRepasseModal.CodigoClassificacao.HasValue)
      {
        ListaCondicao.Add("EM.EX_IdClassificacao = @CodigoClassificacao");
        ListaParametros.Add(new SqlParameter("@CodigoClassificacao", extratoRepasseModal.CodigoClassificacao));
      }

      if (ListaCondicao.Count > 1)
        query += string.Format(" WHERE {0}", string.Join(" OR ", ListaCondicao.ToArray()));
      else if (ListaCondicao.Count == 1)
        query += string.Format(" WHERE {0}", ListaCondicao[0]);


      return Contexto.Database.SqlQuery<ExtratoMedicoGrid>(query, ListaParametros.ToArray()).ToList();
    }
    
    public ExtratoMedicoModel GetExtratoMedicoModelById(int Id)
    {
      R_ExtratoMedico medico = Contexto.R_ExtratoMedico.Where(a => a.EX_Id == Id).FirstOrDefault();

      if (medico != null)
        return new ExtratoMedicoModel()
        {
          Codigo = medico.EX_Id,
          CodigoClassificacao = medico.EX_IdClassificacao,
          CodigoMedico = medico.EX_IdMedico,
          CodigoRepasse = medico.EX_CodigoRepasse,
          CodigoConvenio = medico.EX_IdConvenio,
          CPFCNPJ = medico.EX_CPFCNPJDeposito,
          DataApuracao = medico.EX_DataApuracao,
          DataPagamento = medico.EX_DataPagamento,
          TipoConta = medico.EX_TipoConta,
          TipoLancamento = medico.EX_TipoLancamento,
          Valor = medico.EX_Valor,
          Observacao = medico.Observacao
        };

      else
        return null;
    }

    public ExtratoMedicoEdit GetExtratoMedicoEditById(int IdExtrato)
    {
      string query = @"SELECT
                        EM.EX_Id [CodigoExtrato],
                        EM.EX_IdMedico [CodigoMedico],
                        R.R_Numero [NumeroRepasse],
                        EM.EX_DataApuracao [DataApuracao],
                        EM.EX_DataPagamento [DataPagamento],
                        EM.EX_Valor [Valor],
                        EM.EX_IdClassificacao [CodigoClassificacao],
                        EM.EX_TipoLancamento [TipoLancamento],
                        EM.EX_TipoConta [TipoConta],
                        EM.EX_CPFCNPJDeposito [CPFCNPJ],
                        EM.Observacao [Observacoes],
                        CR.CR_Enum [EnumClassificacaoRepasse]
                       FROM R_ExtratoMedico EM
                       INNER JOIN R_ClassificacaoRepasse CR ON CR.CR_Id = EM.EX_IdClassificacao AND EM.EX_Id = @IdExtrato
                       LEFT JOIN R_Repasse R ON R.R_Id = EM.EX_CodigoRepasse	";

      return Contexto.Database.SqlQuery<ExtratoMedicoEdit>(query, new SqlParameter("@IdExtrato", IdExtrato)).FirstOrDefault();
    }

    public R_ExtratoMedico GetEntityById(int IdExtrato)
    {
      string query = @"SELECT
                        *
                       FROM R_ExtratoMedico
                       where EX_Id = @IdExtrato";

      return Contexto.Database.SqlQuery<R_ExtratoMedico>(query, new SqlParameter("@IdExtrato", IdExtrato)).FirstOrDefault();
    }

    public R_ExtratoMedico GetEntityByIdRegistroPagamento(int IdRegistroPagamento)
    {
      string query = @"SELECT
                        *
                       FROM R_ExtratoMedico
                       where EX_IdRegistroPagamento = @IdRegistroPagamento";

      return Contexto.Database.SqlQuery<R_ExtratoMedico>(query, new SqlParameter("@IdRegistroPagamento", IdRegistroPagamento)).FirstOrDefault();
    }

    public R_ExtratoMedico GetEntityByIdRegistroImposto(int IdRegistroImposto)
    {
      string query = @"SELECT
                        *
                       FROM R_ExtratoMedico
                       where EX_IdRegistroImposto = @IdRegistroImposto";

      return Contexto.Database.SqlQuery<R_ExtratoMedico>(query, new SqlParameter("@IdRegistroImposto", IdRegistroImposto)).FirstOrDefault();
    }

    public R_ExtratoMedico GetByClassificacao(int IdMedico, EnumClassificacaoRepasse Classificacao)
    {
      int IdClassificacao = new ClassificacaoRepasseServices(Contexto).GetIdByEnum(Classificacao);

      return Contexto.R_ExtratoMedico.Where(a => a.EX_IdMedico == IdMedico && a.EX_IdClassificacao == IdClassificacao).FirstOrDefault();
    }

    public List<R_ExtratoMedico> GetByTerm(string term)
    {
      if (string.IsNullOrEmpty(term))
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_ExtratoMedico");

        return Contexto.Database.SqlQuery<R_ExtratoMedico>(query).ToList();

      }
      else
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_ExtratoMedico
                                       WHERE R_CPFCNPJDeposito LIKE @termo");

        return Contexto.Database.SqlQuery<R_ExtratoMedico>(query, new SqlParameter("@termo", string.Format("%{0}%", term))).ToList();

      }
    }

    public R_ExtratoMedico GetById(int IdExtrato)
    {
      string query = @"SELECT
                        *
                       FROM R_ExtratoMedico 
                       WHERE EX_Id = @IdExtrato";

      return Contexto.Database.SqlQuery<R_ExtratoMedico>(query, new SqlParameter("@IdExtrato", IdExtrato)).FirstOrDefault();
    }

    public void Create(ExtratoMedicoModel model)
    {
      try
      {
        R_ExtratoMedico medico = model.ExtratoMedicoToCreate(User.IdUsuario);
        using (TransactionScope scope = new TransactionScope(TransactionScopeOption.RequiresNew))
        {
          Create(medico);

          new LogExtratoMedicoServices(User, Contexto).Create(medico.EX_Id, "Extrato Criado");
          scope.Complete();
        }
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }

    public void Edit(ExtratoMedicoEdit model)
    {
      if (model.TipoContaEnum == EnumTipoConta.PJ && string.IsNullOrEmpty(model.CPFCNPJ))
        throw new CustomException("É necessário selecionar uma empresa para conta PJ.");

      R_ExtratoMedico extratoMedico = GetById(model.CodigoExtrato);

      if (extratoMedico.EX_DataPagamento.HasValue)
        throw new CustomException("Extrato pago, não pode ser mais editado.");

      ClassificacaoRepasseServices classificacaoRepasseServices = new ClassificacaoRepasseServices();
      R_ClassificacaoRepasse classificacaoRepasse = classificacaoRepasseServices.GetById(extratoMedico.EX_IdClassificacao);

      if (model.TipoContaEnum == EnumTipoConta.PF)
      {
        MedicoService medicoService = new MedicoService();
        model.CPFCNPJ = medicoService.GetCPFMedico(model.CodigoMedico);
      }

      if (classificacaoRepasse.CR_Enum == (int)EnumClassificacaoRepasse.PROCESSAMENTODEREPASSE)
        extratoMedico.ExtratoMedicoToEditProcessamento(model);
      else
        extratoMedico.ExtratoMedicoToEditNoProcessamento(model);

      using (TransactionScope scope = new TransactionScope(TransactionScopeOption.RequiresNew))
      {
        Contexto.Entry(extratoMedico).State = EntityState.Modified;
        List<LogAux> ListLogAux = GetCamposModificados<R_ExtratoMedico>(Contexto.ChangeTracker.Entries().ToList());
        new LogExtratoMedicoServices(User, Contexto).Create(extratoMedico, ListLogAux, "Extrato Editado");
        Contexto.SaveChanges();
        scope.Complete();
      }
    }

    public void Delete(int IdExtrato)
    {
      R_ExtratoMedico extratoMedico = GetEntityById(IdExtrato);
      string query = string.Empty;

      if (extratoMedico.EX_IdClassificacao == (int)EnumClassificacaoRepasse.PROCESSAMENTODEREPASSE)
      {
        RepasseService repasseService = new RepasseService(User, Contexto);
        repasseService.CancelarProcessamento(Convert.ToInt32(extratoMedico.EX_CodigoRepasse));
      }
      else
      {
        using (var scope = new TransactionScope(TransactionScopeOption.RequiresNew))
        {
          string deleteLog = @"DELETE FROM R_LogExtratoMedico WHERE LEM_IdExtratoMedico = @IdExtrato";
          Contexto.Database.ExecuteSqlCommand(deleteLog, new SqlParameter("@IdExtrato", IdExtrato));
          Delete(extratoMedico);
          scope.Complete();
        }
      }
    }

    public void DeleteAllByIdRepasse(string idRepasse)
    {
      string query = @"delete from R_ExtratoMedico where EX_CodigoRepasse = @idRepasse";
      Contexto.Database.ExecuteSqlCommand(query, new SqlParameter("@idRepasse", idRepasse));

    }

    public bool IfHaveRegistroPagamentoByRepasse(string idCodigoRepasse)
    {
      string query = @"SELECT
                        COUNT(1)
                       FROM R_ExtratoMedico
                       WHERE EX_CodigoRepasse = @idCodigoRepasse AND EX_IdRegistroPagamento IS NOT NULL";

      int quantidade = Contexto.Database.SqlQuery<int>(query, new SqlParameter("@idCodigoRepasse", idCodigoRepasse)).FirstOrDefault();
      if (quantidade == 0)
        return false;
      else
        return true;
    }

    public bool IfHaveExtratoMedicoByRepasse(string idCodigoRepasse)
    {
      string query = @"SELECT
                        COUNT(1)
                       FROM R_ExtratoMedico
                       WHERE EX_CodigoRepasse = @idCodigoRepasse";

      int quantidade = Contexto.Database.SqlQuery<int>(query, new SqlParameter("@idCodigoRepasse", idCodigoRepasse)).FirstOrDefault();
      if (quantidade == 0)
        return false;
      else
        return true;
    }

    public List<ExtratoMedicoRegistroPagamentoModel> GetAllExtratoWihtOutRegistroPagamento()
    {
      string query = @"SELECT
                        EXM.EX_Id [Id],
                        EXM.EX_CPFCNPJDeposito [CPFCNPJ],
                        EXM.EX_Valor [Valor],
                        EXM.EX_IdMedico [IdMedico],
                        ISNULL(EM.EM_AgenciaEmpresa, M.M_AgenciaMedico) [Agencia],
                        ISNULL(EM.EM_BancoEmpresa, M.M_BancoMedico) [Banco],
                        ISNULL(EM.EM_ContaEmpresa, M.M_ContaMedico) [Conta]
                        FROM R_ExtratoMedico EXM
                       LEFT JOIN R_EmpresaMedico EM ON EXM.EX_IdMedico = EM.EM_IdMedico AND EM.EM_CNPJ = EXM.EX_CPFCNPJDeposito
                       LEFT JOIN R_Medico M ON EXM.EX_IdMedico = M.M_Id
                       WHERE EX_IdRegistroPagamento IS NULL		";

      return Contexto.Database.SqlQuery<ExtratoMedicoRegistroPagamentoModel>(query).ToList();
    }

    public void UpdateExtratosComPagamento(List<int> ListaIdExtratos, int IdRegistroPagamento)
    {
      string condicao = string.Join(",", ListaIdExtratos.ToArray());
      string query = string.Format(@"UPDATE R_ExtratoMedico SET EX_IdRegistroPagamento = @IdRegistroPagamento WHERE EX_Id IN ({0})", condicao);
      Contexto.Database.ExecuteSqlCommand(query, new SqlParameter("@IdRegistroPagamento", IdRegistroPagamento));
    }

    public void UpdateExtratosComImposto(List<int> ListaIdExtratos, int IdRegistroImposto)
    {
      string condicao = string.Join(",", ListaIdExtratos.ToArray());
      string query = string.Format(@"UPDATE R_ExtratoMedico SET EX_IdRegistroImposto = @IdRegistroImposto WHERE EX_Id IN ({0})", condicao);
      Contexto.Database.ExecuteSqlCommand(query, new SqlParameter("@IdRegistroImposto", IdRegistroImposto));
    }

    public void RemoveIdRegistro(int idRegistro)
    {
      try
      {
        string query = @"UPDATE R_ExtratoMedico SET EX_IdRegistroPagamento = NULL WHERE EX_IdRegistroPagamento = @Id";
        Contexto.Database.ExecuteSqlCommand(query, new SqlParameter("@Id", idRegistro));
      }
      catch (Exception)
      {
        throw;
      }
    }

    public void RemoveIdRegistroImposto(int idRegistro)
    {
      try
      {
        string query = @"UPDATE R_ExtratoMedico SET EX_IdRegistroImposto = NULL WHERE EX_IdRegistroImposto = @Id";
        Contexto.Database.ExecuteSqlCommand(query, new SqlParameter("@Id", idRegistro));
      }
      catch (Exception)
      {
        throw;
      }
    }

    public void RealizarPagamento(int IdRegistroPagamento, DateTime DataPagamento)
    {
      R_ExtratoMedico extratoMedico = GetEntityByIdRegistroPagamento(IdRegistroPagamento);
      extratoMedico.EX_DataPagamento = DataPagamento;
      Edit(extratoMedico);
    }

    public void RealizarPagamentoImposto(int IdRegistroImposto, DateTime DataPagamento)
    {
      R_ExtratoMedico extratoMedico = GetEntityByIdRegistroImposto(IdRegistroImposto);
      extratoMedico.EX_DataPagamento = DataPagamento;
      Edit(extratoMedico);
    }

    public bool IfExistExtratoDiretorPendenteMes(DateTime Data)
    {
      DateTime DataInicio = new DateTime(Data.Year, Data.Month, 1);
      DateTime DataFim = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.DaysInMonth(Data.Year, Data.Month));

      string query = @"SELECT
                        Count(1)
                       FROM R_ExtratoMedico EM
                       INNER JOIN R_Medico M ON EM.EX_IdMedico = M.M_Id
                       AND M.M_Diretor IS NOT NULL 
                       AND EM.EX_DataPagamento IS NULL
                       AND EX_DataApuracao BETWEEN Convert(date, '2020-10-01 00:00:00.000') AND Convert(date, '2020-10-31 00:00:00.000')";

      int quantidade = Contexto.Database.SqlQuery<int>(query, new SqlParameter("@DataInicio", DataInicio)
                                           , new SqlParameter("@DataFim", DataFim)).FirstOrDefault();

      if (quantidade == 0)
        return false;
      else
        return true;
    }

  }
}


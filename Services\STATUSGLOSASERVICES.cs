﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using PagedList;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RepasseConvenio.Services
{
  public class StatusGlosaServices : ServiceBase
  {
    public StatusGlosaServices(RepasseEntities Contexto)
        : base(Contexto)
    { }
    public StatusGlosaServices()
       : base()
    { }

    public StatusGlosaServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public StatusGlosaServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public int GetIdByEnum(StatusGlosaEnum statusGlosaEnum)
    {
      return Contexto.R_StatusGlosa.Where(a => a.SG_Enum == (int)statusGlosaEnum).Select(a => a.SG_Id).FirstOrDefault();
    }

    public R_StatusGlosa GetByEnum(StatusGlosaEnum statusGlosaEnum)
    {
      return Contexto.R_StatusGlosa.Where(a => a.SG_Enum == (int)statusGlosaEnum).FirstOrDefault();
    }

    public R_StatusGlosa GetById(int Id)
    {
      return Contexto.R_StatusGlosa.Where(a => a.SG_Id == Id).FirstOrDefault();
    }

    public List<R_StatusGlosa> Getall()
    {
      string query = @"select * from R_StatusGlosa";
      return Contexto.Database.SqlQuery<R_StatusGlosa>(query).ToList();
    }

    public List<R_StatusGlosa> GetByTerm(string term)
    {
      if (string.IsNullOrEmpty(term))
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_StatusGlosa");

        return Contexto.Database.SqlQuery<R_StatusGlosa>(query).ToList();

      }
      else
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_StatusGlosa
                                       WHERE SG_Descricao LIKE @termo");

        return Contexto.Database.SqlQuery<R_StatusGlosa>(query, new SqlParameter("@termo", string.Format("%{0}%", term))).ToList();

      }
    }
  }
}


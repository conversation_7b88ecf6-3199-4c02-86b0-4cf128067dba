﻿
@using RepasseConvenio.Models
@using PagedList
@using PagedList.Mvc;
@model IPagedList<R_ClassificacaoRepasse>

@{
  ViewBag.Title = "Lista Convênio";
}

<script src="~/signalr/hubs"></script>
@Scripts.Render("~/Views/ClassificacaoRepasse/ClassificacaoRepasse.js")
@Styles.Render("~/Views/ClassificacaoRepasse/ClassificacaoRepasse.css")
@*@Scripts.Render("~/Views/Convenio/Convenio.css")*@

<div class="row">
  <section class="col-lg-12 connectedSortable ui-sortable">
    <div class="card">
      <div class="card-header ui-sortable-handle card-header-sm">
        <h3 class="card-title">
          <i class="fas fa-pagelines"></i>
          Classificação da Movimentação
        </h3>
        <div class="card-tools">
          <a class="btn btn-block btn-outline-primary" href="@Url.Action("Create")" id="Create"> Novo </a>
        </div>
        <div class="card-tools">
        </div>
      </div><!-- /.card-header -->

      <div class="card-body">
        <div class="card-acoes col-2">
          <a class="btn btn-block btn-outline-primary disabled" id="Editar"> Editar </a>
        </div>


        <div class="tab-content p-0">
          <div class="box-body">
            <div class="row">
              <div class="col-md-12 table-responsive p-0">
                <table id="TableTela" class="table-striped table table-sm table-hover text-nowrap">
                  <thead>
                    <tr>
                      <th>
                        Descrição
                      </th>
                      <th>
                        Tipo de lançamento
                      </th>
                      <th>
                        Base IRRF
                      </th>
                      <th>
                        Base INSS
                      </th>
                      <th>
                        Base PIS/Confins/CSLL
                      </th>
                      <th>
                        Base ISSQN
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    @foreach (var item in Model)
                    {
                      <tr class="CrSelecionavel" data-codigo="@item.CR_Id">
                        <td>
                          <input type="hidden" @item.CR_Id />
                          @($"{item.CR_Enum.ToString().PadLeft(3, '0')}- {item.CR_Descricao}")
                        </td>
                        <td>
                          @{
                            if (!string.IsNullOrEmpty(item.CR_TipoLancamento))
                            {
                              if (item.CR_TipoLancamento.Equals("D"))
                              {
                                <text>Débito</text>
                              }
                              else if (item.CR_TipoLancamento.Equals("C"))
                              {
                                <text>Crédito</text>
                              }
                            }


                          }
                        </td>
                        <td>
                          @{
                            if (item.CR_BaseIRRF == true)
                            {
                              <text>Sim</text>
                            }
                            else
                            {
                              <text>Não</text>
                            }
                          }
                        </td>
                        <td>
                          @{
                            if (item.CR_BaseINSS == true)
                            {
                              <text>Sim</text>
                            }
                            else
                            {
                              <text>Não</text>
                            }
                          }
                        </td>
                        <td>
                          @{
                            if (item.CR_BasePISConfinsCSLL == true)
                            {
                              <text>Sim</text>
                            }
                            else
                            {
                              <text>Não</text>
                            }
                          }
                        </td>
                        <td>
                          @{
                            if (item.CR_BaseISSQN == true)
                            {
                              <text>Sim</text>
                            }
                            else
                            {
                              <text>Não</text>
                            }
                          }
                        </td>
                      </tr>
                    }
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <div class="box-footer with-border" style="padding: 0 10pt;">
            <div class="row">
              <div class="col-md-8 ">
                @Html.PagedListPager(Model, Page => Url.Action("Index", new { Page }))
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>

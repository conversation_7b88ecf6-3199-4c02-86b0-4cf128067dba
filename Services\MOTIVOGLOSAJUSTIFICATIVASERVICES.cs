﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Transactions;
using RepasseConvenio.Infrastructure;
using System.Text.RegularExpressions;
using RepasseConvenio.WSPortalCooperado;
using System.Security.Cryptography.X509Certificates;

namespace RepasseConvenio.Services
{
  public class MotivoGlosaJustificativaServices : ServiceBase
  {
    public MotivoGlosaJustificativaServices()
   : base()
    { }
    public MotivoGlosaJustificativaServices(RepasseEntities Contexto)
       : base(Contexto)
    { }

    public MotivoGlosaJustificativaServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public MotivoGlosaJustificativaServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public void Create(List<int> ListProcGuiaDemonstrativo, int IdLoteGlosa)
    {
      ItensLoteGlosaServices itensLoteGlosaServices = new ItensLoteGlosaServices();
      List<ItensLoteMotivoGlosaJustificativa> ListItensLoteMotivoGlosaJustificativa = itensLoteGlosaServices.GetListItensLoteMotivoGlosaJustificativa(ListProcGuiaDemonstrativo, IdLoteGlosa);

      List<string> Values = new List<string>();
      foreach (var item in ListItensLoteMotivoGlosaJustificativa)
      {
        Values.Add(string.Format("({0},{1})", item.CodigoJustificativa, item.CodigoGlosa));
      }
      string Insert = @"INSERT INTO R_MotivoGlosaJustificativa (MGJ_CodigoJustificativaGlosa, MGJ_CodigoMotivoGlosa)
                        VALUES #valores#";

      Insert = Insert.Replace("#valores#", string.Join(",", Values.ToArray()));

      Contexto.Database.ExecuteSqlCommand(Insert);
    }

  }
}
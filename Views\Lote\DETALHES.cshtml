﻿
@using RepasseConvenio.Models
@using PagedList
@using PagedList.Mvc;
@model List<ItensLoteModel>
@{
  ViewBag.Title = "Itens do lote";
}

<div class="row">
  <section class="col-lg-12 connectedSortable ui-sortable">

    <div class="card">
      <div class="card-header ui-sortable-handle">
        <div class="row" style="justify-content:space-between;">
          <h3 class="card-title">
            <i class="fas fa-book mr-1"></i>
            Detalhes Lote - @ViewBag.NrLote
          </h3>
          <div class="card-tools">
          </div>
        </div>
      </div>

      <div class="card-body">
        <div class="tab-content p-0">
          <div class="box-body">
            <div class="row">
              <div class="col-md-12 table-responsive p-0">
                <table id="TableTela" class="table-striped table table-sm table-hover text-nowrap">
                  <thead>
                    <tr>
                      <th> Status</th>
                      <th> Situação</th>
                      <th> G<PERSON>a </th>
                      <th> Atendimento</th>
                      <th> Total </th>
                      <th> Glosado </th>
                      <th> Faturado </th>
                    </tr>
                  </thead>
                  <tbody>
                    @for (var i = 0; i < Model.ToArray().Length; i++)
                    {
                    <tr>
                      <td>
                        @Model[i].StatusLote
                      </td>
                      <td>
                        @Model[i].SituacaoLote
                      </td>
                      <td>
                        @Model[i].NumeroGuia
                      </td>
                      <td>
                        @Model[i].DataAtendimento.ToString("dd/MM/yyyy")
                      </td>
                      <td>
                        @String.Format("R$ {0:0.00}", @Model[i].ValorTotal)
                      </td>
                      <td>
                        @String.Format("R$ {0:0.00}", @Model[i].ValorGlosado)
                      </td>
                      <td>
                        @String.Format("R$ {0:0.00}", @Model[i].ValorFaturado)
                      </td>
                    </tr>
                    }
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="box-footer with-border" style="padding: 5px 10px;">
        <div class="row">
          <div class="col-md-8 ">
            <button type="button" value="Voltar" class="btn btn-secondary" onclick="window.location.href='@Url.Action("Index","Lote")'">Voltar</button>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>

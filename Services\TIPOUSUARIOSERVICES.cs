﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Models;
using System;
using System.Linq;

namespace RepasseConvenio.Services
{
  public class TipoUsuarioServices : ServiceBase
  {
    public TipoUsuarioServices(RepasseEntities Contexto)
        : base(Contexto)
    { }
    public TipoUsuarioServices()
       : base()
    { }

    public TipoUsuarioServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public TipoUsuarioServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public int GetByIdTipo(TipoUsuario tipo)
    {
      return Contexto.R_TipoUsuario.Where(a => a.TU_Enum == (int)tipo).Select(a => a.TU_Id).FirstOrDefault();
    }

    public string GetById(int Id)
    {
      return Contexto.R_TipoUsuario.Where(a => a.TU_Id == Id).Select(a => a.TU_Descricao).FirstOrDefault();
    }
  }
}

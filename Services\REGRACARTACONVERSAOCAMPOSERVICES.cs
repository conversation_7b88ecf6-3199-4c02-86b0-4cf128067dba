﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using PagedList;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RepasseConvenio.Services
{
  public class RegraCartaConversaoCampoServices : ServiceBase
  {
    public RegraCartaConversaoCampoServices(RepasseEntities Contexto)
        : base(Contexto)
    { }
    public RegraCartaConversaoCampoServices()
       : base()
    { }

    public RegraCartaConversaoCampoServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public RegraCartaConversaoCampoServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public R_RegraCartaConversaoCampo GetById(int? Id)
    {
      return Contexto.R_RegraCartaConversaoCampo.Where(a => a.RCCC_Id == Id).FirstOrDefault();
    }

    public List<R_RegraCartaConversaoCampo> GetByTerm(string term)
    {
      if (string.IsNullOrEmpty(term))
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_RegraCartaConversaoCampo");

        return Contexto.Database.SqlQuery<R_RegraCartaConversaoCampo>(query).ToList();

      }
      else
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_RegraCartaConversaoCampo
                                       WHERE RCCC_Campo LIKE @termo");

        return Contexto.Database.SqlQuery<R_RegraCartaConversaoCampo>(query, new SqlParameter("@termo", string.Format("%{0}%", term))).ToList();

      }
    }

    public List<R_RegraCartaConversaoCampo> GetAll()
    {
      string query = @"SELECT * FROM R_RegraCartaConversaoCampo";

      return Contexto.Database.SqlQuery<R_RegraCartaConversaoCampo>(query).ToList();
    }

    public R_RegraCartaConversaoCampo GetByCampo(string Campo, string Tipo)
    {
      string query = @"SELECT * FROM R_RegraCartaConversaoCampo WHERE RCCC_Campo = @Campo AND RCCC_TipoCampo = @Tipo;";

      return Contexto.Database.SqlQuery<R_RegraCartaConversaoCampo>(query, new SqlParameter("@Campo", Campo), new SqlParameter("@Tipo", Tipo)).FirstOrDefault();
    }

    public string GetTypoCampo(int IdCampo)
    {
      string query = @"SELECT
                        RCCC_TipoCampo
                       FROM R_RegraCartaConversaoCampo
                       WHERE RCCC_Id = @IdCampo";

      string tipoCampo = Contexto.Database.SqlQuery<string>(query, new SqlParameter("@IdCampo", IdCampo)).FirstOrDefault();

      return tipoCampo;
    }
  }
}


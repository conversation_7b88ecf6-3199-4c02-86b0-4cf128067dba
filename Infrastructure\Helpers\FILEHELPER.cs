﻿using iTextSharp.text;
using iTextSharp.text.pdf;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RepasseConvenio.Infrastructure.Helpers
{
  class FileHelper
  {
    public static MemoryStream RetornaMemorySteam(string filePath)
    {
      return new MemoryStream();
    }

    public static byte[] ReadFully(Stream input)
    {
      return new byte[0];
    }
  
    public static byte[] MergePdfs(List<byte[]> pdfs)
    {
      return new byte[0];
    }

  }
}

﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Models;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace RepasseConvenio.Controllers
{
  public class Select2Controller : LibController
  {
    [HttpGet]
    public ActionResult GetUsuarioSelect(string term)
    {
      UsuarioServices UsuarioServices = new UsuarioServices();
      List<Select2Model> Lista = new List<Select2Model>();
      Lista = UsuarioServices.GetByTerm(term).Select(list => list.ToSelect2Model()).ToList();
      return Json(new { items = Lista }, JsonRequestBehavior.AllowGet);
    }
    [HttpGet]
    public ActionResult GetConvenioSelect(string term)
    {
      ConvenioServices convenioServices = new ConvenioServices();
      List<Select2Model> Lista = new List<Select2Model>();
      Lista = convenioServices.GetByTerm(term).Select(list => list.ToSelect2Model()).ToList();
      return Json(new { items = Lista }, JsonRequestBehavior.AllowGet);
    }

    [HttpGet]
    public ActionResult GetStatusRepasseSelect(string term)
    {
      StatusRepasseServices StatusRepasseServices = new StatusRepasseServices();
      List<Select2Model> Lista = new List<Select2Model>();
      Lista = StatusRepasseServices.GetByTerm(term).Select(list => list.ToSelect2Model()).ToList();
      return Json(new { items = Lista }, JsonRequestBehavior.AllowGet);
    }

    [HttpGet]
    public ActionResult GetBancoSelect(string term)
    {
      BancoServices BancoServices = new BancoServices();
      List<Select2Model> Lista = new List<Select2Model>();
      Lista = BancoServices.GetByTerm(term).Select(list => list.ToSelect2Model()).ToList();
      return Json(new { items = Lista }, JsonRequestBehavior.AllowGet);
    }

    [HttpGet]
    public ActionResult GetHospitalSelect(string term)
    {
      HospitalServices HospitalServices = new HospitalServices();
      List<Select2Model> Lista = new List<Select2Model>();
      Lista = HospitalServices.GetByTerm(term).Select(list => list.ToSelect2Model()).ToList();
      return Json(new { items = Lista }, JsonRequestBehavior.AllowGet);
    }

    [HttpGet]
    public ActionResult GetProcedimentoSelect(string term)
    {
      ProcedimentoServices ProcedimentoServices = new ProcedimentoServices();
      List<Select2Model> Lista = new List<Select2Model>();
      Lista = ProcedimentoServices.GetByTerm(term).Select(list => list.ToSelect2Model()).ToList();
      return Json(new { items = Lista }, JsonRequestBehavior.AllowGet);
    }

    [HttpGet]
    public ActionResult GetGrauParticipacaoSelect(string term)
    {
      GrauParticipacaoServices GrauParticipacaoServices = new GrauParticipacaoServices();
      List<Select2Model> Lista = new List<Select2Model>();
      Lista = GrauParticipacaoServices.GetByTerm(term).Select(list => list.ToSelect2Model()).ToList();
      return Json(new { items = Lista }, JsonRequestBehavior.AllowGet);
    }

    [HttpGet]
    public ActionResult GetMedicoSelect(string term)
    {
      MedicoService MedicoService = new MedicoService();
      List<Select2Model> Lista = new List<Select2Model>();
      Lista = MedicoService.GetByTerm(term).Select(list => list.ToSelect2Model()).ToList();
      return Json(new { items = Lista }, JsonRequestBehavior.AllowGet);
    }

    [HttpGet]
    public ActionResult GetTipoRegistroImpostoSelect(string term)
    {
      TipoRegistroImpostoServices TipoRegistroImpostoServices = new TipoRegistroImpostoServices();
      List<Select2Model> Lista = new List<Select2Model>();
      Lista = TipoRegistroImpostoServices.GetByTerm(term).Select(list => list.ToSelect2Model()).ToList();
      return Json(new { items = Lista }, JsonRequestBehavior.AllowGet);
    }

    [HttpGet]
    public ActionResult GetRegraCartaConversaoCampoSelect(string term)
    {
      RegraCartaConversaoCampoServices regraCartaConversaoCampoServices = new RegraCartaConversaoCampoServices();
      List<Select2Model> Lista = new List<Select2Model>();
      Lista = regraCartaConversaoCampoServices.GetByTerm(term).Select(list => list.ToSelect2Model()).ToList();
      return Json(new { items = Lista }, JsonRequestBehavior.AllowGet);
    }

    [HttpGet]
    public ActionResult GetRegraCartaConversaoAcaoSelect(string term, string id)
    {
      if (string.IsNullOrEmpty(id))
        return Json(new { items = new List<Select2Model>() }, JsonRequestBehavior.AllowGet);

      RegraCartaConversaoAcaoServices regraCartaConversaoAcaoServices = new RegraCartaConversaoAcaoServices();
      List<Select2Model> Lista = new List<Select2Model>();
      Lista = regraCartaConversaoAcaoServices.GetByTerm(term, Convert.ToInt32(id)).Select(list => list.ToSelect2Model()).ToList();
      return Json(new { items = Lista }, JsonRequestBehavior.AllowGet);
    }

    [HttpGet]
    public ActionResult GetClassificacaoSelect(string term)
    {
      ClassificacaoRepasseServices ClassificacaoRepasseServices = new ClassificacaoRepasseServices();
      List<Select2Model> Lista = new List<Select2Model>();
      Lista = ClassificacaoRepasseServices.GetByTerm(term).Select(list => list.ToSelect2Model()).ToList();
      return Json(new { items = Lista }, JsonRequestBehavior.AllowGet);
    }

    [HttpGet]
    public ActionResult GetEmpresaMedicoCNPJSelect(string term, string id)
    {
      if (string.IsNullOrEmpty(id))
        return Json(new { items = new List<Select2Model>() }, JsonRequestBehavior.AllowGet);

      EmpresaMedicoService EmpresaMedicoService = new EmpresaMedicoService();
      List<Select2Model> Lista = new List<Select2Model>();
      Lista = EmpresaMedicoService.GetByTerm(term, Convert.ToInt32(id)).Select(list => list.ToSelect2Model()).ToList();
      return Json(new { items = Lista }, JsonRequestBehavior.AllowGet);
    }

    [HttpGet]
    public ActionResult GEtEmpresaMedicoByMedicoSelect(string term, string id)
    {
      if (string.IsNullOrEmpty(id))
        return Json(new { items = new List<Select2Model>() }, JsonRequestBehavior.AllowGet);

      EmpresaMedicoService empresaMedicoService = new EmpresaMedicoService();
      List<Select2Model> Lista = new List<Select2Model>();
      Lista = empresaMedicoService.GetByTermAndIdMedico(term, Convert.ToInt32(id)).Select(list => list.ToSelect2ModelWithId()).ToList();
      return Json(new { items = Lista }, JsonRequestBehavior.AllowGet);
    }

    [HttpGet]
    public ActionResult GetJustificativaSelect(string term)
    {
      JustificativaGlosaService justificativaServices = new JustificativaGlosaService();
      List<Select2Model> Lista = new List<Select2Model>();
      Lista = justificativaServices.GetByTerm(term).Select(list => list.ToSelect2Model()).ToList();
      return Json(new { items = Lista }, JsonRequestBehavior.AllowGet);
    }

    [HttpGet]
    public ActionResult GetRelacaoResponsavelConvenio(string term, string id)
    {
      RelacaoResponsavelConvenioServices services = new RelacaoResponsavelConvenioServices();
      List<Select2Model> Lista = new List<Select2Model>();
      Lista = services.GetByTerm(term, Convert.ToInt32(id)).Select(list => list.ToSelect2Model()).ToList();
      return Json(new { items = Lista }, JsonRequestBehavior.AllowGet);
    }

    [HttpGet]
    public ActionResult GetMotivoGlosaSelect(string term)
    {
      MotivoGlosaService motivoServices = new MotivoGlosaService();
      List<Select2Model> Lista = new List<Select2Model>();
      Lista = motivoServices.GetByTerm(term).Select(list => list.ToSelect2Model()).ToList();
      return Json(new { items = Lista }, JsonRequestBehavior.AllowGet);
    }

    [HttpGet]
    public ActionResult GetStatusGlosaSelect(string term)
    {
      StatusGlosaServices StatusGlosaServices = new StatusGlosaServices();
      List<Select2Model> Lista = new List<Select2Model>();
      Lista = StatusGlosaServices.GetByTerm(term).Select(list => list.ToSelect2Model()).ToList();
      return Json(new { items = Lista }, JsonRequestBehavior.AllowGet);
    }

    [HttpGet]
    public ActionResult GetRepasseSelect(string term)
    {
      RepasseService RepasseService = new RepasseService();
      List<Select2Model> Lista = new List<Select2Model>();
      Lista = RepasseService.GetByTerm(term).Select(list => list.ToSelect2Model()).ToList();
      return Json(new { items = Lista }, JsonRequestBehavior.AllowGet);
    }
  }
}
﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace RepasseConvenio.Controllers
{
  [Authorize]
  [Security("4ADB3520-ACC6-4E8A-9149-C37F09151812")]

  public class RateioGrauParticipacaoController : LibController
  {

    private RateioGrauParticipacaoService RateioGrauParticipacaoService
    {
      get
      {
        if (_RateioGrauParticipacaoService == null)
          _RateioGrauParticipacaoService = new RateioGrauParticipacaoService(ContextoUsuario.UserLogged);

        return _RateioGrauParticipacaoService;
      }
    }
    private RateioGrauParticipacaoService _RateioGrauParticipacaoService;
    [Security("35C06193-29EF-4160-8E91-068B36D28212")]

    public ActionResult Index(int? codRateio)
    {
      try
      {
        if (codRateio == 0 || !codRateio.HasValue)
          throw new Exception("Nenhum médico foi selecionado");

        List<RateioGPIndex> medico = new RateioGrauParticipacaoService().GettoIndex(codRateio.Value);

        ViewBag.CodigoRateio = codRateio;

        return View(medico);
      }
      catch (Exception)
      {
        throw;
      }
    }
    [Security("3859D31B-B601-495C-BC5A-47756243EE57")]

    public ActionResult Create(int? codRateio)
    {
      try
      {
        if (!codRateio.HasValue)
          throw new Exception("Nenhum rateio selecionado");

        RateioGrauParticipacaoModel model = new RateioGrauParticipacaoModel();

        model.CodigoRateio = codRateio.Value;

        return View(model);
      }
      catch (Exception)
      {
        throw;
      }
    }

    [HttpPost]
    [Security("3859D31B-B601-495C-BC5A-47756243EE57")]

    public ActionResult Create(RateioGrauParticipacaoModel model)
    {
      try
      {
        if (ModelState.IsValid)
        {
          if (RateioGrauParticipacaoService.Exist(model.CodigoRateio, int.Parse(model.MedicoSelect.id), int.Parse(model.GrauParticipacaoSelect.id)))
            throw new Exception("Já existe um Rateio Grau Participacao cadastrado.");

          RateioGrauParticipacaoService.Create(model);

          return RedirectToAction("Index", new { codRateio = model.CodigoRateio });
        }
        return View(model);
      }
      catch (Exception)
      {
        throw;
      }
    }
    [Security("5467ADE2-A093-4751-8926-399F91F96197")]

    public ActionResult Edit(int? Cod)
    {
      try
      {
        if (Cod == 0)
          throw new Exception("Nenhum rateio foi selecionado");

        RateioGrauParticipacaoModel model = new RateioGrauParticipacaoService().GetbyId(Cod.Value);

        if (model == null)
          throw new Exception("Rateio não encontrado");

        return View(model);
      }
      catch (Exception)
      {
        throw;
      }
    }

    [HttpPost]
    [Security("5467ADE2-A093-4751-8926-399F91F96197")]

    public ActionResult Edit(RateioGrauParticipacaoModel model)
    {
      try
      {
        if (ModelState.IsValid)
        {
          if (RateioGrauParticipacaoService.Exist(model.CodigoRateio, int.Parse(model.MedicoSelect.id), int.Parse(model.GrauParticipacaoSelect.id)))
            throw new Exception("Já existe um Rateio Grau Participacao cadastrado.");

          RateioGrauParticipacaoService.Edit(model);

          return RedirectToAction("Index", "RateioGrauParticipacao", new { codRateio = model.CodigoRateio });
        }
        return View(model);
      }
      catch (Exception)
      {
        throw;
      }
    }

  }
}
﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using PagedList;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Security.Principal;
using System.Web;
using System.Web.Script.Serialization;

namespace RepasseConvenio.Models
{
  public class RegraCartaConversaoModel
  {
    public RegraCartaConversaoModel()
    {
      ListaRegraCartaConversaoCondicaoModel = new List<RegraCartaConversaoCondicaoModel>();
    }
    public int Codigo { get; set; }

    [DisplayName("Prioridade")]
    public int? Prioridade { get; set; }

    [DisplayName("Descrição")]
    public string Descricao { get; set; }
    public int CodigoMedico { get; set; }

    public int? CodigoEmpresaMedico { get; set; }

    public int CodigoRegraCartaConversaoCampo { get; set; }

    [DisplayName("Selecione o Campo")]
    [URLSelect("Select2/GetRegraCartaConversaoCampoSelect")]
    [PlaceHolderAttr("Selecione o Campo")]
    public Select2Model RegraCartaConversaoCampoSelect
    {
      get
      {
        RegraCartaConversaoCampoServices regraCartaConversaoCampoServices = new RegraCartaConversaoCampoServices();
        return regraCartaConversaoCampoServices.GetById(this.CodigoRegraCartaConversaoCampo).ToSelect2Model();
      }
      set
      {
        if (value != null && !String.IsNullOrEmpty(value.id) && !value.id.Equals("0"))
          this.CodigoRegraCartaConversaoCampo = int.Parse(value.id);
      }
    }

    public int CodigoRegraCartaConversaoAcao { get; set; }

    [DisplayName("Condição")]
    [URLSelect("Select2/GetRegraCartaConversaoAcaoSelect")]
    [PlaceHolderAttr("Selecione a condição")]
    public Select2Model RegraCartaConversaoAcaoSelect
    {
      get
      {
        RegraCartaConversaoAcaoServices regraCartaConversaoAcaoServices = new RegraCartaConversaoAcaoServices();
        return regraCartaConversaoAcaoServices.GetById(this.CodigoRegraCartaConversaoAcao).ToSelect2Model();
      }
      set
      {
        if (value != null && !String.IsNullOrEmpty(value.id) && !value.id.Equals("0"))
          this.CodigoRegraCartaConversaoAcao = int.Parse(value.id);
      }
    }

    [DisplayName("Selecione a Empresa")]
    [URLSelect("Select2/GEtEmpresaMedicoByMedicoSelect")]
    [PlaceHolderAttr("Selecione a Empresa")]
    public Select2Model EmpresaMedicoSelect
    {
      get
      {
        EmpresaMedicoService empresaMedicoService = new EmpresaMedicoService();
        return empresaMedicoService.GetById(this.CodigoEmpresaMedico).ToSelect2ModelWithId();
      }
      set
      {
        if (value != null && !String.IsNullOrEmpty(value.id) && !value.id.Equals("0"))
          this.CodigoEmpresaMedico = int.Parse(value.id);
      }
    }

    [DisplayName("Conteúdo")]
    public string Conteudo { get; set; }

    public List<RegraCartaConversaoCondicaoModel> ListaRegraCartaConversaoCondicaoModel { get; set; }

  }

  public class RegraCartaConversaoCabecalhoIndex
  {
    public int CodigoMedico { get; set; }

    public IPagedList<RegraCartaConversaoIndex> ListaRegraCartaConversaoIndex { get; set; }
  }

  public class RegraCartaConversaoIndex
  {
    public int Codigo { get; set; }

    public string Descricao { get; set; }

    public int? Prioridade { get; set; }

    public string Empresa { get; set; }

    public bool Valida { get; set; }
  }

  public class RegraCartaConversaoCondicaoModel
  {
    public int Sequencia { get; set; }
    public OperadorLogicoEnum operadorLogicoEnum { get; set; }
    public ParentesesEnum parentesesEnum { get; set; }

    public int? CodigoRegraCartaConversaoCampo { get; set; }

    [DisplayName("Selecione o Campo")]
    [URLSelect("Select2/GetRegraCartaConversaoCampoSelect")]
    [PlaceHolderAttr("Selecione o Campo")]
    public Select2Model RegraCartaConversaoCampoSelect
    {
      get
      {
        RegraCartaConversaoCampoServices regraCartaConversaoCampoServices = new RegraCartaConversaoCampoServices();
        return regraCartaConversaoCampoServices.GetById(this.CodigoRegraCartaConversaoCampo).ToSelect2Model();
      }
      set
      {
        if (value != null && !String.IsNullOrEmpty(value.id) && !value.id.Equals("0"))
          this.CodigoRegraCartaConversaoCampo = int.Parse(value.id);
      }
    }

    public int? CodigoRegraCartaConversaoAcao { get; set; }

    [DisplayName("Selecione a Ação")]
    [URLSelect("Select2/GetRegraCartaConversaoAcaoSelect")]
    [PlaceHolderAttr("Selecione a Ação")]
    public Select2Model RegraCartaConversaoAcaoSelect
    {
      get
      {
        RegraCartaConversaoAcaoServices regraCartaConversaoAcaoServices = new RegraCartaConversaoAcaoServices();
        return regraCartaConversaoAcaoServices.GetById(this.CodigoRegraCartaConversaoAcao).ToSelect2Model();
      }
      set
      {
        if (value != null && !String.IsNullOrEmpty(value.id) && !value.id.Equals("0"))
          this.CodigoRegraCartaConversaoAcao = int.Parse(value.id);
      }
    }

    [DisplayName("Conteúdo")]
    public string Conteudo { get; set; }

  }

  public class RegraCarta
  {
    public string RazaoSocialConvenio { get; set; }
    public decimal ValorProcedimento { get; set; }
    public string RazaoSocialHospital { get; set; }
    public string AtendimentoHospital { get; set; }
    public string NroGuiaUnicooper { get; set; }
    public string NomePaciente { get; set; }
    public string NroCarteirinhaPaciente { get; set; }
    public string Acomodacao { get; set; }
    public string DescricaoProcedimento { get; set; }
    public string CodigoProcedimento { get; set; }
    public string Senha { get; set; }
    public string CodigoGuiaPrincipal { get; set; }
    public string SolicitacaoInternacao { get; set; }
    public string NomeMedico { get; set; }
    public string CPFMedico { get; set; }
    public string CRMMedico { get; set; }
  }

  public class RetornoValidacaoRegra
  {
    public TipoPessoaEnum tipoPessoaEnum { get; set; }
    public string CPFCNPJ { get; set; }
  }

  public static class RegraCartaConversaoModelConversions
  {
    public static R_RegraCartaConversao RegraCartaConversaoCreateModel(this RegraCartaConversaoModel regraCartaConversaoModel)
    {
      return new R_RegraCartaConversao()
      {
        RCC_Valida = false,
        RCC_Html = "",
        RCC_QuerySql = "",
        RCC_IdMedico = regraCartaConversaoModel.CodigoMedico
      };
    }

    public static RegraCartaConversaoModel EntityToModel(this R_RegraCartaConversao regraCartaConversao)
    {
      RegraCartaConversaoModel regraCartaConversaoModel = new RegraCartaConversaoModel();
      regraCartaConversaoModel.Codigo = regraCartaConversao.RCC_Id;
      regraCartaConversaoModel.CodigoMedico = regraCartaConversao.RCC_IdMedico;
      regraCartaConversaoModel.Descricao = regraCartaConversao.RCC_Descricao;
      regraCartaConversaoModel.Prioridade = regraCartaConversao.RCC_Prioridade;
      regraCartaConversaoModel.CodigoEmpresaMedico = regraCartaConversao.RCC_IdEmpresaMedico;
      if (!String.IsNullOrEmpty(regraCartaConversao.RCC_Html))
      {
        JavaScriptSerializer javaScriptSerializer = new JavaScriptSerializer();
        regraCartaConversaoModel.ListaRegraCartaConversaoCondicaoModel = javaScriptSerializer.Deserialize<List<RegraCartaConversaoCondicaoModel>>(regraCartaConversao.RCC_Html).OrderBy(a => a.Sequencia).ToList();
      }

      return regraCartaConversaoModel;
    }

    public static void EntityToCondicaoEdit(this R_RegraCartaConversao regraCartaConversao, List<RegraCartaConversaoCondicaoModel> RegraCartaConversaoCondicaoModel)
    {
      JavaScriptSerializer javaScriptSerializer = new JavaScriptSerializer();
      regraCartaConversao.RCC_Html = javaScriptSerializer.Serialize(RegraCartaConversaoCondicaoModel);
    }

    public static RetornoValidacaoRegra RetornoValidacaoRegraCreate(this RetornoValidacaoRegra retornoValidacaoRegra, string CPFCNPJ, TipoPessoaEnum tipoPessoaEnum)
    {
      return new RetornoValidacaoRegra()
      {
        CPFCNPJ = CPFCNPJ,
        tipoPessoaEnum = tipoPessoaEnum
      };
    }

    public static void ModelToEntitySalvar(this R_RegraCartaConversao regraCartaConversao, RegraCartaConversaoModel regraCartaConversaoModel)
    {
      regraCartaConversao.RCC_Descricao = regraCartaConversaoModel.Descricao;
      regraCartaConversao.RCC_Prioridade = regraCartaConversaoModel.Prioridade;
      regraCartaConversao.RCC_IdEmpresaMedico = regraCartaConversaoModel.CodigoEmpresaMedico;
    }
  }
}
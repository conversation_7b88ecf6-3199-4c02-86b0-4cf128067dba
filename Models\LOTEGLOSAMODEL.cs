﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Web;
using System.Xml.Serialization;

namespace RepasseConvenio.Models
{
  public class ProcessaGlosaRepasse
  {
    public ProcessaGlosaRepasse()
    {
      DataVencimentoLoteGlosa = DateTime.Now;
    }
    public DateTime DataVencimentoLoteGlosa { get; set; }
  }

  public class LoteGlosaFiltroModel
  {
    public List<LoteGlosaIndex> ListLoteGlosa { get; set; }

    public int? CodigoStatusGlosa { get; set; }

    public int? CodigoConvenio { get; set; }

    [DisplayName("Status Glosa")]
    [URLSelect("Select2/GetStatusGlosaSelect")]
    [PlaceHolderAttr("Selecione o Status Glosa")]
    public Select2Model StatusGlosaSelect
    {
      get
      {
        StatusGlosaServices StatusGlosaService = new StatusGlosaServices();
        return StatusGlosaService.GetById(this.CodigoStatusGlosa.GetValueOrDefault()).ToSelect2Model();
      }
      set
      {
        if (value != null && !string.IsNullOrEmpty(value.id) && !value.id.Equals("0"))
          this.CodigoStatusGlosa = int.Parse(value.id);
      }
    }

    [DisplayName("Convênio")]
    [URLSelect("Select2/GetConvenioSelect")]
    [PlaceHolderAttr("Selecione o Convênio")]
    public Select2Model ConvenioSelect
    {
      get
      {
        ConvenioServices ConvenioServices = new ConvenioServices();
        return ConvenioServices.GetById(this.CodigoConvenio.GetValueOrDefault()).ToSelect2Model();
      }
      set
      {
        if (value != null && !string.IsNullOrEmpty(value.id) && !value.id.Equals("0"))
          this.CodigoConvenio = int.Parse(value.id);
      }
    }
  }

  public class LoteGlosaIndex
  {
    public int CodigoGlosa { get; set; }
    public string Codigo { get; set; }
    public DateTime DataInicio { get; set; }
    public DateTime DataCriacao { get; set; }
    public DateTime DataVencimentoAnalise { get; set; }
    public DateTime? DataReapresentacao { get; set; }
    public string RazaoSocialConvenio { get; set; }
    public string DescricaoStatus { get; set; }
    public string UsuarioCriacao { get; set; }

    public StatusGlosaEnum StatusGlosaEnum { get; set; }
  }

  public class LoteGlosaProcessaCabecalho
  {
    public int IdConvenio { get; set; }
    public int IdStatusItensLote { get; set; }
    public StatusItensLoteEnum statusItensLoteEnum { get; set; }
    public int? VencimentoGlosa { get; set; }
    public List<LoteGlosaProcessa> ListaLoteGlosaProcessa { get; set; }
    public int IdLote { get; set; }
    public string NumeroLote { get; set; }
  }

  public class AtendimentoNaoPago
  {
    public int Codigo { get; set; }
    public string NroLote { get; set; }
    public string NroGuiaAtendimento { get; set; }
    public string StatusGuia { get; set; }
    public decimal ValorFaturado { get; set; }
  }

  public class GlosarNaoPago
  {
    public GlosarNaoPago()
    {
      this.DataInicioGlosa = DateTime.Now;
    }

    [DisplayName("Data Início")]
    public DateTime DataInicioGlosa { get; set; }
    public int IdMotivo { get; set; }
    [DisplayName("Motivo da Glosa")]
    [URLSelect("Select2/GetMotivoGlosaSelect")]
    [PlaceHolderAttr("Selecione o Motivo")]
    public Select2Model MotivoSelect
    {
      get
      {
        MotivoGlosaService motivoService = new MotivoGlosaService();
        return motivoService.GetById(this.IdMotivo).ToSelect2Model();
      }
      set
      {
        if (value != null && !String.IsNullOrEmpty(value.id) && !value.id.Equals("0"))
          this.IdMotivo = int.Parse(value.id);
      }
    }
  }

  public class ConvenioVencimento
  {
    public int CodigoConvenio { get; set; }
    public int CodigoItemLote { get; set; }
    public string Nome { get; set; }
    public int? DiasVencimento { get; set; }
    public int IdLote { get; set; }
    public string NumeroLote { get; set; }
  }

  public class LoteGlosaProcessa
  {
    public int IdItensLote { get; set; }
    public int IdConvenio { get; set; }
    public int IdStatusItensLote { get; set; }
    public int? IdGuiaDemonstrativo { get; set; }
    public int IdGuiaAtendimento { get; set; }
    public StatusItensLoteEnum EnumStatusItensLote { get; set; }
    public int? VencimentoGlosa { get; set; }
    public int IdLote { get; set; }
    public string NumeroLote { get; set; }
  }

  public class UltimoRegistro
  {
    public string CodigoWithOutPrefix { get; set; }
    public string CodigoCompleto { get; set; }
  }

  public class LoteGlosaToRemoveCabecalho
  {
    public int IdLote { get; set; }
    public int IdStatus { get; set; }
    public StatusGlosaEnum statusGlosaEnum { get; set; }
    public List<LoteGlosaToRemove> ListLoteGlosaToRemove { get; set; }
  }

  public class LoteGlosaToRemove
  {
    public int IdLote { get; set; }
    public int IdStatus { get; set; }
    public int IdGuiaAtendimento { get; set; }
    public StatusGlosaEnum statusGlosaEnum { get; set; }
  }

  public class LoteGlosaTransResponGrid
  {
    public int? CodigoResponsavel { get; set; }

    [DisplayName("Selecione o Responsável")]
    [URLSelect("Select2/GetRelacaoResponsavelConvenio")]
    [PlaceHolderAttr("Selecione o Responsável")]
    public Select2Model ResponsavelSelect
    {
      get
      {
        ResponsavelConvenioServices responsavelConvenioServices = new ResponsavelConvenioServices();
        return responsavelConvenioServices.GetById(this.CodigoResponsavel).ToSelect2Model();
      }
      set
      {
        if (value != null && !String.IsNullOrEmpty(value.id) && !value.id.Equals("0"))
          this.CodigoResponsavel = int.Parse(value.id);
      }
    }
  }

  public class LogLoteGlosa
  {
    public int Codigo { get; set; }
    public string Log { get; set; }
    public EnumTipoLogLoteGlosa TipoLog { get; set; }
    public DateTime Data { get; set; }
    public int CodigoLoteGlosa { get; set; }
    public string NomeUsuario { get; set; }
  }

  public class XMLLoteGlosa
  {
    public string NumeroCarteirinha { get; set; }

    public string NumeroAtendimento { get; set; }
    public string CodigoProcedimento { get; set; }
    public string DescricaoProcedimento { get; set; }
    public DateTime DataAtendimento { get; set; }

    public decimal TotalApresentado { get; set; }

    public decimal TotalGlosado { get; set; }

    public decimal TotalPago { get; set; }

    public string ObservacoesGerais { get; set; }

    public string CodigoGlosa { get; set; }

    public string DescricaoGlosa { get; set; }

    public string CodigoJustificativa { get; set; }

    public string DescricaoJustificativa { get; set; }

    [XmlElement(IsNullable = true)]
    public string Comentario { get; set; }

  }

  public class RelatorioLoteGlosa
  {
    public string NumeroCarteirinha { get; set; }
    public string NumeroAtendimento { get; set; }
    public string CodigoProcedimento { get; set; }
    public string DescricaoProcedimento { get; set; }
    public DateTime DataAtendimento { get; set; }
    public decimal TotalApresentado { get; set; }
    public decimal TotalGlosado { get; set; }
    public decimal TotalPago { get; set; }
    public string ObservacoesGerais { get; set; }
    public string CodigoGlosa { get; set; }
    public string DescricaoGlosa { get; set; }
    public string CodigoJustificativa { get; set; }
    public string DescricaoJustificativa { get; set; }
    public string Comentario { get; set; }
  }

  public class LotesGlosaVencendoCabecalho
  {
    public string EmailResponsavel { get; set; }

    public List<LotesGlosaVencendo> ListaLotesGlosaVencendo { get; set; }
  }

  public class LotesGlosaVencendo
  {
    public int Codigo { get; set; }
    public string CodigoLote { get; set; }
    public string StatusDescricao { get; set; }
    public string StatusTipoGlosa { get; set; }
    public DateTime DataInicio { get; set; }
    public DateTime DataVencimento { get; set; }
    public string Convenio { get; set; }
    public string EmailResponsavel { get; set; }
  }

  public static class LoteGlosaConversions
  {
    public static R_LoteGlosa ModelToEntityCreate(this LoteGlosaProcessaCabecalho loteGlosaProcessaCabecalho
                                                , int IdStatusGlosa
                                                , int IdTipoLoteGlosa
                                                , DateTime DataVencimentoAnalise
                                                , string Codigo
                                                , int IdRepasse
                                                , int IdLote
                                                , int IdUsuario
                                                , DateTime DataInicio)
    {
      return new R_LoteGlosa()
      {
        LG_IdRepasse = IdRepasse,
        LG_Codigo = Codigo,
        LG_IdConvenio = loteGlosaProcessaCabecalho.IdConvenio,
        LG_DataCriacao = DateTime.Now,
        LG_IdStatusGlosa = IdStatusGlosa,
        LG_IdTipoLoteGlosa = IdTipoLoteGlosa,
        LG_DataVencimentoAnalise = DataVencimentoAnalise,
        LG_IdLote = IdLote,
        LG_IdUsuarioCriacao = IdUsuario,
        LG_DataInicio = DataInicio
      };
    }
  }
}
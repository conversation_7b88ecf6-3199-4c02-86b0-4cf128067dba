﻿.Selected {
  transform: scale(1.007);
  background-color: #d6101085 !important;
  transition: all 100ms;
  /*border: 2px solid red;*/
}

.UnSelected {
  transform: scale(1);
  /*background-color: #17a2b8 !important;*/
  transition: all 100ms;
}

.card-tools > button {
  margin: 0 2pt !important;
}

.card-tools > a {
  margin: 0 2pt !important;
}

.card-acoes {
  display: flex;
  
  align-items: flex-end;
  justify-content: flex-end;
  margin: 5px 0;
}

.card-acoes > a {
  margin: 0 2pt;
}
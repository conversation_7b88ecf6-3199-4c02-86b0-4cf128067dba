﻿@using RepasseConvenio.Models
@model List<ExtratoMedicoIndex>

<style>
  tr.noBorder td {
    border: 0 !important;
  }
</style>
@{
  decimal total = 0;
  if (Model.Count > 0)
  {
    total = Model.Sum(a => a.<PERSON>);
  }
}
<table id="TableTela" class="table-striped table table-sm table-hover text-nowrap">
  <thead>
    <tr class="noBorder">
      <th colspan="6" style="text-align: end;padding-right: 10px;border-top: 0 !important;">
        Total: @if (total == 0)
        {<text>0,00</text> }
      else
      { <text>@total</text>}
      </th>
    </tr>
    <tr>
      <th>
        CPF/CNPJ
      </th>
      <th>
        Data Apuração
      </th>
      <th>
        Classificação
      </th>
      <th>
        Tipo Lançamento
      </th>
      <th>
        Valor
      </th>
      <th>
        Data Pagamento
      </th>
    </tr>
  </thead>
  <tbody>
    @foreach (ExtratoMedicoIndex item in Model)
    {
      <tr class="TrSelecionavel" data-codigoextrato="@item.Codigo" data-codigomedico="@item.CodigoMedico" data-codigoclassificacao="@item.CodigoClassificacao">
        <td class="cnpj">
          @item.CPFCNPJ
        </td>
        <td>
          @item.DataApuracao.ToString("dd/MM/yyyy")
        </td>
        <td>
          @item.DescricaoClassificacao
        </td>
        <td>
          @item.TipoLancamento
        </td>
        <td>
          R$ @item.Valor
        </td>
        <td>
          @if (item.DataPagamento.HasValue)
          {
            <text>
              @item.DataPagamento.Value.ToString("dd/MM/yyyy")
            </text>
          }
          else
          {
            <text>

            </text>
          }
        </td>
      </tr>
    }
  </tbody>
</table>

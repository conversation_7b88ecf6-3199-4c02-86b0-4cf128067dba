﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Web;

namespace RepasseConvenio.Models
{
  public class RateioMedicoModel
  {
    public int Codigo { get; set; }
    public string CodigoProcedimento { get; set; }
    public string DescricaoProcedimento { get; set; }
    public int CodigoHospital { get; set; }
    [DisplayName("Médico")]
    public int CodigoMedico { get; set; }

    [DisplayName("Selecione o Hospital")]
    [URLSelect("Select2/GetHospitalSelect")]
    [PlaceHolderAttr("Selecione o Hospital")]
    public Select2Model HospitalSelect
    {
      get
      {
        HospitalServices HospitalServices = new HospitalServices();
        return HospitalServices.GetById(this.CodigoHospital).ToSelect2Model();
      }
      set
      {
        if (value != null && !String.IsNullOrEmpty(value.id) && !value.id.Equals("0"))
          this.CodigoHospital = int.Parse(value.id);
      }
    }

    [DisplayName("Selecione o Procedimento")]
    [URLSelect("Select2/GetProcedimentoSelect")]
    [PlaceHolderAttr("Selecione o Procedimento")]
    public Select2Model ProcedimentoSelect
    {
      get
      {
        ProcedimentoServices ProcedimentoServices = new ProcedimentoServices();
        return ProcedimentoServices.GetById(this.CodigoProcedimento).ToSelect2Model();
      }
      set
      {
        if (value != null && !String.IsNullOrEmpty(value.id) && !value.id.Equals("0"))
        {
          this.CodigoProcedimento = value.id;
        }
      }
    }
  }

  public class RateioMedicoIndex
  {
    public int Codigo { get; set; }
    public string CodigoProcedimento { get; set; }
    public string DescricaoProcedimento { get; set; }
    public string NomeHospital { get; set; }
    public string CNPJHospital { get; set; }
    public int CodigoMedico { get; set; }
  }

  public static class RateioMedicoModelConversion
  {

    public static R_RateioMedico toRateioMedicoCreate(this RateioMedicoModel model)
    {
      R_RateioMedico entity = new R_RateioMedico();

      entity.RM_IdMedico = model.CodigoMedico;
      entity.RM_CodigoProcedimento = model.CodigoProcedimento;
      entity.RM_DescricaoProcedimento = model.DescricaoProcedimento;
      entity.RM_IdHospital = model.CodigoHospital;

      return entity;
    }
    public static R_RateioMedico toRateioMedicoEdit(this RateioMedicoModel model)
    {
      R_RateioMedico entity = new R_RateioMedico();
      entity.RM_Id = model.Codigo;
      entity.RM_IdMedico = model.CodigoMedico;
      entity.RM_CodigoProcedimento = model.CodigoProcedimento;
      entity.RM_DescricaoProcedimento = model.DescricaoProcedimento;
      entity.RM_IdHospital = model.CodigoHospital;

      return entity;
    }
  }
}
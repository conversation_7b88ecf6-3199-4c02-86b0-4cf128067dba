﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Web;

namespace RepasseConvenio.Models
{
  public class NotaFiscalRepasseModel
  {
    public NotaFiscalRepasseModel()
    {
      ListGuiaNotaFiscalEdit = new List<GuiaNotaFiscalEdit>();
    }
    public int NFM_Codigo { get; set; }
    [DisplayName("Número")]
    public string NFM_Numero { get; set; }
    [DisplayName("Serie")]
    public string NFM_Serie { get; set; }
    [DisplayName("Data de Emissão")]
    public DateTime NFM_DataEmissao { get; set; }
    [DisplayName("Valor Total")]
    public string NFM_ValorTotalText
    {
      get
      {
        return NFM_ValorTotal.ToString();
      }
      set
      {
        string valText = string.IsNullOrEmpty(value) ? "0" : value.Replace("R$", "").Replace(" ", "");
        decimal val = 0;
        decimal.TryParse(valText, out val);
        NFM_ValorTotal = val;
      }
    }
    [DisplayName("Valor Liquido")]
    public string NFM_ValorLiquidoText
    {
      get
      {
        return NFM_ValorLiquido.ToString();
      }
      set
      {
        string valText = string.IsNullOrEmpty(value) ? "0" : value.Replace("R$", "").Replace(" ", "");
        decimal val = 0;
        decimal.TryParse(valText, out val);
        NFM_ValorLiquido = val;
      }
    }
    [DisplayName("Impostos")]
    public string NFM_ImpostosText
    {
      get
      {
        return NFM_Impostos.ToString();
      }
      set
      {
        string valText = string.IsNullOrEmpty(value) ? "0" : value.Replace("R$", "").Replace(" ", "");
        decimal val = 0;
        decimal.TryParse(valText, out val);
        NFM_Impostos = val;
      }
    }
    public decimal NFM_ValorTotal { get; set; }
    public decimal NFM_ValorLiquido { get; set; }
    public decimal NFM_Impostos { get; set; }
    public decimal IIS { get; set; }
    [DisplayName("IIS")]
    public string IISText
    {
      get
      {
        return IIS.ToString();
      }
      set
      {
        string valText = string.IsNullOrEmpty(value) ? "0" : value.Replace("R$", "").Replace(" ", "");
        decimal val = 0;
        decimal.TryParse(valText, out val);
        IIS = val;
      }
    }
    public decimal PIS { get; set; }
    [DisplayName("PIS")]
    public string PISText
    {
      get
      {
        return PIS.ToString();
      }
      set
      {
        string valText = string.IsNullOrEmpty(value) ? "0" : value.Replace("R$", "").Replace(" ", "");
        decimal val = 0;
        decimal.TryParse(valText, out val);
        PIS = val;
      }
    }
    public decimal COFINS { get; set; }
    [DisplayName("COFINS")]
    public string COFINSText
    {
      get
      {
        return COFINS.ToString();
      }
      set
      {
        string valText = string.IsNullOrEmpty(value) ? "0" : value.Replace("R$", "").Replace(" ", "");
        decimal val = 0;
        decimal.TryParse(valText, out val);
        COFINS = val;
      }
    }
    public decimal CSLL { get; set; }
    [DisplayName("CSLL")]
    public string CSLLText
    {
      get
      {
        return CSLL.ToString();
      }
      set
      {
        string valText = string.IsNullOrEmpty(value) ? "0" : value.Replace("R$", "").Replace(" ", "");
        decimal val = 0;
        decimal.TryParse(valText, out val);
        CSLL = val;
      }
    }

    [DisplayName("Data")]
    public DateTime NFM_DataCriacao { get; set; }
    public int NFM_CodigoUsuarioCriacao { get; set; }
    public int NFM_CodigoRepasse { get; set; }
    public List<GuiaNotaFiscalEdit> ListGuiaNotaFiscalEdit { get; set; }

  }

  public class NotaFiscalRepasseEdit
  {
    public int NFM_Codigo { get; set; }
    [DisplayName("Número")]
    public string NFM_Numero { get; set; }
    [DisplayName("Serie")]
    public string NFM_Serie { get; set; }
    [DisplayName("Data de Emissão")]
    public DateTime NFM_DataEmissao { get; set; }
    [DisplayName("Valor Total")]
    public string NFM_ValorTotalText
    {
      get
      {
        return NFM_ValorTotal.ToString();
      }
      set
      {
        string valText = string.IsNullOrEmpty(value) ? "0" : value.Replace("R$", "").Replace(" ", "");
        decimal val = 0;
        decimal.TryParse(valText, out val);
        NFM_ValorTotal = val;
      }
    }
    [DisplayName("Valor Liquido")]
    public string NFM_ValorLiquidoText
    {
      get
      {
        return NFM_ValorLiquido.ToString();
      }
      set
      {
        string valText = string.IsNullOrEmpty(value) ? "0" : value.Replace("R$", "").Replace(" ", "");
        decimal val = 0;
        decimal.TryParse(valText, out val);
        NFM_ValorLiquido = val;
      }
    }
    [DisplayName("Impostos")]
    public string NFM_ImpostosText
    {
      get
      {
        return NFM_Impostos.ToString();
      }
      set
      {
        string valText = string.IsNullOrEmpty(value) ? "0" : value.Replace("R$", "").Replace(" ", "");
        decimal val = 0;
        decimal.TryParse(valText, out val);
        NFM_Impostos = val;
      }
    }
    public decimal NFM_ValorTotal { get; set; }
    public decimal NFM_ValorLiquido { get; set; }
    public decimal NFM_Impostos { get; set; }
    public decimal IIS { get; set; }
    [DisplayName("IIS")]
    public string IISText
    {
      get
      {
        return IIS.ToString();
      }
      set
      {
        string valText = string.IsNullOrEmpty(value) ? "0" : value.Replace("R$", "").Replace(" ", "");
        decimal val = 0;
        decimal.TryParse(valText, out val);
        IIS = val;
      }
    }
    public decimal PIS { get; set; }
    [DisplayName("PIS")]
    public string PISText
    {
      get
      {
        return PIS.ToString();
      }
      set
      {
        string valText = string.IsNullOrEmpty(value) ? "0" : value.Replace("R$", "").Replace(" ", "");
        decimal val = 0;
        decimal.TryParse(valText, out val);
        PIS = val;
      }
    }
    public decimal COFINS { get; set; }
    [DisplayName("COFINS")]
    public string COFINSText
    {
      get
      {
        return COFINS.ToString();
      }
      set
      {
        string valText = string.IsNullOrEmpty(value) ? "0" : value.Replace("R$", "").Replace(" ", "");
        decimal val = 0;
        decimal.TryParse(valText, out val);
        COFINS = val;
      }
    }
    public decimal CSLL { get; set; }
    [DisplayName("CSLL")]
    public string CSLLText
    {
      get
      {
        return CSLL.ToString();
      }
      set
      {
        string valText = string.IsNullOrEmpty(value) ? "0" : value.Replace("R$", "").Replace(" ", "");
        decimal val = 0;
        decimal.TryParse(valText, out val);
        CSLL = val;
      }
    }

    [DisplayName("Data")]
    public DateTime NFM_DataCriacao { get; set; }
    public int NFM_CodigoUsuarioCriacao { get; set; }
    public int NFM_CodigoRepasse { get; set; }

    public List<GuiaNotaFiscalEdit> ListGuiaNotaFiscalEdit { get; set; }
  }

  public static class NotaFiscaRepasseModelConversions
  {
    public static NotaFiscalRepasseModel ToIntegraByPlanilha(this NotaFiscalRepasseModel model, int IdRepasse)
    {
      NotaFiscalRepasseModel notaFiscalRepasseModel = new NotaFiscalRepasseModel();
      notaFiscalRepasseModel.NFM_Numero = "0";
      notaFiscalRepasseModel.NFM_Serie = "0";
      notaFiscalRepasseModel.NFM_DataEmissao = DateTime.Now;
      notaFiscalRepasseModel.NFM_ValorTotal = 0;
      notaFiscalRepasseModel.NFM_ValorLiquido = 0;
      notaFiscalRepasseModel.NFM_Impostos = 0;
      notaFiscalRepasseModel.NFM_CodigoRepasse = IdRepasse;
      notaFiscalRepasseModel.IIS = 0;
      notaFiscalRepasseModel.PIS = 0;
      notaFiscalRepasseModel.COFINS = 0;
      notaFiscalRepasseModel.CSLL = 0;
      return notaFiscalRepasseModel;
    }

    public static R_NotaFiscaRepasse ToNotaFiscaRepasseCreate(this NotaFiscalRepasseModel model, int IdUsuario)
    {
      R_NotaFiscaRepasse entity = new R_NotaFiscaRepasse();
      //entity.NF_Id = model.Codigo;
      entity.NF_Numero = model.NFM_Numero;
      entity.NF_Serie = model.NFM_Serie;
      entity.NF_DataEmissao = model.NFM_DataEmissao;
      entity.NF_ValorTotal = model.NFM_ValorTotal;
      entity.NF_ValorLiquido = model.NFM_ValorLiquido;
      entity.NF_Impostos = model.IIS + model.PIS + model.COFINS + model.CSLL;
      entity.NF_DataCriacao = DateTime.Now;
      entity.NF_IdUsuarioCriacao = IdUsuario;
      entity.NF_IdRepasse = model.NFM_CodigoRepasse;
      entity.NF_IIS = model.IIS;
      entity.NF_PIS = model.PIS;
      entity.NF_COFINS = model.COFINS;
      entity.NF_CSLL = model.CSLL;
      return entity;
    }

    public static bool ToCadastroValido(this NotaFiscalRepasseModel model)
    {

      //if (string.IsNullOrEmpty(model.NFM_Numero))
      //  return false;

      //if (string.IsNullOrEmpty(model.NFM_Serie))
      //  return false;

      //if (model.NFM_DataEmissao == new DateTime(0001, 01, 01))
      //  return false;

      if (model.NFM_CodigoRepasse == 0)
        return false;

      return true;
    }

    public static R_NotaFiscaRepasse ToNotaFiscaRepasseEdit(this NotaFiscalRepasseModel model, R_NotaFiscaRepasse entity)
    {
      entity.NF_Numero = model.NFM_Numero;
      entity.NF_Serie = model.NFM_Serie;
      entity.NF_DataEmissao = model.NFM_DataEmissao;
      entity.NF_ValorTotal = model.NFM_ValorTotal;
      entity.NF_ValorLiquido = model.NFM_ValorLiquido;
      entity.NF_Impostos = model.IIS + model.PIS + model.COFINS + model.CSLL;
      entity.NF_IIS = model.IIS;
      entity.NF_PIS = model.PIS;
      entity.NF_COFINS = model.COFINS;
      entity.NF_CSLL = model.CSLL;
      return entity;
    }

    public static NotaFiscalRepasseModel ToNotaFiscaRepasseModel(this R_NotaFiscaRepasse entity)
    {
      if (entity == null)
        return new NotaFiscalRepasseModel();

      NotaFiscalRepasseModel model = new NotaFiscalRepasseModel();
      model.NFM_Codigo = entity.NF_Id;
      model.NFM_Numero = entity.NF_Numero;
      model.NFM_Serie = entity.NF_Serie;
      model.NFM_DataEmissao = entity.NF_DataEmissao;
      model.NFM_ValorTotal = entity.NF_ValorTotal;
      model.NFM_ValorLiquido = entity.NF_ValorLiquido;
      model.NFM_Impostos = entity.NF_Impostos;
      model.NFM_DataCriacao = entity.NF_DataCriacao;
      model.NFM_CodigoUsuarioCriacao = entity.NF_IdUsuarioCriacao;
      model.NFM_CodigoRepasse = entity.NF_IdRepasse;
      model.IIS = entity.NF_IIS.Value;
      model.PIS = entity.NF_PIS.Value;
      model.COFINS = entity.NF_COFINS.Value;
      model.CSLL = entity.NF_CSLL.Value;
      return model;
    }

    public static NotaFiscalRepasseEdit ToNotaFiscaRepasseEdit(this R_NotaFiscaRepasse entity)
    {
      if (entity == null)
        return new NotaFiscalRepasseEdit();

      NotaFiscalRepasseEdit model = new NotaFiscalRepasseEdit();
      model.NFM_Codigo = entity.NF_Id;
      model.NFM_Numero = entity.NF_Numero;
      model.NFM_Serie = entity.NF_Serie;
      model.NFM_DataEmissao = entity.NF_DataEmissao;
      model.NFM_ValorTotal = entity.NF_ValorTotal;
      model.NFM_ValorLiquido = entity.NF_ValorLiquido;
      model.NFM_Impostos = entity.NF_Impostos;
      model.NFM_DataCriacao = entity.NF_DataCriacao;
      model.NFM_CodigoUsuarioCriacao = entity.NF_IdUsuarioCriacao;
      model.NFM_CodigoRepasse = entity.NF_IdRepasse;
      model.IIS = entity.NF_IIS.Value;
      model.PIS = entity.NF_PIS.Value;
      model.COFINS = entity.NF_COFINS.Value;
      model.CSLL = entity.NF_CSLL.Value;
      return model;
    }
  }

}
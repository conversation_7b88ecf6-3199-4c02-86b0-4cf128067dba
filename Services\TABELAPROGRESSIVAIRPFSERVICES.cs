﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using PagedList;
using RepasseConvenio.Infrastructure;
using RepasseConvenio.Models;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;

namespace RepasseConvenio.Services
{
  public class TabelaProgressivaIRPFServices : ServiceBase
  {
    public TabelaProgressivaIRPFServices(RepasseEntities Contexto)
        : base(Contexto)
    { }

    public TabelaProgressivaIRPFServices()
       : base()
    { }

    public TabelaProgressivaIRPFServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public TabelaProgressivaIRPFServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
          : base(UsuarioLogado, Contexto)
    { }

    public IPagedList<TabelaProgressivaIRPFModel> Get(int pageNumber, string filtro)
    {
      string query = String.Format(@"SELECT 
	                                    V.T_Id AS Codigo,
	                                    V.T_DtInicioVigencia AS DtInicioVigencia,
	                                    V.T_DtFimVigencia AS DtFimVigencia,
	                                    V.T_Descricao AS Descricao
                                    FROM R_TabelaProgressivaIRPF V
                                    ");

      return Contexto.Database.SqlQuery<TabelaProgressivaIRPFModel>(query)
             .ToList().OrderBy(a => a.DtInicioVigencia).ToPagedList(pageNumber, PageSize);
    }

    public R_TabelaProgressivaIRPF GetById(int Id)
    {
      return Contexto.R_TabelaProgressivaIRPF.Where(a => a.T_Id == Id).FirstOrDefault();
    }

    public int GetId(int Id)
    {
      string query = @"select T_Id from R_TabelaProgressivaIRPF
                         where T_Id = @Id
                      ";
      return Contexto.Database.SqlQuery<int>(query,new SqlParameter("Id", Id)).FirstOrDefault();



    }


    public void Create(TabelaProgressivaIRPFModel model)
    {
      ValidaModel(model);
      R_TabelaProgressivaIRPF entity = model.ToEntityIRCreate();
      Create(entity);
    }

    public void Delete(int Id)
    {
      int valoresTabela = new ValoresTabelaProgressivaIRPFServices(Contexto).GetQtdItensByIdTabelaIRPF(Id);

      if (valoresTabela > 0)
        throw new CustomException("Não é possível deletar pois existem valores para a tabela.");

      R_TabelaProgressivaIRPF R_TabelaProgressivaIRPFf = GetById(Id);

      Contexto.R_TabelaProgressivaIRPF.Remove(R_TabelaProgressivaIRPFf);
      Contexto.SaveChanges();
    }

    public void Edit(TabelaProgressivaIRPFModel model)
    {
      try
      {
        ValidaModelEdit(model);
        R_TabelaProgressivaIRPF valor = GetById(model.Codigo);
        valor.ToEntityIREdit(model);
        Edit(valor);
      }
      catch (CustomException ex)
      {
        throw new CustomException(ex.Message);
      }
    }

    public List<int> GetIdTabelasByPeriodo( DateTime InicioVigencia, DateTime FimVigencia)
    {
      string query = @"SELECT
                      T.T_Id
                      FROM R_TabelaProgressivaIRPF T
                      WHERE 
                      T_DtInicioVigencia BETWEEN @InicioVigencia AND @FimVigencia
                      OR
                      T_DtFimVigencia BETWEEN @InicioVigencia AND @FimVigencia
                      ";
      return Contexto.Database.SqlQuery<int>(query
                                               , new SqlParameter("@InicioVigencia", InicioVigencia)
                                               , new SqlParameter("@FimVigencia", FimVigencia)
                                               ).ToList();
    }

    public void ValidaModel(TabelaProgressivaIRPFModel model)
    {
      if (model.DtInicioVigencia == new DateTime(0001, 01, 01))
        throw new CustomException("Campo Início Vigência é obrigatório.");

      if (!model.DtFimVigencia.HasValue)
        throw new CustomException("Campo Final Vigência é obrigatório.");

      if (model.DtFimVigencia <= model.DtInicioVigencia)
        throw new CustomException("Campo Final Vigência deve ser maior que a data Início Vigência.");

      if (string.IsNullOrEmpty(model.Descricao))
        throw new CustomException("Campo Descrição é obrigatório.");

      List<int> IdTabelas = GetIdTabelasByPeriodo(model.DtInicioVigencia, model.DtFimVigencia.Value);

      if (IdTabelas.Count > 0 && !IdTabelas.Contains(model.Codigo))
        throw new CustomException("Já existe tabela com esse periodo cadastrado.");

     
    }

    public void ValidaModelEdit(TabelaProgressivaIRPFModel model)
    {
      if (model.DtInicioVigencia == new DateTime(0001, 01, 01))
        throw new CustomException("Campo Início Vigência é obrigatório.");

      if (!model.DtFimVigencia.HasValue)
        throw new CustomException("Campo Final Vigência é obrigatório.");

      if (model.DtFimVigencia <= model.DtInicioVigencia)
        throw new CustomException("Campo Final Vigência deve ser maior que a data Início Vigência.");

      if (string.IsNullOrEmpty(model.Descricao))
        throw new CustomException("Campo Descrição é obrigatório.");

      List<int> IdTabelas = GetIdTabelasByPeriodoEdit( model.Codigo, model.DtInicioVigencia, model.DtFimVigencia.Value);

      if(IdTabelas.Count > 0)
        throw new CustomException("Já existe tabela com esse periodo cadastrado.");

     
    }

    public List<int> GetIdTabelasByPeriodoEdit(int Id, DateTime InicioVigencia, DateTime FimVigencia)
    {
      string query = @"SELECT
                      T.T_Id
                      FROM R_TabelaProgressivaIRPF T
                      WHERE (T.T_Id != @Id
                      AND
                     (( T_DtInicioVigencia BETWEEN @InicioVigencia AND @FimVigencia)
                      OR
                     ( T_DtFimVigencia BETWEEN @InicioVigencia AND @FimVigencia)))
                      ";
      return Contexto.Database.SqlQuery<int>(query
                                               , new SqlParameter("@Id", Id)
                                               , new SqlParameter("@InicioVigencia", InicioVigencia)
                                               , new SqlParameter("@FimVigencia", FimVigencia)
                                               ).ToList();
    }



  }
}
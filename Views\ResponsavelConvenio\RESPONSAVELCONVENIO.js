﻿var ResponsavelConvenio;

ResponsavelConvenio = function () {
};

ResponsavelConvenio.init = function () {
  idResponsavel = null;
  idRelacao = null;

  $(document).on('click', '#RemoverResponsavel', function () {
    if (idRelacao == null)
      AlertaToast("Aviso", "Selecione um responsável", "warning", 5000, "#FCFFD3");
    else
      ResponsavelConvenio.ConfirmarDelete(idRelacao);
  });

  $(document).on('click', '#EditarResponsavel', function () {
    if (idResponsavel == null)
      AlertaToast("Aviso", "Selecione um responsável", "warning", 5000, "#FCFFD3"); 
    else
      window.location.href = GetURLBaseComplete() + "/ResponsavelConvenio/Edit?CodigoResponsavel=" + idResponsavel;
  });

  $(document).on('keyup', '#ModalNovoResponsavel #CPF', function () {
    let cpf = $(this).val();

    if (cpf.length == 14 && cpf.indexOf("_") < 0)
      ResponsavelConvenio.BuscarResponsavel(cpf);
  });

  $(document).on("click", ".TrSelecionavel", function () {
    idRelacao = $(this).data("codigorelacao");
    idResponsavel = $(this).data("codigoresponsavel"); 

    if (!$(this).hasClass("Selected")) {
      $('.Selected').each(function (index, element) {
        $(element).removeClass("Selected")
        $(element).addClass("UnSelected")
      });

      if ($(this).hasClass("Selected")) {
        $(this).removeClass("Selected")
        $(this).addClass("UnSelected")
      }
      else {
        $(this).addClass("Selected")
        $(this).removeClass("UnSelected")
      }
    }
    else {
      $(this).removeClass("Selected")
      $(this).addClass("UnSelected")
    }
  });

  $(document).on('click', '#AdicionarResponsavel', function () {
    let data = {
      NomeResponsavel: $("#ModalNovoResponsavel #NomeResponsavel").val(),
      CPF: $("#ModalNovoResponsavel #CPF").val(),
      Email: $("#ModalNovoResponsavel #Email").val(),
      CodigoConvenio: $("#CodigoConvenio").val(),
    };

    ResponsavelConvenio.Create(data);
  });
}

ResponsavelConvenio.Create = function (novoResponsavel) {
  $.ajax({
    type: 'POST',
    url: GetURLBaseComplete() + '/ResponsavelConvenio/Create',
    data: novoResponsavel, 
    dataType: 'json',
    success: function (data) {
      if (!data.Erro) {
        Alerta(data.Titulo, data.Mensagem, "success", "Fechar");
        ResponsavelConvenio.GridExtratos();

        $("#ModalNovoResponsavel").modal("hide");

        $("#ModalNovoResponsavel #NomeResponsavel").val("");
        $("#ModalNovoResponsavel #CPF").val("");
        $("#ModalNovoResponsavel #Email").val("");

        idRelacao = null;
        idResponsavel = null;
      }
      else
        Alerta(data.Titulo, data.Mensagem, "error", "Fechar");
    },
    error: function (err) {
      Alerta("Erro", "Gentileza tentar mais tarde.", "error", "Fechar");
    }
  });
}

ResponsavelConvenio.ConfirmarDelete = function (IdRelacao) {
    Swal.fire({
      title: "Desvincular Responsável do Convênio",
      html: "Deseja continuar com a desvínculo do responsável?",
      icon: "warning",
      confirmButtonText: "Sim",
      showCancelButton: "Cancelar",
      showLoaderOnConfirm: true,
      dangerMode: true,
    })
      .then((willDelete) => {
        if (willDelete.isConfirmed) {
          ResponsavelConvenio.AjaxDelete(IdRelacao);
        }
      });
}

ResponsavelConvenio.AjaxDelete = function (IdRelacao) {
  var data = {
    CodigoRelacao: IdRelacao
  };

  $.ajax({
    type: 'POST',
    url: GetURLBaseComplete() + '/ResponsavelConvenio/Delete',
    data: data,
    dataType: 'json',
    success: function (data) {
      if (!data.Erro) {
        Alerta(data.Titulo, data.Mensagem, "success", "Fechar");
        ResponsavelConvenio.GridExtratos();
        idRelacao = null;
        idResponsavel = null;
      }
      else
        Alerta(data.Titulo, data.Mensagem, "error", "Fechar");
    },
    error: function (err) {
      AlertaSwal("Erro", "Gentileza tentar mais tarde.", "error", "Fechar");
    }
  });
}

ResponsavelConvenio.GridExtratos = function () {
  var data = {
    CodigoConvenio: $("#CodigoConvenio").val()
  };

  $.ajax({
    type: 'GET',
    url: GetURLBaseComplete() + '/ResponsavelConvenio/_Grid',
    data: data,
    dataType: 'html',
    success: function (data) {
      $('#GridResponsaveis').html(data);
    }
  });
}

ResponsavelConvenio.BuscarResponsavel = function (CPF) {
  var data = {
    CPF: CPF
  };

  $.ajax({
    type: 'GET',
    url: GetURLBaseComplete() + '/ResponsavelConvenio/_GetResponsavel',
    data: data,
    dataType: 'json',
    success: function (data) {
      $("#ModalNovoResponsavel #NomeResponsavel").val(data.Nome);
      $("#ModalNovoResponsavel #Email").val(data.Email);
    }
  });
}

$(document).ready(function () {
  ResponsavelConvenio.init();
});


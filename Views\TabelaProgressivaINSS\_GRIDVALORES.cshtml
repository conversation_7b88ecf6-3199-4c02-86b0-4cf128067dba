﻿@using PagedList.Mvc
@using RepasseConvenio.Models
@using RepasseConvenio.Infrastructure
@using RepasseConvenio.Infrastructure.Controls
@using System.Globalization;
@model List<ValoresTabelaProgressivaINSSModel>



<div class="card-primary card-outline">
  <div class="card-header" style="padding: 0.5rem 1.5rem;">
    <h3 class="card-title"> <i class="far fa-copy"></i> Faixa de Valores</h3>
    <div class="card-tools" style="display:flex;">

      <button id="open-modal-valores" type="button" class="btn btn-sm btn-block btn-outline-primary">
        Nova Faixa
        <i class="fas fa-plus"></i>
      </button>
    </div>
  </div>
  <!-- /.card-header -->
  <div class="card-body p-0">
    @*<div class="card-acoes col-2">
      <a class="btn btn-block btn-outline-primary disabled" id="TabelaDelete"> Excluir </a>
    </div>*@

    @using (Html.BeginForm("", "", FormMethod.Post, new { id = "PostDocumentos" }))
    {
      <div class="divTableItens">
        <table id="TableTela" class="table-striped table table-sm table-hover text-nowrap">
          <thead>
            <tr>
              <th>
                Valor Inicial
              </th>
              <th>
                Valor Final
              </th>
              <th>
                Alíquota 
              </th>
              <th>
              </th>
            </tr>
          </thead>
          <tbody>
            @for (var i = 0; i < Model.ToArray().Length; i++)
            {
              <tr class="TrSelecionavel" data-codigovalores="@Model[i].Codigo">
                @Html.HiddenFor(m => Model[i].Codigo)
                @Html.HiddenFor(m => Model[i].CodigoTabela)

                <td>
                  @Model[i].ValorInicial.ToString("C2", CultureInfo.CreateSpecificCulture("pt-BR"))
                </td>
                <td>
                  @Model[i].ValorFinal.ToString("C2", CultureInfo.CreateSpecificCulture("pt-BR"))
                </td>
                <td>
                  @Model[i].AliquotaProgressiva %
                </td>
                <td class="project-actions text-right">
                  <a href="#" data-codigo="@Model[i].Codigo" class="btn btn-sm btn-outline-danger btnRemover" title="">
                    <i class="fas fa-trash"></i>
                  </a>
                </td>
              </tr>
            }
          </tbody>
        </table>
      </div>
    }
  </div>
</div>
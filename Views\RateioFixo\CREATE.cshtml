﻿@using PagedList.Mvc
@using RepasseConvenio.Models
@using RepasseConvenio.Infrastructure
@using RepasseConvenio.Infrastructure.Controls
@model RateioFixoModel

@{
  ViewBag.Title = "Criar";
  Layout = "~/Views/Shared/_Layout.cshtml";
}

@using (Html.BeginForm("Create", "RateioFixo", FormMethod.Post))
{
  @Html.HiddenFor(m => m.CodigoRateio)

  <div class="row">
    <section class="col-lg-12 connectedSortable ui-sortable">
      <div class="card">
        <div class="card-header ui-sortable-handle">
          <h3 class="card-title">
            <i class="fas fa-chart-pie mr-1"></i>
           Rateio Fixo
          </h3>
          <div class="card-tools">
          </div>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                @Html.LabelFor(m => m.MedicoSelect)
                @Html.LibSelect2For(m => m.MedicoSelect, new { @class = "form-control" })
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                @Html.LabelFor(m => m.Funcao)
                @Html.LibEditorFor(m => m.Funcao, new { @class = "form-control" })
                @Html.ValidationMessageFor(m => m.Funcao, "", new { @class = "text-danger" })
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                @Html.LabelFor(m => m.Percentual)
                @Html.LibEditorFor(m => m.Percentual, new { @class = "form-control porcentagem" })
                @Html.ValidationMessageFor(m => m.Percentual, "", new { @class = "text-danger" })
              </div>
            </div>
          </div>

        </div>
        <div class="card-footer">
          <button type="button" value="Voltar" class="btn btn-secondary" onclick="window.history.back();">Cancelar</button>
          <button type="submit" value="Create" class="btn btn-info pull-right">Salvar</button>
        </div>
      </div>
    </section>
  </div>
}
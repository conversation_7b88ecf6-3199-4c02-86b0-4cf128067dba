﻿var Repasse = function () {

};

Repasse.init = function () {
  $("#BancoInsertSelectLookup").on("select2:select", function (e) {
    var varIdBanco = $("#BancoInsertSelect_id").val();
    Repasse.GetInserirBancoRepasse(varIdBanco)
  })

  $(document).on("click", "#NovoDepositoRepasse", function () {
    $("#ModalDepositoRepasse").modal("show");
  });

  $(document).on("click", "#InserirDepositoRepasse", function () {
    $("#ModalIserteDepositoRepasse").modal("show");
  });

  $(document).on("click", ".btnInserteSaveBanco", function () {
    Repasse.InsertDepositoRepasse();
    $("#ModalIserteDepositoRepasse").modal("hide");
  });




  $(document).on("click", "#BancoSelect_id", function () {
    var varIdBanco = $("#BancoSelect_id").val();
    Repasse.GetInserirBancoRepasse(varIdBanco, varIdRepasse)

  });



  $(document).on("click", ".btnSaveBanco", function () {
    Repasse.CreateDepositoRepasse();
    $("#ModalDepositoRepasse").modal("hide");
  });

  $(document).on("click", "#ExcluirDepositoRepasse", function () {
    var codigo = $(this).data("iddepositorepasse");
    var varIdRepasse = $("#Codigo").val();
    var model = {
      codigoDepositoRepasse: codigo,
      IdRepasse: varIdRepasse
    };

    Swal.fire({
      title: "Deseja Excluir?",
      html: "Clique em sim para confirmar a exclusão do Depósito Repasse.",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Sim",
      cancelButtonText: "Não",
      showLoaderOnConfirm: true,
      dangerMode: true,
    })
      .then((will) => {
        if (will.isConfirmed) {
          $.ajax({
            type: 'POST',
            url: GetURLBaseComplete() + '/DepositoRepasse/Delete',
            data: model,
            dataType: 'json',
            success: function (data) {
              if (!data.Erro) {
                AlertToReload(data.Titulo, data.Mensagem, data.Tipo, "Fechar");
              }
            },
            error: function (err) {
              AlertToReload(data.Titulo, data.Mensagem, data.Tipo, "Fechar");
            }
          });
        } else {
        }
      });
  });

  $(document).on("click", "#ImportNotaFiscal", function () {
    var IdRepasse = $('#Codigo').val();
    var model = {
      IdRepasse: IdRepasse
    }

    $.ajax({
      type: 'POST',
      url: GetURLBaseComplete() + '/DemonstrativoConvenio/ImportNotaFiscal',
      dataType: "json",
      data: model,
      success: function (data) {
        if (!data.Erro) {
          AlertToReload("Sucesso", data.Mensagem, "success");
        }
        else {
          Alerta("Erro", data.Mensagem, "error");
        }
      },
      error: function (err) {
        Alerta("Erro", "Gentileza tentar mais tarde.", "error");
      }
    });
  });

  $(document).on("click", ".btnExcluirGuiaNotaFiscal", function () {
    var IdGuiaNotaFiscal = $(this).data("idguianotafiscal");
    var model = {
      IdGuiaNotaFiscal: IdGuiaNotaFiscal
    };

    $.ajax({
      type: 'POST',
      url: GetURLBaseComplete() + '/GuiaNotaFiscal/Delete',
      dataType: "json",
      data: model,
      success: function (data) {
        var IdNotaFiscal = $('#IdNotaFiscal').val();
        model = {
          IdNotaFiscal: IdNotaFiscal
        };
        if (!data.Erro) {
          $.ajax({
            type: 'GET',
            url: GetURLBaseComplete() + '/Repasse/GetPartialGuiaNotaFiscalEdit',
            dataType: 'html',
            data: model,
            success: function (data) {
              $("#PartialGuiasNotaFiscalEdit").html(data);
            },
          });
          Alerta("Sucesso", data.Mensagem, "success");
        }
        else {
          Alerta("Erro", data.Mensagem, "error");
        }
      },
      error: function (err) {
        Alerta("Erro", "Gentileza tentar mais tarde.", "error");
      }
    });
  });

  $(document).on("click", ".btnEditGuiaNotaFiscal", function () {
    var numeroLinha = $(this).data("linha");
    var IdGuiaNotaFiscal = $(this).data("idguianotafiscal");
    var valorValorPago = $('input[name ="[' + numeroLinha + '].ValorPago').inputmask('unmaskedvalue');
    var valorApresentado = $('input[name ="[' + numeroLinha + '].ValorApresentado').inputmask('unmaskedvalue');

    var model = {
      IdGuiaNotaFiscal: IdGuiaNotaFiscal,
      valorValorPago: valorValorPago,
      valorApresentado: valorApresentado
    }

    $.ajax({
      type: 'POST',
      url: GetURLBaseComplete() + '/GuiaNotaFiscal/Edit',
      dataType: "json",
      data: model,
      success: function (data) {
        var IdNotaFiscal = $('#IdNotaFiscal').val();
        model = {
          IdNotaFiscal: IdNotaFiscal
        };
        if (!data.Erro) {
          $.ajax({
            type: 'GET',
            url: GetURLBaseComplete() + '/Repasse/GetPartialGuiaNotaFiscalEdit',
            dataType: 'html',
            data: model,
            success: function (data) {
              $("#PartialGuiasNotaFiscalEdit").html(data);
            },
          });
          Alerta("Sucesso", data.Mensagem, "success");
        }
        else {
          Alerta("Erro", data.Mensagem, "error");
        }
      },
      error: function (err) {
        Alerta("Erro", "Gentileza tentar mais tarde.", "error");
      }
    });

  });

  $(document).on("click", "#CheckAllGuiaNotaFiscal", function () {
    //if ($(this).prop('checked'))
    //  $('.btnExcluirSelecionados').removeClass("disabled");
    //else
    //  $('.btnExcluirSelecionados').addClass("disabled");

    $(".isSelectedPartialNotaFiscal").prop('checked', $(this).prop('checked'));
  });


  $(document).on("click", "#btnCreateGuiaNotaFiscal", function () {
    var ListIdGuia = [];
    $('.isSelectedPartialNotaFiscal').each(function (index, element) {
      if ($(element).is(':checked'))
        ListIdGuia.push($(element).data('codigoguia'));
    });
    var CodigoNotaFiscal = $('#IdNotaFiscal').val();
    var model = { ListaIdGuiasAtendimento: ListIdGuia, IdNotaFiscal: CodigoNotaFiscal };

    $.ajax({
      type: 'POST',
      url: GetURLBaseComplete() + '/GuiaNotaFiscal/Create',
      dataType: "json",
      data: model,
      success: function (data) {
        if (!data.Erro) {
          $.ajax({
            type: 'GET',
            url: GetURLBaseComplete() + '/Repasse/GetPartialGuiaNotaFiscalEdit',
            dataType: 'html',
            data: model,
            success: function (data) {
              $("#PartialGuiasNotaFiscalEdit").html(data);
              $("#ModalGuiaNotaFiscal").modal("hide");
            },
          });
          Alerta("Sucesso", data.message, "success");
        }
        else {
          Alerta("Erro", data.message, "error");
        }
      },
      error: function (err) {
        Alerta("Erro", "Gentileza tentar mais tarde.", "error");
      }
    });
  });

  $(document).on("click", "#PesquisarLoteGuia", function () {
    var NumeroGuia = $('#NumeroGuia').val();
    var NumeroLote = $('#NumeroLote').val();
    var IdNotaFiscal = $('#IdNotaFiscal').val();
    var IdRepase = $('#Codigo').val();

    var model = {
      NumeroGuia: NumeroGuia,
      NumeroLote: NumeroLote,
      IdNotaFiscal: IdNotaFiscal,
      IdRepasse: IdRepase
    }

    $.ajax({
      type: 'POST',
      url: GetURLBaseComplete() + '/GuiaNotaFiscal/ValidaExistLoteOrGuia',
      dataType: "json",
      data: model,
      success: function (data) {
        if (!data.RetornoAjax.Erro) {
          $.ajax({
            type: 'GET',
            url: GetURLBaseComplete() + '/Repasse/GetPartialGuiaNotaFiscal',
            dataType: 'html',
            data: model,
            success: function (data) {
              $("#PartialGuiaNotaFiscal").html(data);
            },
          });
        }
        else {
          if (!data.CustomException)
            Alerta("Erro", data.Mensagem, "error");
          else {
            Swal.fire({
              title: data.RetornoAjax.Mensagem,
              icon: 'warning',
              showCancelButton: true,
              confirmButtonColor: '#3085d6',
              cancelButtonColor: '#d33',
              cancelButtonText: "Não!",
              confirmButtonText: "Sim, Integrar!"
            }).then((result) => {
              if (result.isConfirmed) {
                $.ajax({
                  url: GetURLBaseComplete() + '/GuiaNotaFiscal/IntegraLoteGuia',
                  dataType: "json",
                  type: "POST",
                  data: model,
                  success: function (data) {
                    if (!data.Erro) {
                      $.ajax({
                        type: 'GET',
                        url: GetURLBaseComplete() + '/Repasse/GetPartialGuiaNotaFiscal',
                        dataType: 'html',
                        data: model,
                        success: function (data) {
                          $("#PartialGuiaNotaFiscal").html(data);
                        },
                      });
                    } else {
                      Alerta("Erro", data.Mensagem, "error");
                    }
                  }
                })
              }
            });
          }
        }
      },
      error: function (err) {
        Alerta("Erro", "Gentileza tentar mais tarde.", "error");
      }
    });
  });

  $(document).on("click", "#btnInserirGuias", function () {
    $('#ModalGuiaNotaFiscal').modal("show");
  });

  $(document).on("click", "#NovaNotaFiscal", function () {
    //$('#ModalNotaFiscal').modal("show");
    var idRepasse = $(this).data("idrepasse");
    //$('#NFM_CodigoRepasse').val(idRepasse);
    var utlForm = GetURLBaseComplete() + '/NotaFiscalRepasse/Create';
    var codigo = 0;
    $('.btnCreateEditNota').attr("id", "btnCreateNotaFiscal");
    Repasse.GetDadosNotaFiscal(idRepasse, codigo, utlForm);
  });

  $(document).on("click", "#btnCreateNotaFiscal", function () {
    Repasse.CreateNotaFiscal();
  });

  $(document).on("click", ".btnEditNotaFiscal", function () {
    //btnExcluirSelecionados
    var codigo = $(this).data("idnota");
    var idRepasse = $(this).data("idrepasse");
    var utlFormEdit = GetURLBaseComplete() + '/NotaFiscalRepasse/Edit';
    $('#IdNotaFiscal').val(codigo);
    $('#btnInserirGuias').show();
    $('.btnCreateEditNota').attr("id", "btnEditNotaFiscal");
    Repasse.GetDadosNotaFiscal(idRepasse, codigo, utlFormEdit);
  });

  $(document).on("click", ".btnRemoverNotaFiscal", function () {
    var codigo = $(this).data("idnota");
    Repasse.RemoveNotaFiscal(codigo);
  });


  $(document).on("click", "#btnEditNotaFiscal", function () {
    Repasse.EditNotaFiscal(true);
  });

  $(document).on("click", "#NovaPlanRecebimento", function () {
    //$('#ModalPlanRecebimento').modal("show");
    var idRepasse = $(this).data("idrepasse");
    //$('#NFM_CodigoRepasse').val(idRepasse);
    var utlForm = GetURLBaseComplete() + '/PlanilhaRecebimento/Create';
    var codigo = 0;
    Repasse.GetDadosPlanilhaRecebimento(idRepasse, codigo, utlForm);
  });

  $(document).on("click", "#btnCreatePlanRecebimento", function () {
    Repasse.CreatePlanilhaRecebimento();
  });

  $(document).on("click", ".btnEditPlanRecebimento", function () {
    var codigo = $(this).data("idplan");
    var idRepasse = $(this).data("idrepasse");
    var utlFormEdit = GetURLBaseComplete() + '/PlanilhaRecebimento/Edit';
    Repasse.GetDadosPlanilhaRecebimento(idRepasse, codigo, utlFormEdit);
  });

  $(document).on("click", "#btnEditPlanRecebimento", function () {
    Repasse.EditPlanilhaRecebimento(true);
  });

  $(document).on("click", ".btnExcluirSelecionados", function () {
    var ListaCodigo = [];
    var IdRepasse = $('#Codigo').val();

    $('.isSelected').each(function (index, element) {
      if ($(element).is(':checked'))
        ListaCodigo.push($(element).data('codigoplanilha'));
    });

    Repasse.RemovePlanilhaRecebimento(ListaCodigo, IdRepasse);
  });

  $(document).on("click", ".btnRemoverPlanRecebimento", function () {
    var ListaCodigo = [];
    var IdRepasse = $('#Codigo').val();
    ListaCodigo.push($(this).data('idplan'))
    Repasse.RemovePlanilhaRecebimento(ListaCodigo, IdRepasse);
  });

  $(document).on("click", "#CheckAllPlanRecebimento", function () {
    if ($(this).prop('checked'))
      $('.btnExcluirSelecionados').removeClass("disabled");
    else
      $('.btnExcluirSelecionados').addClass("disabled");

    $(".isSelected").prop('checked', $(this).prop('checked'));
  });

  $(document).on("click", "#btnCancelarProcessamento", function () {
    let idRepasse = $(this).data("idrepasse");
    AlertaWithRedirect("Cancelar Processamento", "Tem certeza que deseja cancelar o processamento?", "warning", "Sim", GetURLBaseComplete() + "/Repasse/CancelarProcessamento/" + idRepasse)
  });

  $(document).on("change", ".isSelected", function () {
    if ($('.isSelected:checked').length == 0)
      $('.btnExcluirSelecionados').addClass("disabled");
    else
      $('.btnExcluirSelecionados').removeClass("disabled");

    if (!$(this).prop("checked")) {
      $("#CheckAllPlanRecebimento").prop("checked", false);
    }
    if ($('.isSelected').length == $('.isSelected:checked').length)
      $("#CheckAllPlanRecebimento").prop("checked", true);
  });

  $(document).on("change", ".isSelectedAtdNaoPg", function () {
    if ($('.isSelectedAtdNaoPg:checked').length == 0)
      $('#GerarGlosaNaoPg').addClass("disabled");
    else
      $('#GerarGlosaNaoPg').removeClass("disabled");

    if (!$(this).prop("checked")) {
      $("#CheckAllAtdNaoPg").prop("checked", false);
    }
    if ($('.isSelectedAtdNaoPg').length == $('.isSelectedAtdNaoPg:checked').length)
      $("#CheckAllAtdNaoPg").prop("checked", true);
  });

  $(document).on("click", "#CheckAllAtdNaoPg", function () {
    if ($(this).prop('checked'))
      $('#GerarGlosaNaoPg').removeClass("disabled");
    else
      $('#GerarGlosaNaoPg').addClass("disabled");

    $(".isSelectedAtdNaoPg").prop('checked', $(this).prop('checked'));
  });

  $(document).on("click", "#GerarGlosaNaoPg", function () {
    let ListaCodigo = [];
    let DescMotivo = $("#MotivoSelect_text").val();
    let CodigoMotivo = $("#MotivoSelect_id").val();
    let Data = $("#DataInicioGlosa").val();

    $('.isSelectedAtdNaoPg').each(function (index, element) {
      if ($(element).is(':checked'))
        ListaCodigo.push($(element).data('codigoitem'));
    });

    if (ListaCodigo.length == 0) {
      AlertaToast("Aviso", "Selecione um item", "warning", 5000, "#FCFFD3");
      return;
    }
    else if (!DescMotivo) {
      AlertaToast("Aviso", "Selecione um motivo", "warning", 5000, "#FCFFD3");
      return;
    }
    else if (!Data) {
      AlertaToast("Aviso", "Selecione uma data", "warning", 5000, "#FCFFD3");
      return;
    }
    else if (!CodigoMotivo || CodigoMotivo == "0") {
      AlertaToast("Aviso", "Selecione um motivo", "warning", 5000, "#FCFFD3");
      return;
    }

    Repasse.GlosaAtdNaoPago(ListaCodigo, CodigoMotivo, DescMotivo, Data);
  });

  $(document).on("click", "#open-modal-buscalote", function () {
    $('#ModalBuscaLote').modal("show");
    var idRepasse = $(this).data("idrepasse");
    var idconvenio = $(this).data("idconvenio");
    $("#BL_IdConvenio").val(idconvenio);
    $("#BL_IdRepasse").val(idRepasse);
  });

  $(document).on("click", "#btnPesquisaLotes", function () {
    Repasse.PesquisaLotes();
  });

  $(document).on("click", "#btnInserirLotes", function () {
    Repasse.InserirLotes();
  });

  $(document).on('click', '#ImportPlan', function () {
    $("#inputfileimportplan").trigger("click");
  });

  $("#inputfileimportplan").on('change', function (a) {
    if (typeof (FileReader) != "undefined") {
      var input = this;
      var files = input.files;

      var file = files[0];
      var formData = new FormData();
      formData.append("FileUpload", file);
      formData.append("IdRepasse", $("#Codigo").val());

      $.ajax({
        url: GetURLBaseComplete() + '/DemonstrativoConvenio/ImportPlanilha',
        dataType: "json",
        type: "POST",
        data: formData,
        contentType: false,
        processData: false,
        success: function (data) {
          if (data.status == "success") {
            AlertToReload("Importação", data.message, "success");
          } else {
            //Alerta("Erro", data, "error", 3000);
            AlertToReload("Importação", data.message, "error");
          }
        }
      });

    } else {
      alert("Este navegador não suporta inserir o tipo de arquivo necessário.");
    }
  });

  $(document).on("click", "#ProcessaRepasse", function () {
    var codigo = $(this).data("idrepasse");

    $.ajax({
      type: 'POST',
      url: GetURLBaseComplete() + '/Repasse/ProcessarRepasse',
      dataType: "json",
      data: {
        id: codigo
      },
      success: function (data) {
        if (!data.RetornoAjax.Erro) {
          if (data.StatusRepasse == 2 || data.StatusRepasse == 5) {
            let errorirect = GetURLBaseComplete() + '/Repasse/Edit?codigo=' + codigo;
            AlertaWithRedirect(data.RetornoAjax.Titulo, data.RetornoAjax.Mensagem, "success", "OK", errorirect);
          }
          else {
            let errorirect = GetURLBaseComplete() + '/Repasse/Details?codigo=' + codigo;
            AlertaWithRedirect(data.RetornoAjax.Titulo, data.RetornoAjax.Mensagem, "success", "OK", errorirect);
          }

        }
        else {
          Alerta(data.RetornoAjax.Titulo, data.RetornoAjax.Mensagem, "error");
        }
      },
      error: function (err) {
        Alerta("Erro", "Gentileza tentar mais tarde.", "error");
      }
    });
  });

  $(document).on("click", "#ProcessarGlosa", function () {
    Repasse.ProcessarGlosa();
  });

  $(document).on("click", ".VisualizarGuiaDemonstrativo", function () {
    var codigo = $(this).data("codigodemonstrativo");
    Repasse.GetGridProcedimentoDemonstrativo(codigo);
  });

  $(document).on("click", ".VisualizarGuiaAtendimento", function () {
    var codigoGuia = $(this).data("codigoguiaatendimento");
    Repasse.GetGridGuiasAtendimento(codigoGuia);
  });

  $(document).on("click", "#MostrarExtratos", function () {
    $("#ClassificacaoSelect_id").val("");
    $("#ClassificacaoSelect_text").val("");
    $("#ClassificacaoSelectLookup").val(null).trigger('change');
    $("#NomeMedico").val("");
    Repasse.GetGridExtrato();
  });

  $(document).on("click", "#VisuLotes", function () {
    var idRepasse = $(this).data("idrepasse")
    Repasse.GetLotesRepasse(idRepasse);
  });

  $(document).on("click", "#AtdNaoPg", function () {
    $("#ModalAtdNaoPago input").attr("readonly", false);
    Repasse.GetAtdNaoPago($(this).data("idrepasse"));
  });

  $(document).on("click", "#FiltrarExtratos", function () {
    Repasse.FiltrarExtrato();
  });

  $(document).on("click", ".VisualizarItensLoteRepasse", function () {
    var codigoLote = $(this).data("codigolote")
    Repasse.GetItensLote(codigoLote);
  });

  $(document).on("click", ".TrSelecionavel", function () {
    var idRepasse = $(this).data("codigorepasse");
    var StatusRepasse = $(this).data("statusrepasse");

    var urlEdit = GetURLBaseComplete() + "/Repasse/Edit?codigo=" + idRepasse;
    var urlDetalhes = GetURLBaseComplete() + "/Repasse/Details?codigo=" + idRepasse;
    var urlDelete = GetURLBaseComplete() + "/Repasse/RemoverRepasse?codigoRepasse=" + idRepasse;

    if (!$(this).hasClass("Selected")) {
      $('.Selected').each(function (index, element) {
        $(element).removeClass("Selected")
        $(element).addClass("UnSelected")
      });

      if ($(this).hasClass("Selected")) {
        $(this).removeClass("Selected")
        $(this).addClass("UnSelected")
      }
      else {
        $(this).addClass("Selected")
        $(this).removeClass("UnSelected")
      }
    }
    else {
      $(this).removeClass("Selected")
      $(this).addClass("UnSelected")
    }

    var countSelected = 0;
    $('.Selected').each(function (index, element) {
      if ($(element).hasClass("Selected")) {
        $('#RepasseEdit').attr("href", urlEdit);
        $('#RepasseDetalhes').attr("href", urlDetalhes);
        $('#RepasseExcluir').data("CodigoRepasse", idRepasse);
        countSelected++;
      }
    });

    if (countSelected == 0) {
      $('#RepasseEdit').show();
      $('#RepasseDetalhes').show();
      $('#RepasseEdit').removeAttr("href");
      $('#RepasseDetalhes').removeAttr("href");
      $('#RepasseExcluir').data("CodigoRepasse", 0);

      $('#RepasseEdit').attr("disabled", true);
      $('#RepasseDetalhes').attr("disabled", true);
      $('#RepasseExcluir').attr("disabled", true);

      $('#RepasseEdit').addClass("disabled");
      $('#RepasseDetalhes').addClass("disabled");
      $('#RepasseExcluir').addClass("disabled");
    }
    else {
      if (StatusRepasse == 4 || StatusRepasse == 6 || StatusRepasse == 3) {
        $('#RepasseEdit').hide();
        $('#RepasseDetalhes').show();
        $('#RepasseDetalhes').attr("disabled", false);
        $('#RepasseDetalhes').removeClass("disabled");
      }
      else {
        $('#RepasseEdit').show();
        $('#RepasseDetalhes').hide();
        $('#RepasseEdit').attr("disabled", false);
        $('#RepasseEdit').removeClass("disabled");
      }
      $('#RepasseExcluir').attr("disabled", false);
      $('#RepasseExcluir').removeClass("disabled");
    }
  });

  $(document).on("click", "#RepasseExcluir", function () {
    var IdRepasse = $(this).data("CodigoRepasse");
    var model = {
      codigoRepasse: IdRepasse
    };

    Swal.fire({
      title: "Deseja Excluir?",
      html: "Clique em sim para confirmar a exclusão do repasse.",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Sim",
      cancelButtonText: "Não",
      showLoaderOnConfirm: true,
      dangerMode: true,
    })
      .then((will) => {
        if (will.isConfirmed) {
          $.ajax({
            type: 'POST',
            url: GetURLBaseComplete() + '/Repasse/RemoverRepasse',
            data: model,
            dataType: 'json',
            success: function (data) {
              if (!data.Erro)
                AlertaToast(data.Titulo, data.Mensagem, data.Tipo, data.Timer, data.Color);
              else
                AlertaToast(data.Titulo, data.Mensagem, data.Tipo, data.Timer, data.Color);
            },
            error: function (err) {
              Alerta("Erro", "Gentileza tentar mais tarde.", "error", "Fechar");
            }
          });
        } else {
        }
      });
  });

  $(document).on("paste keyup", "#DataVencimentoLoteGlosa", function () {
    if ($('#DataVencimentoLoteGlosa').inputmask('unmaskedvalue').length == 8) {
      var idConvenio = $('#CodigoConvenio').val();
      var dataVencimento = $(this).val();
      Repasse.GetDataVencimento(idConvenio, dataVencimento)
    }
  });

  $('#DataVencimentoLoteGlosa').datepicker({
    onSelect: function (formattedDate, date, inst) {
      if ($('#DataVencimentoLoteGlosa').inputmask('unmaskedvalue').length == 8) {
        var idConvenio = $('#CodigoConvenio').val();
        Repasse.GetDataVencimento(idConvenio, formattedDate)
      }
    }
  });

  $(document).on("click", "#SubmitProcessaGlosa", function () {
    var codigo = $("#Codigo").val();
    var dataProcessamento = $("#DataVencimentoLoteGlosa").val();
    Repasse.ProcessarGlosaAjax(codigo, dataProcessamento)
  });
};

Repasse.GlosaAtdNaoPago = function (listIdRegistro, CodigoMotivo, DescMotivo, DataInicial) {
  var data = {
    DataInicial: DataInicial,
    ListIdRegistro: listIdRegistro,
    IdRepasse: $('#Codigo').val(),
    IdMotivo: CodigoMotivo,
    DescMotivo: DescMotivo
  };

  return $.ajax({
    type: 'POST',
    url: GetURLBaseComplete() + '/Repasse/GlosaNaoPago',
    data: data,
    dataType: 'json',
    success: function (data) {
      $("#ModalAtdNaoPago").modal("hide");

      if (!data.Erro)
        Alerta(data.Titulo, data.Mensagem, "success", "Fechar");
      else
        Alerta(data.Titulo, data.Mensagem, "error", "Fechar");
    },
    error: function (err) {
      Alerta("Erro", "Gentileza tentar mais tarde.", "error", "Fechar");
    }
  });
}

Repasse.CreateNotaFiscal = function () {
  $.ajax({
    type: 'POST',
    url: GetURLBaseComplete() + '/NotaFiscalRepasse/Create',
    processData: false,
    data: $("#PostNotaFiscal").serialize(),
    tradicional: true,
    success: function (data) {
      if (!data.retornoAjax.Erro) {
        $('#btnInserirGuias').show();
        Alerta(data.retornoAjax.Titulo, data.retornoAjax.Mensagem, "success");
        $('#IdNotaFiscal').val(data.IdNotafiscal)
        //AlertToReload(data.Titulo, data.Mensagem, "success");
        //Repasse.GetGridNotaFiscal();
        //Alerta(data.Titulo, data.Mensagem, "success");
      }
      else {
        Alerta(data.retornoAjax.Titulo, data.retornoAjax.Mensagem, "error");
      }
    },
    error: function (err) {
      Alerta("Erro", "Gentileza tentar mais tarde.", "error");
    }
  });
}

Repasse.EditNotaFiscal = function () {
  $.ajax({
    type: 'POST',
    url: GetURLBaseComplete() + '/NotaFiscalRepasse/Edit',
    processData: false,
    data: $("#PostNotaFiscal").serialize(),
    tradicional: true,
    success: function (data) {
      if (!data.Erro) {
        Repasse.GetGridNotaFiscal();
        Alerta(data.Titulo, data.Mensagem, "success");
      }
      else {
        Alerta(data.Titulo, data.Mensagem, "error");
      }
    },
    error: function (err) {
      Alerta("Erro", "Gentileza tentar mais tarde.", "error");
    }
  });
}

Repasse.GetDadosNotaFiscal = function (codRepasse, codigo, urlForm) {
  $.ajax({
    type: 'GET',
    url: GetURLBaseComplete() + '/Repasse/GetPartialNotaFiscal',
    dataType: 'html',
    data: {
      idRepasse: codRepasse,
      id: codigo
    },
    success: function (data) {
      $('#ModalNotaFiscal').modal("show");
      $("#PartialNotaFiscal").html(data);
      document.getElementById("PostNotaFiscal").action = urlForm;
      RepasseConvenio.IniciaMascaras();
      RepasseConvenio.IniciaInputs();
    },
  });
}

Repasse.GetGridNotaFiscal = function () {
  var codRepasse = $("#Codigo").val();
  $.ajax({
    type: 'GET',
    url: GetURLBaseComplete() + '/Repasse/GridNotaFiscalRepasse',
    dataType: 'html',
    data: {
      id: codRepasse
    },
    success: function (data) {
      $("#GridNotaFiscalRepasse").html(data);
      $('#ModalNotaFiscal').modal("hide");
    },
  });
}

Repasse.GetAtdNaoPago = function (idRepasse) {
  $.ajax({
    type: 'GET',
    url: GetURLBaseComplete() + '/Repasse/GetGridAtdNaoPagos',
    dataType: 'html',
    data: {
      IdRepasse: idRepasse
    },
    success: function (data) {
      $("#ModalAtdNaoPago .modal-body").html(data);
      $('#ModalAtdNaoPago').modal("show");
    },
  });
}

Repasse.RemoveNotaFiscal = function (codigo) {
  $.ajax({
    type: 'POST',
    url: GetURLBaseComplete() + '/NotaFiscalRepasse/Delete',
    dataType: "json",
    data: {
      id: codigo
    },
    success: function (data) {
      if (!data.Erro) {
        //Repasse.GetGridNotaFiscal();
        AlertToReload(data.Titulo, data.Mensagem, "success");
        //Alerta(data.Titulo, data.Mensagem, "success");
      }
      else {
        Alerta(data.Titulo, data.Mensagem, "error");
      }
    },
    error: function (err) {
      Alerta("Erro", "Gentileza tentar mais tarde.", "error");
    }
  });
}

Repasse.CreatePlanilhaRecebimento = function () {
  $.ajax({
    type: 'POST',
    url: GetURLBaseComplete() + '/PlanilhaRecebimento/Create',
    processData: false,
    data: $("#PostPlanRecebimento").serialize(),
    tradicional: true,
    success: function (data) {
      if (!data.Erro) {
        AlertToReload(data.Titulo, data.Mensagem, "success");
        //Repasse.GetGridPlanilhaRecebimento();
        //Alerta(data.Titulo, data.Mensagem, "success");
      }
      else {
        Alerta(data.Titulo, data.Mensagem, "error");
      }
    },
    error: function (err) {
      Alerta("Erro", "Gentileza tentar mais tarde.", "error");
    }
  });
}

Repasse.EditPlanilhaRecebimento = function () {
  $.ajax({
    type: 'POST',
    url: GetURLBaseComplete() + '/PlanilhaRecebimento/Edit',
    processData: false,
    data: $("#PostPlanRecebimento").serialize(),
    tradicional: true,
    success: function (data) {
      if (!data.Erro) {
        Repasse.GetGridPlanilhaRecebimento();
        Alerta(data.Titulo, data.Mensagem, "success");
      }
      else {
        Alerta(data.Titulo, data.Mensagem, "error");
      }
    },
    error: function (err) {
      Alerta("Erro", "Gentileza tentar mais tarde.", "error");
    }
  });
}

Repasse.GetDadosPlanilhaRecebimento = function (codRepasse, codigo, urlForm) {
  $.ajax({
    type: 'GET',
    url: GetURLBaseComplete() + '/Repasse/GetPartialPlanRecebimento',
    dataType: 'html',
    data: {
      idRepasse: codRepasse,
      id: codigo
    },
    success: function (data) {
      $('#ModalPlanRecebimento').modal("show");
      $("#DivPlanilhaRecebimento").html(data);
      document.getElementById("PostPlanRecebimento").action = urlForm;
      RepasseConvenio.IniciaMascaras();
      RepasseConvenio.IniciaInputs();
    },
  });
}

Repasse.GetGridPlanilhaRecebimento = function () {
  var codRepasse = $("#Codigo").val();
  $.ajax({
    type: 'GET',
    url: GetURLBaseComplete() + '/Repasse/GridPlanRecebimento',
    dataType: 'html',
    data: {
      id: codRepasse
    },
    success: function (data) {
      $('#ModalPlanRecebimento').modal("hide");
      $("#GridPlanilhaRecebimento").html(data);
    },
  });
}

Repasse.RemovePlanilhaRecebimento = function (codigo, idRepasse) {
  var Html = "";

  if (codigo.length == 1)
    Html = "Você irá excluir 1 item da planilha de recebimento, deseja continuar?";
  else if (codigo.length > 1)
    Html = "Você irá excluir " + codigo.length + " itens da planilha de recebimento, deseja continuar?";

  if (codigo.length > 0)
    Swal.fire({
      title: "Deseja continuar a exclusão?",
      html: Html,
      icon: "warning",
      confirmButtonText: "Excluir",
      showCancelButton: "Cancelar",
      showLoaderOnConfirm: true,
      dangerMode: true,
    })
      .then((willDelete) => {
        if (willDelete.isConfirmed) {
          $.ajax({
            type: 'POST',
            url: GetURLBaseComplete() + '/DemonstrativoConvenio/DeleteDemonstrativo',
            dataType: "json",
            data: {
              idItensToDelete: codigo,
              IdRepasse: idRepasse
            },
            success: function (data) {
              if (!data.Erro) {
                //Repasse.GetGridPlanilhaRecebimento();
                AlertToReload(data.Titulo, data.Mensagem, "success");
              }
              else {
                Alerta(data.Titulo, data.Mensagem, "error");
              }
            },
            error: function (err) {
              Alerta("Erro", "Gentileza tentar mais tarde.", "error");
            }
          });
        } else {
        }
      });
  else
    Alerta("Error", "Você deve selecionar pelo menos um item.", "error", "OK");


}

Repasse.PesquisaLotes = function () {
  $.ajax({
    type: 'GET',
    url: GetURLBaseComplete() + '/Repasse/PesquisaLotes',
    processData: false,
    data: $("#GetPesquisaLotes").serialize(),
    tradicional: true,
    dataType: 'html',
    success: function (data) {
      $("#DivGridLotes").show();
      $("#DivGridLotes").html(data);
    },
    error: function (err) {
      Alerta("Erro", "Gentileza tentar mais tarde.", "error");
    }
  });
}

Repasse.InserirLotes = function () {
  $.ajax({
    type: 'POST',
    url: GetURLBaseComplete() + '/Repasse/InserirLotes',
    processData: false,
    data: $("#PostInserirLotes").serialize(),
    tradicional: true,
    success: function (data) {
      if (!data.Erro) {
        $('#ModalBuscaLote').modal("hide");
        Repasse.GetGridPlanilhaRecebimento();
        Alerta(data.Titulo, data.Mensagem, "success");
      }
      else {
        $(".field-validation-valid").text("")
        if (data.erros.length > 0) {
          for (var i = 0; i < data.erros.length; i++) {
            $("[data-valmsg-for='" + data.erros[i].campo + "']").text(data.erros[i].message);
          }
        }
        Alerta(data.Titulo, data.Mensagem, "error");
      }
    },
    error: function (err) {
      Alerta("Erro", "Gentileza tentar mais tarde.", "error");
    }
  });
}

Repasse.GetGridGuiasDemonstrativo = function (IdDemonstrativo) {
  var model = {
    CodigoDemonstrativo: IdDemonstrativo
  }
  $.ajax({
    type: 'GET',
    url: GetURLBaseComplete() + '/Repasse/GetGridGuiaDemonstrativo',
    dataType: 'html',
    data: model,
    success: function (data) {
      $("#PartialGuiasDemonstrativo").html(data);
      $('#ModalGuiasDemonstrativo').modal("show");
    },
  });
}

Repasse.GetGridProcedimentoDemonstrativo = function (IdDemonstrativo) {
  var model = {
    CodigoDemonstrativo: IdDemonstrativo
  }
  $.ajax({
    type: 'GET',
    url: GetURLBaseComplete() + '/Repasse/GetGridProcedimentoDemonstrativo',
    dataType: 'html',
    data: model,
    success: function (data) {
      $("#PartialProcGuiasDemonstrativo").html(data);
      $('#ModalProcedimentosDemonstrativo').modal("show");
    },
  });
}

Repasse.GetGridGuiasAtendimento = function (IdGuiaAtendimento) {
  var model = {
    CodigoGuiaAtendimento: IdGuiaAtendimento
  }
  $.ajax({
    type: 'GET',
    url: GetURLBaseComplete() + '/Repasse/GetGridGuiaAtenimento',
    dataType: 'html',
    data: model,
    success: function (data) {
      $("#PartialGuiasAtendimento").html(data);
      RepasseConvenio.IniciaMascaras();
      $('#ModalGuiasAtendimento').modal("show");
    },
  });
}

Repasse.GetGridExtrato = function () {

  $.ajax({
    type: 'GET',
    url: GetURLBaseComplete() + '/Repasse/GetGridExtrato',
    dataType: 'html',
    data: $('#FormExtratoFiltrar').serialize(),
    processData: false,
    tradicional: true,
    success: function (data) {
      $("#PartialExtratos").html(data);
      RepasseConvenio.IniciaMascaras();
      $('#ModalExtratos').modal("show");
    },
  });
}

Repasse.FiltrarExtrato = function () {

  $.ajax({
    type: 'GET',
    url: GetURLBaseComplete() + '/Repasse/GetGridExtrato',
    dataType: 'html',
    data: $('#FormExtratoFiltrar').serialize(),
    processData: false,
    tradicional: true,
    success: function (data) {
      $("#PartialExtratos").html(data);
      RepasseConvenio.IniciaMascaras();
    },
  });
}

Repasse.GetLotesRepasse = function (IdRepase) {
  var model = {
    IdRepasse: IdRepase
  }

  $.ajax({
    type: 'GET',
    url: GetURLBaseComplete() + '/Repasse/GetLotesRepasse',
    dataType: 'html',
    data: model,
    success: function (data) {
      $("#PartialLotes").html(data);
      RepasseConvenio.IniciaMascaras();
      $('#ModalLotes').modal("show");
    },
  });
}

Repasse.GetItensLote = function (CodigoLote) {
  var model = {
    CodigoLote: CodigoLote
  }

  $.ajax({
    type: 'GET',
    url: GetURLBaseComplete() + '/Repasse/GetGridItensLote',
    dataType: 'html',
    data: model,
    success: function (data) {
      $("#PartialItensLotes").html(data);
      RepasseConvenio.IniciaMascaras();
      $('#ModalItensLote').modal("show");
    },
  });
}

Repasse.ProcessarGlosa = async function () {
  var data = new Date();
  var DataAtual = ('0' + data.getDate()).slice(-2) + '/' + ('0' + (data.getMonth() + 1)).slice(-2) + '/' + data.getFullYear();
  $('#DataVencimentoLoteGlosa').val(DataAtual);
  var idConvenio = $('#CodigoConvenio').val();
  Repasse.GetDataVencimento(idConvenio, DataAtual);
}

Repasse.GetDataVencimento = function (IdConvenio, data) {
  $.ajax({
    type: 'POST',
    url: GetURLBaseComplete() + '/LoteGlosa/GetDataVencimento',
    dataType: "json",
    data: {
      IdConvenio: IdConvenio,
      DataCreditoFatura: data
    },
    success: function (data) {
      if (!data.RetornoAjax.Erro) {
        $('#ShowDataVencimento').html("Vencimento do Lote será: " + data.DataVencimento);
        $('#ShowDataVencimento').show();
        $('#DataVencimentoLoteGlosa').removeAttr("readonly");
        $("#ModalProcessarLoteGlosa").modal("show");
      }
      else
        Alerta("Erro", data.RetornoAjax.Mensagem, "error");
    },
    error: function (err) {
      Alerta("Erro", "Gentileza tentar mais tarde.", "error");
    }
  });
}

Repasse.ProcessarGlosaAjax = function (IdRepasse, DataProcessamento) {
  $.ajax({
    type: 'POST',
    url: GetURLBaseComplete() + '/LoteGlosa/ProcessarGlosa',
    dataType: "json",
    data: {
      IdRepasse: IdRepasse,
      DataCreditoFatura: DataProcessamento
    },
    success: function (data) {
      if (!data.Erro) {
        AlertToReload("Sucesso", data.message, "success");
      }
      else {
        AlertToReload("Erro", data.message, "error");
      }
    },
    error: function (err) {
      Alerta("Erro", "Gentileza tentar mais tarde.", "error");
    }
  });
}


Repasse.CreateDepositoRepasse = function () {

  var varIdRepasse = $("#Codigo").val();
  var varIdBanco = $("#BancoSelect_id").val();
  var varDataDeposito = $("#DataDeposito").val();
  var varValorDeposito = $("#ValorDeposito").inputmask('unmaskedvalue');
  var varNumeroDocumento = $("#NumeroDcumento").val();
  var varValorUtilizado = $("#ValorUtilizado").inputmask('unmaskedvalue');

  var data = {
    IdRepasse: varIdRepasse,
    IdBanco: varIdBanco,
    DataDeposito: varDataDeposito,
    ValorDeposito: varValorDeposito,
    NumeroDoc: varNumeroDocumento,
    ValorUtilizado: varValorUtilizado
  }

  //Chamada no servidor para efetuar a alteracao
  $.ajax({
    type: 'POST',
    cache: false,
    url: GetURLBaseComplete() + '/Repasse/CreateDepositoRepasse', // controller / metododo do controller para criar
    dataType: 'json',
    data: data,
    success: function (data) {
      if (!data.Erro) {
        AlertToReload("Sucesso", data.message, "success");
      }
      else
        AlertToReload("Erro", data.message, "error");
    },
    err: function (erro) {
      console.log(erro);
    }
  });
};

Repasse.InsertDepositoRepasse = function () {

  var varIdRepasse = $("#Codigo").val();
  var varIdBanco = $("#BancoInsertSelect_id").val();

  var data = {
    IdRepasse: varIdRepasse,
    IdBanco: varIdBanco
  }

  //Chamada no servidor para efetuar a alteracao
  $.ajax({
    type: 'POST',
    cache: false,
    url: GetURLBaseComplete() + '/DepositoRepasse/InserteDepositoRepasse', // controller / metododo do controller para criar
    dataType: 'json',
    data: data,
    success: function (data) {
      if (!data.Erro) {
        AlertToReload("Sucesso", data.message, "success", "Fechar");
      }
      else
        AlertToReload("Erro", data.message, "error", "Fechar");
    },
    err: function (erro) {
      AlertToReload("Sucesso", data.message, "success");
    }
  });


};


Repasse.GetDepositoRepasse = function (id) {   // qualquer nome para esse metodo
  $.ajax({
    type: 'GET',
    cache: false,
    url: GetURLBaseComplete() + '/Repasse/GetPartialDepositoRepasse',   // controller/Método para atualizar a view
    dataType: 'html',
    data: {
      id: id,
    },
    success: function (data) {
      $('#GridDepositoRepasse').html(data);  // id da div da view e html partial
    },
    err: function (erro) {
      console.log(erro);
    }
  });

}


Repasse.GetInserirBancoRepasse = function (idBanco) {   // qualquer nome para esse metodo
  $.ajax({
    type: 'GET',
    cache: false,
    url: GetURLBaseComplete() + '/DepositoRepasse/GetInserirDepositoRepasse',   // controller/Método para atualizar a view
    dataType: 'json',
    data: {
      IdBanco: idBanco,
    },
    success: function (data) {
      $("#DataInserteDeposito").val(data.DataDeposito);
      $("#ValorInserteDeposito").val(data.ValorDeposito);
      $("#NumeroInserteDcumento").val(data.NumeroDocumento);
      $("#ValorInserteUtilizado").val(data.ValorUtilizado);

      /*$('#GridDepositoRepasse').html(data);*/  // id da div da view e html partial
    },
    err: function (erro) {
      console.log(erro);
    }
  });

}



$(document).ready(function () {
  Repasse.init();
});
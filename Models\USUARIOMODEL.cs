﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Web;

namespace RepasseConvenio.Models
{
  public class UsuarioModel
  {
    public int Codigo { get; set; }
    public string Nome { get; set; }
    public string Email { get; set; }
    public string CPF { get; set; }
    public string Senha { get; set; }
    public int TipoUsuario { get; set; }

    public TipoUsuario TipoUsuarioEnum { get; set; }
  }
 
  public static class UsuarioConversions
  {
    public static R_Usuario ToUsuarioCreate(this UsuarioModel model)
    {
      R_Usuario entity = new R_Usuario();
      entity.U_Nome = model.Nome;
      entity.U_Email = model.Email;
      entity.U_Password = model.Senha;
      entity.U_IdTipoUsuario = model.TipoUsuario;
      return entity;
    }

    public static R_Usuario ToUsuarioEdit(this UsuarioModel model)
    {
      R_Usuario entity = new R_Usuario();
      entity.U_Id = model.Codigo;
      entity.U_Nome = model.Nome;
      entity.U_Email = model.Email;
      entity.U_Password = model.Senha;
      return entity;
    }

    public static UsuarioModel ToR_Usuario(this R_Usuario model)
    {
      UsuarioModel user = new UsuarioModel();
      user.Codigo = model.U_Id;
      user.CPF = model.U_CPF;
      user.Email = model.U_Email;
      user.Nome = model.U_Nome;
      user.Senha = model.U_Password;
      user.TipoUsuario = model.U_IdTipoUsuario;

      return user;
    }
  }

}
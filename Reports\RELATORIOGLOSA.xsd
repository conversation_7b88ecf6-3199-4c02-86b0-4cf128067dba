﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="RelatorioGlosa" targetNamespace="http://tempuri.org/RelatorioGlosa.xsd" xmlns:mstns="http://tempuri.org/RelatorioGlosa.xsd" xmlns="http://tempuri.org/RelatorioGlosa.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="RelatorioGlosa" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:EnableTableAdapterManager="true" msprop:Generator_DataSetName="RelatorioGlosa" msprop:Generator_UserDSName="RelatorioGlosa">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="RelatorioGlosaRDLC" msprop:Generator_TableClassName="RelatorioGlosaRDLCDataTable" msprop:Generator_TableVarName="tableRelatorioGlosaRDLC" msprop:Generator_RowChangedName="RelatorioGlosaRDLCRowChanged" msprop:Generator_TablePropName="RelatorioGlosaRDLC" msprop:Generator_RowDeletingName="RelatorioGlosaRDLCRowDeleting" msprop:Generator_RowChangingName="RelatorioGlosaRDLCRowChanging" msprop:Generator_RowEvHandlerName="RelatorioGlosaRDLCRowChangeEventHandler" msprop:Generator_RowDeletedName="RelatorioGlosaRDLCRowDeleted" msprop:Generator_RowClassName="RelatorioGlosaRDLCRow" msprop:Generator_UserTableName="RelatorioGlosaRDLC" msprop:Generator_RowEvArgName="RelatorioGlosaRDLCRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="NumeroCarteirinha" msprop:Generator_ColumnVarNameInTable="columnNumeroCarteirinha" msprop:Generator_ColumnPropNameInRow="NumeroCarteirinha" msprop:Generator_ColumnPropNameInTable="NumeroCarteirinhaColumn" msprop:Generator_UserColumnName="NumeroCarteirinha" type="xs:string" minOccurs="0" />
              <xs:element name="NumeroAtendimento" msprop:Generator_ColumnVarNameInTable="columnNumeroAtendimento" msprop:Generator_ColumnPropNameInRow="NumeroAtendimento" msprop:Generator_ColumnPropNameInTable="NumeroAtendimentoColumn" msprop:Generator_UserColumnName="NumeroAtendimento" type="xs:string" minOccurs="0" />
              <xs:element name="CodigoProcedimento" msprop:Generator_ColumnVarNameInTable="columnCodigoProcedimento" msprop:Generator_ColumnPropNameInRow="CodigoProcedimento" msprop:Generator_ColumnPropNameInTable="CodigoProcedimentoColumn" msprop:Generator_UserColumnName="CodigoProcedimento" type="xs:string" minOccurs="0" />
              <xs:element name="DescricaoProcedimento" msprop:Generator_ColumnVarNameInTable="columnDescricaoProcedimento" msprop:Generator_ColumnPropNameInRow="DescricaoProcedimento" msprop:Generator_ColumnPropNameInTable="DescricaoProcedimentoColumn" msprop:Generator_UserColumnName="DescricaoProcedimento" type="xs:string" minOccurs="0" />
              <xs:element name="DataAtendimento" msprop:Generator_ColumnVarNameInTable="columnDataAtendimento" msprop:Generator_ColumnPropNameInRow="DataAtendimento" msprop:Generator_ColumnPropNameInTable="DataAtendimentoColumn" msprop:Generator_UserColumnName="DataAtendimento" type="xs:dateTime" minOccurs="0" />
              <xs:element name="TotalApresentado" msprop:Generator_ColumnVarNameInTable="columnTotalApresentado" msprop:Generator_ColumnPropNameInRow="TotalApresentado" msprop:Generator_ColumnPropNameInTable="TotalApresentadoColumn" msprop:Generator_UserColumnName="TotalApresentado" type="xs:decimal" minOccurs="0" />
              <xs:element name="TotalGlosado" msprop:Generator_ColumnVarNameInTable="columnTotalGlosado" msprop:Generator_ColumnPropNameInRow="TotalGlosado" msprop:Generator_ColumnPropNameInTable="TotalGlosadoColumn" msprop:Generator_UserColumnName="TotalGlosado" type="xs:decimal" minOccurs="0" />
              <xs:element name="TotalPago" msprop:Generator_ColumnVarNameInTable="columnTotalPago" msprop:Generator_ColumnPropNameInRow="TotalPago" msprop:Generator_ColumnPropNameInTable="TotalPagoColumn" msprop:Generator_UserColumnName="TotalPago" type="xs:decimal" minOccurs="0" />
              <xs:element name="ObservacoesGerais" msprop:Generator_ColumnVarNameInTable="columnObservacoesGerais" msprop:Generator_ColumnPropNameInRow="ObservacoesGerais" msprop:Generator_ColumnPropNameInTable="ObservacoesGeraisColumn" msprop:Generator_UserColumnName="ObservacoesGerais" type="xs:string" minOccurs="0" />
              <xs:element name="CodigoGlosa" msprop:Generator_ColumnVarNameInTable="columnCodigoGlosa" msprop:Generator_ColumnPropNameInRow="CodigoGlosa" msprop:Generator_ColumnPropNameInTable="CodigoGlosaColumn" msprop:Generator_UserColumnName="CodigoGlosa" type="xs:string" minOccurs="0" />
              <xs:element name="DescricaoGlosa" msprop:Generator_ColumnVarNameInTable="columnDescricaoGlosa" msprop:Generator_ColumnPropNameInRow="DescricaoGlosa" msprop:Generator_ColumnPropNameInTable="DescricaoGlosaColumn" msprop:Generator_UserColumnName="DescricaoGlosa" type="xs:string" minOccurs="0" />
              <xs:element name="CodigoJustificativa" msprop:Generator_ColumnVarNameInTable="columnCodigoJustificativa" msprop:Generator_ColumnPropNameInRow="CodigoJustificativa" msprop:Generator_ColumnPropNameInTable="CodigoJustificativaColumn" msprop:Generator_UserColumnName="CodigoJustificativa" type="xs:string" minOccurs="0" />
              <xs:element name="DescricaoJustificativa" msprop:Generator_ColumnVarNameInTable="columnDescricaoJustificativa" msprop:Generator_ColumnPropNameInRow="DescricaoJustificativa" msprop:Generator_ColumnPropNameInTable="DescricaoJustificativaColumn" msprop:Generator_UserColumnName="DescricaoJustificativa" type="xs:string" minOccurs="0" />
              <xs:element name="Comentario" msprop:Generator_ColumnVarNameInTable="columnComentario" msprop:Generator_ColumnPropNameInRow="Comentario" msprop:Generator_ColumnPropNameInTable="ComentarioColumn" msprop:Generator_UserColumnName="Comentario" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>
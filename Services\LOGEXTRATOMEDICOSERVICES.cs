﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web.Script.Serialization;

namespace RepasseConvenio.Services
{
  public class LogExtratoMedicoServices : ServiceBase
  {
    public LogExtratoMedicoServices(RepasseEntities Contexto)
        : base(Contexto)
    { }
    public LogExtratoMedicoServices()
       : base()
    { }

    public LogExtratoMedicoServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public LogExtratoMedicoServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public void Create(int IdExtrato, string mensagemLog = "")
    {
      R_LogExtratoMedico logExtratoMedico = new R_LogExtratoMedico();
      logExtratoMedico = logExtratoMedico.Create(IdExtrato, User.IdUsuario, mensagemLog);
      Create(logExtratoMedico);
    }

    public void Create(R_ExtratoMedico extratoMedico, List<LogAux> ListLogAux, string mensagemLog = "")
    {
      R_LogExtratoMedico logExtratoMedico = new R_LogExtratoMedico();
      logExtratoMedico = logExtratoMedico.Edit(ListLogAux, extratoMedico.EX_Id, User.IdUsuario, mensagemLog);
      Create(logExtratoMedico);
    }

    public List<LogExtratoMedicoModal> GetLogExtratoMedicoModal(int IdExtratoMedico)
    {
      string query = @"SELECT 
                        LEM_Id [Codigo],
                        LEM_Descricao [Descricao],
                        LEM_Data [DataCriacao],
                        U.U_Nome [Usuario]
                       FROM R_LogExtratoMedico LEM
                       INNER JOIN R_Usuario U ON U.U_Id = LEM.LEM_IdUsuario AND LEM_IdExtratoMedico = @IdExtratoMedico";

      return Contexto.Database.SqlQuery<LogExtratoMedicoModal>(query, new SqlParameter("@IdExtratoMedico", IdExtratoMedico)).ToList();
    }

    public List<LogAux> GetLogLogAux(int IdLog)
    {
      string query = @"select LEM_Log from R_LogExtratoMedico where LEM_Id = @IdLog";
      string LogAuxSerialize = Contexto.Database.SqlQuery<string>(query, new SqlParameter("@IdLog", IdLog)).FirstOrDefault();
      List<LogAux> logAux = new List<LogAux>();
      if (string.IsNullOrEmpty(LogAuxSerialize))
        throw new CustomException("Log sem detalhes adicionais.");

      JavaScriptSerializer javaScriptSerializer = new JavaScriptSerializer();
      logAux = javaScriptSerializer.Deserialize<List<LogAux>>(LogAuxSerialize);
      return logAux;
    }

  }
}
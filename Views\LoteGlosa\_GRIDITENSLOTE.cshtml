﻿@using RepasseConvenio.Models
@using System.Globalization
@model List<ItensLoteGlosaIndex>

<table class="table table-sm table-striped table-hover text-nowrap">
  <thead>
    <tr>
      <th>
        @Html.CheckBox("CheckAllItensGlosa")
      </th>
      <th>
        <PERSON><PERSON><PERSON>
      </th>
      <th>
        Glosa
      </th>
      <th>
        Faturado
      </th>
      <th>
        Apresentado
      </th>
      <th>
        Glosado
      </th>
      <th>
        Pago
      </th>
      <th>
        Procedimento
      </th>
      <th>
        Justificativa
      </th>
      <th>
        Status
      </th>
    </tr>
  </thead>
  <tbody>
    @foreach (ItensLoteGlosaIndex item in Model)
    {
    <tr>
      <td>
        <input class="isSelected" data-codigoproc="@item.CodigoProcDemonstrativo" data-codigoguiaatendimento="@item.CodigoGuiaAtendimento" data-val="true" type="checkbox" value="true">
      </td>
      <td>
        @item.NumeroGuia
      </td>
      <td>
        @item.DescricaoGlosa
      </td>
      <td>
        @item.TotalFaturado.ToString("C2", CultureInfo.CreateSpecificCulture("pt-BR"))
      </td>
      <td>
        @item.TotalApresentado.ToString("C2", CultureInfo.CreateSpecificCulture("pt-BR"))
      </td>
      <td>
        @item.TotalGlosado.ToString("C2", CultureInfo.CreateSpecificCulture("pt-BR"))
      </td>
      <td>
        @item.TotalPago.ToString("C2", CultureInfo.CreateSpecificCulture("pt-BR"))
      </td>
      <td>
        @string.Format("{0} - {1}", item.CodigoProcedimento, item.DescricaoProcedimento)
      </td>
      <td>
        @item.Justificativa
      </td>
      <td>
        @item.DescricaoStatus
      </td>
    </tr>
    }
  </tbody>
</table>
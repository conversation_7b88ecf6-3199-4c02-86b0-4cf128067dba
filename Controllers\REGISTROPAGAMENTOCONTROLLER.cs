﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using PagedList;
using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Controls;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Permissions;
using System.Web;
using System.Web.Mvc;

namespace RepasseConvenio.Controllers
{
  [Authorize]
  [Security("3CFEE760-C45F-4480-85F2-48F0783D31C8")]
  public class RegistroPagamentoController : LibController
  {
    private RegistroPagamentoServices RegistroPagamentoServices
    {
      get
      {
        if (_RegistroPagamentoServices == null)
          _RegistroPagamentoServices = new RegistroPagamentoServices(ContextoUsuario.UserLogged);

        return _RegistroPagamentoServices;
      }
    }
    private RegistroPagamentoServices _RegistroPagamentoServices;


    [Security("1823E0A7-A629-4C5F-BF0E-C4475649E181")]
    public ActionResult Index(int page = 1, string search = "", string data = "")
    {
      try
      {
        RegistroPagamentoIndex registro = new RegistroPagamentoIndex();
        registro.ListRegistro = RegistroPagamentoServices.Get(page, search, data);

        return View(registro);
      }
      catch (Exception)
      {
        throw;
      }
    }

    [HttpPost]
    [Security("1823E0A7-A629-4C5F-BF0E-C4475649E181")]
    public ActionResult Index(RegistroPagamentoIndex registro)
    {
      try
      {
        if ((!registro.DataAte.HasValue && !registro.DataDe.HasValue) && (registro.MedicoSelect == null || !registro.CodigoMedico.HasValue))
          throw new CustomException("É necessário preencher os campos de Data/Medico.");

        if (registro.DataDe.HasValue && registro.DataAte.HasValue)
        {
          if (registro.DataAte.Value > registro.DataDe.Value.AddDays(30))
            registro.DataAte = registro.DataDe.Value.AddDays(30);
        }

        List<ExtratoMedicoRegistroPagamentoModel> ListextratoMedicoRegistros = RegistroPagamentoServices.GetExtratoPagamento(registro.DataDe, registro.DataAte, registro.CodigoMedico);
        new RegistroPagamentoServices(ContextoUsuario.UserLogged).GerarRegistroPagamento(ListextratoMedicoRegistros);

        MessageListToast.Add(new Message(MessageType.Success, "Registro pagamento processado com sucesso."));
        return RedirectToAction("Index");
      }
      catch (CustomException ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return RedirectToAction("Index");
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, "Tivemos uma falha na sua solicitação."));
        return RedirectToAction("Index");
      }
    }

    [Security("A8259452-9CA8-4164-8FC0-11492171EC1E")]
    public JsonResult RealizarPagamento(DateTime DataPagamento, List<int> ListIdRegistro)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        if (ListIdRegistro.Count == 0)
          throw new CustomException("Nenhum Registro pagamento foi selecionado.");

        RegistroPagamentoServices.RealizarPagamento(DataPagamento, ListIdRegistro);

        retornoAjax.Titulo = "Sucesso";
        retornoAjax.Mensagem = "Pagamento realizado com sucesso.";
        retornoAjax.Erro = false;
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
      catch (CustomException ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
      catch (Exception ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
    }
    [Security("A54DB565-2D93-451A-8D6B-3E3F53E1894E")]
    public JsonResult Delete(List<int> idsRegistro)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {

        if (idsRegistro == null || idsRegistro.Count == 0)
          throw new CustomException("Nenhum Registro pagamento foi selecionado.");

        RegistroPagamentoServices.DeleteRegistros(idsRegistro);

        retornoAjax.Titulo = "Sucesso";
        retornoAjax.Mensagem = "Registros de Pagamento deletados com sucesso.";
        retornoAjax.TipoMensagem = "success";
        retornoAjax.Erro = false;
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
      catch (Exception ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
    }
    [Security("6EC266AD-4D91-453D-894F-0B5D0FCA82A2")]
    [HttpGet]
    public PartialViewResult GetPartialGrid(int? Page,string search,string data)
    {
      try
      {
        Page = Page ?? 1;
        IPagedList<RegistroPagamentoModel> Lista = RegistroPagamentoServices.Get(Page.Value,search,data);
        return PartialView("_PartialGrid", Lista);
      }
      catch (Exception ex)
      {
        throw;
      }
    }

    [Security("6DE576D0-A734-4B85-B214-D6BBC465F9E2")]
    [HttpGet]
    public PartialViewResult GetExtratos(int id)
    {
      try
      {
        List<ExtratoMedicoRegistroPagamento> Lista = RegistroPagamentoServices.GetExtratos(id);

        if (Lista == null)
          Lista = new List<ExtratoMedicoRegistroPagamento>();

        return PartialView("_GridDetalhesExtratos", Lista);
      }
      catch (Exception)
      {
        throw;
      }
    }
  }
}
﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Services;
using System.ComponentModel;

namespace RepasseConvenio.Models
{
  public class JustificativaGlosaModel
  {
    public int Codigo { get; set; }
    [DisplayName("Código")]
    public string CodigoJustificativa { get; set; }
    [DisplayName("Descrição")]
    public string Descricao { get; set; }
  }
  public class JustificativaItemGlosaModel
  {
    public int CodigoJustificativa { get; set; }
    [DisplayName("Comentário")]
    public string Comentario { get; set; }
    [DisplayName("Selecione a Justificativa")]
    [URLSelect("Select2/GetJustificativaSelect")]
    [PlaceHolderAttr("Selecione a Justificativa")]
    public Select2Model JustificativaSelect
    {
      get
      {
        JustificativaGlosaService justificativaService = new JustificativaGlosaService();
        return justificativaService.GetById(this.CodigoJustificativa).ToSelect2Model();
      }
      set
      {
        if (value != null && !string.IsNullOrEmpty(value.id) && !value.id.Equals("0"))
          this.CodigoJustificativa = int.Parse(value.id);
      }
    }
  }

  public class AnaliseDetalhada
  {
    public int CodigoJustificativa { get; set; }
    [DisplayName("Comentário")]
    public string ComentarioAnaliseDetalhada { get; set; }
    [DisplayName("Selecione a Justificativa")]
    [URLSelect("Select2/GetJustificativaSelect")]
    [PlaceHolderAttr("Selecione a Justificativa")]
    public Select2Model JustificativaAnaliseDetalhadaSelect
    {
      get
      {
        JustificativaGlosaService justificativaService = new JustificativaGlosaService();
        return justificativaService.GetById(this.CodigoJustificativa).ToSelect2Model();
      }
      set
      {
        if (value != null && !string.IsNullOrEmpty(value.id) && !value.id.Equals("0"))
          this.CodigoJustificativa = int.Parse(value.id);
      }
    }
  }


  public static class JustificativaGlosaConversion
  {
    public static R_JustificativaGlosa JustificativaToCreate(this JustificativaGlosaModel model)
    {
      R_JustificativaGlosa entity = new R_JustificativaGlosa();
      entity.JG_Codigo = model.CodigoJustificativa;
      entity.JG_Descricao = model.Descricao;

      return entity;
    }
    public static R_JustificativaGlosa JustificativaToEdit(this R_JustificativaGlosa entity, JustificativaGlosaModel model)
    {
      entity.JG_Codigo = model.CodigoJustificativa;
      entity.JG_Descricao = model.Descricao;

      return entity;
    }

    public static JustificativaGlosaModel OffEntityIRToModel(this R_JustificativaGlosa entity)
    {
      JustificativaGlosaModel model = new JustificativaGlosaModel();
      model.Codigo = entity.JG_Id; 
      model.CodigoJustificativa = entity.JG_Codigo;
      model.Descricao = entity.JG_Descricao;

      return model;
    }
  }
}
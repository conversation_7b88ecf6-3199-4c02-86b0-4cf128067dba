﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using PagedList;
using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Controls;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Infrastructure.Mensagem;
using RepasseConvenio.Models;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace RepasseConvenio.Controllers
{
  [Authorize]
  [Security("E725E100-8A62-4256-9448-2D8EC0AAE43A")]

  public class MedicoController : LibController
  {
    private MedicoService MedicoService
    {
      get
      {
        if (_MedicoService == null)
          _MedicoService = new MedicoService(ContextoUsuario.UserLogged);

        return _MedicoService;
      }
    }
    private MedicoService _MedicoService;
    [Security("A92ECC3D-68B8-49BC-989D-85A67729935B")]

    public ActionResult Index(int pagina = 1)
    {
      try
      {
        MedicoIndex medicoIndex = TempData["MedicoIndex"] as MedicoIndex;
        if (medicoIndex != null)
          return View(medicoIndex);

        medicoIndex = new MedicoIndex();
        medicoIndex.ListaMedicoModel = new MedicoService().Get(pagina);
        return View(medicoIndex);
      }
      catch (Exception)
      {
        throw;
      }
    }
    [HttpGet]
    public ActionResult Detalhes(int? IdMedico)
    {
      try
      {
        MedicoModel medicoModel = new MedicoModel();
        medicoModel = new MedicoService().DetalhesMedico(IdMedico.Value);
          return View(medicoModel);
      
      }
      catch (Exception)
      {
        throw;
      }
    }

    [HttpPost]
    public ActionResult Detalhes(MedicoModel medicoModel)
    {
      try
      {
        MedicoService.EditMedicoDetalhes(medicoModel);
        MessageListToast.Add(new Message(MessageType.Success, "Detalhes do médico atualizado com suceso.", "12000"));
        return RedirectToAction("Index");

      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message, "12000"));
        return View(medicoModel);
      }
      
    }

    [HttpGet]
    public ActionResult MedicoSearch(MedicoIndex medicoIndex, int pagina = 1)
    {
      try
      {
        medicoIndex.ListaMedicoModel = new MedicoService().MedicoSearch(medicoIndex, pagina);
        TempData["MedicoIndex"] = medicoIndex;
        return RedirectToAction("Index");
      }
      catch (Exception ex)
      {
        return RedirectToAction("Index");
      }
    }

    [HttpPost]
    public JsonResult IntegraMedico(string ConnectionId)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        MedicoService medicoService = new MedicoService();
        medicoService.IntegraMedico(ConnectionId);
        retornoAjax.Erro = false;
        retornoAjax.Titulo = "Sucesso";
        retornoAjax.Mensagem = "Médicos integrados com sucesso.";
        return Json(retornoAjax);
      }
      catch (CustomException ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        return Json(retornoAjax);
      }
      catch (Exception ex)
      {
        retornoAjax.Erro = false;
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = CustomMensagens.MensagemErroException;
        return Json(retornoAjax);
      }
    }
  }
}
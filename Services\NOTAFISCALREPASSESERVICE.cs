﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Models;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Transactions;
using System.Web;

namespace RepasseConvenio.Services
{
  public class NotaFiscalRepasseService : ServiceBase
  {
    public NotaFiscalRepasseService()
   : base()
    { }
    public NotaFiscalRepasseService(RepasseEntities Contexto)
       : base(Contexto)
    { }

    public NotaFiscalRepasseService(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public NotaFiscalRepasseService(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public List<NotaFiscalRepasseModel> GetAllNotaFiscaRepasseModel(int IdRepasse)
    {
      string query = @"SELECT
                      NF_Id AS NFM_Codigo,
                      NF_Numero AS NFM_Numero,
                      NF_Serie AS NFM_Serie,
                      NF_DataEmissao AS NFM_DataEmissao,
                      NF_ValorTotal AS NFM_ValorTotal,
                      NF_ValorLiquido AS NFM_ValorLiquido,
                      NF_Impostos AS NFM_Impostos,
                      NF_DataCriacao AS NFM_DataCriacao,
                      NF_IdUsuarioCriacao AS NFM_CodigoUsuarioCriacao,
                      NF_IdRepasse AS NFM_CodigoRepasse
                      FROM R_NotaFiscaRepasse
                      WHERE NF_IdRepasse = @IdRepasse";

      return Contexto.Database.SqlQuery<NotaFiscalRepasseModel>(query, new SqlParameter("@IdRepasse", IdRepasse)).ToList();
    }

    public NotaFiscalRepasseModel GetRepasseModelById(int Id)
    {
      string query = @"SELECT
                      NF_Id AS NFM_Codigo,
                      NF_Numero AS NFM_Numero,
                      NF_Serie AS NFM_Serie,
                      NF_DataEmissao AS NFM_DataEmissao,
                      NF_ValorTotal AS NFM_ValorTotal,
                      NF_ValorLiquido AS NFM_ValorLiquido,
                      NF_Impostos AS NFM_Impostos,
                      NF_DataCriacao AS NFM_DataCriacao,
                      NF_IdUsuarioCriacao AS NFM_CodigoUsuarioCriacao,
                      NF_IdRepasse AS NFM_CodigoRepasse
                      FROM R_NotaFiscaRepasse
                      WHERE 
                      NF_Id = @Id
                      ";

      return Contexto.Database.SqlQuery<NotaFiscalRepasseModel>(query, new SqlParameter("@Id", Id)).FirstOrDefault();
    }

    public R_NotaFiscaRepasse GetById(int id)
    {
      return Contexto.R_NotaFiscaRepasse.Where(a => a.NF_Id == id).FirstOrDefault();
    }

    public R_NotaFiscaRepasse GetByIdRepasse(int IdRepasse)
    {
      string query = @"SELECT
                         *
                        FROM R_NotaFiscaRepasse NFR
                        WHERE NFR.NF_IdRepasse = @IdRepasse";
      return Contexto.Database.SqlQuery<R_NotaFiscaRepasse>(query, new SqlParameter("@IdRepasse", IdRepasse)).FirstOrDefault();
    }


    public R_NotaFiscaRepasse Create(NotaFiscalRepasseModel repasseModel)
    {
      if (!repasseModel.ToCadastroValido())
        throw new CustomException("Gentileza preencher todos os campos.");

      if (HaveNota(repasseModel.NFM_CodigoRepasse))
        throw new CustomException("Só pode ser adicionado uma nota por repasse.");

      R_NotaFiscaRepasse entity = repasseModel.ToNotaFiscaRepasseCreate(User.IdUsuario);
      Create(entity);
      return entity;
    }

    public R_NotaFiscaRepasse CreateImportPlanilhaRepasse(NotaFiscalRepasseModel repasseModel)
    {
      if (!repasseModel.ToCadastroValido())
        throw new CustomException("Gentileza preencher todos os campos.");

      if (HaveNota(repasseModel.NFM_CodigoRepasse))
        throw new CustomException("Só pode ser adicionado uma nota por repasse.");

      R_NotaFiscaRepasse entity = repasseModel.ToNotaFiscaRepasseCreate(User.IdUsuario);
      Create(entity);
      return entity;
    }

    public void Edit(NotaFiscalRepasseModel repasseModel)
    {
      if (!repasseModel.ToCadastroValido())
        throw new CustomException("Gentileza preencher todos os campos.");

      R_NotaFiscaRepasse entity = GetById(repasseModel.NFM_Codigo);

      if (entity != null)
      {
        entity = repasseModel.ToNotaFiscaRepasseEdit(entity);
        Edit(entity);
      }
    }
    public void Delete(int id)
    {
      R_NotaFiscaRepasse entity = GetById(id);

      using (TransactionScope scope = new TransactionScope(TransactionScopeOption.RequiresNew))
      {
        if (entity != null)
        {
          string query = @"DELETE FROM R_GuiaNotaFiscal WHERE GNF_IdNotaFiscal = @id";
          Contexto.Database.ExecuteSqlCommand(query, new SqlParameter("@id", id));
          Delete(entity);

          if (GetQuantidadeNotaFiscalByRepase(entity.NF_IdRepasse) == 0)
          {

            RepasseService repasseService = new RepasseService(Contexto);
            R_Repasse repasse = repasseService.GetByIdSQL(entity.NF_IdRepasse);
            repasse.R_NF = false;
            Edit(repasse);
          }

        }
        scope.Complete();
      }

    }

    public int GetQuantidadeNotaFiscalByRepase(int IdRepasse)
    {
      string query = @"SELECT count(1) FROM R_NotaFiscaRepasse WHERE NF_IdRepasse = @IdRepasse";

      return Contexto.Database.SqlQuery<int>(query, new SqlParameter("@IdRepasse", IdRepasse)).FirstOrDefault();
    }

    public bool HaveNota(int IdRepasse)
    {
      string query = @"SELECT
                        Count(1)
                       FROM R_NotaFiscaRepasse 
                       WHERE NF_IdRepasse = @IdRepasse";

      int Quantidade = Contexto.Database.SqlQuery<int>(query, new SqlParameter("@IdRepasse", IdRepasse)).FirstOrDefault();

      if (Quantidade == 0)
        return false;
      else
        return true;
    }

    public R_NotaFiscaRepasse GetNotaFiscaRepasseByIdRepasse(int IdRepasse)
    {
      string query = @"SELECT
                        *
                        FROM R_NotaFiscaRepasse 
                        WHERE NF_IdRepasse = @IdRepasse";

      return Contexto.Database.SqlQuery<R_NotaFiscaRepasse>(query, new SqlParameter("@IdRepasse", IdRepasse)).FirstOrDefault();
    }

    public bool ValidaPreenchimento(R_NotaFiscaRepasse notaFiscaRepasse)
    {
      if (string.IsNullOrEmpty(notaFiscaRepasse.NF_Numero)
      || string.IsNullOrEmpty(notaFiscaRepasse.NF_Serie)
      || notaFiscaRepasse.NF_DataCriacao == DateTime.MinValue
      || notaFiscaRepasse.NF_DataEmissao == DateTime.MinValue
      || notaFiscaRepasse.NF_ValorTotal == 0
      || notaFiscaRepasse.NF_ValorLiquido == 0)
        return false;
      else return true;
    }

    public bool ValidaPreenchimento(int IdRepasse)
    {
      R_NotaFiscaRepasse notaFiscaRepasse = GetByIdRepasse(IdRepasse);
      if (string.IsNullOrEmpty(notaFiscaRepasse.NF_Numero)
      || string.IsNullOrEmpty(notaFiscaRepasse.NF_Serie)
      || notaFiscaRepasse.NF_DataCriacao == DateTime.MinValue
      || notaFiscaRepasse.NF_DataEmissao == DateTime.MinValue
      || notaFiscaRepasse.NF_ValorTotal == 0
      || notaFiscaRepasse.NF_ValorLiquido == 0)
        return false;
      else return true;
    }
  }
}
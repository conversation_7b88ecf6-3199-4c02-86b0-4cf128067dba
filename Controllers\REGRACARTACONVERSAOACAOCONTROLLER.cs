﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Models;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace RepasseConvenio.Controllers
{
  [Authorize]
  public class RegraCartaConversaoAcaoController : Controller
  {
    [HttpGet]
    public JsonResult GetTypoAcao(string idacao)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        RegraCartaConversaoAcaoServices regraCartaConversaoAcaoServices = new RegraCartaConversaoAcaoServices();
        string tipoCampo = regraCartaConversaoAcaoServices.GetTipoAcao(Convert.ToInt32(idacao));

        retornoAjax.Erro = false;
        retornoAjax.Mensagem = tipoCampo;
        retornoAjax.Titulo = "Sucesso.";
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
      catch (CustomException ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Titulo = "Error.";
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
      catch (Exception ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.Mensagem = "Ocorreu uma falha na sua solicitação.";
        retornoAjax.Titulo = "Error.";
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
    }
  }
}
﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;

namespace RepasseConvenio.Models
{
  public class ClassificacaoRepasseModel
  {

    public int Id { get; set; }
    
    [Range(0, 999, ErrorMessage = "Não pode ser mais de 999!")]
    public int Enum { get; set; }

    public int Ordem { get; set; }

    [Required(ErrorMessage = "O que campo descrição é requerido!")]
    public string Descricao { get; set; }

    public String TipoLancamento { get; set; }

    [Required(ErrorMessage = "O que campo Tipo Lançamento é requerido!")]
    public EnumTipoLancamento? enumTipoLancamento { 
      get 
      {
        if (!String.IsNullOrEmpty(TipoLancamento))
        {
          var teste = EnumTipoLancamento.Parse(typeof(EnumTipoLancamento), this.TipoLancamento);
          return (EnumTipoLancamento)teste;
        }
        return null;
      }
      set 
      {
        this.TipoLancamento = value.ToString();
      }
    }

    
    public bool? BaseIRRP { get; set; }
    public bool BaseIRRPFloat { 
      get 
      { 
        if (BaseIRRP.HasValue) {
          return (bool)BaseIRRP;  
        }
        return false;
      } 
      set {
        this.BaseIRRP = value;
      }
    }




    public bool? BaseINSS { get; set; }
    public bool BaseINSSFloat
    {
      get
      {
        if (BaseINSS.HasValue)
        {
          return (bool)BaseINSS;
        }
        return false;
      }
      set
      {
        this.BaseINSS = value;
      }
    }



    public bool? BasePISConfins { get; set; }
    public bool BasePISConfinsFloat
    {
      get
      {
        if (BasePISConfins.HasValue)
        {
          return (bool)BasePISConfins;
        }
        return false;
      }
      set
      {
        this.BasePISConfins = value;
      }
    }


    public bool? BaseISSQN { get; set; }
    public bool BaseISSQNFloat
    {
      get
      {
        if (BaseISSQN.HasValue)
        {
          return (bool)BaseISSQN;
        }
        return false;
      }
      set
      {
        this.BaseISSQN = value;
      }
    }

  }

  public static class ClassificacaoRepasseModelConvertions
  {
    public static ClassificacaoRepasseModel ToModel(this R_ClassificacaoRepasse entity)
    {
      ClassificacaoRepasseModel model = new ClassificacaoRepasseModel
      {
        Id = entity.CR_Id,
        Enum = entity.CR_Enum,
        Ordem = entity.CR_Ordem,
        Descricao = entity.CR_Descricao,
        TipoLancamento = entity.CR_TipoLancamento,
        BaseINSS = entity.CR_BaseINSS,
        BaseIRRP = entity.CR_BaseIRRF,
        BaseISSQN = entity.CR_BaseISSQN,
        BasePISConfins = entity.CR_BasePISConfinsCSLL
      };
      return model;
    }

    public static R_ClassificacaoRepasse ToDB(this ClassificacaoRepasseModel model)
    {
      R_ClassificacaoRepasse entity = new R_ClassificacaoRepasse
      {
        CR_Id = model.Id,
        CR_Enum = model.Enum,
        CR_Ordem = model.Ordem,
        CR_Descricao = model.Descricao,
        CR_TipoLancamento = model.TipoLancamento,
        CR_BaseINSS = model.BaseINSS,
        CR_BaseIRRF = model.BaseIRRP,
        CR_BaseISSQN = model.BaseISSQN,
        CR_BasePISConfinsCSLL = model.BasePISConfins
      };
      return entity;
    }
  }
}
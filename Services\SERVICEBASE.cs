﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Configuration;
using System.Data.Entity.Core.Objects;
using System.Data.Entity.Infrastructure;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Data;
using System.Data.SqlClient;
using System.Data.Entity;
using System.Text.RegularExpressions;
using System.Web.ModelBinding;

namespace RepasseConvenio.Services
{
  public class ServiceBase
  {
    public ServiceBase()
    {
      this._contexto = new RepasseEntities();
    }

    public ServiceBase(RepasseEntities contexto)
    {
      this._contexto = contexto;
    }

    public ServiceBase(UsuarioLogado user)
    {
      this._User = user;
      this._contexto = new RepasseEntities();
    }

    public ServiceBase(Type type)
    {
      this._contexto = new RepasseEntities();
      this._TypeEntity = type;
    }

    public ServiceBase(UsuarioLogado user, RepasseEntities contexto)
    {
      this._User = user;
      this._contexto = contexto;
    }

    protected static int PageSize = 30;

    public static Semaphore semaphore;

    public static SqlCommand Command;

    public RepasseEntities Contexto
    {
      get
      {
        return this._contexto;
      }
      private set
      {
        this._contexto = value;
      }
    }
    private RepasseEntities _contexto;

    public Type TypeEntity
    {
      get
      {
        return this._TypeEntity;
      }
      set
      {
        this._TypeEntity = value;
      }
    }
    private Type _TypeEntity;

    public UsuarioLogado User
    {
      get
      {
        return this._User;
      }
      set
      {
        this._User = value;
      }
    }
    private UsuarioLogado _User;

    public static DataSet Select(string query, Dictionary<string, object> dictionary = null)
    {
      try
      {
        Command.CommandText = query;

        if (dictionary != null)
        {
          foreach (var item in dictionary)
          {
            Command.Parameters.AddWithValue(item.Key, item.Value);
          }
        }

        SqlDataAdapter da = new SqlDataAdapter();
        DataSet ds = new DataSet();
        da.SelectCommand = Command;
        da.Fill(ds);

        return ds;
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }
    public virtual int GetQtdeRegistroPagina()
    {
      int qtde; int.TryParse(ConfigurationManager.AppSettings["QtdeRegistroPagina"], out qtde);
      return qtde;
    }

    #region [Prefixos Codigos]
    protected string PrefixoDocumento = ConfigurationManager.AppSettings["PrefixoDocumento"];
    #endregion

    public virtual DataMapperPaging<T> ExecuteQueryable<T>(IQueryable<T> queryable, Expression<Func<T, object>> orderBy, int? pageNumber)
    {
      int pageAux = (pageNumber ?? 1) - 1;

      DataMapperPaging<T> dataMapper = new DataMapperPaging<T>();
      dataMapper.CurrentPageIndex = pageAux;
      dataMapper.CurrentPageIndexPagedList = (pageNumber ?? 1);
      dataMapper.PageSize = this.GetQtdeRegistroPagina();
      dataMapper.PageCount = queryable.Count();

      //(SqlCommand)dataContext.GetCommand(query)

      var list = queryable.OrderBy(orderBy).Skip(pageAux * this.GetQtdeRegistroPagina()).Take(this.GetQtdeRegistroPagina()).ToList();
      //List<T> list = queryable.ToList();

      dataMapper.SourceView = list;

      return dataMapper;
    }

    public virtual DataMapperPaging<T> ExecuteQueryableSQL<T>(string queryable, string queryCount, int? pageNumber, bool pagination = true)
    {
      int pageAux = (pageNumber ?? 1) - 1;
      DataMapperPaging<T> dataMapper = new DataMapperPaging<T>();
      dataMapper.CurrentPageIndex = ((pageNumber ?? 1) - 1) * this.GetQtdeRegistroPagina();
      dataMapper.CurrentPageIndexPagedList = (pageNumber ?? 1);
      dataMapper.PageSize = this.GetQtdeRegistroPagina();
      dataMapper.PageCount = Contexto.Database.SqlQuery<int>(queryCount).FirstOrDefault();

      if ((dataMapper.PageCount / dataMapper.PageSize) < (dataMapper.CurrentPageIndexPagedList - 1))
      {
        dataMapper.CurrentPageIndexPagedList = 1;
        dataMapper.CurrentPageIndex = 0 * this.GetQtdeRegistroPagina();
        pageAux = 0;
      }

      if (pagination)
      {
        queryable = string.Format(@"{0}
                                    OFFSET {1} ROWS
                                    FETCH NEXT {2} ROWS ONLY
                                    OPTION (MAXRECURSION 1000)
                                   ", queryable
                                   , pageAux * this.GetQtdeRegistroPagina()
                                   , this.GetQtdeRegistroPagina());
      }

      var list = Contexto.Database.SqlQuery<T>(queryable).ToList();
      dataMapper.SourceView = list;

      return dataMapper;
    }

    public virtual DataMapperPaging<T> ExecuteQueryableOutOrderBy<T>(IQueryable<T> queryable, int? pageNumber)
    {
      int pageAux = (pageNumber ?? 1) - 1;

      DataMapperPaging<T> dataMapper = new DataMapperPaging<T>();
      dataMapper.CurrentPageIndex = pageAux;
      dataMapper.CurrentPageIndexPagedList = (pageNumber ?? 1);
      dataMapper.PageSize = this.GetQtdeRegistroPagina();
      dataMapper.PageCount = queryable.Count();

      var list = queryable.Skip(pageAux * this.GetQtdeRegistroPagina()).Take(this.GetQtdeRegistroPagina()).ToList();

      dataMapper.SourceView = list;

      return dataMapper;
    }

    public void Create<T>(T model) where T : class
    {
      Contexto.Entry(Convert.ChangeType(model, model.GetType())).State = EntityState.Added;
      Contexto.SaveChanges();
    }

    public void Edit<T>(T model) where T : class
    {
      Contexto.Entry(Convert.ChangeType(model, model.GetType())).State = EntityState.Modified;
      Contexto.SaveChanges();
    }

    public void Delete<T>(T model) where T : class
    {
      Contexto.Entry(Convert.ChangeType(model, model.GetType())).State = EntityState.Deleted;
      Contexto.SaveChanges();
    }

    public virtual void BeforeSave(List<string> erros, object model)
    {
      if (erros != null && erros.Count() > 0)
        throw new CustomException(erros, CustomException.TipoException.Erro);
    }

    public virtual void AfterSave(List<string> erros, object model)
    {
      if (erros != null && erros.Count() > 0)
        throw new CustomException(erros, CustomException.TipoException.Erro);
    }


    public virtual string GerarCodigo(int numeroCodigo, string Prefixo = "")
    {
      string Codigo = numeroCodigo.ToString().PadLeft(10, '0');
      return Prefixo + Codigo;
    }

    public List<LogAux> GetCamposModificados<T>(List<DbEntityEntry> Entries)
    {
      DateTime DateChanged = DateTime.Now;
      List<LogAux> logdetalhe = new List<LogAux>();
      foreach (var entry in Entries)
      {
        try
        {
          var type = typeof(T);

          var metadataType = type.GetCustomAttributes(typeof(MetadataTypeAttribute), true)
              .OfType<MetadataTypeAttribute>().FirstOrDefault();
          var metaData = (metadataType != null)
              ? ModelMetadataProviders.Current.GetMetadataForType(null, metadataType.MetadataClassType)
              : ModelMetadataProviders.Current.GetMetadataForType(null, type);
          var propertMetaData = metaData.Properties
              .Where(e =>
              {
                var attribute = metaData.ModelType.GetProperty(e.PropertyName)
                    .GetCustomAttributes(typeof(DisplayNameAttribute), false)
                    .FirstOrDefault() as DisplayNameAttribute;
                return attribute != null;
              })
              .ToList();


          foreach (var propName in entry.CurrentValues.PropertyNames)
          {
            var current = entry.CurrentValues[propName] != null ? entry.CurrentValues[propName].ToString() : "";
            var original = entry.GetDatabaseValues().GetValue<object>(propName) != null ? entry.GetDatabaseValues().GetValue<object>(propName).ToString() : "";
            if (!current.Equals(original))
            {
              LogAux logaux = new LogAux();
              logaux.PropertyName = propName;
              logaux.DisplayName = propertMetaData.Where(a => a.PropertyName.Equals(propName)).FirstOrDefault() != null ? propertMetaData.Where(a => a.PropertyName.Equals(propName)).FirstOrDefault().DisplayName : propName;
              logaux.OldValue = original;
              logaux.NewValue = current;
              logdetalhe.Add(logaux);
            }
          }

        }
        catch (Exception ex)
        {
          throw new Exception(ex.Message);
        }
      }
      return logdetalhe;
    }


  }
}
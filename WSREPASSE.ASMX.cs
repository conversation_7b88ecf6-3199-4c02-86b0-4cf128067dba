﻿using RepasseConvenio.Models;
using RepasseConvenio.Services;
using System;
using System.Web.Services;

namespace RepasseConvenio
{
  [WebService(Namespace = "http://tempuri.org/")]
  [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
  [System.ComponentModel.ToolboxItem(false)]
  [System.Web.Script.Services.ScriptService]
  public class WSRepasse : WebService
  {
    [WebMethod]
    public string APDiretores(WSEntrada WS)
    {
      if (!WS.Hash.Equals("7B5906F7-AE2A-447A-9D69-902793BB29B9"))
        throw new Exception("Falha");

      return new WSRepasseServices().ApuracaoProlaboreDiretores();
    }

    [WebMethod]
    public string EnviaLotesVencendo(WSEntrada WS)
    {
      if (!WS.Hash.Equals("73258B9C-F635-4A68-AC94-2CB2A986627F"))
        throw new Exception("Falha");

      return new WSRepasseServices().EnviaLotesVencendo();
    }

    [WebMethod]
    public string ItensRecorrente(WSEntrada WS)
    {
      if (!WS.Hash.Equals("BD9221A8-B2D4-4579-BBCD-A11F194C9786"))
        throw new Exception("Falha");

      return new WSRepasseServices().ItensRecorrentes();
    }

    [WebMethod]
    public string LoteGlosasVencidas(WSEntrada WS)
    {
      if (!WS.Hash.Equals("83D1D27F-0969-43EC-A5F6-752B0334CE36"))
        throw new Exception("Falha");

      return new WSRepasseServices().ValidaLotesGlosaVencidas();
    }

    [WebMethod]
    public string LembreteVencientoGlosa(WSEntrada WS)
    {
      if (!WS.Hash.Equals("C3A5F513-F29B-4366-B219-E3847B4F3DE2"))
        throw new Exception("Falha");

      return new WSRepasseServices().LembreteVencientoGlosa();
    }
  }
}

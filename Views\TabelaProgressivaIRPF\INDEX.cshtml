﻿@using RepasseConvenio.Infrastructure

@using RepasseConvenio.Models
@using PagedList
@using PagedList.Mvc
@model IPagedList<TabelaProgressivaIRPFModel>

@{
  ViewBag.Title = "Tabela IRPF";
}

@Styles.Render("~/Views/TabelaProgressivaIRPF/TabelaIRPF.css")
@Scripts.Render("~/Views/TabelaProgressivaIRPF/TabelaIRPF.js?ia=b")


<div class="row">
  <section class="col-lg-12 connectedSortable ui-sortable">

    <div class="card">
      <div class="card-header ui-sortable-handle">
        <div class="row" style="justify-content:space-between;">
          <h3 class="card-title">
            <i class="fas fa-book mr-1"></i>
            Tabela IRPF
          </h3>
          <div class="card-tools">
            @if (ContextoUsuario.UserLogged.IsInPermissao(Guid.Parse("20AB9B79-9EC6-4AF4-BD4C-3512E36E89CB")))
            {

              <button id="CreateGRD" onclick="window.location.href='@Url.Action("Create", "TabelaProgressivaIRPF")'" style="padding: 0.1rem 0.1rem;" type="button" class="btn btn-sm btn-block btn-outline-primary">
                <i class="fas fa-plus"></i> Novo
              </button>
            }
          </div>
        </div>
      </div>

      <div class="card-body">
        <div class="tab-content p-0">
          <div class="box-body">
            <div class="card-acoes col-4">
              @if (ContextoUsuario.UserLogged.IsInPermissao(Guid.Parse("D713ADC3-1E9D-40B1-9D82-FFA1C4762E65")))
              {

                <a class="btn btn-block btn-outline-primary disabled" id="IrpfEdit"> Editar </a>
              }
              @if (ContextoUsuario.UserLogged.IsInPermissao(Guid.Parse("D169537E-AFFB-4D69-ADCC-0ABDEBEA014E")))
              {

                <a class="btn btn-block btn-outline-primary disabled" id="IrpfDelete"> Excluir </a>
              }
            </div>

            <div class="row">
              <div class="col-md-12 table-responsive p-0">
                <table id="TableTela" class="table-striped table table-sm table-hover text-nowrap">
                  <thead>
                    <tr>
                      <th>
                        Descrição
                      </th>
                      <th>
                        Data De
                      </th>
                      <th>
                        Data Até
                      </th>
                      <th>
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    @foreach (TabelaProgressivaIRPFModel item in Model)
                    {
                      <tr class="TrSelecionavel" data-codigoirpf="@item.Codigo">
                        <td>
                          @item.Descricao
                        </td>
                        <td>
                          @item.DtInicioVigencia.ToString("dd/MM/yyyy")
                        </td>
                        <td>
                          @if (item.DtFimVigencia.HasValue)
                          {
                            @item.DtFimVigencia.Value.ToString("dd/MM/yyyy")
                          }
                        </td>
                        @*<td class="pull-rigth">
                            <a href="@Url.Action("Edit", "TabelaProgressivaIRPF", new { id = item.Codigo  })" class="btn btn-sm TIcones btn-outline-warning" title="Atualizar">
                              <i class="fa fa-edit"></i>
                            </a>
                            <a onclick="confirmaDelete('@String.Format("Deseja excluir a tabela {0}?",item.Descricao)', '@Url.Action("Delete", "TabelaProgressivaIRPF", new { id = item.Codigo })')" class="btn btn-sm TIcones btn-outline-danger" title="Excluir">
                              <i class="fa fa-trash"></i>
                            </a>
                          </td>*@
                      </tr>
                    }
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="box-footer with-border" style="padding: 0 10pt;">
        <div class="row">
          <div class="col-md-8 ">
            @Html.PagedListPager(Model, page => Url.Action("Index", new { page }))
          </div>
        </div>
      </div>
    </div>
  </section>
</div>
@*Html.Partial("_ModalNovaGRD", new ModalNovaGRD())*@
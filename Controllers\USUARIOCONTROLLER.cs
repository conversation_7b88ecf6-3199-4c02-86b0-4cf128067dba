﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Models;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace RepasseConvenio.Controllers
{
  [Authorize]
  public class UsuarioController : LibController
  {

    private UsuarioServices UsuarioServices
    {
      get
      {
        if (_UsuarioServices == null)
          _UsuarioServices = new UsuarioServices(ContextoUsuario.UserLogged);

        return _UsuarioServices;
      }
    }
    private UsuarioServices _UsuarioServices;

    public ActionResult Index(int? Page, string filtro)
    {
      int page = Page ?? 1;

      List<UsuarioModel> ListUser = UsuarioServices.Get(page, filtro);

      return View(ListUser);
    }

    public ActionResult Create()
    {
      try
      {
        return View();
      }
      catch (Exception ex)
      {
        throw;
      }
    }

    [HttpPost]
    public ActionResult Create(UsuarioModel model)
    {
      try
      {
        if (ModelState.IsValid)
        {
          UsuarioServices.Create(model);

          return RedirectToAction("Index");
        }
        return View();
      }
      catch (Exception)
      {
        throw;
      }
    }

    public ActionResult Edit(int Cod)
    {
      try
      {
        if (Cod == 0)
          throw new Exception("Código do usuário não informado");

        R_Usuario user = UsuarioServices.GetById(Cod);

        if (user == null)
          throw new Exception("Usuário não encontrado");

        UsuarioModel usuarioModel = new UsuarioModel()
        {
          Codigo = user.U_Id,
          CPF = user.U_CPF,
          TipoUsuario = user.U_IdTipoUsuario,
          Email = user.U_Email,
          Nome = user.U_Nome,
          Senha = user.U_Password
        };

        return View(user);
      }
      catch (Exception)
      {

        throw;
      }
    }

    [HttpPost]
    public ActionResult Edit(UsuarioModel model)
    {
      try
      {
        if (ModelState.IsValid)
        {
          UsuarioServices.Edit(model);

          return RedirectToAction("Index");
        }
        return View();
      }
      catch (Exception)
      {
        throw;
      }
    }
  }
}
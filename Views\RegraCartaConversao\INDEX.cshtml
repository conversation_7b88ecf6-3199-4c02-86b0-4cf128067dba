﻿@using RepasseConvenio.Models
@using PagedList
@using PagedList.Mvc;
@model RegraCartaConversaoCabecalhoIndex

@{
  ViewBag.Title = "Lista Carta Conversão";
}

<div class="row">
  <section class="col-lg-12 connectedSortable ui-sortable">
    <div class="card">
      <div class="card-header ui-sortable-handle">
        <div class="row" style="justify-content:space-between;">
          <h3 class="card-title">
            <i class="fas fa-user mr-1"></i>
            Carta Conversão
          </h3>
          <div class="card-tools">
            <a type="button" class="btn btn-info pull-right" href="@Url.Action("Create", "RegraCartaConversao", new { CodigoMedico = Model.CodigoMedico })">Nova regra</a>
          </div>
        </div>
      </div><!-- /.card-header -->
      <div class="card-body">
        <div class="tab-content p-0">
          <div class="box-body">
            <div class="row">
              <div class="col-md-12 table-responsive p-0">
                <table class="table table-sm table-striped table-hover text-nowrap">
                  <thead>
                    <tr>
                      <th>
                        Prioridade
                      </th>
                      <th>
                        Descricao
                      </th>
                      <th>
                        Empresa
                      </th>
                      <th>
                        Status
                      </th>
                      <th>
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    @foreach (var item in Model.ListaRegraCartaConversaoIndex)
                    {
                    <tr>
                      <td>
                        @item.Prioridade
                      </td>
                      <td>
                        @item.Descricao
                      </td>
                      <td>
                        @item.Empresa
                      </td>
                      <td>
                        @if (item.Valida)
                        {
                          <text> Válida </text>
                        }
                        else
                        {
                          <text> Inválida </text>
                        }
                      </td>
                      <td class="pull-rigth">
                        @if ((item.Prioridade.HasValue && item.Prioridade.Value != 0) || !item.Prioridade.HasValue)
                        {
                          <a href="@Url.Action("Edit", "RegraCartaConversao", new { IdRegraCartaConversao = item.Codigo  })" class="btn btn-warning" title="Editar Regra Conversão">
                            Editar
                          </a>
                        }
                      </td>
                    </tr>
                    }
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="box-footer with-border" style="padding: 0 10pt;">
        <div class="row">
          <div class="col-md-8 ">
            @Html.PagedListPager(Model.ListaRegraCartaConversaoIndex, pagina => Url.Action("Index", new { pagina }))
          </div>
        </div>
      </div>
    </div>
  </section>
</div>

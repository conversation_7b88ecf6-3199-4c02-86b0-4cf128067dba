﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using PagedList;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RepasseConvenio.Services
{
  public class GrauParticipacaoServices : ServiceBase
  {
    public GrauParticipacaoServices(RepasseEntities Contexto)
        : base(Contexto)
    { }
    public GrauParticipacaoServices()
       : base()
    { }

    public GrauParticipacaoServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public GrauParticipacaoServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }
    public int GetIdByCodigo(string codigo)
    {
      return Contexto.R_GrauParticipacao.Where(a => a.GP_Codigo.Equals(codigo)).Select(a=>a.GP_Id).FirstOrDefault();
    }

    public R_GrauParticipacao GetById(int Id)
    {
      return Contexto.R_GrauParticipacao.Where(a => a.GP_Id == Id).FirstOrDefault();
    }

    public List<R_GrauParticipacao> GetByTerm(string term)
    {
      if (string.IsNullOrEmpty(term))
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_GrauParticipacao");

        return Contexto.Database.SqlQuery<R_GrauParticipacao>(query).ToList();

      }
      else
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_GrauParticipacao
                                       WHERE GP_Nome LIKE @termo OR GP_Codigo LIKE @termo");

        return Contexto.Database.SqlQuery<R_GrauParticipacao>(query, new SqlParameter("@termo", string.Format("%{0}%", term))).ToList();

      }
    }
  }
}


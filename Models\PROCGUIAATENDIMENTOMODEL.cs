﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using Microsoft.SqlServer.Server;
using RepasseConvenio.WSPortalCooperado;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Web;

namespace RepasseConvenio.Models
{
  public class ProcGuiaAtendimentoModel
  {
    public int Codigo { get; set; }
    public int CodigoGuiaAtendimento { get; set; }
    public int SeqProcedimento { get; set; }
    public DateTime? DtRealizacao { get; set; }
    public string HrIniRealizacao { get; set; }
    public string HrFimRealizacao { get; set; }
    public string CodProcedimento { get; set; }
    public string DescProcedimento { get; set; }
    public int? QtdeRealizada { get; set; }
    public string CodViaAcesso { get; set; }
    public string CodTecUtilizada { get; set; }
    public decimal? PercFator { get; set; }
    public decimal? VlrUnit { get; set; }
    public decimal? VlrTotal { get; set; }
    public string DescTipoProcedimento { get; set; }
    public string CodTabANS { get; set; }
    public decimal? PercFatorFaturamento { get; set; }
    public bool Urgencia { get; set; }

  }

  public class ProcGuiaAtendimentoGrid
  {
    public DateTime Data { get; set; }

    public string HoraInicio { get; set; }

    public string HoraFim { get; set; }

    public bool Urgencia { get; set; }

    public string UrgenciaPersonalizada
    {
      get
      {
        return this.Urgencia ? "S" : "N";
      }
    }

    public string Procedimento { get; set; }

    public string Descricao { get; set; }

    public int Quantidade { get; set; }

    public string TipoProcedimento { get; set; }

    public string Via { get; set; }

    public string Tecnica { get; set; }

    public decimal Porcentagem { get; set; }

    public decimal ValorFaturado { get; set; }
  }
  public static class ProcGuiaAtendimentoModelConversions
  {
    public static R_ProcGuiaAtendimento ToProcGuiaAtendimentoCreate(this ProcGuiaAtendimentoModel model)
    {
      R_ProcGuiaAtendimento entity = new R_ProcGuiaAtendimento();
      //entity.PGA_Id = model.Codigo;
      entity.PGA_IdGuiaAtendimento = model.CodigoGuiaAtendimento;
      entity.PGA_SeqProcedimento = model.SeqProcedimento;
      entity.PGA_DtRealizacao = model.DtRealizacao;
      entity.PGA_HrIniRealizacao = model.HrIniRealizacao;
      entity.PGA_HrFimRealizacao = model.HrFimRealizacao;
      entity.PGA_CodProcedimento = model.CodProcedimento;
      entity.PGA_DescProcedimento = model.DescProcedimento;
      entity.PGA_QtdeRealizada = model.QtdeRealizada;
      entity.PGA_CodViaAcesso = model.CodViaAcesso;
      entity.PGA_CodTecUtilizada = model.CodTecUtilizada;
      entity.PGA_PercFator = model.PercFator;
      entity.PGA_VlrUnit = model.VlrUnit;
      entity.PGA_VlrTotal = model.VlrTotal;
      entity.PGA_DescTipoProcedimento = model.DescTipoProcedimento;
      entity.PGA_CodTabANS = model.CodTabANS;
      entity.PGA_PercFatorFaturamento = model.PercFatorFaturamento;
      entity.PGA_Urgencia = model.Urgencia;

      return entity;
    }

    public static R_ProcGuiaAtendimento ToProcGuiaAtendimentoCreate(this ProcedimentoRepasseWS model, int IdGuiaAtendimento)
    {
      R_ProcGuiaAtendimento entity = new R_ProcGuiaAtendimento();
      //entity.PGA_Id = model.Codigo;
      entity.PGA_IdGuiaAtendimento = IdGuiaAtendimento;
      entity.PGA_SeqProcedimento = model.SeqProcedimento;
      entity.PGA_DtRealizacao = model.DtRealizacao;
      entity.PGA_HrIniRealizacao = model.HrIniRealizacao;
      entity.PGA_HrFimRealizacao = model.HrFimRealizacao;
      entity.PGA_CodProcedimento = model.CodProcedimento;
      entity.PGA_DescProcedimento = model.DescProcedimento;
      entity.PGA_QtdeRealizada = model.QtdeRealizada;
      entity.PGA_CodViaAcesso = model.CodViaAcesso;
      entity.PGA_CodTecUtilizada = model.CodTecUtilizada;
      entity.PGA_PercFator = model.PercFator;
      entity.PGA_VlrUnit = model.VlrUnit;
      entity.PGA_VlrTotal = model.VlrTotal;
      entity.PGA_DescTipoProcedimento = model.DescTipoProcedimento;
      entity.PGA_CodTabANS = model.CodTabANS;
      entity.PGA_PercFatorFaturamento = model.PercFatorFaturamento;
      entity.PGA_Urgencia = model.Urgencia;
      entity.GA_ValorFaturado = model.ValorFaturado;
      entity.PGA_TaxaAdministrativa = model.TaxaAdm;
      return entity;
    }


    public static R_ProcGuiaAtendimento ToProcGuiaAtendimentoEdit(this ProcGuiaAtendimentoModel model, R_ProcGuiaAtendimento entity)
    {
      entity.PGA_IdGuiaAtendimento = model.CodigoGuiaAtendimento;
      entity.PGA_SeqProcedimento = model.SeqProcedimento;
      entity.PGA_DtRealizacao = model.DtRealizacao;
      entity.PGA_HrIniRealizacao = model.HrIniRealizacao;
      entity.PGA_HrFimRealizacao = model.HrFimRealizacao;
      entity.PGA_CodProcedimento = model.CodProcedimento;
      entity.PGA_DescProcedimento = model.DescProcedimento;
      entity.PGA_QtdeRealizada = model.QtdeRealizada;
      entity.PGA_CodViaAcesso = model.CodViaAcesso;
      entity.PGA_CodTecUtilizada = model.CodTecUtilizada;
      entity.PGA_PercFator = model.PercFator;
      entity.PGA_VlrUnit = model.VlrUnit;
      entity.PGA_VlrTotal = model.VlrTotal;
      entity.PGA_DescTipoProcedimento = model.DescTipoProcedimento;
      entity.PGA_CodTabANS = model.CodTabANS;
      entity.PGA_PercFatorFaturamento = model.PercFatorFaturamento;
      entity.PGA_Urgencia = model.Urgencia;

      return entity;
    }

    public static ProcGuiaAtendimentoModel ToProcGuiaAtendimentoModel(this R_ProcGuiaAtendimento entity)
    {
      ProcGuiaAtendimentoModel model = new ProcGuiaAtendimentoModel();
      model.Codigo = entity.PGA_Id;
      model.CodigoGuiaAtendimento = entity.PGA_IdGuiaAtendimento;
      model.SeqProcedimento = entity.PGA_SeqProcedimento;
      model.DtRealizacao = entity.PGA_DtRealizacao;
      model.HrIniRealizacao = entity.PGA_HrIniRealizacao;
      model.HrFimRealizacao = entity.PGA_HrFimRealizacao;
      model.CodProcedimento = entity.PGA_CodProcedimento;
      model.DescProcedimento = entity.PGA_DescProcedimento;
      model.QtdeRealizada = entity.PGA_QtdeRealizada;
      model.CodViaAcesso = entity.PGA_CodViaAcesso;
      model.CodTecUtilizada = entity.PGA_CodTecUtilizada;
      model.PercFator = entity.PGA_PercFator;
      model.VlrUnit = entity.PGA_VlrUnit;
      model.VlrTotal = entity.PGA_VlrTotal;
      model.DescTipoProcedimento = entity.PGA_DescTipoProcedimento;
      model.CodTabANS = entity.PGA_CodTabANS;
      model.PercFatorFaturamento = entity.PGA_PercFatorFaturamento;
      model.Urgencia = entity.PGA_Urgencia;

      return model;
    }
  }
}
﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Models;
using RepasseConvenio.WSPortalCooperado;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.Entity;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Text;
using System.Transactions;
using System.Web;
using System.Data.OleDb;
using RepasseConvenio.Infrastructure;
using Microsoft.Ajax.Utilities;

namespace RepasseConvenio.Services
{
  public class GuiaDemonstrativoService : ServiceBase
  {
    public GuiaDemonstrativoService()
   : base()
    { }
    public GuiaDemonstrativoService(RepasseEntities Contexto)
       : base(Contexto)
    { }

    public GuiaDemonstrativoService(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public GuiaDemonstrativoService(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public void Delete(int IdGuiaAtendimento, int IdRepasse)
    {
      GuiaDemonstrativoPreDelete guiaDemonstrativoPreDelete = GetGuiaDemonstrativoPreDelete(IdGuiaAtendimento, IdRepasse);
      ProcGuiaDemonstrativoService procGuiaDemonstrativoService = new ProcGuiaDemonstrativoService();
      procGuiaDemonstrativoService.Delete(guiaDemonstrativoPreDelete.IdGuiaDemonstartivo);

      string query = @"DELETE FROM R_GuiaDemonstrativo WHERE GD_Id = @IdGuiaDemonstrativo";
      Contexto.Database.ExecuteSqlCommand(query, new SqlParameter("@IdGuiaDemonstrativo", guiaDemonstrativoPreDelete.IdGuiaDemonstartivo));

      DemonstrativoConvenioService demonstrativoConvenioService = new DemonstrativoConvenioService();
      demonstrativoConvenioService.UpdateValores(guiaDemonstrativoPreDelete.IdDemonstrativo);
    }


    public GuiaDemonstrativoPreDelete GetGuiaDemonstrativoPreDelete(int IdGuiaAtendimento, int IdRepasse)
    {
      string query = @"SELECT 
                        GD.GD_Id [IdGuiaDemonstartivo],
                        DC.DC_Id [IdDemonstrativo]
                       FROM R_GuiaDemonstrativo GD
                       INNER JOIN R_DemonstrativoConvenio DC ON DC.DC_Id = GD.GD_IdDemonstrativoConvenio AND DC.DC_IdRepasse = @IdRepasse AND GD.GD_IdGuia = @IdGuiaAtendimento";

      return Contexto.Database.SqlQuery<GuiaDemonstrativoPreDelete>(query, new SqlParameter("@IdRepasse", IdRepasse)
                                                  , new SqlParameter("@IdGuiaAtendimento", IdGuiaAtendimento)).FirstOrDefault();
    }

    public void ImportacaoPlanilha(Anexo anexo, int IdRepasse)
    {
      Contexto.Database.CommandTimeout = 1000 * 5 * 60;

      #region[Services]
      DemonstrativoConvenioService demonstrativoConvenioService = new DemonstrativoConvenioService(User, Contexto);
      ProcGuiaDemonstrativoService procGuiaDemonstrativoService = new ProcGuiaDemonstrativoService(Contexto);
      GuiaAtendimentoService guiaAtendimentoService = new GuiaAtendimentoService(User, Contexto);
      LoteServices loteServices = new LoteServices(Contexto);
      LogGuiaAtendimentoService logGuiaAtendimentoService = new LogGuiaAtendimentoService(User, Contexto);
      RepasseService repasseService = new RepasseService(User, Contexto);
      #endregion

      string CNPJConvenio = repasseService.GetCNPJConvenioByIdRepasse(IdRepasse);

      #region[Criando Planilha Para Integra;'ao]
      StringBuilder sb = new StringBuilder();
      string Diretorio = ConfigurationManager.AppSettings["PathTempFiles"];
      string name = anexo.FileName + "_" + DateTime.Now.ToString("ddMMyyyyHHmmss");
      string outpath = Path.Combine(Diretorio, name);

      using (Stream file = new FileStream(outpath, FileMode.OpenOrCreate))
      {
        file.Write(anexo.DocAnexo, 0, anexo.DocAnexo.Length);
        file.Close();
      }
      #endregion

      #region[Conectando a Planilha]
      string connectionString = ConfigurationManager.ConnectionStrings["DefaultConnection"].ConnectionString;
      SqlConnection con = new SqlConnection(connectionString);
      connectionString = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + outpath + ";Extended Properties=\"Excel 12.0;HDR=YES;IMEX=1\"";
      #endregion

      using (var conn = new OleDbConnection(connectionString))
      {
        try
        {
          conn.Open();
          var sheets = conn.GetOleDbSchemaTable(System.Data.OleDb.OleDbSchemaGuid.Tables, new object[] { null, null, null, "TABLE" });
          if (sheets.Rows.Count > 0)
          {
            var ds = new DataSet();
            using (var cmd = conn.CreateCommand())
            {
              cmd.CommandText = "SELECT * FROM [" + sheets.Rows[0]["TABLE_NAME"].ToString() + "] ";
              var adapter = new OleDbDataAdapter(cmd);
              adapter.Fill(ds);
            }
            con.Open();

            #region[Pegando Informacoes da Planilha]
            List<GuiaDemonstrativoIntegra> ListaGuiaDemonstrativoIntegra = new List<GuiaDemonstrativoIntegra>();
            foreach (DataRow item in ds.Tables[0].Rows)
            {
              try
              {
                GuiaDemonstrativoIntegra guiaDemonstrativoIntegra = new GuiaDemonstrativoIntegra();
                guiaDemonstrativoIntegra.NumeroCarteirinha = item["Numero_Carteirinha"].ToString();
                guiaDemonstrativoIntegra.NumeroAtendimento = item["Numero_Atendimento"].ToString();
                guiaDemonstrativoIntegra.CodigoProcedimento = item["Codigo_Procedimento"].ToString();
                guiaDemonstrativoIntegra.DescricaoProcedimento = item["Descricao_Procedimento"].ToString();
                guiaDemonstrativoIntegra.DataAtendimento = Convert.ToDateTime(item["Data_Atendimento"].ToString());
                guiaDemonstrativoIntegra.ItensGlosados = Convert.ToInt32(item["Itens_Glosados"].ToString());
                guiaDemonstrativoIntegra.ValorApresentado = Convert.ToDecimal(item["Valor_Apresentado"].ToString());
                guiaDemonstrativoIntegra.ValorGlosado = Convert.ToDecimal(item["Valor_Glosado"].ToString());
                guiaDemonstrativoIntegra.ValorPago = Convert.ToDecimal(item["Valor_Pago"].ToString());
                guiaDemonstrativoIntegra.NomePlanilha = anexo.FileName;
                guiaDemonstrativoIntegra.IdRepasse = IdRepasse;
                guiaDemonstrativoIntegra.DescricaoGlosa = item["Descricao_Glosa"].ToString();
                guiaDemonstrativoIntegra.CodigoGlosa = item["Codigo_Glosa"].ToString();
                guiaDemonstrativoIntegra.ObservacoesGerais = item["Observacoes_Gerais"].ToString();
                guiaDemonstrativoIntegra.NroUnicooper = item["Numero_GuiaAtendimento"].ToString();
                ListaGuiaDemonstrativoIntegra.Add(guiaDemonstrativoIntegra);
              }
              catch (Exception ex)
              {
                con.Close();
                con.Dispose();
                throw new Exception("Favor verificar os nomes das células do cabeçalho.");
              }
            }
            #endregion

            if (ListaGuiaDemonstrativoIntegra.Count() == 0)
              throw new CustomException("Nenhum item encontrado na planilha.");

            #region[Validando Erros de Preenchimento]
            int ContadorErros = 0;
            foreach (GuiaDemonstrativoIntegra guiaDemonstrativoIntegra in ListaGuiaDemonstrativoIntegra)
            {
              bool validacao = PreenchimentoCorreto(guiaDemonstrativoIntegra);
              if (!validacao)
                ContadorErros++;
            }
            #endregion

            if (ContadorErros == 0)
            {
              #region[Agrupando Lista de Procedimentos Recebidas da Planilha]
              var GroupBy = ListaGuiaDemonstrativoIntegra.GroupBy(a => new { a.NroUnicooper, a.NumeroCarteirinha, a.NumeroAtendimento, a.DataAtendimento.Value.Date }).ToList();
              List<GuiaDemonstrativoIntegraCabecalho> ListaGuiaDemonstrativoIntegraCabecalho = GroupBy.Select(a => new GuiaDemonstrativoIntegraCabecalho()
              {
                NroUnicooper = a.Key.NroUnicooper,
                NumeroAtendimento = a.Key.NumeroAtendimento,
                DataAtendimento = a.Key.Date,
                NumeroCarteirinha = a.Key.NumeroCarteirinha,
                ValorApresentado = a.Sum(b => b.ValorApresentado),
                ValorFaturado = a.Sum(b => b.ValorFaturado),
                ValorGlosado = a.Sum(b => b.ValorGlosado),
                ValorPago = a.Sum(b => b.ValorPago),
                IdRepasse = a.Select(b => b.IdRepasse).FirstOrDefault(),
                NomePlanilha = a.Select(b => b.NomePlanilha).FirstOrDefault(),
                ListaGuiaDemonstrativoIntegra = a.ToList()
              }).ToList();
              #endregion

              foreach (GuiaDemonstrativoIntegraCabecalho guiaDemonstrativoIntegra in ListaGuiaDemonstrativoIntegraCabecalho)
              {
                #region[Validando Existencia Guia, Se Existir Faz a Integração de Guia e Lote]
                R_GuiaAtendimento guiaAtendimento = guiaAtendimentoService.GetGuiaAtendimento(guiaDemonstrativoIntegra.NroUnicooper);
                RetornoGetGuiaLoteRepasse retornoGetGuiaLoteRepasse = new RetornoGetGuiaLoteRepasse();
                if (guiaAtendimento == null)
                {
                  IntegraRepasse integraRepasse = new IntegraRepasse();
                  IntegraGuiaRepasse integraGuiaRepasse = new IntegraGuiaRepasse();
                  integraRepasse.Timeout = 1000 * 5 * 60;
                  integraGuiaRepasse.NroUnicooper = guiaDemonstrativoIntegra.NroUnicooper;
                  integraGuiaRepasse.CNPJConvenio = CNPJConvenio;
                  retornoGetGuiaLoteRepasse = integraRepasse.GetGuiaLoteRepasse(integraGuiaRepasse);

                  if (!retornoGetGuiaLoteRepasse.Erro)
                  {
                    if (retornoGetGuiaLoteRepasse.loteRepasse.ListaGuiasRepasse.Count() == 0)
                    {
                      throw new CustomException("Guias não valorizadas no Portal Cooperado.");
                    }
                    guiaAtendimentoService.IntegraGuias(retornoGetGuiaLoteRepasse);
                  }
                  else
                  {
                    if (retornoGetGuiaLoteRepasse.ErroPersonalizado)
                      throw new CustomException(retornoGetGuiaLoteRepasse.Mensagem);
                    else
                      throw new CustomException("Ocorreu um erro na sua solicitação, gentileza tentar novamente mais tarde.");
                  }
                }
                #endregion
              }

              int IdDemonstrativo = 0;
              #region[Gerando Demonstrativo]
              NotaFiscalRepasseService notaFiscalRepasseService = new NotaFiscalRepasseService(User);
              GuiaNotaFiscalService guiaNotaFiscalService = new GuiaNotaFiscalService(User);
              R_NotaFiscaRepasse notaFiscaRepasse = notaFiscalRepasseService.GetNotaFiscaRepasseByIdRepasse(IdRepasse);
              using (TransactionScope scope = new TransactionScope(TransactionScopeOption.RequiresNew, new TimeSpan(0, 10,0)))
              {
                List<R_GuiaDemonstrativo> ListGuiaDemonstrativoToEdit = GetGuiaDemonstrativoToEdit(ListaGuiaDemonstrativoIntegraCabecalho, IdRepasse);
                if (ListGuiaDemonstrativoToEdit.Count() != 0)
                {
                  Update(ListGuiaDemonstrativoToEdit, ListaGuiaDemonstrativoIntegraCabecalho, TipoImportacaoGuiaDemonstrativoEnum.Planilha);
                  foreach (var item in ListGuiaDemonstrativoToEdit)
                  {
                    ListaGuiaDemonstrativoIntegraCabecalho.Remove(ListaGuiaDemonstrativoIntegraCabecalho.Where(a => a.NroUnicooper == item.GD_NumeroGuia).FirstOrDefault());
                  }
                }

                if (ListaGuiaDemonstrativoIntegraCabecalho.Count() != 0)
                {
                  NotaFiscalRepasseModel notaFiscalRepasseModel = new NotaFiscalRepasseModel();
                  if (notaFiscaRepasse == null)
                  {
                    notaFiscaRepasse = new R_NotaFiscaRepasse();
                    notaFiscalRepasseModel = notaFiscalRepasseModel.ToIntegraByPlanilha(IdRepasse);
                    notaFiscaRepasse = notaFiscalRepasseService.Create(notaFiscalRepasseModel);
                  }
                  else
                  {
                    bool ValidaPreenchimento = notaFiscalRepasseService.ValidaPreenchimento(notaFiscaRepasse);
                    if (ValidaPreenchimento)
                    {
                      R_RepasseModel repasseModel = new R_RepasseModel();
                      int IdCnpjConvenio = repasseService.GetIdCNPJConvenioByIdRepasse(IdRepasse);
                      repasseModel.DataInicio = DateTime.Now;
                      repasseModel.CodigoResponsavel = User.IdUsuario;
                      repasseModel.CodigoConvenio = IdCnpjConvenio;

                      DepositoRepasseService depositoRepasseService = new DepositoRepasseService();
                      ImportPlanilhaDeposito importPlanilhaDeposito = depositoRepasseService.GetImportPlanilhaDeposito(IdRepasse);
                      if (importPlanilhaDeposito != null || importPlanilhaDeposito.IdDepositoRepasse != 0)
                      {
                        if (importPlanilhaDeposito.ValorDeposito >= importPlanilhaDeposito.ValorUtilizado)
                          IdRepasse = repasseService.Create(repasseModel, importPlanilhaDeposito.IdDepositoRepasse);
                        else
                          IdRepasse = repasseService.Create(repasseModel);
                      }
                      else
                        IdRepasse = repasseService.Create(repasseModel);
                      notaFiscalRepasseModel = notaFiscalRepasseModel.ToIntegraByPlanilha(IdRepasse);
                      notaFiscaRepasse = notaFiscalRepasseService.Create(notaFiscalRepasseModel);
                    }
                  }

                  DemonstrativoConvenioCreate demonstrativoConvenioCreate = ListaGuiaDemonstrativoIntegraCabecalho.ListaGuiaDemonstrativoIntegraToDemonstrativoConvenioCreate(anexo.FileName, IdRepasse);
                  #region[Gerando Demonstrativo]
                  R_DemonstrativoConvenio demonstrativoConvenio = demonstrativoConvenioService.Create(demonstrativoConvenioCreate);
                  IdDemonstrativo = demonstrativoConvenio.DC_Id;
                  foreach (GuiaDemonstrativoIntegraCabecalho guiaDemonstrativoIntegraCabecalho in ListaGuiaDemonstrativoIntegraCabecalho)
                  {
                    try
                    {
                      guiaDemonstrativoIntegraCabecalho.IdRepasse = IdRepasse;
                      R_GuiaDemonstrativo guiaDemonstrativo = GetGuiaDemonstrativo(guiaDemonstrativoIntegraCabecalho.NroUnicooper, IdDemonstrativo);
                      if (guiaDemonstrativo == null)
                      {
                        guiaDemonstrativo = Create(guiaDemonstrativoIntegraCabecalho, demonstrativoConvenio, TipoImportacaoGuiaDemonstrativoEnum.Planilha);
                        foreach (GuiaDemonstrativoIntegra guiaDemonstrativoIntegra in guiaDemonstrativoIntegraCabecalho.ListaGuiaDemonstrativoIntegra)
                        {

                          procGuiaDemonstrativoService.Create(guiaDemonstrativoIntegra, guiaDemonstrativo.GD_Id, guiaDemonstrativo.GD_IdGuia.Value);

                        }
                      }
                      demonstrativoConvenio.DC_TotalFaturado += guiaDemonstrativo.GD_TotalFaturado;
                    }
                    catch (Exception ex)
                    {

                    }
                  }
                  Edit(demonstrativoConvenio);
                  #endregion

                  #region[Gerando Nota Fiscal e Guia Nota Fiscal]
                  guiaNotaFiscalService.IntegraByPlanilha(ListaGuiaDemonstrativoIntegraCabecalho, notaFiscaRepasse, IdRepasse);
                  #endregion
                }
                scope.Complete();
              }
              #endregion

              if (ListaGuiaDemonstrativoIntegraCabecalho.Count() != 0)
              {
                #region[Atualizando Valorizacao Repasse]
                int IdStatusRepasseEmConferencia = new StatusRepasseServices(Contexto).GetIdByEnum(EnumStatusRepasse.EMCONFERENCIA);
                R_Repasse repasse = new RepasseService(Contexto).GetById(IdRepasse);
                repasse.R_Valorizacao = true;
                repasse.R_IdStatusRepasse = IdStatusRepasseEmConferencia;
                Edit(repasse);
                #endregion

                #region[Validando Demonstrativo]
                if (IdDemonstrativo != 0)
                {
                  using (TransactionScope scope = new TransactionScope(TransactionScopeOption.RequiresNew))
                  {
                    loteServices.AtualizarStatusLote(IdDemonstrativo);
                    scope.Complete();
                  }
                }
                #endregion
              }
            }
            else
              throw new CustomException("Favor verificar a planilha pois alguns dados apresentam formato inválido.");
          }
        }
        catch (CustomException ex)
        {
          throw ex;
        }
        catch (Exception ex)
        {
          throw ex;
        }
        finally
        {
          conn.Close();
          conn.Dispose();
        }
      }
    }

    public void ImportNotaFiscal(int IdRepasse)
    {
      #region[Services]
      DemonstrativoConvenioService demonstrativoConvenioService = new DemonstrativoConvenioService(User);
      ProcGuiaDemonstrativoService procGuiaDemonstrativoService = new ProcGuiaDemonstrativoService();
      GuiaAtendimentoService guiaAtendimentoService = new GuiaAtendimentoService(User);
      LoteServices loteServices = new LoteServices();
      LogGuiaAtendimentoService logGuiaAtendimentoService = new LogGuiaAtendimentoService(User);
      RepasseService repasseService = new RepasseService();
      #endregion

      string CNPJConvenio = repasseService.GetCNPJConvenioByIdRepasse(IdRepasse);

      #region[Pegando Informacoes da Planilha]
      List<GuiaDemonstrativoIntegra> ListaGuiaDemonstrativoIntegra = GetListGuiaDemonstrativoIntegra(IdRepasse);
      if (ListaGuiaDemonstrativoIntegra.Count() == 0)
        throw new CustomException("Nenhuma guia encontrada na nota fiscal.");
      #endregion

      #region[Validando Erros de Preenchimento]
      int ContadorErros = 0;
      foreach (GuiaDemonstrativoIntegra guiaDemonstrativoIntegra in ListaGuiaDemonstrativoIntegra)
      {
        bool validacao = PreenchimentoCorreto(guiaDemonstrativoIntegra);
        if (!validacao)
          ContadorErros++;
      }
      #endregion

      if (ContadorErros == 0)
      {
        #region[Agrupando Lista de Procedimentos Recebidas da Planilha]
        var GroupBy = ListaGuiaDemonstrativoIntegra.GroupBy(a => new { a.NroUnicooper, a.NumeroCarteirinha, a.NumeroAtendimento, a.DataAtendimento.Value.Date }).ToList();
        List<GuiaDemonstrativoIntegraCabecalho> ListaGuiaDemonstrativoIntegraCabecalho = GroupBy.Select(a => new GuiaDemonstrativoIntegraCabecalho()
        {
          NroUnicooper = a.Key.NroUnicooper,
          NumeroAtendimento = a.Key.NumeroAtendimento,
          DataAtendimento = a.Key.Date,
          NumeroCarteirinha = a.Key.NumeroCarteirinha,
          ValorApresentado = a.Sum(b => b.ValorApresentado),
          ValorFaturado = a.Sum(b => b.ValorFaturado),
          ValorGlosado = a.Sum(b => b.ValorGlosado),
          ValorPago = a.Sum(b => b.ValorPago),
          IdRepasse = a.Select(b => b.IdRepasse).FirstOrDefault(),
          NomePlanilha = a.Select(b => b.NomePlanilha).FirstOrDefault(),
          ListaGuiaDemonstrativoIntegra = a.ToList()
        }).ToList();
        #endregion

        foreach (GuiaDemonstrativoIntegraCabecalho guiaDemonstrativoIntegra in ListaGuiaDemonstrativoIntegraCabecalho)
        {
          #region[Validando Existencia Guia, Se Existir Faz a Integração de Guia e Lote]
          R_GuiaAtendimento guiaAtendimento = guiaAtendimentoService.GetGuiaAtendimento(guiaDemonstrativoIntegra.NroUnicooper);
          RetornoGetGuiaLoteRepasse retornoGetGuiaLoteRepasse = new RetornoGetGuiaLoteRepasse();
          if (guiaAtendimento == null)
          {
            IntegraRepasse integraRepasse = new IntegraRepasse();
            IntegraGuiaRepasse integraGuiaRepasse = new IntegraGuiaRepasse();
            integraGuiaRepasse.NroUnicooper = guiaDemonstrativoIntegra.NroUnicooper;
            integraGuiaRepasse.CNPJConvenio = CNPJConvenio;
            retornoGetGuiaLoteRepasse = integraRepasse.GetGuiaLoteRepasse(integraGuiaRepasse);

            if (!retornoGetGuiaLoteRepasse.Erro)
            {
              guiaAtendimentoService.IntegraGuias(retornoGetGuiaLoteRepasse);
            }
            else
            {
              if (retornoGetGuiaLoteRepasse.ErroPersonalizado)
                throw new CustomException(retornoGetGuiaLoteRepasse.Mensagem);
              else
                throw new CustomException("Ocorreu um erro na sua solicitação, gentileza tentar novamente mais tarde.");
            }
          }
          #endregion
        }

        int IdDemonstrativo = 0;
        #region[Gerando Demonstrativo]
        using (TransactionScope scope = new TransactionScope(TransactionScopeOption.RequiresNew))
        {
          DemonstrativoConvenioCreate demonstrativoConvenioCreate = ListaGuiaDemonstrativoIntegraCabecalho.ListaGuiaDemonstrativoIntegraToDemonstrativoConvenioCreate("", IdRepasse);

          #region[Gerando Demonstrativo]
          R_DemonstrativoConvenio demonstrativoConvenio = demonstrativoConvenioService.Create(demonstrativoConvenioCreate);
          IdDemonstrativo = demonstrativoConvenio.DC_Id;
          foreach (GuiaDemonstrativoIntegraCabecalho guiaDemonstrativoIntegraCabecalho in ListaGuiaDemonstrativoIntegraCabecalho)
          {
            R_GuiaDemonstrativo guiaDemonstrativo = GetGuiaDemonstrativo(guiaDemonstrativoIntegraCabecalho.NroUnicooper, IdDemonstrativo);
            if (guiaDemonstrativo == null)
            {
              guiaDemonstrativo = Create(guiaDemonstrativoIntegraCabecalho, demonstrativoConvenio, TipoImportacaoGuiaDemonstrativoEnum.NotaFiscal);
              foreach (GuiaDemonstrativoIntegra guiaDemonstrativoIntegra in guiaDemonstrativoIntegraCabecalho.ListaGuiaDemonstrativoIntegra)
              {
                procGuiaDemonstrativoService.Create(guiaDemonstrativoIntegra, guiaDemonstrativo.GD_Id, guiaDemonstrativo.GD_IdGuia.Value);
              }
            }
          }
          Edit(demonstrativoConvenio);
          #endregion
          scope.Complete();
        }
        #endregion

        #region[Atualizando Valorizacao Repasse]
        int IdStatusRepasseEmConferencia = new StatusRepasseServices(Contexto).GetIdByEnum(EnumStatusRepasse.EMCONFERENCIA);
        R_Repasse repasse = new RepasseService(Contexto).GetById(IdRepasse);
        repasse.R_Valorizacao = true;
        repasse.R_IdStatusRepasse = IdStatusRepasseEmConferencia;
        Edit(repasse);
        #endregion

        #region[Validando Demonstrativo]
        using (TransactionScope scope = new TransactionScope(TransactionScopeOption.RequiresNew))
        {
          loteServices.AtualizarStatusLote(IdDemonstrativo);
          scope.Complete();
        }
        #endregion
      }
      else
        throw new CustomException("Favor verificar a planilha pois alguns dados apresentam formato inválido.");
    }

    public void ImportGuiaDemonstrativoAndNotaFiscal()
    {

    }

    public List<GuiaDemonstrativoIntegra> GetListGuiaDemonstrativoIntegra(int IdRepasse)
    {
      string query = @"SELECT 
                        GA.GA_CodCarteiraPaciente [NumeroCarteirinha],
                        GA.GA_CodAtendimento [NumeroAtendimento],
                        PGA.PGA_CodProcedimento [CodigoProcedimento],
                        PGA.PGA_DescProcedimento [DescricaoProcedimento],
                        GA.GA_DataAtendimento [DataAtendimento],
                        0 [ItensGlosados],
                        GNF.GNF_ValorApresentado [ValorApresentado],
                        GNF.GNF_ValorGlosado [ValorGlosado],
                        GNF.GNF_ValorPago [ValorPago],
                        GNF.GNF_ValorFaturado [ValorFaturado],
                        '' [NomePlanilha],
                        R.R_Id [IdRepasse],
                        '' [DescricaoGlosa],
                        '' [CodigoGlosa],
                        '' [ObservacoesGerais],
                        GA.GA_NroUnicooper [NroUnicooper]
                       FROM R_NotaFiscaRepasse NFR
                       INNER JOIN R_Repasse R ON R.R_Id = NFR.NF_IdRepasse AND NF_IdRepasse = @IdRepasse
                       INNER JOIN R_GuiaNotaFiscal GNF ON GNF.GNF_IdNotaFiscal = NFR.NF_Id
                       INNER JOIN R_GuiaAtendimento GA ON GA.GA_Id = GNF.GNF_IdGuiaAtendimento
                       INNER JOIN R_ProcGuiaAtendimento PGA ON PGA.PGA_IdGuiaAtendimento = GA.GA_Id
                        LEFT JOIN R_GuiaDemonstrativo GD ON GD.GD_IdGuia = GA.GA_Id
                        WHERE GD.GD_Id IS NULL";

      return Contexto.Database.SqlQuery<GuiaDemonstrativoIntegra>(query, new SqlParameter("IdRepasse", IdRepasse)).ToList();
    }

    public bool PreenchimentoCorreto(GuiaDemonstrativoIntegra guiaDemonstrativoIntegra)
    {
      if (string.IsNullOrEmpty(guiaDemonstrativoIntegra.NumeroAtendimento))
        return false;
      else
      if (string.IsNullOrEmpty(guiaDemonstrativoIntegra.NumeroCarteirinha))
        return false;
      else
      if (!guiaDemonstrativoIntegra.DataAtendimento.HasValue)
        return false;
      else
      if (!guiaDemonstrativoIntegra.ValorApresentado.HasValue)
        return false;
      else if (string.IsNullOrEmpty(guiaDemonstrativoIntegra.NroUnicooper))
        return false;

      return true;
    }

    public R_GuiaDemonstrativo GetGuiaDemonstrativo(string NroUnicooper, int IdDemonstrativo)
    {
      string query = @"SELECT
                        *
                       FROM R_GuiaDemonstrativo
                       WHERE GD_NumeroGuia = @NroUnicooper
                       AND GD_IdDemonstrativoConvenio = @IdDemonstrativo";

      return Contexto.Database.SqlQuery<R_GuiaDemonstrativo>(query, new SqlParameter("@NroUnicooper", NroUnicooper)
                                                                  , new SqlParameter("@IdDemonstrativo", IdDemonstrativo)).FirstOrDefault();
    }

    public R_GuiaDemonstrativo Create(GuiaDemonstrativoIntegraCabecalho guiaDemonstrativoIntegraCabecalho, R_DemonstrativoConvenio demonstrativoConvenio, TipoImportacaoGuiaDemonstrativoEnum tipoImportacaoGuiaDemonstrativoEnum)
    {
      GuiaAtendimentoService guiaAtendimentoService = new GuiaAtendimentoService();
      LogGuiaAtendimentoService logGuiaAtendimentoService = new LogGuiaAtendimentoService(User);
      StatusGuiaDemonstrativoConvenioServices statusGuiaDemonstrativoConvenioServices = new StatusGuiaDemonstrativoConvenioServices();
      int IdStatusguiaNaoProcessado = statusGuiaDemonstrativoConvenioServices.GetIdByEnum(StatusGuiaDemonstrativoEnum.NaoProcessado);

      //guiaAtendimento.GA_ValorPago = guiaDemonstrativoIntegraCabecalho.ValorPago;
      //guiaAtendimento.GA_VlrGlosa = guiaDemonstrativoIntegraCabecalho.ValorGlosado;

      //Edit(guiaAtendimento);
      R_GuiaAtendimento guiaAtendimento = guiaAtendimentoService.GetGuiaAtendimento(guiaDemonstrativoIntegraCabecalho.NroUnicooper);

      TipoImportacaoGuiaDemonstrativoServices tipoImportacaoGuiaDemonstrativoServices = new TipoImportacaoGuiaDemonstrativoServices();
      int IdTipoImportacao = tipoImportacaoGuiaDemonstrativoServices.GetIdByEnum(tipoImportacaoGuiaDemonstrativoEnum);
      R_GuiaDemonstrativo guiaDemonstrativo = guiaDemonstrativoIntegraCabecalho.ModeltoEntityCreate(demonstrativoConvenio.DC_Id, guiaAtendimento.GA_Id, guiaAtendimento.GA_NroUnicooper, guiaAtendimento.GA_ValorFaturado, IdStatusguiaNaoProcessado, IdTipoImportacao);
      Create(guiaDemonstrativo);

      GuiaDemonstrativoDelete guiaDemonstrativoDelete = GetGuiaDemonstrativoDelete(guiaAtendimento.GA_Id);
      guiaAtendimentoService.UpdateValoresGuiaAtendimento(guiaDemonstrativoDelete.TotalPago, guiaDemonstrativo.GD_TotalGlosado, guiaAtendimento.GA_Id);

      logGuiaAtendimentoService
           .CreateLog(string.Format(@"Guia {0} adicionada ao 
                                    demonstrativo {1} que tem a planilha com o nome {2}
                                    e ao repasse {3}", guiaAtendimento.GA_NroUnicooper, demonstrativoConvenio.DC_Id, demonstrativoConvenio.DC_NomePlanilha, demonstrativoConvenio.DC_IdRepasse));

      return guiaDemonstrativo;
    }

    public void Update(List<R_GuiaDemonstrativo> ListGuiaDemonstrativoToEdit, List<GuiaDemonstrativoIntegraCabecalho> ListaGuiaDemonstrativoIntegraCabecalho, TipoImportacaoGuiaDemonstrativoEnum tipoImportacaoGuiaDemonstrativoEnum)
    {
      GuiaAtendimentoService guiaAtendimentoService = new GuiaAtendimentoService();
      LogGuiaAtendimentoService logGuiaAtendimentoService = new LogGuiaAtendimentoService(User);

      TipoImportacaoGuiaDemonstrativoServices tipoImportacaoGuiaDemonstrativoServices = new TipoImportacaoGuiaDemonstrativoServices();
      int IdTipoImportacao = tipoImportacaoGuiaDemonstrativoServices.GetIdByEnum(tipoImportacaoGuiaDemonstrativoEnum);
      foreach (var item in ListGuiaDemonstrativoToEdit)
      {
        R_GuiaAtendimento guiaAtendimento = guiaAtendimentoService.GetGuiaAtendimento(item.GD_NumeroGuia);
        GuiaDemonstrativoIntegraCabecalho guiaDemonstrativoIntegraCabecalho = ListaGuiaDemonstrativoIntegraCabecalho.Where(a => a.NroUnicooper == item.GD_NumeroGuia).FirstOrDefault();
        item.EntityUpdate(guiaDemonstrativoIntegraCabecalho, IdTipoImportacao, guiaAtendimento.GA_ValorFaturado);
        Edit(item);
        GuiaDemonstrativoDelete guiaDemonstrativoDelete = GetGuiaDemonstrativoDelete(guiaAtendimento.GA_Id);
        guiaAtendimentoService.UpdateValoresGuiaAtendimento(guiaDemonstrativoDelete.TotalPago, guiaDemonstrativoIntegraCabecalho.ValorGlosado, guiaAtendimento.GA_Id);
      }
    }

    public List<GuiaDemonstrativoIndex> GetListaGuiaDemonstrativoIndex(int IdDemonstrativo)
    {
      string query = @"SELECT 
                        GD.GD_Id [Codigo],
                        GD.GD_IdGuia [CodigoGuia],
                        DC.DC_IdRepasse [IdRepasse],
                        GD.GD_NumeroGuia [NumeroGuia],
                        GD.GD_NumeroCarteirinha [NumeroCarteirinha],
                        GD.GD_NumeroAtendimento [NumeroAtendimento],
                        GD.GD_DataAtendimento [DataAtendimento],
                        GD.GD_TotalFaturado [ValorFaturado],
                        GD.GD_TotalApresentado [ValorApresentado],
                        GD.GD_TotalGlosado [ValorGlosado],
                        GD.GD_TotalPago [ValorPago]
                       FROM R_GuiaDemonstrativo GD
                       INNER JOIN R_DemonstrativoConvenio DC ON DC.DC_Id = GD.GD_IdDemonstrativoConvenio AND GD.GD_IdDemonstrativoConvenio = @IdDemonstrativo";

      return Contexto.Database.SqlQuery<GuiaDemonstrativoIndex>(query, new SqlParameter("@IdDemonstrativo", IdDemonstrativo)).ToList();
    }

    public List<R_GuiaDemonstrativo> GetListGuiaDemonstrativo(int IdDemonstrativo)
    {
      string query = @"SELECT * FROM R_GuiaDemonstrativo WHERE GD_IdDemonstrativoConvenio = @IdDemonstrativo";
      return Contexto.Database.SqlQuery<R_GuiaDemonstrativo>(query, new SqlParameter("@IdDemonstrativo", IdDemonstrativo)).ToList();
    }

    public List<R_GuiaDemonstrativo> GetListGuiaDemonstrativoNaoProcessado(int IdDemonstrativo)
    {
      string query = @"SELECT 
                        GD.*
                       FROM R_GuiaDemonstrativo GD
                       INNER JOIN R_StatusGuiaDemonstrativoConvenio SGD ON SGD.SGDC_Id = GD.DC_IdStatusGuiaDemonstrativoConvenio 
									                                                      AND GD.GD_IdDemonstrativoConvenio = @IdDemonstrativo
												                                                AND SGD.SGDC_Enum = @StatusNaoProcesado";
      return Contexto.Database.SqlQuery<R_GuiaDemonstrativo>(query, new SqlParameter("@IdDemonstrativo", IdDemonstrativo)
                                                                  , new SqlParameter("@StatusNaoProcesado", (int)StatusGuiaDemonstrativoEnum.NaoProcessado)).ToList();
    }

    public List<GuiaDemonstrativoDelete> GetListIdGuiaAtendimento(int IdDemonstrativo)
    {
      string query = @"SELECT
                        GD.GD_IdGuia [IdGuiaAtendimento],
                        GA.GA_NroUnicooper [NumeroGuia]
                       FROM R_GuiaDemonstrativo GD
                       INNER JOIN R_GuiaAtendimento GA ON GA.GA_Id = GD.GD_IdGuia AND GD.GD_IdDemonstrativoConvenio = @IdDemonstrativo";

      return Contexto.Database.SqlQuery<GuiaDemonstrativoDelete>(query, new SqlParameter("@IdDemonstrativo", IdDemonstrativo)).ToList();
    }

    public List<int> GetAllIdGuiaDemonstrativo(int IdDemonstrativo)
    {
      string query = @"SELECT GD_Id FROM R_GuiaDemonstrativo WHERE GD_IdDemonstrativoConvenio = @IdDemonstrativo";
      return Contexto.Database.SqlQuery<int>(query, new SqlParameter("@IdDemonstrativo", IdDemonstrativo)).ToList();
    }

    public int GetRandomIdGuiaAtendimento(int IdDemonstrativo)
    {
      string query = @"SELECT 
                        TOP(1) GD_IdGuia
                       FROM R_GuiaDemonstrativo WHERE GD_IdDemonstrativoConvenio = @IdDemonstrativo";
      return Contexto.Database.SqlQuery<int>(query, new SqlParameter("@IdDemonstrativo", IdDemonstrativo)).FirstOrDefault();
    }

    public R_GuiaDemonstrativo GetGuiaDemonstrativoByIdGuia(int IdGuia)
    {
      string query = @"SELECT
                        *
                       FROM R_GuiaDemonstrativo 
                       WHERE GD_IdGuia = @IdGuia";

      return Contexto.Database.SqlQuery<R_GuiaDemonstrativo>(query, new SqlParameter("@IdGuia", IdGuia)).FirstOrDefault();
    }

    public void ProcessaGuiaDemonstrativo(R_GuiaDemonstrativo guiaDemonstrativo
                                        , StatusGuiaDemonstrativoEnum status
                                        , string infoProcessamento)
    {
      StatusGuiaDemonstrativoConvenioServices statusGuiaDemonstrativoConvenioServices = new StatusGuiaDemonstrativoConvenioServices();
      guiaDemonstrativo.DC_IdStatusGuiaDemonstrativoConvenio = statusGuiaDemonstrativoConvenioServices.GetIdByEnum(status);
      guiaDemonstrativo.GD_InfoProcessamento = infoProcessamento;
      guiaDemonstrativo.GD_DataProcessamento = DateTime.Now;
      Edit(guiaDemonstrativo);
    }

    public GuiaDemonstrativoAtualizaLote GetGuiaDemonstrativoAtualizaLote(int IdGuiaAtendimento)
    {
      string query = @"SELECT 
                        GD_TotalFaturado [TotalFaturado],
                        SUM(GD_TotalPago) [TotalPago],
                        GD_IdGuia [IdGuia]
                       FROM R_GuiaDemonstrativo 
                       WHERE GD_IdGuia = @IdGuiaAtendimento
                       GROUP BY GD_TotalFaturado, GD_IdGuia";

      return Contexto.Database.SqlQuery<GuiaDemonstrativoAtualizaLote>(query, new SqlParameter("@IdGuiaAtendimento", IdGuiaAtendimento)).FirstOrDefault();
    }

    public GuiaDemonstrativoDelete GetGuiaDemonstrativoDelete(int IdGuiaAtendimento)
    {
      string query = @"SELECT
                        ISNULL(sum(GD.GD_TotalPago), 0) [TotalPago],
                        ISNULL(GA.GA_ValorFaturado - GA.GA_ValorPago, 0) [TotalGlosado]
                       FROM R_GuiaDemonstrativo GD
                       INNER JOIN R_GuiaAtendimento GA ON GA.GA_Id = GD.GD_IdGuia AND  GD.GD_IdGuia = @IdGuiaAtendimento
                       GROUP BY GA_ValorFaturado, GA_ValorPago";

      return Contexto.Database.SqlQuery<GuiaDemonstrativoDelete>(query, new SqlParameter("@IdGuiaAtendimento", IdGuiaAtendimento)).FirstOrDefault();
    }

    public GuiaDemonstrativoUpdate GetGuiaDemonstrativoUpdate(int IdGuiaAtendimento)
    {
      string query = @"SELECT
                        ISNULL(sum(GD.GD_TotalPago), 0) [TotalPago],
                        ISNULL(GA.GA_ValorFaturado - GA.GA_ValorPago, 0) [TotalGlosado]
                       FROM R_GuiaDemonstrativo GD
                       INNER JOIN R_GuiaAtendimento GA ON GA.GA_Id = GD.GD_IdGuia AND  GD.GD_IdGuia = @IdGuiaAtendimento
                       GROUP BY GA_ValorFaturado, GA_ValorPago";

      return Contexto.Database.SqlQuery<GuiaDemonstrativoUpdate>(query, new SqlParameter("@IdGuiaAtendimento", IdGuiaAtendimento)).FirstOrDefault();
    }

    public List<R_GuiaDemonstrativo> GetGuiaDemonstrativoToEdit(List<GuiaDemonstrativoIntegraCabecalho> ListaGuiaDemonstrativoIntegraCabecalho, int IdRepasse)
    {
      string query = @"SELECT
                         GD.*
                       FROM R_GuiaDemonstrativo GD
                       INNER JOIN R_DemonstrativoConvenio DC ON DC.DC_Id = GD.GD_IdDemonstrativoConvenio AND GD_NumeroGuia IN (#NroGuias#)
                       INNER JOIN R_Repasse R ON R.R_Id = DC.DC_IdRepasse
                       INNER JOIN R_StatusRepasse SR ON SR.SR_Id = R.R_IdStatusRepasse AND (SR.SR_Enum = @Elaboracao OR SR.SR_Enum = @EMCONFERENCIA)";

      query = query.Replace("#NroGuias#", string.Join(",", ListaGuiaDemonstrativoIntegraCabecalho.Select(A => A.NroUnicooper).ToArray()));

      return Contexto.Database.SqlQuery<R_GuiaDemonstrativo>(query, new SqlParameter("@Elaboracao", (int)EnumStatusRepasse.ELABORACAO)
                                                                  , new SqlParameter("@EMCONFERENCIA", (int)EnumStatusRepasse.EMCONFERENCIA)).ToList();
    }

  }
}
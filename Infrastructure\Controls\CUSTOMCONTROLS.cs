﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Linq.Expressions;
using System.Web.Mvc.Html;
using System.Text;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace RepasseConvenio.Infrastructure.Controls
{
  public static class CustomControls
  {
    public static MvcHtmlString LibSelect2For<TModel, TProperty>(this HtmlHelper<TModel> htmlHelper, Expression<Func<TModel, TProperty>> expression, object attributes = null, Select2Configuration select2Configuration = null)
    {
      return new MvcHtmlString();
    }

    public static MvcHtmlString LibButtonFor(this HtmlHelper htmlHelper, ButtonConfiguration buttonConfiguration)
    {
      return new MvcHtmlString();
    }

    public static MvcHtmlString LibPagination<T>(this HtmlHelper htmlHelper, PagedList<T> model, string classe = "")
    {
      return new MvcHtmlString();
    }

    public static MvcHtmlString RepasseAlertToast(this HtmlHelper htmlHelper)
    {
      return new MvcHtmlString();
    }


    public static MvcHtmlString LibDropDown<TModel, TProperty>(this HtmlHelper<TModel> htmlHelper, Expression<Func<TModel, TProperty>> expression, string id = null, DropDownConfiguration configuration = null)
    {
      return new MvcHtmlString();
    }

    public static MvcHtmlString LibDropDownForEnum<T>(this HtmlHelper htmlHelper, T teste, string fieldName, string selectedValue, bool hasOptional = true)
    {
      return new MvcHtmlString();
    }

    public static MvcHtmlString LibLookup<TModel, TProperty>(this HtmlHelper<TModel> htmlHelper, Expression<Func<TModel, TProperty>> expression, LookupConfiguration configuration = null)
    {
      return new MvcHtmlString();
    }

    private static void RenderString(string value, string idControl, string description, ref StringBuilder sb)
    {
    }

    public static MvcHtmlString LibCKEditor<TModel, TProperty>(this HtmlHelper<TModel> htmlHelper, Expression<Func<TModel, TProperty>> expression)
    {
      return new MvcHtmlString();
    }

    public static MvcHtmlString LibEditorFor<TModel, TProperty>(this HtmlHelper<TModel> htmlHelper, Expression<Func<TModel, TProperty>> expression, object attributes = null)
    {
      return new MvcHtmlString();
    }

    public static MvcHtmlString LibLabelFor<TModel, TProperty>(this HtmlHelper<TModel> htmlHelper, Expression<Func<TModel, TProperty>> expression, object attributes = null)
    {
      return new MvcHtmlString();
    }

    public static MvcHtmlString LibDisplayForEnum(SelectList values, string value)
    {
      return new MvcHtmlString();
    }

    private static string GetName(SelectList values, string value)
    {
      return string.Empty;
    }

    public static MvcHtmlString LibDropDown<TModel, TProperty>(this HtmlHelper<TModel> htmlHelper, Expression<Func<TModel, TProperty>> expression, LookupConfiguration configuration)
    {
      return new MvcHtmlString();
    }

    public static MvcHtmlString LibComboCheckBox<TModel, TProperty>(this HtmlHelper<TModel> htmlHelper, Expression<Func<TModel, TProperty>> expression, LookupConfiguration configuration)
    {
      return new MvcHtmlString();
    }

    public static MvcHtmlString LibDate<TModel, TProperty>(this HtmlHelper<TModel> htmlHelper, Expression<Func<TModel, TProperty>> expression, DateConfiguration configuration = null)
    {
      return new MvcHtmlString();
    }

    public static MvcHtmlString LibDateTime<TModel, TProperty>(this HtmlHelper<TModel> htmlHelper, Expression<Func<TModel, TProperty>> expression, DateTimeConfiguration configuration = null)
    {
      return new MvcHtmlString();
    }

    public static MvcHtmlString LibLabel<TModel, TProperty>(this HtmlHelper<TModel> htmlHelper, Expression<Func<TModel, TProperty>> expression)
    {
      return new MvcHtmlString();
    }

  }

  public class LookupConfiguration
  {
    public LookupConfiguration()
    {
      AfterSelect = "";
      CSSClass = "";
    }

    public string Source { get; set; }

    public string AfterSelect { get; set; }

    public string CSSClass { get; set; }

    public string DataValueBind { get; set; }

    public string ControlBindValueSelected { get; set; }

    public bool KeyAttributeOtherType { get; set; }

    public bool Disabled { get; set; }
  }

  public class DateConfiguration
  {
  }

  public class DateTimeConfiguration
  {
  }
}
﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using PagedList;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RepasseConvenio.Services
{
  public class TipoLoteGlosaServices : ServiceBase
  {
    public TipoLoteGlosaServices(RepasseEntities Contexto)
        : base(Contexto)
    { }
    public TipoLoteGlosaServices()
       : base()
    { }

    public TipoLoteGlosaServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public TipoLoteGlosaServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public int GetIdByEnum(TipoLoteGlosaEnum tipoLoteGlosaEnum)
    {
      return Contexto.R_TipoLoteGlosa.Where(a => a.TLG_Enum == (int)tipoLoteGlosaEnum).Select(a => a.TLG_Id).FirstOrDefault();
    }
    public R_TipoLoteGlosa GetByEnum(TipoLoteGlosaEnum tipoLoteGlosaEnum)
    {
      return Contexto.R_TipoLoteGlosa.Where(a => a.TLG_Enum == (int)tipoLoteGlosaEnum).FirstOrDefault();
    }

    public R_TipoLoteGlosa GetById(int Id)
    {
      return Contexto.R_TipoLoteGlosa.Where(a => a.TLG_Id == Id).FirstOrDefault();
    }

    public List<R_TipoLoteGlosa> Getall()
    {
      string query = @"select * from R_TipoLoteGlosa";
      return Contexto.Database.SqlQuery<R_TipoLoteGlosa>(query).ToList();
    }
  }
}


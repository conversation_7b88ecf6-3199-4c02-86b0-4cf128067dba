﻿@using RepasseConvenio.Models
@using PagedList
@using PagedList.Mvc;
@model MedicoIndex

@{
  ViewBag.Title = "Lista Médico";
}

<script src="~/signalr/hubs"></script>
@Scripts.Render("~/Scripts/SignalR.js")
@Scripts.Render("~/Views/Medico/Medico.js")
@Styles.Render("~/Views/Medico/Medico.css")

<div class="row">
  <section class="col-lg-12 connectedSortable ui-sortable">
    <div class="card">
      <div class="card-header ui-sortable-handle">
        <div class="row" style="justify-content:space-between;">
          <h3 class="card-title">
            <i class="fas fa-user mr-1"></i>
            Medicos
          </h3>
          <div class="card-tools">
            <button type="button" class="btn btn-block btn-outline-primary" id="IntegrarMedicos"> Integrar Médicos </button>
          </div>
        </div>
        @using (Html.BeginForm("MedicoSearch", "Medico", FormMethod.Get, new { enctype = "multipart/form-data" }))
        {
          <fieldset class="border p-3 border-top p-3">
          <legend class="col-lg-auto">Filtro</legend>
          <div class="row" style="align-items: flex-end;">
            <div class="col-md-3">
              <div class="form-group">
                @Html.LabelFor(a => a.Nome)
                @Html.TextBoxFor(a => a.Nome, new { @class = "form-control" })
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-group">
                @Html.LabelFor(a => a.CPF)
                @Html.TextBoxFor(a => a.CPF, new { @class = "form-control CPF" })
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-group">
                @Html.LabelFor(a => a.CRM)
                @Html.TextBoxFor(a => a.CRM, new { @class = "form-control" })
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                @Html.LabelFor(a => a.Email)
                @Html.TextBoxFor(a => a.Email, new { @class = "form-control" })
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-group">
                <button type="submit" class="btn btn-block btn-primary">Pesquisar</button>
              </div>
            </div>
          </div>
          </fieldset>
        }
      </div><!-- /.card-header -->
      <div class="card-body">
        <div class="card-acoes">
          <a class="btn btn-block btn-outline-primary disabled" id="CartaConversao"> Carta Conversão </a>
          <a class="btn btn-block btn-outline-primary disabled" id="RateioMedico"> Rateio Médico </a>
          <a class="btn btn-block btn-outline-primary disabled" id="ExtratoMedico"> Extrato Médico </a>
          <a class="btn btn-block btn-outline-primary disabled" id="EmpresaMedico"> Empresa Médico </a>
          <a class="btn btn-block btn-outline-primary disabled" id="DetalhesMedico"> Detalhes Médico </a>
          <a class="btn btn-block btn-outline-primary disabled" id="ItensRecorrentes" style="width: 215px !important;"> Lançamentos Recorrentes </a>
        </div>
        <div class="tab-content p-0">
          <div class="box-body">
            <div class="row">
              <div class="col-md-12 table-responsive p-0">
                <table class="table table-sm table-striped table-hover text-nowrap">
                  <thead>
                    <tr>
                      <th>
                        Nome
                      </th>
                      <th>
                        CPF
                      </th>
                      <th>
                        CRM
                      </th>
                      <th>
                        Email
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    @foreach (MedicoModel item in Model.ListaMedicoModel)
                    {
                      <tr class="TrSelecionavel" data-codigomedico="@item.Codigo">
                        <td>
                          <input type="hidden" @item.Codigo />

                          @item.Nome
                        </td>
                        <td>
                          @item.CPF
                        </td>
                        <td>
                          @item.CRM
                        </td>
                        <td>
                          @item.Email
                        </td>
                      </tr>
                    }
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="box-footer with-border" style="padding: 0 10pt;">
        <div class="row">
          <div class="col-md-8 ">
            @Html.PagedListPager(Model.ListaMedicoModel, pagina => Url.Action("Index", new { pagina }))
          </div>
        </div>
      </div>
    </div>
  </section>
</div>

﻿<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <title>@ViewBag.Title - Repasse </title>
  @using RepasseConvenio.Infrastructure.Controls

  <!-- Tell the browser to be responsive to screen width -->
  <meta name="viewport" content="width=device-width, initial-scale=1">

  @Styles.Render("~/Content/css")
  @Scripts.Render("~/bundles/jquery")
  @Scripts.Render("~/bundles/modernizr")
  @Scripts.Render("~/bundles/bootstrap")
  @Scripts.Render("~/bundles/inputmask")
  @Scripts.Render("~/bundles/plugins")
  @Scripts.Render("~/bundles/signal")

  @RenderSection("scripts", required: false)
  @RenderSection("Styles", required: false)

</head>
<body class="hold-transition sidebar-mini layout-fixed">
  <div class="modal-backdrop overlay" id="fundo-loading" style="opacity: 0; display: none;">
    <i class="fa fa-refresh fa-spin"></i>
  </div>
  @using RepasseConvenio.Infrastructure
  <div class="wrapper">

    <nav class="main-header navbar navbar-expand navbar-white navbar-light">
      <ul class="navbar-nav">
        <li class="nav-item">
          <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
        </li>
      </ul>

      @*<ul class="navbar-nav ml-auto">
          <li class="nav-item dropdown">
            <a class="nav-link" data-toggle="dropdown" href="#">
              <i class="far fa-bell"></i>
              <span class="badge badge-warning navbar-badge">15</span>
            </a>
            <div class="dropdown-menu dropdown-menu-lg dropdown-menu-right">
              <span class="dropdown-item dropdown-header">15 Notifications</span>
              <div class="dropdown-divider"></div>
              <a href="#" class="dropdown-item">
                <i class="fas fa-envelope mr-2"></i> 4 new messages
                <span class="float-right text-muted text-sm">3 mins</span>
              </a>
              <div class="dropdown-divider"></div>
              <a href="#" class="dropdown-item">
                <i class="fas fa-users mr-2"></i> 8 friend requests
                <span class="float-right text-muted text-sm">12 hours</span>
              </a>
              <div class="dropdown-divider"></div>
              <a href="#" class="dropdown-item">
                <i class="fas fa-file mr-2"></i> 3 new reports
                <span class="float-right text-muted text-sm">2 days</span>
              </a>
              <div class="dropdown-divider"></div>
              <a href="#" class="dropdown-item dropdown-footer">See All Notifications</a>
            </div>
          </li>

        </ul>*@
    </nav>
    <aside class="main-sidebar sidebar-dark-primary elevation-4">
      <a href="" class="brand-link">
        @*<img src="~/Content/img/AdminLTELogo.png" alt="AdminLTE Logo" class="brand-image img-circle elevation-3"
          style="opacity: .8">*@
        <span class="brand-text font-weight-light">Repasse Convênio</span>
      </a>

      <div class="sidebar">
        <div class="user-panel mt-3 pb-3 mb-3 d-flex">
          <div class="image">
            @*<img src="~/Content/img/avatar.png" class="img-circle elevation-2" alt="User Image">*@
          </div>
          <div class="info">
            <a href="#" class="d-block">@ContextoUsuario.UserLogged.Nome</a>
          </div>
        </div>

        <nav class="mt-2">
          <ul class="nav nav-pills nav-sidebar flex-column nav-compact" data-widget="treeview" role="menu" data-accordion="false">
            <li class="nav-header">Repasse</li>
            <li class="nav-item">
              <a href="@Url.Action("Index", "Repasse")" class="nav-link">
                <i class="nav-icon far fa-calendar-alt"></i>
                <p>
                  Repasses
                </p>
              </a>
            </li>
            <li class="nav-item">
              <a href="@Url.Action("Index", "RegistroPagamento")" class="nav-link">
                <i class="nav-icon far fa-file"></i>
                <p>
                  Apuração Pagamento
                </p>
              </a>
            </li>
            <li class="nav-item">
              <a href="@Url.Action("Index", "RegistroImposto")" class="nav-link">
                <i class="nav-icon far fa-file"></i>
                <p>
                  Apuração Imposto
                </p>
              </a>
            </li>

            <li class="nav-header">Glosas</li>

            <li class="nav-item">
              <a href="@Url.Action("Index", "LoteGlosa")" class="nav-link">
                <i class="nav-icon far fa-file"></i>
                <p>
                  Manutenção Glosa
                </p>
              </a>
            </li>

            <li class="nav-item">
              <a href="@Url.Action("Index", "Lote")" class="nav-link">
                <i class="nav-icon far fa-file"></i>
                <p>
                  Acompanhamento de Lotes
                </p>
              </a>
            </li>

            <li class="nav-header">Cadastros</li>
            <li class="nav-item">
              <a href="@Url.Action("Index", "Medico")" class="nav-link">
                <i class="nav-icon far fa-user"></i>
                <p>
                  Médico
                </p>
              </a>
            </li>
            <li class="nav-item">
              <a href="@Url.Action("Index", "Convenio")" class="nav-link">
                <i class="nav-icon far fa-file"></i>
                <p>
                  Convênio
                </p>
              </a>
            </li>

            <li class="nav-item">
              <a href="@Url.Action("Index", "TabelaProgressivaINSS")" class="nav-link">
                <i class="nav-icon far fa-file"></i>
                <p>
                  Tabela Progressiva INSS
                </p>
              </a>
            </li>

            <li class="nav-item">
              <a href="@Url.Action("Index", "TabelaProgressivaIRPF")" class="nav-link">
                <i class="nav-icon far fa-file"></i>
                <p>
                  Tabela IRPF
                </p>
              </a>
            </li>

            <li class="nav-item">
              <a href="@Url.Action("Index", "JustificativaGlosa")" class="nav-link">
                <i class="nav-icon far fa-file"></i>
                <p>
                  Justificativa de Glosa
                </p>
              </a>
            </li>


            <li class="nav-item">
              <a href="@Url.Action("Index", "BancoUnicooper")" class="nav-link">
                <i class="nav-icon far fa-file"></i>
                <p>
                  Banco Unicooper
                </p>
              </a>
            </li>

            <li class="nav-item">
              <a href="@Url.Action("Logout", "Account")" class="nav-link">
                <i class="nav-icon far fa-file"></i>
                <p>
                  Sair
                </p>
              </a>
            </li>

            @*<li class="nav-item">
      <a href="@Url.Action("Index", "ExtratoMedico")" class="nav-link">
        <i class="nav-icon far fa-file"></i>
        <p>
          Extratos
        </p>
      </a>
    </li>*@
          </ul>
        </nav>
      </div>
    </aside>

    <div class="content-wrapper">
     <br />
      <section class="content">
        <div class="container-fluid">
          @RenderBody()
        </div>
      </section>
    </div>
    <footer class="main-footer">
      @*<strong>Copyright &copy; 2014-2019 <a href="http://adminlte.io">AdminLTE.io</a>.</strong>
        All rights reserved.*@
      <div class="float-right d-none d-sm-inline-block">
        <b>Version</b> 3.0.5
      </div>
    </footer>
  </div>
  @Html.Partial("ModalRetornoUsuario")
  @Html.RepasseAlertToast();
</body>
</html>

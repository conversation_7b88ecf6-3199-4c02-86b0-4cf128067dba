﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using PagedList;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;
using RepasseConvenio.Infrastructure.Controls;
using System.Web.UI.WebControls;
using System.Globalization;

namespace RepasseConvenio.Services
{
  public class RegistroPagamentoServices : ServiceBase
  {
    public RegistroPagamentoServices(RepasseEntities Contexto)
        : base(Contexto)
    { }
    public RegistroPagamentoServices()
       : base()
    { }

    public RegistroPagamentoServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public RegistroPagamentoServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public R_RegistroPagamento GetById(int Id)
    {
      return Contexto.R_RegistroPagamento.Where(a => a.RP_Id == Id).FirstOrDefault();
    }

    public List<R_RegistroPagamento> GetByTerm(string term)
    {
      if (string.IsNullOrEmpty(term))
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_RegistroPagamento");

        return Contexto.Database.SqlQuery<R_RegistroPagamento>(query).ToList();

      }
      else
      {
        string query = string.Format(@"SELECT 
                                          *
                                       FROM 
                                          R_RegistroPagamento
                                       WHERE RP_CPFCNPJPagamento LIKE @termo");

        return Contexto.Database.SqlQuery<R_RegistroPagamento>(query, new SqlParameter("@termo", string.Format("%{0}%", term))).ToList();

      }
    }

    public IPagedList<RegistroPagamentoModel> Get(int page, string search, string data)
    {
      string query = @"SELECT
                      RP_Id [Codigo]
                      , RP_IdMedico [CodigoMedico]
                      , M.M_Nome [NomeMedico]
                      , RP_DataGeracao [DataGeracao]
                      , RP_CPFCNPJPagamento [CPFCNPJ]
                      , RP_Banco [Banco]
                      , RP_Agencia [Agencia]
                      , RP_Conta [Conta]
                      , RP_Valor [Valor]
                      , RP_IdUsuarioProcessamento [UsuarioProcessamento]
                      , SRP.SRP_Enum [EnumStatusRegistroPagamento]
                      , SRP.SRP_Descricao [DescricaoStatusRegistroPagamento]
                      , R.RP_DataPagamento [DataPagamento]
                      FROM R_RegistroPagamento R
                      INNER JOIN R_Medico M ON R.RP_IdMedico = M.M_Id
                      INNER JOIN R_StatusRegistroPagamento SRP ON SRP.SRP_Id = R.RP_IdStatusRegistroPagamento
                      [CONDICAO]
                       ";

      List<string> condicao = new List<string>();
      List<SqlParameter> listparametrs = new List<SqlParameter>();

      if (!string.IsNullOrEmpty(search))
      {
        search = search.ToLower();
        condicao.Add(String.Format(@" LOWER(M.M_Nome) LIKE ('%'+ @Search  +'%')"));
        listparametrs.Add(new SqlParameter("@Search", search));
      }

      if (!string.IsNullOrEmpty(data))
      {
        DateTime dtPag = Convert.ToDateTime(data, new CultureInfo("pt-Br"));
        condicao.Add(String.Format(@" R.RP_DataPagamento = @DtPag"));
        listparametrs.Add(new SqlParameter("@DtPag", dtPag));
      }

      if (listparametrs.Count > 0)
      {
        query = query.Replace("[CONDICAO]", String.Format("WHERE {0}", String.Join(" AND ", condicao.ToArray())));
        return Contexto.Database.SqlQuery<RegistroPagamentoModel>(query,listparametrs.ToArray()).ToList().ToPagedList(page, PageSize);
      }
      else
      {
        query = query.Replace("[CONDICAO]", "");
        return Contexto.Database.SqlQuery<RegistroPagamentoModel>(query).ToList().ToPagedList(page, PageSize);
      }
    }

    public void GerarRegistroPagamento(List<ExtratoMedicoRegistroPagamentoModel> ListaExtratoMedicoRegistroPagamentoModel)
    {
      ExtratoMedicoServices extratoMedicoServices = new ExtratoMedicoServices();
      MedicoService medicoService = new MedicoService();
      StatusRegistroPagamentoServices statusRegistroPagamentoServices = new StatusRegistroPagamentoServices();

      #region[Processamento Repasse]
      var GroupByProcessamentoRepasse = ListaExtratoMedicoRegistroPagamentoModel.Where(a => a.ClassificacaoRepasse == EnumClassificacaoRepasse.PROCESSAMENTODEREPASSE).GroupBy(a => new { a.CPFCNPJ, a.IdMedico, a.Banco, a.Agencia, a.Conta }).ToList();
      List<ExtratoRegistroPagamentoCreateModel> extratosProcessamentoRepasse = GroupByProcessamentoRepasse.Select(a => new ExtratoRegistroPagamentoCreateModel()
      {
        CPFCNPJ = a.Key.CPFCNPJ,
        IdMedico = a.Key.IdMedico,
        Agencia = a.Key.Agencia,
        Banco = a.Key.Banco,
        Conta = a.Key.Conta,
        ValorTotal = a.Sum(b => b.TipoLancamento == "D" ? (b.Valor * -1) : b.Valor),
        Extratos = a.ToList(),
      }).ToList();

      foreach (ExtratoRegistroPagamentoCreateModel CreateModel in extratosProcessamentoRepasse)
      {
        using (var scope = new TransactionScope(TransactionScopeOption.RequiresNew))
        {
          int IdStatusRepasse = int.MinValue;
          bool StatusMedico = medicoService.GetStatusMedico(CreateModel.IdMedico);
          if (StatusMedico)
            IdStatusRepasse = statusRegistroPagamentoServices.GetIdByEnum(EnumStatusRegistroPagamento.Repasse);
          else
            IdStatusRepasse = statusRegistroPagamentoServices.GetIdByEnum(EnumStatusRegistroPagamento.RepasseAtoNaoCooperado);

          R_RegistroPagamento registroPagamento = Create(CreateModel, IdStatusRepasse);
          List<int> ListaIdExtrato = CreateModel.Extratos.Select(a => a.Id).ToList();
          extratoMedicoServices.UpdateExtratosComPagamento(ListaIdExtrato, registroPagamento.RP_Id);

          ValoresTabelaProgressivaINSSServices valoresTabelaProgressivaINSSServices = new ValoresTabelaProgressivaINSSServices(Contexto);
          R_ValoresTabelaProgressivaINSS valorINSS = valoresTabelaProgressivaINSSServices.GetValorTabelaProgressivaINSS(DateTime.Now, CreateModel.ValorTotal);
          R_ValoresTabelaProgressivaIRPF valorIRPF = valoresTabelaProgressivaINSSServices.GetValorTabelaProgressivaIRPF(DateTime.Now, CreateModel.ValorTotal);
          int IdClassificacaoINSS = new ClassificacaoRepasseServices(Contexto).GetIdByEnum(EnumClassificacaoRepasse.INSS);
          int IdClassificacaoIRPF = new ClassificacaoRepasseServices(Contexto).GetIdByEnum(EnumClassificacaoRepasse.IRPF);

          CodDocumentoService codDocumentoService = new CodDocumentoService();
          string CodigoDocumento = codDocumentoService.GetSequencialDocumento();

          #region Extrato Débito INSS
          if (valorINSS != null && valorINSS.V_AliquotaProgressiva > 0)
          {
            R_ExtratoMedico extratoDebitoINSS = new R_ExtratoMedico();
            extratoDebitoINSS.EX_DataCriacao = DateTime.Now;
            extratoDebitoINSS.EX_DataApuracao = DateTime.Now;
            //extratoDebitoINSS.EX_DataPagamento = DateTime.Now;
            //extratoDebitoINSS.EX_CodigoRepasse = ;
            extratoDebitoINSS.EX_TipoLancamento = EnumTipoLancamento.D.ToString();
            extratoDebitoINSS.EX_IdClassificacao = IdClassificacaoINSS;
            extratoDebitoINSS.EX_Valor = CreateModel.ValorTotal * (valorINSS.V_AliquotaProgressiva / 100);
            //extratoDebitoINSS.EX_IdConvenio = ;
            extratoDebitoINSS.EX_TipoConta = TipoPessoaEnum.PF.ToString();
            extratoDebitoINSS.EX_CPFCNPJDeposito = CreateModel.CPFCNPJ;
            extratoDebitoINSS.EX_IdMedico = CreateModel.IdMedico;
            extratoDebitoINSS.Observacao = $"Extrato débito criado";
            extratoDebitoINSS.EX_CodigoDocumento = CodigoDocumento;
            extratoDebitoINSS.EX_IdUsuarioCriacao = 4;
            extratoDebitoINSS.EX_IdRegistroPagamento = registroPagamento.RP_Id;
            Create(extratoDebitoINSS);

            registroPagamento.RP_Valor -= extratoDebitoINSS.EX_Valor;
          }
          #endregion

          #region Extrato Débito IRPF
          if (valorIRPF != null && valorIRPF.V_AliquotaProgressiva > 0)
          {
            R_ExtratoMedico extratoDebitoIRPF = new R_ExtratoMedico();
            extratoDebitoIRPF.EX_DataCriacao = DateTime.Now;
            extratoDebitoIRPF.EX_DataApuracao = DateTime.Now;
            //extratoDebitoIRPF.EX_DataPagamento = DateTime.Now;
            //extratoDebitoIRPF.EX_CodigoRepasse = ;
            extratoDebitoIRPF.EX_TipoLancamento = EnumTipoLancamento.D.ToString();
            extratoDebitoIRPF.EX_IdClassificacao = IdClassificacaoIRPF;
            extratoDebitoIRPF.EX_Valor = registroPagamento.RP_Valor * (valorIRPF.V_AliquotaProgressiva / 100);
            //extratoDebitoIRPF.EX_IdConvenio = ;
            extratoDebitoIRPF.EX_TipoConta = TipoPessoaEnum.PF.ToString();
            extratoDebitoIRPF.EX_CPFCNPJDeposito = CreateModel.CPFCNPJ;
            extratoDebitoIRPF.EX_IdMedico = CreateModel.IdMedico;
            extratoDebitoIRPF.Observacao = $"Extrato débito criado";
            extratoDebitoIRPF.EX_CodigoDocumento = CodigoDocumento;
            extratoDebitoIRPF.EX_IdUsuarioCriacao = 4;
            extratoDebitoIRPF.EX_IdRegistroPagamento = registroPagamento.RP_Id;
            Create(extratoDebitoIRPF);

            registroPagamento.RP_Valor -= extratoDebitoIRPF.EX_Valor;
          }
          #endregion

          Edit(registroPagamento);

          scope.Complete();
        }
      }
      #endregion

      #region[Processamento ProLabore]
      var GroupByProLabore = ListaExtratoMedicoRegistroPagamentoModel
                              .Where(a => a.ClassificacaoRepasse == EnumClassificacaoRepasse.PROLABORE
                              || a.ClassificacaoRepasse == EnumClassificacaoRepasse.INSS
                              || a.ClassificacaoRepasse == EnumClassificacaoRepasse.IRPF).GroupBy(a => new { a.CPFCNPJ, a.IdMedico, a.Banco, a.Agencia, a.Conta }).ToList();

      List<ExtratoRegistroPagamentoCreateModel> extratosProLabore = GroupByProLabore.Select(a => new ExtratoRegistroPagamentoCreateModel()
      {
        CPFCNPJ = a.Key.CPFCNPJ,
        IdMedico = a.Key.IdMedico,
        Agencia = a.Key.Agencia,
        Banco = a.Key.Banco,
        Conta = a.Key.Conta,
        ValorTotal = a.Sum(b => b.TipoLancamento == "D" ? (b.Valor * -1) : b.Valor),
        Extratos = a.ToList(),
      }).ToList();

      foreach (ExtratoRegistroPagamentoCreateModel CreateModel in extratosProLabore)
      {
        using (var scope = new TransactionScope(TransactionScopeOption.RequiresNew))
        {
          int IdStatusRepasse = int.MinValue;
          bool StatusMedico = medicoService.GetStatusMedico(CreateModel.IdMedico);
          if (StatusMedico)
            IdStatusRepasse = statusRegistroPagamentoServices.GetIdByEnum(EnumStatusRegistroPagamento.Prolabore);
          else
            IdStatusRepasse = statusRegistroPagamentoServices.GetIdByEnum(EnumStatusRegistroPagamento.ProlaboreAtoNaoCooperado);



          R_RegistroPagamento registroPagamento = Create(CreateModel, IdStatusRepasse);
          List<int> ListaIdExtrato = CreateModel.Extratos.Select(a => a.Id).ToList();
          extratoMedicoServices.UpdateExtratosComPagamento(ListaIdExtrato, registroPagamento.RP_Id);
          scope.Complete();
        }
      }
      #endregion
    }
    public R_RegistroPagamento Create(ExtratoRegistroPagamentoCreateModel extratoRegistroPagamentoCreateModel, int IdStatusRegistro)
    {
      R_RegistroPagamento registroPagamento = new R_RegistroPagamento();
      registroPagamento = extratoRegistroPagamentoCreateModel.toCreateRegistroPagamento(User.IdUsuario, IdStatusRegistro);

      Create(registroPagamento);
      return registroPagamento;
    }

    public List<ExtratoMedicoRegistroPagamentoModel> GetExtratoPagamento(DateTime? datade, DateTime? dataate, int? idMedico)
    {
      try
      {
        string query = @"SELECT
                          EXM.EX_Id [Id],
                          EXM.EX_CPFCNPJDeposito [CPFCNPJ],
                          EXM.EX_Valor [Valor],
                          EXM.EX_IdMedico [IdMedico],
                          EXM.EX_TipoLancamento [TipoLancamento],
                          ISNULL(EM.EM_AgenciaEmpresa, M.M_AgenciaMedico) [Agencia],
                          ISNULL(EM.EM_BancoEmpresa, M.M_BancoMedico) [Banco],
                          ISNULL(EM.EM_ContaEmpresa, M.M_ContaMedico) [Conta],
                          CR.CR_Enum [ClassificacaoRepasse]
                        FROM R_ExtratoMedico EXM
                        INNER JOIN R_ClassificacaoRepasse CR ON CR.CR_Id = EXM.EX_IdClassificacao
							          LEFT JOIN R_EmpresaMedico EM ON EXM.EX_IdMedico = EM.EM_IdMedico AND EM.EM_CNPJ = EXM.EX_CPFCNPJDeposito
							          LEFT JOIN R_Medico M ON EXM.EX_IdMedico = M.M_Id
						            WHERE EX_IdRegistroPagamento IS NULL ";

        List<SqlParameter> param = new List<SqlParameter>();

        if (dataate.HasValue && datade.HasValue)
        {
          query = query + "AND Convert(date,EXM.EX_DataApuracao) BETWEEN Convert(date,@datade) AND Convert(date,@dataate)";

          param.Add(new SqlParameter("@datade", datade.Value));
          param.Add(new SqlParameter("@dataate", dataate.Value));
        }

        if (idMedico.HasValue && idMedico != 0)
        {
          query = query + " AND EXM.EX_IdMedico = @id";
          param.Add(new SqlParameter("@id", idMedico.Value));
        }

        return Contexto.Database.SqlQuery<ExtratoMedicoRegistroPagamentoModel>(query, param.ToArray()).ToList();
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }

    public void RealizarPagamento(DateTime DataPagamento, List<int> ListIdRegistro)
    {
      ExtratoMedicoServices extratoMedicoServices = new ExtratoMedicoServices();
      ControleMensalService controleMensalService = new ControleMensalService();

      foreach (var item in ListIdRegistro)
      {
        using (TransactionScope scope = new TransactionScope(TransactionScopeOption.RequiresNew))
        {
          #region[Registro Pagamento]
          R_RegistroPagamento registroPagamento = GetById(item);
          if (!registroPagamento.RP_DataPagamento.HasValue)
          {
            registroPagamento.RP_DataPagamento = DataPagamento;
            Edit(registroPagamento);
            #endregion

            #region[Pagamento Extrato]
            extratoMedicoServices.RealizarPagamento(item, DataPagamento);
            #endregion

            #region[Atualizando Controle Mensal]
            controleMensalService.AtualizarControleMensal(DataPagamento);
            #endregion
          }
          scope.Complete();
        }
      }


    }

    public void DeleteRegistros(List<int> idsRegistro)
    {
      using (var scope = new TransactionScope(TransactionScopeOption.RequiresNew))
      {
        foreach (int id in idsRegistro)
        {
          R_RegistroPagamento registroPagamento = GetById(id);

          if (registroPagamento.RP_DataPagamento.HasValue)
            throw new CustomException(string.Format("Pagamento para {0} de R${1} já realizado, logo não pode ser excluído.", registroPagamento.R_Medico.M_Nome, registroPagamento.RP_Valor));
          new ExtratoMedicoServices().RemoveIdRegistro(registroPagamento.RP_Id);
          Delete(registroPagamento);
        }

        scope.Complete();
      }
    }

    public List<ExtratoMedicoRegistroPagamento> GetExtratos(int idRegistro)
    {
      string Query = @"SELECT
	                        EX.EX_Id Codigo,
	                        GA.GA_NroUnicooper NroGuiaAtendimento,
	                        EX.EX_Valor Valor,
	                        EX.EX_DataApuracao DataProcessamento,
	                        EX.EX_TipoLancamento TipoLancamento
                        FROM R_ExtratoMedico EX
                        LEFT JOIN R_GuiaAtendimento GA ON GA.GA_Id=EX.EX_IdGuiaAtendimento
                        WHERE EX_IdRegistroPagamento = @Id";

      return Contexto.Database.SqlQuery<ExtratoMedicoRegistroPagamento>(Query, new SqlParameter("@Id", idRegistro)).ToList();
    }
  }
}


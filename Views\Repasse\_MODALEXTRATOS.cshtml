﻿@using RepasseConvenio.Models
@using RepasseConvenio.Infrastructure.Controls
@model ExtratoRepasseModal

<div class="modal fade" id="ModalExtratos" data-keyboard="false" data-backdrop="static">
  <div class="modal-dialog" style="max-width: 90%;">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">Extratos</h4>

        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">×</span>
        </button>
      </div>

      <div class="modal-body">
        @using (Html.BeginForm("Filtrar", "Extrato", FormMethod.Post, new { enctype = "multipart/form-data", id = "FormExtratoFiltrar" }))
        {
          @Html.HiddenFor(a => a.IdRepasseExtrato)
          <div class="row col-md-12"  style=" display: flex; align-items: flex-end;">
            <div class="col-md-4">
              <div class="form-group">
                @Html.LabelFor(a => a.NomeMedico)
                @Html.EditorFor(a => a.NomeMedico, new { htmlAttributes = new { @class = "form-control" } })
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                @Html.LabelFor(a => a.ClassificacaoSelect)
                @Html.LibSelect2For(a => a.ClassificacaoSelect, new { @class = "form-control" })
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-group">
                <button type="button" class="btn btn-info" id="FiltrarExtratos"> Filtrar </button>
              </div>
            </div>
          </div>
        }


        <div class="row">
          <div class="col-md-12 container">
            <div id="PartialExtratos">
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
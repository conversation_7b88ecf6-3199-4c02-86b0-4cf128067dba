﻿@using RepasseConvenio.Models
@using PagedList
@using PagedList.Mvc;
@model ProtocoloGlosaIndexCabecalho

@{
  ViewBag.Title = "Protocolo Glosa";
}

@Scripts.Render("~/Views/ProtocoloGlosa/ProtocoloGlosa.js?a=a")
@Styles.Render("~/Views/ProtocoloGlosa/ProtocoloGlosa.css?a=a")

<div class="row">
  <section class="col-lg-12 connectedSortable ui-sortable">
    <div class="card">
      <div class="card-header ui-sortable-handle">
        <h3 class="card-title">
          <i class="fas fa-chart-pie mr-1"></i>
          Protocolo Glosas
        </h3>
        <div class="card-tools">
          <div class="card-acoes">
            @*<button type="button" class="btn btn-outline-primary disabled" style="margin-left: 10px" id="TransferirResponsavel">Transf. Resp.</button>
              <button type="button" class="btn btn-outline-secondary disabled" style="margin-left: 10px" id="DetalhesLoteGlosa">Detalhes</button>*@
            @if (Model.ListaProtocoloGlosaIndex.Count() == 0)
            {
              <button type="button" class="btn btn-outline-info" id="NovoProtocolo" onclick="location.href='@Url.Action("Create", "ProtocoloGlosa", new { CodigoLoteGlosa = Model.IdLoteGlosa })'">Novo</button>
            }
            else
            {
              <a type="button" class="btn btn-outline-info disabled" disabled id="DetalhesProtocolo">Detalhes</a>
            }
          </div>
        </div>
      </div><!-- /.card-header -->
      <div class="card-body">
        <div class="tab-content p-0">
          <div class="box-body">
            <div class="row">
              <div class="col-md-12 table-responsive p-0 ">
                <table class="table table-sm table-striped table-hover text-nowrap">
                  <thead>
                    <tr>
                      <th>
                        Número Protocolo
                      </th>
                      <th>
                        Emitida em
                      </th>
                      <th>
                        Prazo Retorno Recurso
                      </th>
                      <th>
                        Criado em
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    @foreach (ProtocoloGlosaIndex item in Model.ListaProtocoloGlosaIndex)
                    {
                      <tr class="TrSelecionavel" data-codigoprotocologlosa="@item.Codigo">
                        <td>
                          @item.NumeroProtocolo
                        </td>
                        <td>
                          @item.DataEmissao.ToString("dd/MM/yyyy")
                        </td>
                        <td>
                          @item.DataPrazo.ToString("dd/MM/yyyy")
                        </td>
                        <td>
                          @item.DataCriacao.ToString("dd/MM/yyyy")
                        </td>
                      </tr>
                    }
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <div class="box-footer with-border" style="padding: 0 10pt;">
            <div class="row">
              <div class="col-md-8 ">
                @Html.PagedListPager(Model.QuantidadeProtocoloGlosa, Page => Url.Action("Index", new { Page }))
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="card-footer">
        <button type="button" value="Voltar" class="btn btn-secondary" onclick="window.location.href='@Url.Action("Index","LoteGlosa")'">Voltar</button>
      </div>
    </div>
  </section>
</div>

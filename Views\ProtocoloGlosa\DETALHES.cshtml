﻿@using RepasseConvenio.Models
@using RepasseConvenio.Infrastructure
@using RepasseConvenio.Infrastructure.Controls
@model ProtocoloGlosaDetalhes

@{
  ViewBag.Title = "Detalhes Protocolo";
  Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="row">
  <section class="col-lg-12 connectedSortable ui-sortable">
    <div class="card">
      <div class="card-header ui-sortable-handle">
        <h3 class="card-title">
          <i class="fas fa-chart-pie mr-1"></i>
        </h3>
        <div class="card-tools">
        </div>
      </div><!-- /.card-header -->
      <div class="card-body">
        <div class="row">
          <div class="col-md-4">
            <div class="form-group">
              @Html.LabelFor(c => c.NumeroProtocolo)
              @Html.EditorFor(c => c.NumeroProtocolo, new { htmlAttributes = new { @class = "form-control", disabled = true } })
              @Html.ValidationMessageFor(c => c.NumeroProtocolo, "", new { @class = "text-danger" })
            </div>
          </div>
          <div class="col-md-4">
            <div class="form-group">
              @Html.LabelFor(c => c.DataEmissao)
              @Html.EditorFor(c => c.DataEmissao, new { htmlAttributes = new { @class = "form-control airDatePickerDateTime", disabled = true } })
              @Html.ValidationMessageFor(c => c.DataEmissao, "", new { @class = "text-danger" })
            </div>
          </div>
          <div class="col-md-4">
            <div class="form-group">
              @Html.LabelFor(c => c.DataPrazo)
              @Html.EditorFor(c => c.DataPrazo, new { htmlAttributes = new { @class = "form-control airDatePickerDateTime", disabled = true } })
              @Html.ValidationMessageFor(c => c.DataPrazo, "", new { @class = "text-danger" })
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-12">
            <div class="form-group">
              @Html.LibLabel(c => c.ObservacoesGerais)
              @Html.TextAreaFor(c => c.ObservacoesGerais, new { @class = "form-control", disabled = true })
              @Html.ValidationMessageFor(c => c.ObservacoesGerais, "", new { @class = "text-danger" })
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-12">
            <table class="table table-sm table-striped table-hover text-nowrap">
              <thead>
                <tr>
                  <th>
                    Arquivo
                  </th>
                  <th>
                    Inserido em:
                  </th>
                  <th>
                  </th>
                </tr>
              </thead>
              <tbody>
                @foreach (var item in Model.ListaAnexoProtocoloGlosaDetalhes)
                {
                  <tr>
                    <td>
                      @item.NomeArquivoWithExtension
                    </td>
                    <td>
                      @item.DataAnexo.ToString("dd/MM/yyyy")
                    </td>
                    <td>
                      <button class="customBtnNeutro BaixarAnexo" data-codigoprotocologlosa="@item.Codigo" onclick="window.open('@Url.Action("Download","AnexoProtocoloGlosa", new { Id = item.Codigo })', '_blank')"><i class="fas fa-download"></i></button>
                    </td>
                  </tr>
                }
              </tbody>
            </table>
          </div>
        </div>

      </div>
      <div class="card-footer">
        <button type="button" value="Voltar" class="btn btn-secondary" onclick="window.location.href='@Url.Action("Index","ProtocoloGlosa", new { IdLoteGosa = Model.CodigoLoteGlosa })'">Voltar</button>
      </div>
    </div>
  </section>
</div>

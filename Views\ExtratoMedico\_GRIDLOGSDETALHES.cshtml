﻿@using RepasseConvenio.Models
@model List<LogAux>

<table class="table table-sm table-striped table-hover text-nowrap">
  <thead>
    <tr>
      <th>
        Campo
      </th>
      <th>
        Valor Antigo
      </th>
      <th>
        Novo valor
      </th>
    </tr>
  </thead>
  <tbody>
    @foreach (LogAux item in Model)
    {
      <tr>
        <td>
          @item.DisplayName
        </td>
        <td>
          @item.OldValue
        </td>
        <td>
          @item.NewValue
        </td>
      </tr>
    }
  </tbody>
</table>

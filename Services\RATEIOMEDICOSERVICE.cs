﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Models;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Web;

namespace RepasseConvenio.Services
{
  public class RateioMedicoService : ServiceBase
  {
    public RateioMedicoService()
   : base()
    { }
    public RateioMedicoService(RepasseEntities Contexto)
       : base(Contexto)
    { }

    public RateioMedicoService(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public RateioMedicoService(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public List<RateioMedicoModel> Get()
    {
      string query = @"SELECT
                          RM_CodigoProcedimento [CodigoProcedimento]
                        , RM_DescricaoProcedimento [DescricaoProcedimento]
                        , RM_IdHospital [CodigoHospital]
                        , RM_IdMedico [CodigoMedico]
                        , RM_Id [Codigo]
                     FROM R_RateioMedico";

      return Contexto.Database.SqlQuery<RateioMedicoModel>(query).ToList();
    }

    public List<RateioMedicoIndex> GettoIndex(int id)
    {
      string query = @"SELECT
                          RM.RM_CodigoProcedimento [CodigoProcedimento]
                        , RM.RM_DescricaoProcedimento [DescricaoProcedimento]
                        , H.H_Nome [NomeHospital]
                        , H.H_CNPJ [CNPJHospital]
                        , M.M_Id [CodigoMedico]
                        , RM.RM_Id [Codigo]
                     FROM R_RateioMedico RM
                      INNER JOIN R_Medico M ON M.M_Id = RM.RM_IdMedico
                      LEFT JOIN R_Hospital H ON H.H_Id = RM.RM_IdHospital 
                      WHERE M.M_Id = @id";

      return Contexto.Database.SqlQuery<RateioMedicoIndex>(query, new SqlParameter("@id",id)).ToList();
    }

    public RateioMedicoIndex GettoIndexbyId(int id)
    {
      string query = @"SELECT
                          RM.RM_CodigoProcedimento [CodigoProcedimento]
                        , RM.RM_DescricaoProcedimento [DescricaoProcedimento]
                        , H.H_Nome [NomeHospital]
                        , H.H_CNPJ [CNPJHospital]
                        , M.M_Id [CodigoMedico]
                        , RM.RM_Id [Codigo]
                     FROM R_RateioMedico RM
                      INNER JOIN R_Medico M ON M.M_Id = RM.RM_IdMedico
                      LEFT JOIN R_Hospital H ON H.H_Id = RM.RM_IdHospital 
                      WHERE RM.RM_Id = @id";

      return Contexto.Database.SqlQuery<RateioMedicoIndex>(query, new SqlParameter("@id", id)).FirstOrDefault();
    }

    public RateioMedicoModel GetbyId(int id)
    {
      string query = @"SELECT
                          RM.RM_CodigoProcedimento [CodigoProcedimento]
                        , RM.RM_DescricaoProcedimento [DescricaoProcedimento]
                        , H.H_Nome [NomeHospital]
                        , H.H_CNPJ [CNPJHospital]
                        , M.M_Id [CodigoMedico]
                        , RM.RM_Id [Codigo]
                     FROM R_RateioMedico RM
                      INNER JOIN R_Medico M ON M.M_Id = RM.RM_IdMedico
                      LEFT JOIN R_Hospital H ON H.H_Id = RM.RM_IdHospital 
                      WHERE RM.RM_Id = @id";

      return Contexto.Database.SqlQuery<RateioMedicoModel>(query, new SqlParameter("@id", id)).FirstOrDefault();
    }

    public bool ExistProcedimento(string CodProc, int hospital, int medico)
    {
      R_RateioMedico rateioMedico = Contexto.R_RateioMedico.Where(a => a.RM_CodigoProcedimento.ToUpper().Equals(CodProc.ToUpper()) && a.RM_IdHospital == hospital && a.RM_IdMedico == medico).FirstOrDefault();

      if (rateioMedico == null)
        return false;
      else
        return true;
    }

    public void Create(RateioMedicoModel model)
    {
      try
      {
        R_RateioMedico rateio = model.toRateioMedicoCreate();
        Create(rateio);
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }

    public void Edit(RateioMedicoModel model)
    {
      R_RateioMedico rateio = model.toRateioMedicoEdit();
      Edit(rateio);
    }
  }
}
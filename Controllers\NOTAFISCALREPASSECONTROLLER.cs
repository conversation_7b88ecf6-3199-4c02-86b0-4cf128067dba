﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Models;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;


namespace RepasseConvenio.Controllers
{
  public class NotaFiscalRepasseController : LibController
  {
    private NotaFiscalRepasseService NotaFiscalRepasseService
    {
      get
      {
        if (_NotaFiscalRepasseService == null)
          _NotaFiscalRepasseService = new NotaFiscalRepasseService(ContextoUsuario.UserLogged);

        return _NotaFiscalRepasseService;
      }
    }
    private NotaFiscalRepasseService _NotaFiscalRepasseService;

    [HttpPost]
    public JsonResult Create(NotaFiscalRepasseModel model)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        R_NotaFiscaRepasse notaFiscaRepasse = NotaFiscalRepasseService.Create(model);
        retornoAjax.Titulo = "Sucesso";
        retornoAjax.Mensagem = "Nota Fiscal inserida com sucesso.";
        retornoAjax.TipoMensagem = "success";
        retornoAjax.Erro = false;
        return Json(new { retornoAjax = retornoAjax, IdNotafiscal = notaFiscaRepasse.NF_Id }, JsonRequestBehavior.AllowGet);
      }
      catch (CustomException ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(new { retornoAjax = retornoAjax }, JsonRequestBehavior.AllowGet);
      }
      catch (Exception ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(new { retornoAjax = retornoAjax }, JsonRequestBehavior.AllowGet);
      }
    }

    [HttpPost]
    public ActionResult Edit(NotaFiscalRepasseModel model)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        NotaFiscalRepasseService.Edit(model);
        retornoAjax.Titulo = "Sucesso";
        retornoAjax.Mensagem = "Nota Fiscal alterada com sucesso.";
        retornoAjax.TipoMensagem = "success";
        retornoAjax.Erro = false;
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
      catch (CustomException ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
      catch (Exception ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
    }

    [HttpPost]
    public ActionResult Delete(int id)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        NotaFiscalRepasseService.Delete(id);
        retornoAjax.Titulo = "Sucesso";
        retornoAjax.Mensagem = "Nota fiscal removida com sucesso.";
        retornoAjax.TipoMensagem = "success";
        retornoAjax.Erro = false;
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
      catch (CustomException ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
      catch (Exception ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
    }
  }
}
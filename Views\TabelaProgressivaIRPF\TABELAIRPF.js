﻿var TabelaProgressivaIRPF = function () {

};

TabelaProgressivaIRPF.init = function () {

  $(document).on("click", "#open-modal-valoresIR", function () {
    $('#modalvaloresir').modal("show");
  });

  $(document).on("click", "#btnCreateIR", function () {
    TabelaProgressivaIRPF.CreateValor();
  });

  $(document).on("click", ".btnEditIR", function () {
    TabelaProgressivaIRPF.EditValor();
  });

  $(document).on("click", ".btnRemover", function () {
    var codigo = $(this).data("codigo");
    TabelaProgressivaIRPF.RemoveValor(codigo);
  });


};

TabelaProgressivaIRPF.CreateValor = function () {
  $.ajax({
    type: 'POST',
    url: GetURLBaseComplete() + '/ValoresTabelaProgressivaIRPF/Create',
    processData: false,
    data: $("#FormValoresIR").serialize(),
    tradicional: true,
    success: function (data) {
      if (!data.Erro) {
        AlertaToast(data.Titulo, data.Mensagem, data.TipoMensagem, 5000, "#97f7c1");
        TabelaProgressivaIRPF.GetValores();
      }
      else {
        Alerta(data.Titulo, data.Mensagem, "error");
      }
    },
    error: function (err) {
      Alerta("Erro", "Gentileza tentar mais tarde.", "error");
    }
  });
}

TabelaProgressivaIRPF.EditValor = function () {
  $.ajax({
    type: 'POST',
    url: GetURLBaseComplete() + '/ValoresTabelaProgressivaIRPF/Edit',
    processData: false,
    data: $("#FormValores").serialize(),
    tradicional: true,
    success: function (data) {
      if (!data.Erro) {
        TabelaProgressivaIRPF.GetValores();
        AlertaToast(data.Titulo, data.Mensagem, data.TipoMensagem, 5000, "#97f7c1");
      }
      else {
        Alerta(data.Titulo, data.Mensagem, "error");
      }
    },
    error: function (err) {
      Alerta("Erro", "Gentileza tentar mais tarde.", "error");
    }
  });
}

TabelaProgressivaIRPF.GetValores = function () {
  var codTabelaProgressivaIRPF = $("#Codigo").val();
  $.ajax({
    type: 'GET',
    url: GetURLBaseComplete() + '/TabelaProgressivaIRPF/GridValoresTPIRPF',
    dataType: 'html',
    data: {
      id: codTabelaProgressivaIRPF
    },
    success: function (data) {
      $("#DivGridValoresTabela").html(data);
      $('#modalvaloresir').modal("hide");
    },
  });
}

TabelaProgressivaIRPF.RemoveValor = function (codigo) {
  if (codigo != 0)
    Swal.fire({
      title: "Deseja continuar a exclusão?",
      html: "Você irá excluir este item da Tabela IRPF, deseja continuar?",
      icon: "warning",
      confirmButtonText: "Excluir",
      showCancelButton: true,
      cancelButtonText: "Cancelar",
      showLoaderOnConfirm: true,
      dangerMode: true,
    })
      .then((willDelete) => {
        if (willDelete.isConfirmed) {
          $.ajax({
            type: 'POST',
            url: GetURLBaseComplete() + '/ValoresTabelaProgressivaIRPF/Delete',
            dataType: "json",
            data: {
              id: codigo,
            },
            success: function (data) {
              if (!data.Erro) {
                AlertaToast(data.Titulo, data.Mensagem, data.TipoMensagem, 5000, "#97f7c1");
                TabelaProgressivaIRPF.GetValores();
              }
              else {
                Alerta(data.Titulo, data.Mensagem, "error");
              }
            },
            error: function (err) {
              Alerta("Erro", "Gentileza tentar mais tarde.", "error");
            }
          });
        } else {
        }
      });
  else
    Alerta("Error", "Você deve selecionar pelo menos um item.", "error", "OK");
}


  $(document).on("click", ".TrSelecionavel", function () {
    var idIRPF = $(this).data("codigoirpf");
    var urlEdit = GetURLBaseComplete() + "/TabelaProgressivaIRPF/Edit?codigo=" + idIRPF;
    var urlDelete = GetURLBaseComplete() + "/TabelaProgressivaIRPF/Delete?codigo=" + idIRPF;


    if (!$(this).hasClass("Selected")) {
      $('.Selected').each(function (index, element) {
        $(element).removeClass("Selected")
        $(element).addClass("UnSelected")
      });

      if ($(this).hasClass("Selected")) {
        $(this).removeClass("Selected")
        $(this).addClass("UnSelected")
      }
      else {
        $(this).addClass("Selected")
        $(this).removeClass("UnSelected")
      }
    }
    else {
      $(this).removeClass("Selected")
      $(this).addClass("UnSelected")
    }

    var countSelected = 0;
    $('.Selected').each(function (index, element) {
      if ($(element).hasClass("Selected")) {
        $('#IrpfEdit').attr("href", urlEdit);
        $('#IrpfDelete').attr("href", urlDelete);

        countSelected++;
      }
    });

    if (countSelected == 0) {
      $('#IrpfEdit').removeAttr("href");
      $('#IrpfDelete').removeAttr("href");


      $('#IrpfEdit').attr("disabled", true);
      $('#IrpfDelete').attr("disabled", true);


      $('#IrpfEdit').addClass("disabled");
      $('#IrpfDelete').addClass("disabled");

    }
    else {
      $('#IrpfEdit').attr("disabled", false);
      $('#IrpfDelete').attr("disabled", false);


      $('#IrpfEdit').removeClass("disabled");
      $('#IrpfDelete').removeClass("disabled");

    }
  });

$(document).ready(function () {
  TabelaProgressivaIRPF.init();
});
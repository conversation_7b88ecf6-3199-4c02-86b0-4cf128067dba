﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Services;
using RepasseConvenio.WSPortalCooperado;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Security.Principal;
using System.Web;

namespace RepasseConvenio.Models
{
  [MetadataType(typeof(ExtratoMedicoMetadata))]
  public partial class R_ExtratoMedico
  {
  }

  internal sealed class ExtratoMedicoMetadata
  {
    public int EX_Id { get; set; }

    [DisplayName("Apuração")]
    public System.DateTime EX_DataApuracao { get; set; }
    [DisplayName("Data Pagamento")]
    public Nullable<System.DateTime> EX_DataPagamento { get; set; }
    public string EX_CodigoRepasse { get; set; }
    [DisplayName("Lançamento")]
    public string EX_TipoLancamento { get; set; }
    [DisplayName("Classificação")]
    public int EX_IdClassificacao { get; set; }
    [DisplayName("Valor")]
    public decimal EX_Valor { get; set; }
    public Nullable<int> EX_IdConvenio { get; set; }
    [DisplayName("Conta")]
    public string EX_TipoConta { get; set; }
    [DisplayName("Empresa")]
    public string EX_CPFCNPJDeposito { get; set; }
    public int EX_IdMedico { get; set; }
    [DisplayName("Observações")]
    public string Observacao { get; set; }
    public Nullable<int> EX_IdRegistroPagamento { get; set; }
    public string EX_CodigoDocumento { get; set; }
    public Nullable<int> EX_IdGuiaAtendimento { get; set; }
    public Nullable<int> EX_IdRegistroImposto { get; set; }
    public int EX_IdUsuarioCriacao { get; set; }
    public System.DateTime EX_DataCriacao { get; set; }
  }

  public class ExtratoMedicoEdit
  {
    public int CodigoExtrato { get; set; }

    public int CodigoMedico { get; set; }

    [DisplayName("Repasse")]
    public string NumeroRepasse { get; set; }

    [DisplayName("Apuração")]
    [Required(ErrorMessage = "É necessário informar a Data Apuração.")]
    public DateTime DataApuracao { get; set; }

    [DisplayName("Data Pagamento")]
    public DateTime? DataPagamento { get; set; }

    [DisplayName("Valor")]
    [Required(ErrorMessage = "É necessário informar o valor.")]
    public decimal Valor { get; set; }

    public int? CodigoClassificacao { get; set; }

    [DisplayName("Selecione a Classificação")]
    [URLSelect("Select2/GetClassificacaoSelect")]
    [PlaceHolderAttr("Selecione a Classificação")]
    [Required(ErrorMessage = "É necessário selecionar a Classificação.")]
    public Select2Model ClassificacaoSelect
    {
      get
      {
        ClassificacaoRepasseServices ClassificacaoRepasseServices = new ClassificacaoRepasseServices();
        return ClassificacaoRepasseServices.GetById(this.CodigoClassificacao).ToSelect2Model();
      }
      set
      {
        if (value != null && !String.IsNullOrEmpty(value.id) && !value.id.Equals("0"))
          this.CodigoClassificacao = int.Parse(value.id);
      }
    }

    [DisplayName("Lançamento")]
    [Required(ErrorMessage = "Campo lançamento é obrigatório")]
    public EnumTipoLancamento TipoLancamentoEnum
    {
      get
      {
        if (!string.IsNullOrEmpty(this.TipoLancamento))
          return (EnumTipoLancamento)Enum.Parse(typeof(EnumTipoLancamento), this.TipoLancamento);
        else
          return EnumTipoLancamento.C;
      }
      set
      {
        this.TipoLancamento = value.ToString();
      }
    }
    public string TipoLancamento { get; set; }

    [DisplayName("Conta")]
    [Required(ErrorMessage = "Campo Conta é obrigatório")]
    public EnumTipoConta TipoContaEnum
    {
      get
      {
        if (!string.IsNullOrEmpty(this.TipoConta))
          return (EnumTipoConta)Enum.Parse(typeof(EnumTipoConta), this.TipoConta);
        else
          return EnumTipoConta.PF;
      }
      set
      {
        this.TipoConta = value.ToString();
      }
    }
    public string TipoConta { get; set; }

    public int? CodigoEmpresa { get; set; }

    public string CPFCNPJ { get; set; }

    [DisplayName("Selecione a Empresa")]
    [URLSelect("Select2/GetEmpresaMedicoCNPJSelect")]
    [PlaceHolderAttr("Selecione a Empresa")]
    public Select2Model EmpresaMedicoCNPJSelect
    {
      get
      {
        EmpresaMedicoService EmpresaMedicoService = new EmpresaMedicoService();
        return EmpresaMedicoService.GetByCNPJ(this.CPFCNPJ).ToSelect2Model();
      }
      set
      {
        if (value != null && !String.IsNullOrEmpty(value.id) && !value.id.Equals("0"))
          this.CPFCNPJ = value.id;
      }
    }

    [DisplayName("Observações")]
    public string Observacoes { get; set; }

    public EnumClassificacaoRepasse EnumClassificacaoRepasse { get; set; }
  }

  public class ExtratoMedicoModel
  {
    public int Codigo { get; set; }

    [DisplayName("Repasse")]
    public string CodigoRepasse { get; set; }

    [DisplayName("Tipo de Lançamento")]
    [Required(ErrorMessage = "É necessário selecionar o Tipo de Lançamento.")]
    public EnumTipoLancamento TipoLancamentoEnum { get; set; }

    public string TipoLancamento { get; set; }

    [DisplayName("Data Apuração")]
    [Required(ErrorMessage = "É necessário selecionar a Data Apuração.")]
    public DateTime DataApuracao { get; set; }

    [DisplayName("Data Pagamento")]
    public DateTime? DataPagamento { get; set; }
    public int? CodigoConvenio { get; set; }
    [DisplayName("Observações")]
    public string Observacao { get; set; }

    [Required(ErrorMessage = "É necessário incluir o Valor.")]
    public decimal Valor { get; set; }

    [Required(ErrorMessage = "É necessário selecionar a Classificação.")]
    public int CodigoClassificacao { get; set; }

    [DisplayName("Tipo Conta")]
    public EnumTipoConta TipoContaEnum { get; set; }

    public string TipoConta { get; set; }

    [DisplayName("CPF/CNPJ")]
    public string CPFCNPJ { get; set; }

    [Required(ErrorMessage = "Médico não selecionado")]
    public int CodigoMedico { get; set; }

    [DisplayName("Selecione o Convênio")]
    [URLSelect("Select2/GetConvenioSelect")]
    [PlaceHolderAttr("Selecione o Convênio")]
    public Select2Model ConvenioSelect
    {
      get
      {
        ConvenioServices ConvenioServices = new ConvenioServices();
        return ConvenioServices.GetById(this.CodigoConvenio.GetValueOrDefault()).ToSelect2Model();
      }
      set
      {
        if (value != null && !String.IsNullOrEmpty(value.id) && !value.id.Equals("0"))
          this.CodigoConvenio = int.Parse(value.id);
      }
    }


    [DisplayName("Selecione a Empresa")]
    [URLSelect("Select2/GetEmpresaMedicoCNPJSelect")]
    [PlaceHolderAttr("Selecione a Empresa")]
    public Select2Model EmpresaMedicoCNPJSelect
    {
      get
      {
        EmpresaMedicoService EmpresaMedicoService = new EmpresaMedicoService();
        return EmpresaMedicoService.GetByCNPJ(this.CPFCNPJ).ToSelect2Model();
      }
      set
      {
        if (value != null && !String.IsNullOrEmpty(value.id) && !value.id.Equals("0"))
          this.CPFCNPJ = value.id;
      }
    }


    [DisplayName("Selecione a Classificação")]
    [URLSelect("Select2/GetClassificacaoSelect")]
    [PlaceHolderAttr("Selecione a Classificação")]
    [Required(ErrorMessage = "É necessário selecionar a Classificação.")]
    public Select2Model ClassificacaoSelect
    {
      get
      {
        ClassificacaoRepasseServices ClassificacaoRepasseServices = new ClassificacaoRepasseServices();
        return ClassificacaoRepasseServices.GetById(this.CodigoClassificacao).ToSelect2Model();
      }
      set
      {
        if (value != null && !String.IsNullOrEmpty(value.id) && !value.id.Equals("0"))
          this.CodigoClassificacao = int.Parse(value.id);
      }
    }

  }

  public class ExtratoMedicoRegistroPagamentoModel
  {
    public int Id { get; set; }
    public int IdMedico { get; set; }
    public string CPFCNPJ { get; set; }
    public decimal Valor { get; set; }
    public string Agencia { get; set; }
    public string Banco { get; set; }
    public string Conta { get; set; }
    public string TipoLancamento { get; set; }
    public EnumClassificacaoRepasse ClassificacaoRepasse { get; set; }
  }

  public class ExtratoRegistroPagamentoCreateModel
  {
    public int IdMedico { get; set; }
    public string CPFCNPJ { get; set; }
    public decimal ValorTotal { get; set; }
    public string Agencia { get; set; }
    public string Banco { get; set; }
    public string Conta { get; set; }

    public List<ExtratoMedicoRegistroPagamentoModel> Extratos { get; set; }
  }

  public class ExtratoMedicoFiltroIndex
  {
    public ExtratoMedicoFiltroIndex()
    {
      this.ListExtratoMedicoIndex = new List<ExtratoMedicoIndex>();
    }
    public ExtratoMedicoFiltroIndex(int IdMedico)
    {
      this.IdMedico = IdMedico;
      this.ListExtratoMedicoIndex = new List<ExtratoMedicoIndex>();
    }
    public List<ExtratoMedicoIndex> ListExtratoMedicoIndex { get; set; }

    public int IdMedico { get; set; }
    public int? CodigoRepasse { get; set; }

    public string Data { get; set; }

  }

  public class ExtratoMedicoIndex
  {
    public int Codigo { get; set; }
    public string CodigoRepasse { get; set; }
    public string TipoLancamento { get; set; }
    public DateTime DataApuracao { get; set; }
    public DateTime? DataPagamento { get; set; }
    public int? CodigoConvenio { get; set; }
    public decimal Valor { get; set; }
    public int CodigoClassificacao { get; set; }
    public string TipoConta { get; set; }
    public string CPFCNPJ { get; set; }
    public int CodigoMedico { get; set; }
    public string NomeMedico { get; set; }
    public string RazaoSocial { get; set; }
    public string DescricaoClassificacao { get; set; }
  }

  public class ExtratoMedicoGrid
  {
    public int Codigo { get; set; }
    public string CodigoRepasse { get; set; }
    public string TipoLancamento { get; set; }
    public DateTime DataApuracao { get; set; }
    public DateTime? DataPagamento { get; set; }
    public int? CodigoConvenio { get; set; }
    public decimal Valor { get; set; }
    public int CodigoClassificacao { get; set; }
    public string TipoConta { get; set; }
    public string CPFCNPJ { get; set; }
    public int CodigoMedico { get; set; }
    public string NomeMedico { get; set; }
    public string RazaoSocial { get; set; }
    public string DescricaoClassificacao { get; set; }
  }

  public class ExtratoMedicoRegistroPagamento
  {
    public int Codigo { get; set; }
    public string NroGuiaAtendimento { get; set; }
    public decimal Valor { get; set; }
    public DateTime DataProcessamento { get; set; }
    public string TipoLancamento { get; set; }
    public string DataProcessamentoAux
    {
      get
      {
        return DataProcessamento.ToString("dd/MM/yyyy HH:ss");
      }
    }
  }

  public static class ExtratoMedicoModelConversion
  {
    public static R_ExtratoMedico ExtratoMedicoToCreate(this ExtratoMedicoModel model, int IdUsuario)
    {
      R_ExtratoMedico entity = new R_ExtratoMedico();

      entity.EX_CodigoRepasse = model.CodigoRepasse;
      entity.EX_CPFCNPJDeposito = model.CPFCNPJ;
      entity.EX_DataApuracao = model.DataApuracao;
      entity.EX_DataPagamento = model.DataPagamento;
      entity.EX_IdClassificacao = model.CodigoClassificacao;
      entity.EX_IdConvenio = model.CodigoConvenio;
      entity.EX_IdMedico = model.CodigoMedico;
      entity.EX_TipoConta = String.Format("{0}", model.TipoContaEnum);
      entity.EX_TipoLancamento = String.Format("{0}", model.TipoLancamentoEnum);
      entity.EX_Valor = model.Valor;
      entity.Observacao = model.Observacao;
      entity.EX_IdUsuarioCriacao = IdUsuario;
      entity.EX_DataCriacao = DateTime.Now;
      return entity;
    }

    public static R_ExtratoMedico ExtratoMedicoToCreatebyRecorrente(this ExtratoMedicoModel model)
    {
      R_ExtratoMedico entity = new R_ExtratoMedico();

      entity.EX_CPFCNPJDeposito = model.CPFCNPJ;
      entity.EX_IdClassificacao = model.CodigoClassificacao;
      entity.EX_IdMedico = model.CodigoMedico;
      entity.EX_TipoConta = model.TipoConta;
      entity.EX_TipoLancamento = model.TipoLancamento;
      entity.EX_Valor = model.Valor;

      return entity;
    }
    public static void ExtratoMedicoToEditProcessamento(this R_ExtratoMedico entity, ExtratoMedicoEdit extratoMedicoEdit)
    {
      entity.EX_Valor = extratoMedicoEdit.Valor;
      entity.EX_TipoConta = extratoMedicoEdit.TipoContaEnum.ToString();
      entity.EX_CPFCNPJDeposito = extratoMedicoEdit.CPFCNPJ;
      entity.EX_IdMedico = extratoMedicoEdit.CodigoMedico;
      entity.Observacao = extratoMedicoEdit.Observacoes;
    }
    public static void ExtratoMedicoToEditNoProcessamento(this R_ExtratoMedico entity, ExtratoMedicoEdit extratoMedicoEdit)
    {
      entity.EX_DataApuracao = extratoMedicoEdit.DataApuracao;
      entity.EX_Valor = extratoMedicoEdit.Valor;
      entity.EX_IdClassificacao = extratoMedicoEdit.CodigoClassificacao.Value;
      entity.EX_TipoLancamento = extratoMedicoEdit.TipoLancamentoEnum.ToString();
      entity.EX_TipoConta = extratoMedicoEdit.TipoContaEnum.ToString();
      entity.EX_CPFCNPJDeposito = extratoMedicoEdit.CPFCNPJ;
      entity.EX_IdMedico = extratoMedicoEdit.CodigoMedico;
      entity.Observacao = extratoMedicoEdit.Observacoes;
    }

    public static R_ExtratoMedico CreateByProcessaRepasse(this R_ExtratoMedico entity
                                                        , int IdClassProcessamentoRepasse
                                                        , R_GuiaAtendimento guia
                                                        , decimal valor
                                                        , int IdMedico
                                                        , RetornoValidacaoRegra retornoValidacaoRegra
                                                        , int IdUsuarioLogado
                                                        , string CodigoDocumento
                                                        , int idRepasse
                                                        , string Observacao
                                                        , EnumTipoLancamento tipoLancamento
                                                        , bool rateio
                                                        , decimal? rateiofixo = null
                                                        , bool BoolTaxaAdministrativa = false
                                                        , decimal TaxaAdministrativa = 0)
    {
      decimal ExValor = 0;

      if (!rateio)
        ExValor = valor != 0 ? valor : 0;
      else
        ExValor = (valor * (rateiofixo.Value / 100));

      if (BoolTaxaAdministrativa)
        ExValor = ExValor * (TaxaAdministrativa / 100);

      return new R_ExtratoMedico()
      {
        EX_IdClassificacao = IdClassProcessamentoRepasse,
        EX_DataApuracao = DateTime.Now,
        EX_IdConvenio = guia.GA_IdConvenio,
        EX_Valor = ExValor,
        EX_IdMedico = IdMedico,
        EX_TipoLancamento = tipoLancamento.ToString(),
        EX_CPFCNPJDeposito = retornoValidacaoRegra.CPFCNPJ,
        EX_TipoConta = retornoValidacaoRegra.tipoPessoaEnum.ToString(),
        Observacao = Observacao,
        EX_CodigoRepasse = idRepasse.ToString(),
        EX_CodigoDocumento = CodigoDocumento,
        EX_IdGuiaAtendimento = guia.GA_Id,
        EX_IdUsuarioCriacao = IdUsuarioLogado,
        EX_DataCriacao = DateTime.Now
      };
    }
  }
}
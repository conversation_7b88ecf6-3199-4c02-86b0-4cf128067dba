﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Linq.Expressions;
using System.Text;
using System.Web.Mvc.Html;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using RepasseConvenio.Infrastructure.Helpers;
using PortalCooperadoHelpers.Infraestrutura.Helpers;

namespace RepasseConvenio.Infrastructure.Controls
{
  public class LibEditor<TModel, TProperty>
  {
    public HtmlHelper<TModel> HtmlHelper { get; set; }

    public Expression<Func<TModel, TProperty>> ExpressionModel { get; set; }

    public object HtmlAttributes { get; set; }

    public LibEditor(HtmlHelper<TModel> htmlHelper, Expression<Func<TModel, TProperty>> expression, object attributes)
    {
    }

    public string RenderHtml()
    {
      string retorno = "";
      return retorno;
    }

  }
}
﻿@using RepasseConvenio.Models
@using PagedList
@using RepasseConvenio.Infrastructure.Controls
@using PagedList.Mvc;
@model ClassificacaoRepasseModel

@{
  ViewBag.Title = "Editar Classificação";
  Layout = "~/Views/Shared/_Layout.cshtml";
}
<div class="card">
  <div class="card-header">
    Editar Classificação da Movimentação
  </div>
  @using (Html.BeginForm())
  {
    @Html.HiddenFor(x => x.Id)
    @Html.HiddenFor(x => x.Ordem)
    @Html.HiddenFor(x => x.Enum)
    
    <div class="card-body">

      <div class="row">
        <div class="col-md-1 form-group">
          <label>Código:</label>
          <input class="form-control" type="text" value="@Model.Enum.ToString().PadLeft(3, '0')" ReadOnly="readonly"/>
        </div>
        <div class="col-md-6 form-group">
          <label>Descrição:</label>
          @Html.EditorFor(x => x.Desc<PERSON>, new { htmlAttributes = new { @class = "form-control", rows = 5 } })
          @Html.ValidationMessageFor(x => x.Descricao)
        </div>
        <div class="col-md-5 form-group">
          <label>Tipo Lancamento:</label>
          @Html.LibDropDown(x => x.enumTipoLancamento)
          @Html.ValidationMessageFor(x => x.enumTipoLancamento)
        </div>
      </div>
      <div class="row">
        <div class="form-group col-md-2">
          @Html.EditorFor(x => x.BaseIRRPFloat)
          <label>Base IRFF</label>
        </div>
        <div class="form-group col-md-2">
          @Html.EditorFor(x => x.BaseINSSFloat)
          <label>Base INSS</label>
        </div>
        <div class="form-group col-md-2">
          @Html.EditorFor(x => x.BasePISConfinsFloat)
          <label>Base PISConfins</label>
        </div>
        <div class="form-group col-md-2">
          @Html.EditorFor(x => x.BaseISSQNFloat)
          <label>Base ISSQN</label>
        </div>
      </div>
      
    </div>
    <div class="card-footer">
      <a class="btn cancel" href="@Url.Action("Index")">Voltar</a>
      <input type="submit" value="Enviar" class="btn btn-outline-success pull-right" />
    </div>
  }

</div>
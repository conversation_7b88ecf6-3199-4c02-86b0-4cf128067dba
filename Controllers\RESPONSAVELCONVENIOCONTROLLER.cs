﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using PagedList;
using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Controls;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using RepasseConvenio.Services;
using System;
using System.Web.Mvc;

namespace RepasseConvenio.Controllers
{
  [Authorize]
  [Security("FAF7C7C8-6A51-470B-9F63-3C4DA15FA093")]

  public class ResponsavelConvenioController : LibController
  {
  [Security("1D9CC86F-427D-4102-B475-A3DA66758BB0")]

    public ActionResult Index(int CodigoConvenio, int? Pagina)
    {
      Pagina = Pagina ?? 1;

      ViewBag.CodigoConvenio = CodigoConvenio;

      IPagedList<ResponsavelConvenioModel> responsaveis = new ResponsavelConvenioServices().GetAllByIdConvenio(CodigoConvenio, Pagina.Value);
      return View(responsaveis);
    }

    [HttpPost]
    [Security("5262BFC0-6EE8-46EA-9178-B16E5A962843")]
    public JsonResult Create(ResponsavelConvenioModel responsavelConvenioModel)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        if (ModelState.IsValid)
        {
          responsavelConvenioModel.CPF = responsavelConvenioModel.CPF.Replace(".", "").Replace("-", "");

          new ResponsavelConvenioServices().Create(responsavelConvenioModel);
          retornoAjax.Erro = false;
          retornoAjax.Mensagem = "Responsável adicionado com sucesso.";
          retornoAjax.Titulo = "Sucesso";
          return Json(retornoAjax);
        }
        retornoAjax.Erro = true;
        retornoAjax.Mensagem = "Gentileza preencher todos os campos.";
        retornoAjax.Titulo = "Erro";
        return Json(retornoAjax);
      }
      catch (CustomException ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Titulo = "Erro";
        return Json(retornoAjax);
      }
      catch
      {
        retornoAjax.Erro = true;
        retornoAjax.Mensagem = "Favor verificar os campos preenchidos.";
        retornoAjax.Titulo = "Erro";
        return Json(retornoAjax);
      }
    }

    [HttpPost]
    [Security("FDB06519-9F75-40EC-A786-E0A332195970")]
    public JsonResult Delete(int CodigoRelacao)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        new ResponsavelConvenioServices().Delete(CodigoRelacao);
        retornoAjax.Erro = false;
        retornoAjax.Mensagem = "Responsável desvinculado com sucesso.";
        retornoAjax.Titulo = "Sucesso";
        return Json(retornoAjax);
      }
      catch (CustomException ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Titulo = "Erro";
        return Json(retornoAjax);
      }
      catch (Exception ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.Mensagem = "Favor verificar os campos preenchidos.";
        retornoAjax.Titulo = "Erro";
        return Json(retornoAjax);
      }
    }

    [HttpGet]
    [Security("D4CFD512-140F-46AE-AA72-DB5372D455A2")]

    public ActionResult Edit(int CodigoResponsavel)
    {
      try
      {
        if (CodigoResponsavel == 0)
          throw new Exception("Nenhum responsável foi selecionado");

        ResponsavelConvenioModel responsavel = new ResponsavelConvenioServices().GetByIdModel(CodigoResponsavel);

        if (responsavel == null)
          throw new Exception("Não foi possivel encontrar este responsável");

        return View(responsavel);
      }
      catch (Exception)
      {
        throw;
      }
    }

    [HttpPost]
    [Security("D4CFD512-140F-46AE-AA72-DB5372D455A2")]

    public ActionResult Edit(ResponsavelConvenioModel responsavel)
    {
      try
      {
        if (ModelState.IsValid)
        {
          new ResponsavelConvenioServices().Edit(responsavel);
          MessageListToast.Add(new Message(MessageType.Success, "Responsável editado com sucesso."));
          return RedirectToAction("Index", new { responsavel.CodigoConvenio });
        }
        MessageListToast.Add(new Message(MessageType.Error, "Favor verificar os campos preenchidos."));
        return View(responsavel);
      }
      catch (CustomException ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return View(responsavel);
      }
      catch (Exception)
      {
        MessageListToast.Add(new Message(MessageType.Error, "Favor verificar os campos preenchidos."));
        return View(responsavel);
      }
    }

    [HttpGet]
    public PartialViewResult _Grid(int CodigoConvenio)
    {
      IPagedList<ResponsavelConvenioModel> responsaveis = new ResponsavelConvenioServices().GetAllByIdConvenio(CodigoConvenio, 1);
      return PartialView("_Grid", responsaveis);
    }

    [HttpGet]
    public JsonResult _GetResponsavel(string CPF)
    {
      CPF = CPF.Replace(".", "").Replace("-", "");

      R_ResponsavelConvenio resp = new ResponsavelConvenioServices().GetByCPF(CPF);
      return Json(new { Nome = resp.RC_Nome, Email = resp.RC_Email }, JsonRequestBehavior.AllowGet);
    }
  }
}
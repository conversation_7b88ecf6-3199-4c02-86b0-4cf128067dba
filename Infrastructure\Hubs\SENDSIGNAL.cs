﻿using Microsoft.AspNet.SignalR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace RepasseConvenio.Infrastructure.Hubs
{

  public class SendSignal
  {
    private IHubContext contexto = null;

    public void openModalRetornoUsuario(string ConnectionId, string Titulo)
    {
    }

    public void creatPStrong(string ConnectionId, string Texto)
    {
    }

    public void creatPSucess(string ConnectionId, string Texto)
    {
    }

    public void setPorcentagemProgress(string ConnectionId, string Porcentagem)
    {
    }

    public void concluirProgress(string ConnectionId, int Porcentagem, string TextoPorcentagem)
    {
    }

  }
}
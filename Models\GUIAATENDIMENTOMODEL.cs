﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.WSPortalCooperado;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Security.Principal;
using System.Web;

namespace RepasseConvenio.Models
{
  public class GuiaAtendimentoModel
  {
    public int Codigo { get; set; }
    public int CodigoConvenio { get; set; }
    public string CodGuiaPrestador { get; set; }
    public DateTime? DtInicioFaturamento { get; set; }
    public DateTime? DtFimFaturamento { get; set; }
    public DateTime? DtEmissao { get; set; }
    public string CodGuiaPrincipal { get; set; }
    public DateTime? DtAutorizacaoGuia { get; set; }
    public string CodSenhaGuia { get; set; }
    public DateTime? DtValidadeSenha { get; set; }
    public string CodGuiaOperadora { get; set; }
    public string CodCarteiraPaciente { get; set; }
    public DateTime? DtValidadeCarteira { get; set; }
    public string NomePaciente { get; set; }
    public string CodCartaoNacSaude { get; set; }
    public int? IdentRN { get; set; }
    public string NomeSolicitante { get; set; }
    public string ConselhoSolicitante { get; set; }
    public string CodNumConselho { get; set; }
    public string CodUF { get; set; }
    public string CodCBO { get; set; }
    public string CaraterAtendimento { get; set; }
    public DateTime? DtSolicitacao { get; set; }
    public string DescIndClinica { get; set; }
    public string CodigoProcSolicitado { get; set; }
    public int? QtdeSolicitado { get; set; }
    public int? QtdeAutorizada { get; set; }
    public string CodPrestContratado { get; set; }
    public string NomePrestador { get; set; }
    public string CodNCNES { get; set; }
    public string DescTipoAtendimento { get; set; }
    public string DescIndAcidente { get; set; }
    public string DescTipoConsulta { get; set; }
    public string MotEnceramento { get; set; }
    public string ObsJustificativa { get; set; }
    public decimal? VlrTotal { get; set; }
    public decimal? VlrGlosa { get; set; }
    public string DescTipoSaidaGuiaConsulta { get; set; }
    public string NroUnicooper { get; set; }
    public int TipoGuia { get; set; }
    public string Complemento { get; set; }
    public string DescPlano { get; set; }
    public DateTime? DataInternacao { get; set; }
    public DateTime? DataAlta { get; set; }
    public string SolicitacaoInternacao { get; set; }
    public DateTime? DataEntrega { get; set; }
    public string AtendimentoHospital { get; set; }
    public string DescTipoAcomodacao { get; set; }
    public int? OrigemGuia { get; set; }
    public DateTime? DataAtendimento { get; set; }
    public string CNPJFaturamento { get; set; }
  }

  public class GuiaAtendimentoGrid
  {
    public GuiaAtendimentoGrid()
    {
      ListaProcGuiaAtendimentoGrid = new List<ProcGuiaAtendimentoGrid>();
    }

    [DisplayName("Hospital")]
    public string Hospital { get; set; }

    [DisplayName("Convênio")]
    public string Convenio { get; set; }

    [DisplayName("Atendimento Hospital")]
    public string AtendimentoHospital { get; set; }

    [DisplayName("Guia(Unicooper)")]
    public string GuiaUnicooper { get; set; }

    [DisplayName("Complemento")]
    public string Complemento { get; set; }

    [DisplayName("Plano")]
    public string Plano { get; set; }

    [DisplayName("Data Autorização")]
    public DateTime DataAutorizacao { get; set; }

    [DisplayName("Data Entrega")]
    public DateTime DataEntrega { get; set; }

    [DisplayName("Tipo Guia")]
    public int TipoGuia { get; set; }

    [DisplayName("Guia")]
    public string Guia { get; set; }

    [DisplayName("Guia Principal")]
    public string GuiaPrincipal { get; set; }

    [DisplayName("Solicitação Internação")]
    public string SolicitacaoInternacao { get; set; }

    [DisplayName("Senha")]
    public string Senha { get; set; }

    [DisplayName("Nome Paciente")]
    public string NomePaciente { get; set; }

    [DisplayName("Nro Carteirinha")]
    public string CarteirinhaPaciente { get; set; }

    [DisplayName("Ínicio Faturamento")]
    public DateTime DataInicioFaturamento { get; set; }

    [DisplayName("Fim Faturamento")]
    public DateTime DataFimFaturamento { get; set; }

    [DisplayName("Acomodação")]
    public string Acomodacao { get; set; }

    [DisplayName("Tipo de Atendimento")]
    public string TipoAtendimento { get; set; }

    [DisplayName("Internação")]
    public DateTime DataInternacao { get; set; }

    [DisplayName("Alta")]
    public DateTime DataAlta { get; set; }

    [DisplayName("CPF")]
    public string CPFMedico { get; set; }
    [DisplayName("Nome Profissional")]
    public string NomeMedico { get; set; }
    [DisplayName("CRM")]
    public string CRMMedico { get; set; }

    public List<ProcGuiaAtendimentoGrid> ListaProcGuiaAtendimentoGrid { get; set; }

  }

  public static class GuiaAtendimentoModelConversions
  {
    public static R_GuiaAtendimento ToGuiaAtendimentoCreate(this GuiaAtendimentoModel model)
    {
      R_GuiaAtendimento entity = new R_GuiaAtendimento();

      entity.GA_Id = model.Codigo ;
      entity.GA_IdConvenio = model.CodigoConvenio ;
      entity.GA_CodGuiaPrestador = model.CodGuiaPrestador;
      entity.GA_DtInicioFaturamento = model.DtInicioFaturamento;
      entity.GA_DtFimFaturamento = model.DtFimFaturamento;
      entity.GA_DtEmissao = model.DtEmissao;
      entity.GA_CodGuiaPrincipal = model.CodGuiaPrincipal;
      entity.GA_DtAutorizacaoGuia = model.DtAutorizacaoGuia;
      entity.GA_CodSenhaGuia = model.CodSenhaGuia;
      entity.GA_DtValidadeSenha = model.DtValidadeSenha;
      entity.GA_CodGuiaOperadora = model.CodGuiaOperadora;
      entity.GA_CodCarteiraPaciente = model.CodCarteiraPaciente;
      entity.GA_DtValidadeCarteira = model.DtValidadeCarteira;
      entity.GA_NomePaciente = model.NomePaciente;
      entity.GA_CodCartaoNacSaude = model.CodCartaoNacSaude;
      entity.GA_IdentRN = model.IdentRN;
      entity.GA_NomeSolicitante = model.NomeSolicitante;
      entity.GA_ConselhoSolicitante = model.ConselhoSolicitante;
      entity.GA_CodNumConselho = model.CodNumConselho;
      entity.GA_CodUF = model.CodUF;
      entity.GA_CodCBO = model.CodCBO;
      entity.GA_CaraterAtendimento = model.CaraterAtendimento;
      entity.GA_DtSolicitacao = model.DtSolicitacao;
      entity.GA_DescIndClinica = model.DescIndClinica;
      entity.GA_CodigoProcSolicitado = model.CodigoProcSolicitado;
      entity.GA_QtdeSolicitado = model.QtdeSolicitado;
      entity.GA_QtdeAutorizada = model.QtdeAutorizada;
      entity.GA_CodPrestContratado = model.CodPrestContratado;
      entity.GA_NomePrestador = model.NomePrestador;
      entity.GA_CodNCNES = model.CodNCNES;
      entity.GA_DescTipoAtendimento = model.DescTipoAtendimento;
      entity.GA_DescIndAcidente = model.DescIndAcidente;
      entity.GA_DescTipoConsulta = model.DescTipoConsulta;
      entity.GA_MotEnceramento = model.MotEnceramento;
      entity.GA_ObsJustificativa = model.ObsJustificativa;
      entity.GA_VlrTotal = model.VlrTotal;
      entity.GA_VlrGlosa = model.VlrGlosa;
      entity.GA_DescTipoSaidaGuiaConsulta = model.DescTipoSaidaGuiaConsulta;
      entity.GA_NroUnicooper = model.NroUnicooper;
      entity.GA_TipoGuia = model.TipoGuia;
      entity.GA_Complemento = model.Complemento;
      entity.GA_DescPlano = model.DescPlano;
      entity.GA_DataInternacao = model.DataInternacao;
      entity.GA_DataAlta = model.DataAlta;
      entity.GA_SolicitacaoInternacao = model.SolicitacaoInternacao;
      entity.GA_DataEntrega = model.DataEntrega;
      entity.GA_AtendimentoHospital = model.AtendimentoHospital;
      entity.GA_DescTipoAcomodacao = model.DescTipoAcomodacao;
      entity.GA_OrigemGuia = model.OrigemGuia ;
      entity.GA_DataAtendimento = model.DataAtendimento;
      entity.GA_CNPJFaturamento = model.CNPJFaturamento;
      return entity;
    }

    public static R_GuiaAtendimento ToGuiaAtendimentoCreate(this GuiaRepasseWS model, int IdConvenio)
    {
      R_GuiaAtendimento entity = new R_GuiaAtendimento();
      entity.GA_CodAtendimento = model.CodRegistro;
      entity.GA_IdConvenio = IdConvenio;
      entity.GA_CodGuiaPrestador = model.CodGuiaPrestador;
      entity.GA_DtInicioFaturamento = model.DtInicioFaturamento;
      entity.GA_DtFimFaturamento = model.DtFimFaturamento;
      entity.GA_DtEmissao = model.DtEmissao;
      entity.GA_CodGuiaPrincipal = model.CodGuiaPrincipal;
      entity.GA_DtAutorizacaoGuia = model.DtAutorizacaoGuia;
      entity.GA_CodSenhaGuia = model.CodSenhaGuia;
      entity.GA_DtValidadeSenha = model.DtValidadeSenha;
      entity.GA_CodGuiaOperadora = model.CodGuiaOperadora;
      entity.GA_CodCarteiraPaciente = model.CodCarteiraPaciente;
      entity.GA_DtValidadeCarteira = model.DtValidadeCarteira;
      entity.GA_NomePaciente = model.NomePaciente;
      entity.GA_CodCartaoNacSaude = model.CodCartaoNacSaude;
      entity.GA_IdentRN = model.IdentRN;
      entity.GA_NomeSolicitante = model.NomeSolicitante;
      entity.GA_ConselhoSolicitante = model.ConselhoSolicitante;
      entity.GA_CodNumConselho = model.CodNumConselho;
      entity.GA_CodUF = model.CodUF;
      entity.GA_CodCBO = model.CodCBO;
      entity.GA_CaraterAtendimento = model.CaraterAtendimento;
      entity.GA_DtSolicitacao = model.DtSolicitacao;
      entity.GA_DescIndClinica = model.DescIndClinica;
      entity.GA_CodigoProcSolicitado = model.CodigoProcSolicitado;
      entity.GA_QtdeSolicitado = model.QtdeSolicitado;
      entity.GA_QtdeAutorizada = model.QtdeAutorizada;
      entity.GA_CodPrestContratado = model.CodPrestContratado;
      entity.GA_NomePrestador = model.NomePrestador;
      entity.GA_CodNCNES = model.CodNCNES;
      entity.GA_DescTipoAtendimento = model.DescTipoAtendimento;
      entity.GA_DescIndAcidente = model.DescIndAcidente;
      entity.GA_DescTipoConsulta = model.DescTipoConsulta;
      entity.GA_MotEnceramento = model.MotEnceramento;
      entity.GA_ObsJustificativa = model.ObsJustificativa;
      entity.GA_VlrTotal = model.VlrTotal;
      entity.GA_VlrGlosa = model.VlrGlosa;
      entity.GA_DescTipoSaidaGuiaConsulta = model.DescTipoSaidaGuiaConsulta;
      entity.GA_NroUnicooper = model.NroUnicooper;
      entity.GA_TipoGuia = model.TipoGuia;
      entity.GA_Complemento = model.Complemento;
      entity.GA_DescPlano = model.DescPlano;
      entity.GA_DataInternacao = model.DataInternacao;
      entity.GA_DataAlta = model.DataAlta;
      entity.GA_SolicitacaoInternacao = model.SolicitacaoInternacao;
      entity.GA_DataEntrega = model.DataEntrega;
      entity.GA_AtendimentoHospital = model.AtendimentoHospital;
      entity.GA_DescTipoAcomodacao = model.DescTipoAcomodacao;
      entity.GA_OrigemGuia = model.OrigemGuia;
      entity.GA_DataAtendimento = model.DataAtendimento;
      entity.GA_CNPJFaturamento = model.CNPJFaturamento;
      entity.GA_ValorFaturado = model.ValorFaturado;
      return entity;
    }

    public static R_GuiaAtendimento ToGuiaAtendimentoEdit(this GuiaAtendimentoModel model, R_GuiaAtendimento entity)
    {
      entity.GA_IdConvenio = model.CodigoConvenio;
      entity.GA_CodGuiaPrestador = model.CodGuiaPrestador;
      entity.GA_DtInicioFaturamento = model.DtInicioFaturamento;
      entity.GA_DtFimFaturamento = model.DtFimFaturamento;
      entity.GA_DtEmissao = model.DtEmissao;
      entity.GA_CodGuiaPrincipal = model.CodGuiaPrincipal;
      entity.GA_DtAutorizacaoGuia = model.DtAutorizacaoGuia;
      entity.GA_CodSenhaGuia = model.CodSenhaGuia;
      entity.GA_DtValidadeSenha = model.DtValidadeSenha;
      entity.GA_CodGuiaOperadora = model.CodGuiaOperadora;
      entity.GA_CodCarteiraPaciente = model.CodCarteiraPaciente;
      entity.GA_DtValidadeCarteira = model.DtValidadeCarteira;
      entity.GA_NomePaciente = model.NomePaciente;
      entity.GA_CodCartaoNacSaude = model.CodCartaoNacSaude;
      entity.GA_IdentRN = model.IdentRN;
      entity.GA_NomeSolicitante = model.NomeSolicitante;
      entity.GA_ConselhoSolicitante = model.ConselhoSolicitante;
      entity.GA_CodNumConselho = model.CodNumConselho;
      entity.GA_CodUF = model.CodUF;
      entity.GA_CodCBO = model.CodCBO;
      entity.GA_CaraterAtendimento = model.CaraterAtendimento;
      entity.GA_DtSolicitacao = model.DtSolicitacao;
      entity.GA_DescIndClinica = model.DescIndClinica;
      entity.GA_CodigoProcSolicitado = model.CodigoProcSolicitado;
      entity.GA_QtdeSolicitado = model.QtdeSolicitado;
      entity.GA_QtdeAutorizada = model.QtdeAutorizada;
      entity.GA_CodPrestContratado = model.CodPrestContratado;
      entity.GA_NomePrestador = model.NomePrestador;
      entity.GA_CodNCNES = model.CodNCNES;
      entity.GA_DescTipoAtendimento = model.DescTipoAtendimento;
      entity.GA_DescIndAcidente = model.DescIndAcidente;
      entity.GA_DescTipoConsulta = model.DescTipoConsulta;
      entity.GA_MotEnceramento = model.MotEnceramento;
      entity.GA_ObsJustificativa = model.ObsJustificativa;
      entity.GA_VlrTotal = model.VlrTotal;
      entity.GA_VlrGlosa = model.VlrGlosa;
      entity.GA_DescTipoSaidaGuiaConsulta = model.DescTipoSaidaGuiaConsulta;
      entity.GA_NroUnicooper = model.NroUnicooper;
      entity.GA_TipoGuia = model.TipoGuia;
      entity.GA_Complemento = model.Complemento;
      entity.GA_DescPlano = model.DescPlano;
      entity.GA_DataInternacao = model.DataInternacao;
      entity.GA_DataAlta = model.DataAlta;
      entity.GA_SolicitacaoInternacao = model.SolicitacaoInternacao;
      entity.GA_DataEntrega = model.DataEntrega;
      entity.GA_AtendimentoHospital = model.AtendimentoHospital;
      entity.GA_DescTipoAcomodacao = model.DescTipoAcomodacao;
      entity.GA_OrigemGuia = model.OrigemGuia;
      entity.GA_DataAtendimento = model.DataAtendimento;
      entity.GA_CNPJFaturamento = model.CNPJFaturamento;

      return entity;
    }

    public static GuiaAtendimentoModel ToGuiaAtendimentoModel(this R_GuiaAtendimento entity)
    {
      GuiaAtendimentoModel model = new GuiaAtendimentoModel();
      model.Codigo = entity.GA_Id;
      model.CodigoConvenio = entity.GA_IdConvenio;
      model.CodGuiaPrestador = entity.GA_CodGuiaPrestador;
      model.DtInicioFaturamento = entity.GA_DtInicioFaturamento;
      model.DtFimFaturamento = entity.GA_DtFimFaturamento;
      model.DtEmissao = entity.GA_DtEmissao;
      model.CodGuiaPrincipal = entity.GA_CodGuiaPrincipal;
      model.DtAutorizacaoGuia = entity.GA_DtAutorizacaoGuia;
      model.CodSenhaGuia = entity.GA_CodSenhaGuia;
      model.DtValidadeSenha = entity.GA_DtValidadeSenha;
      model.CodGuiaOperadora = entity.GA_CodGuiaOperadora;
      model.CodCarteiraPaciente = entity.GA_CodCarteiraPaciente;
      model.DtValidadeCarteira = entity.GA_DtValidadeCarteira;
      model.NomePaciente = entity.GA_NomePaciente;
      model.CodCartaoNacSaude = entity.GA_CodCartaoNacSaude;
      model.IdentRN = entity.GA_IdentRN;
      model.NomeSolicitante = entity.GA_NomeSolicitante;
      model.ConselhoSolicitante = entity.GA_ConselhoSolicitante;
      model.CodNumConselho = entity.GA_CodNumConselho;
      model.CodUF = entity.GA_CodUF;
      model.CodCBO = entity.GA_CodCBO;
      model.CaraterAtendimento = entity.GA_CaraterAtendimento;
      model.DtSolicitacao = entity.GA_DtSolicitacao;
      model.DescIndClinica = entity.GA_DescIndClinica;
      model.CodigoProcSolicitado = entity.GA_CodigoProcSolicitado;
      model.QtdeSolicitado = entity.GA_QtdeSolicitado;
      model.QtdeAutorizada = entity.GA_QtdeAutorizada;
      model.CodPrestContratado = entity.GA_CodPrestContratado;
      model.NomePrestador = entity.GA_NomePrestador;
      model.CodNCNES = entity.GA_CodNCNES;
      model.DescTipoAtendimento = entity.GA_DescTipoAtendimento;
      model.DescIndAcidente = entity.GA_DescIndAcidente;
      model.DescTipoConsulta = entity.GA_DescTipoConsulta;
      model.MotEnceramento = entity.GA_MotEnceramento;
      model.ObsJustificativa = entity.GA_ObsJustificativa;
      model.VlrTotal = entity.GA_VlrTotal;
      model.VlrGlosa = entity.GA_VlrGlosa;
      model.DescTipoSaidaGuiaConsulta = entity.GA_DescTipoSaidaGuiaConsulta;
      model.NroUnicooper = entity.GA_NroUnicooper;
      model.TipoGuia = entity.GA_TipoGuia;
      model.Complemento = entity.GA_Complemento;
      model.DescPlano = entity.GA_DescPlano;
      model.DataInternacao = entity.GA_DataInternacao;
      model.DataAlta = entity.GA_DataAlta;
      model.SolicitacaoInternacao = entity.GA_SolicitacaoInternacao;
      model.DataEntrega = entity.GA_DataEntrega;
      model.AtendimentoHospital = entity.GA_AtendimentoHospital;
      model.DescTipoAcomodacao = entity.GA_DescTipoAcomodacao;
      model.OrigemGuia = entity.GA_OrigemGuia;
      model.DataAtendimento = entity.GA_DataAtendimento;
      model.CNPJFaturamento = entity.GA_CNPJFaturamento;

      return model;
    }
  }
}
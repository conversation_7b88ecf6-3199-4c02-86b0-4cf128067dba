function MakeLookup(fieldName, sourceUrl, afterSelect, autocompleteDisabled) {

  var fieldIdName = fieldName + '_id';
  $('#' + fieldName).autocomplete({
    search: function () {
    },
    open: function () {
    },
    source: function (request, response) {
      if (request.term == ' ')
        request.term = '';

      var idpai = $('#' + fieldName)[0].getAttribute("IdPai");
      if (idpai == 'undefined' || idpai == undefined || idpai == null) {
        var idCPai = $('#' + fieldName)[0].getAttribute("controlpai");
        if (idCPai != 'undefined')
          idpai = $('#' + idCPai + '_id').val();
        if (idpai == undefined)
          idpai = $('#' + idCPai).val();
      }

      var params = { term: encodeURI(request.term), id: idpai };

      $.ajax({
        url: sourceUrl.replace('&amp;', '&'),
        dataType: "json",
        cache: false,
        contentType: "application/x-www-form-urlencoded;charset=UTF-8",
        data: params,
        success: function (data) {

          $('#loading' + fieldName).text('');
          response($.map(data, function (item) {
            return {
              label: item.Description,
              value: ((item.Id != 0) ? item.Id : item.Codigo),
              codigo: item.Codigo
            }
          }));
        }
      });
    },
    focus: function (event, ui) {
      $(this).val(ui.item.label);
      return false;
    },
    select: function (event, ui) {
      $(this).val(ui.item.label);
      $('#' + fieldIdName).val(ui.item.codigo != undefined && ui.item.codigo != '' ? ui.item.codigo : ui.item.value);
      $(this).attr("selectedvalue", ui.item.label);

      var itens = getAllElementsWithAttribute("ControlPai", fieldName);

      $(itens).each(function () {
        $(this).attr('IdPai', ui.item.value)
        $(this).val('');
        $('#' + $(this).Id + '_id').val(ui.item.value);
      })

   

      return false;
    }//,
    //disabled: function (event, ui) {
    //  if (autocompleteDisabled != undefined)
    //    return autocompleteDisabled;
    //  else
    //    return false;
    //}
  });

  $('#' + fieldName).blur(function () {
    var lkpId = $('#' + fieldIdName).val();

    var lkpText = $(this).val();
    var selectedValue = $(this).attr("selectedvalue");

    //if (lkpId <= 0 || lkpText != selectedValue) {
    if (lkpId != '00' && (lkpId <= 0 || lkpText != selectedValue)) {
      $(this).val('');
      $('#' + fieldIdName).val(0);
    }
  });

  if ($('#' + fieldIdName).val() == null || $('#' + fieldIdName).val() == '')
    $('#' + fieldIdName).val(0);

  $('#btnVerTudo' + fieldName).button({
    icons: { primary: "ui-icon-triangle-1-s" },
    text: false
  });

  if ((autocompleteDisabled != undefined) && !autocompleteDisabled) {
    $('#btnVerTudo' + fieldName).click(function (event) {
      if ($('#' + fieldName)[0].readOnly == false && $('#' + fieldName)[0].disabled == false) {
        $('#' + fieldName).val(' ');
        $('#' + fieldName).autocomplete('search');
        $('#' + fieldName).focus();
      }

      event.preventDefault();
    });
  }
}

function getAllElementsWithAttribute(attribute, fieldName) {
  var matchingElements = [];
  var allElements = document.getElementsByTagName('*');
  for (var i = 0, n = allElements.length; i < n; i++) {
    if (allElements[i].getAttribute(attribute) !== null && allElements[i].getAttribute(attribute) == fieldName) {
      // Element exists with attribute. Add to array.
      matchingElements.push(allElements[i]);
    }
  }
  return matchingElements;
}

function MakeDate(fieldName) {
  $('#' + fieldName).datepicker({ "dateFormat": "dd/mm/yy" });
}

function MakeDateTime(fieldName) {
  $('#' + fieldName).datetimepicker();
}

function urlParams(sourceURL) {
  var params = [];
  var queryString = sourceURL.replace('&amp;', '&').split('?');

  if (queryString[1] != undefined) {
    var vars = queryString[1].split("&");

    for (var i = 0; i < vars.length; i++) {
      var pair = vars[i].split("=");
      params.push({ param: pair[0], value: decodeURIComponent(pair[1]) });
    }
  }

  return params;
}

function urlParam(sourceURL, name) {
  var results = new RegExp('[\?&]' + name + '=([^&#]*)').exec(sourceURL);

  return results === null ? '' : results[1];
}

$(document).ready(function () {
  $('input[type=submit]').bind('click', function () {
    for (var instanceName in CKEDITOR.instances)
      CKEDITOR.instances[instanceName].updateElement();
  });
});
﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Controls;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;


namespace RepasseConvenio.Controllers
{
  [Authorize]
  public class DemonstrativoConvenioController : LibController
  {
    [Security("6405A75C-A036-4C35-B1FF-BDC692B08D76")]
    [HttpPost]
    public JsonResult ImportPlanilha(int IdRepasse)
    {
      try
      {
        GuiaDemonstrativoService guiaDemonstrativoService = new GuiaDemonstrativoService(ContextoUsuario.UserLogged);
        Anexo anexo = AnexoHelper.FilesUploadSemConversao(Request, new string[] { ".xls", ".xlsx" }).FirstOrDefault();
        guiaDemonstrativoService.ImportacaoPlanilha(anexo, IdRepasse);
        return Json(new { status = "success", message = "Itens Importados com sucesso" });
      }
      catch (CustomException ex)
      {
        return Json(new { status = "error", message = ex.Message });
      }
      catch (Exception ex)
      {
        //return Json(new { status = "error", message = ex.Message });
        return Json(new { status = "error", message = "Ocorreu um erro na sua solicitação, entre em contato com o administrador ou tente novamente mais tarde" });
      }
    }
    [Security("778E6445-5CD0-4759-886B-1E4DDD5711BF")]
    [HttpPost]
    public JsonResult ImportNotaFiscal(int IdRepasse)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        GuiaDemonstrativoService guiaDemonstrativoService = new GuiaDemonstrativoService(ContextoUsuario.UserLogged);
        guiaDemonstrativoService.ImportNotaFiscal(IdRepasse);
        retornoAjax.Erro = false;
        retornoAjax.messageType = MessageType.Success;
        retornoAjax.Mensagem = "Importação Realizada.";
        retornoAjax.Titulo = "Sucesso.";
        return Json(retornoAjax);
      }
      catch (CustomException ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.messageType = MessageType.Error;
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Titulo = "Erro.";
        return Json(retornoAjax);
      }
      catch (Exception ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.messageType = MessageType.Error;
        retornoAjax.Mensagem = "Houve um erro no momento de processar sua solicitação.";
        retornoAjax.Titulo = "Erro.";
        return Json(retornoAjax);
      }
    }

    [HttpPost]
    public JsonResult DeleteDemonstrativo(List<int> idItensToDelete, int IdRepasse)
    {
      try
      {
        DemonstrativoConvenioService demonstrativoConvenioService = new DemonstrativoConvenioService(ContextoUsuario.UserLogged);
        demonstrativoConvenioService.Delete(idItensToDelete, IdRepasse);
        return Json(new { Erro = false, Titulo = "Sucesso", Mensagem = "Itens deletados com sucesso" });
      }
      catch (CustomException ex)
      {
        return Json(new { Erro = true, Titulo = "Erro", Mensagem = ex.Message });
      }
      catch (Exception ex)
      {
        return Json(new { Erro = true, Titulo = "Erro", Mensagem = "Ocorreu um erro na sua solicitação, entre em contato com o administrador ou tente novamente mais tarde" });
      }
    }

  }
}
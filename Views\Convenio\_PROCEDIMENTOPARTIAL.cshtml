﻿@using RepasseConvenio.Models
@model List<ProcedimentoIndex>

@{
  ViewBag.Title = "Lista Procedimentos";
}
@Scripts.Render("~/Views/Convenio/Convenio.js")

<!-- /.card-header -->
    <section class="col-lg-12 connectedSortable ui-sortable">
        <div class="box-body">
          <div class="col-md-12 table-responsive p-0">
            <table id="TableTela" class="table-striped table table-sm table-hover text-nowrap">
              <thead>
                <tr>
                  <th>
                    Código
                  </th>
                  <th>
                   Procedimento
                  </th>
                  <th>
                  </th>
                </tr>
              </thead>
              <tbody>
                @foreach (ProcedimentoIndex item in Model)
                {
                  <tr>
                    <td>
                      @item.CodProcedimento
                    </td>
                    <td>
                      @item.Descricao
                    </td>
                    <td>
                    </td>
                  </tr>
                }
              </tbody>
            </table>
          </div>
        </div>
    </section>

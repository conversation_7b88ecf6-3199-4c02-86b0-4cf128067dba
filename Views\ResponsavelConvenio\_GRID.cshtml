﻿@using RepasseConvenio.Models
@using PagedList
@model IPagedList<ResponsavelConvenioModel>

<div class="tab-content p-0">
  <div class="box-body">
    <div class="row">
      <div class="col-md-12 table-responsive p-0">
        <table class="table table-sm table-striped table-hover text-nowrap">
          <thead>
            <tr>
              <th>
                Nome
              </th>
              <th>
                CPF
              </th>
              <th>
                Email
              </th>
              <th>
              </th>
            </tr>
          </thead>
          <tbody>
            @foreach (var item in Model)
            {
              <tr class="TrSelecionavel" data-codigorelacao="@(item.CodigoRelacao)" data-codigoresponsavel="@(item.Codigo)">
                <td>
                  @item.NomeResponsavel
                </td>
                <td>
                  @item.CPF
                </td>
                <td>
                  @item.Email
                </td>
              </tr>
            }
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

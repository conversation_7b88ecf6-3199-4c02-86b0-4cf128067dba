﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Models;
using RepasseConvenio.WSPortalCooperado;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.Entity;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Text;
using System.Transactions;
using System.Web;
using System.Data.OleDb;
using RepasseConvenio.Infrastructure;
using Microsoft.Ajax.Utilities;

namespace RepasseConvenio.Services
{
  public class ProcGuiaAtendimentoService : ServiceBase
  {
    public ProcGuiaAtendimentoService()
   : base()
    { }
    public ProcGuiaAtendimentoService(RepasseEntities Contexto)
       : base(Contexto)
    { }

    public ProcGuiaAtendimentoService(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public ProcGuiaAtendimentoService(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public void IntegraProcGuiaAtendimento(List<ProcedimentoRepasseWS> ListaProcedimento, int IdGuiaAtendimento)
    {

      foreach (ProcedimentoRepasseWS item in ListaProcedimento)
      {
        R_ProcGuiaAtendimento procGuiaAtendimento = item.ToProcGuiaAtendimentoCreate(IdGuiaAtendimento);
        Create(procGuiaAtendimento);
      }
    }

    public ProcGuiaDemonstrativoIntegra GetValorFaturado(int IdGuiaAtendimento, string CodProcedimento)
    {
      string query = @"SELECT 
                         GA_ValorFaturado [ValorFaturado],
                         PGA_TaxaAdministrativa [TaxaAdministrativa],
							           PGA_Id [IdProcGuiaAtendimento]
                       FROM R_ProcGuiaAtendimento WHERE PGA_IdGuiaAtendimento	= @IdGuiaAtendimento 
                       AND PGA_CodProcedimento = @CodProcedimento";

      return Contexto.Database.SqlQuery<ProcGuiaDemonstrativoIntegra>(query, new SqlParameter("@IdGuiaAtendimento", IdGuiaAtendimento)
                                               , new SqlParameter("@CodProcedimento", CodProcedimento)).FirstOrDefault();
    }

    public List<ProcGuiaAtendimentoGrid> GetListProcGuiaAtendimentoGrid(int IdGuia)
    {
      string query = @"SELECT
                        PGA.PGA_DtRealizacao [Data],
                        PGA.PGA_HrIniRealizacao [HoraInicio],
                        PGA.PGA_HrFimRealizacao [HoraFim],
                        PGA.PGA_Urgencia [Urgencia],
                        PGA.PGA_CodProcedimento [Procedimento],
                        PGA.PGA_DescProcedimento [Descricao],
                        PGA.PGA_QtdeRealizada [Quantidade],
                        PGA.PGA_DescTipoProcedimento [TipoProcedimento],
                        PGA.PGA_CodViaAcesso [Via],
                        PGA.PGA_CodTecUtilizada [Tecnica],
                        PGA.PGA_PercFatorFaturamento [Porcentagem],
                        PGA.GA_ValorFaturado [ValorFaturado]
                       FROM R_ProcGuiaAtendimento PGA
                       WHERE PGA.PGA_IdGuiaAtendimento = @IdGuia";

      return Contexto.Database.SqlQuery<ProcGuiaAtendimentoGrid>(query, new SqlParameter("@IdGuia", IdGuia)).ToList();
    }
  }
}
﻿@using RepasseConvenio.Infrastructure.Controls
@using RepasseConvenio.Infrastructure.Helpers
@model ForgotPasswordModel

@{
  ViewBag.Title = "Esqueci Minha Senha";
  Layout = "~/Views/Shared/_Layout.cshtml";
}

@Styles.Render("~/Views/Account/Account.css?c=c")

@using (Html.BeginForm())
{
  <div class="form-background">
    <div class="div-position">
      <div style="text-align:center;margin-bottom:10%;">
        <h3><b><PERSON><PERSON><PERSON> minha Senha</b></h3>
      </div>
      <div>
        <div class="form-group">
          @Html.LabelFor(m => m.CPF)
          @Html.LibEditorFor(m => m.CPF, new { @class = "form-control CPF" })
          @Html.ValidationMessageFor(m => m.CPF, "", new { @class = "text-danger" })
        </div>
      </div>
      <div >
        <div class="form-group">
          @Html.LabelFor(m => m.Email)
          @Html.LibEditorFor(m => m.Email, new { @class = "form-control EMAIL" })
          @Html.ValidationMessageFor(m => m.Email, "", new { @class = "text-danger" })
        </div>
      </div>
      <div style="text-align: center">
        <button type="submit" class="btn btn-primary" style="padding: 7.5px 31px !important;margin: 0 10px 15px !important">Resetar</button>
        <br>
        <a class="btn btn-secondary" href="@Url.Action("Login", "Account")" style="padding: 2.5px 20px !important">Voltar</a><br>
      </div>
    </div>
  </div>
}
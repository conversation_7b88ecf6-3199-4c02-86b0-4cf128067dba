<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://tempuri.org/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
      <s:element name="BuscaBasicaLotes">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="model" type="tns:BuscaLoteRepasse" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="BuscaLoteRepasse">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="NumeroLote" nillable="true" type="s:int" />
          <s:element minOccurs="1" maxOccurs="1" name="DataDe" nillable="true" type="s:dateTime" />
          <s:element minOccurs="1" maxOccurs="1" name="DataAte" nillable="true" type="s:dateTime" />
          <s:element minOccurs="0" maxOccurs="1" name="CNPJConvenio" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="BuscaBasicaLotesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="BuscaBasicaLotesResult" type="tns:ResultPesquisaLote" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ResultPesquisaLote">
        <s:complexContent mixed="false">
          <s:extension base="tns:RepasseRetornoWS">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="ListaLotesBasic" type="tns:ArrayOfLoteBasicoWS" />
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="RepasseRetornoWS">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Erro" type="s:boolean" />
          <s:element minOccurs="0" maxOccurs="1" name="Mensagem" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfLoteBasicoWS">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="LoteBasicoWS" nillable="true" type="tns:LoteBasicoWS" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="LoteBasicoWS">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="NumeroLote" type="s:int" />
          <s:element minOccurs="1" maxOccurs="1" name="DataEmissao" type="s:dateTime" />
          <s:element minOccurs="1" maxOccurs="1" name="Valor" type="s:decimal" />
          <s:element minOccurs="0" maxOccurs="1" name="ListaGuiasRepasse" type="tns:ArrayOfGuiaRepasseWS" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfGuiaRepasseWS">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="GuiaRepasseWS" nillable="true" type="tns:GuiaRepasseWS" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="GuiaRepasseWS">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="CPFCooperado" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="CNPJConvenio" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="CodGuiaPrestador" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="DtInicioFaturamento" nillable="true" type="s:dateTime" />
          <s:element minOccurs="1" maxOccurs="1" name="DtFimFaturamento" nillable="true" type="s:dateTime" />
          <s:element minOccurs="1" maxOccurs="1" name="DtEmissao" nillable="true" type="s:dateTime" />
          <s:element minOccurs="0" maxOccurs="1" name="CodGuiaPrincipal" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="DtAutorizacaoGuia" nillable="true" type="s:dateTime" />
          <s:element minOccurs="0" maxOccurs="1" name="CodSenhaGuia" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="DtValidadeSenha" nillable="true" type="s:dateTime" />
          <s:element minOccurs="0" maxOccurs="1" name="CodGuiaOperadora" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="CodCarteiraPaciente" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="DtValidadeCarteira" nillable="true" type="s:dateTime" />
          <s:element minOccurs="0" maxOccurs="1" name="NomePaciente" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="CodCartaoNacSaude" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="IdentRN" nillable="true" type="s:int" />
          <s:element minOccurs="0" maxOccurs="1" name="NomeSolicitante" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ConselhoSolicitante" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="CodNumConselho" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="CodUF" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="CodCBO" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="CaraterAtendimento" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="DtSolicitacao" nillable="true" type="s:dateTime" />
          <s:element minOccurs="0" maxOccurs="1" name="DescIndClinica" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="CodigoProcSolicitado" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="QtdeSolicitado" nillable="true" type="s:int" />
          <s:element minOccurs="1" maxOccurs="1" name="QtdeAutorizada" nillable="true" type="s:int" />
          <s:element minOccurs="0" maxOccurs="1" name="CodPrestContratado" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="NomePrestador" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="CodNCNES" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="DescTipoAtendimento" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="DescIndAcidente" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="DescTipoConsulta" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="MotEnceramento" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ObsJustificativa" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="VlrTotal" nillable="true" type="s:decimal" />
          <s:element minOccurs="1" maxOccurs="1" name="VlrGlosa" nillable="true" type="s:decimal" />
          <s:element minOccurs="0" maxOccurs="1" name="DescTipoSaidaGuiaConsulta" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="NroUnicooper" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="TipoGuia" type="s:int" />
          <s:element minOccurs="0" maxOccurs="1" name="Complemento" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="DescPlano" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="DataInternacao" nillable="true" type="s:dateTime" />
          <s:element minOccurs="1" maxOccurs="1" name="DataAlta" nillable="true" type="s:dateTime" />
          <s:element minOccurs="0" maxOccurs="1" name="SolicitacaoInternacao" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="DataEntrega" nillable="true" type="s:dateTime" />
          <s:element minOccurs="0" maxOccurs="1" name="AtendimentoHospital" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="DescTipoAcomodacao" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="OrigemGuia" nillable="true" type="s:int" />
          <s:element minOccurs="1" maxOccurs="1" name="DataAtendimento" nillable="true" type="s:dateTime" />
          <s:element minOccurs="0" maxOccurs="1" name="CNPJFaturamento" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="ValorFaturado" type="s:decimal" />
          <s:element minOccurs="0" maxOccurs="1" name="CodRegistro" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ParticipacaoGuia" type="tns:ParticipacaoGuiaRepasseWS" />
          <s:element minOccurs="0" maxOccurs="1" name="Procedimentos" type="tns:ArrayOfProcedimentoRepasseWS" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ParticipacaoGuiaRepasseWS">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="CodigoGuiaAtendimento" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="SeqProcedimento" nillable="true" type="s:int" />
          <s:element minOccurs="0" maxOccurs="1" name="CodGrauPart" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="CodCBO" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="DtRealizacaoProcSerie" nillable="true" type="s:dateTime" />
          <s:element minOccurs="0" maxOccurs="1" name="MedicoRepasse" type="tns:MedicoRepasse" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="MedicoRepasse">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Codigo" type="s:int" />
          <s:element minOccurs="0" maxOccurs="1" name="CRM" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="CPF" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Nome" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Email" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="AgenciaMedico" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="BancoMedico" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ContaMedico" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="statusCooperado" type="tns:StatusCooperado" />
        </s:sequence>
      </s:complexType>
      <s:simpleType name="StatusCooperado">
        <s:restriction base="s:string">
          <s:enumeration value="Ativo" />
          <s:enumeration value="Suspenso" />
          <s:enumeration value="Desligado" />
          <s:enumeration value="Emcadastro" />
          <s:enumeration value="Eliminado" />
          <s:enumeration value="Inativo" />
        </s:restriction>
      </s:simpleType>
      <s:complexType name="ArrayOfProcedimentoRepasseWS">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="ProcedimentoRepasseWS" nillable="true" type="tns:ProcedimentoRepasseWS" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ProcedimentoRepasseWS">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="TaxaAdm" type="s:decimal" />
          <s:element minOccurs="0" maxOccurs="1" name="CodigoGuiaAtendimento" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="SeqProcedimento" type="s:int" />
          <s:element minOccurs="1" maxOccurs="1" name="DtRealizacao" nillable="true" type="s:dateTime" />
          <s:element minOccurs="0" maxOccurs="1" name="HrIniRealizacao" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="HrFimRealizacao" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="CodProcedimento" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="DescProcedimento" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="QtdeRealizada" nillable="true" type="s:int" />
          <s:element minOccurs="0" maxOccurs="1" name="CodViaAcesso" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="CodTecUtilizada" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="PercFator" nillable="true" type="s:decimal" />
          <s:element minOccurs="1" maxOccurs="1" name="VlrUnit" nillable="true" type="s:decimal" />
          <s:element minOccurs="1" maxOccurs="1" name="VlrTotal" nillable="true" type="s:decimal" />
          <s:element minOccurs="0" maxOccurs="1" name="DescTipoProcedimento" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="CodTabANS" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="PercFatorFaturamento" nillable="true" type="s:decimal" />
          <s:element minOccurs="1" maxOccurs="1" name="Urgencia" type="s:boolean" />
          <s:element minOccurs="1" maxOccurs="1" name="ValorFaturado" type="s:decimal" />
        </s:sequence>
      </s:complexType>
      <s:element name="BuscaAvancadaLotes">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="model" type="tns:BuscaAvancadaRepasse" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="BuscaAvancadaRepasse">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="NumerosLotes" type="tns:ArrayOfInt" />
          <s:element minOccurs="0" maxOccurs="1" name="CNPJConvenio" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfInt">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="int" type="s:int" />
        </s:sequence>
      </s:complexType>
      <s:element name="BuscaAvancadaLotesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="BuscaAvancadaLotesResult" type="tns:ResultPesquisaLote" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="IntegracaoGuiaRepasse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="model" type="tns:IntegracoesGuiasRepasseWS" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="IntegracoesGuiasRepasseWS">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="MarcarIntegracao" type="s:boolean" />
          <s:element minOccurs="0" maxOccurs="1" name="NroGuias" type="tns:ArrayOfString" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfString">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="string" nillable="true" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="IntegracaoGuiaRepasseResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="IntegracaoGuiaRepasseResult" type="tns:RepasseRetornoWS" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetMedicos">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="password" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetMedicosResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetMedicosResult" type="tns:ArrayOfMedicoRepasse" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfMedicoRepasse">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="MedicoRepasse" nillable="true" type="tns:MedicoRepasse" />
        </s:sequence>
      </s:complexType>
      <s:element name="GetConvenios">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="password" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetConveniosResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetConveniosResult" type="tns:ArrayOfConvenioRepasse" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfConvenioRepasse">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="ConvenioRepasse" nillable="true" type="tns:ConvenioRepasse" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ConvenioRepasse">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Codigo" type="s:int" />
          <s:element minOccurs="0" maxOccurs="1" name="RazaoSocial" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="CNPJConvenio" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="CodANS" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="GetProcedimentosConvenios">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="password" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="idConvenio" type="s:int" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetProcedimentosConveniosResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetProcedimentosConveniosResult" type="tns:ArrayOfProcedimentosConvenioRepasse" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfProcedimentosConvenioRepasse">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="ProcedimentosConvenioRepasse" nillable="true" type="tns:ProcedimentosConvenioRepasse" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ProcedimentosConvenioRepasse">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Codigo" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Descricao" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="GetEmpresasMedico">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="password" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="IdMedico" type="s:int" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetEmpresasMedicoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetEmpresasMedicoResult" type="tns:ArrayOfEmpresaMedicoRepasse" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfEmpresaMedicoRepasse">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="EmpresaMedicoRepasse" nillable="true" type="tns:EmpresaMedicoRepasse" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="EmpresaMedicoRepasse">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="CNPJ" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="RazaoSocial" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="AgenciaEmpresa" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="BancoEmpresa" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ContaEmpresa" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="GetGuiaLoteRepasse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="integraGuiaRepasse" type="tns:IntegraGuiaRepasse" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="IntegraGuiaRepasse">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="CNPJConvenio" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="NroUnicooper" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="GetGuiaLoteRepasseResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetGuiaLoteRepasseResult" type="tns:RetornoGetGuiaLoteRepasse" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="RetornoGetGuiaLoteRepasse">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="loteRepasse" type="tns:LoteRepasse" />
          <s:element minOccurs="1" maxOccurs="1" name="Erro" type="s:boolean" />
          <s:element minOccurs="1" maxOccurs="1" name="ErroPersonalizado" type="s:boolean" />
          <s:element minOccurs="0" maxOccurs="1" name="Mensagem" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="LoteRepasse">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="NumeroLote" type="s:int" />
          <s:element minOccurs="1" maxOccurs="1" name="DataCriacao" type="s:dateTime" />
          <s:element minOccurs="1" maxOccurs="1" name="DataEnvio" nillable="true" type="s:dateTime" />
          <s:element minOccurs="1" maxOccurs="1" name="DataVencimento" nillable="true" type="s:dateTime" />
          <s:element minOccurs="0" maxOccurs="1" name="CNPJConvenio" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ListaGuiasRepasse" type="tns:ArrayOfGuiaRepasseWS" />
        </s:sequence>
      </s:complexType>
      <s:element name="GetLoteRepasse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="integraLoteRepasse" type="tns:IntegraLoteRepasse" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="IntegraLoteRepasse">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="CNPJConvenio" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="NroLote" type="s:int" />
        </s:sequence>
      </s:complexType>
      <s:element name="GetLoteRepasseResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetLoteRepasseResult" type="tns:RetornoGetGuiaLoteRepasse" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetMedicoIntegraGuia">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="CPFMedico" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="password" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetMedicoIntegraGuiaResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetMedicoIntegraGuiaResult" type="tns:RetornoMedicoIntegraGuia" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="RetornoMedicoIntegraGuia">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="medicoIntegraGuia" type="tns:MedicoIntegraGuia" />
          <s:element minOccurs="1" maxOccurs="1" name="Erro" type="s:boolean" />
          <s:element minOccurs="1" maxOccurs="1" name="ErroPersonalizado" type="s:boolean" />
          <s:element minOccurs="0" maxOccurs="1" name="Mensagem" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="MedicoIntegraGuia">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Codigo" type="s:int" />
          <s:element minOccurs="0" maxOccurs="1" name="CRM" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="CPF" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Nome" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Email" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="AgenciaMedico" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="BancoMedico" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ContaMedico" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="statusCooperado" type="tns:StatusCooperado" />
        </s:sequence>
      </s:complexType>
    </s:schema>
  </wsdl:types>
  <wsdl:message name="BuscaBasicaLotesSoapIn">
    <wsdl:part name="parameters" element="tns:BuscaBasicaLotes" />
  </wsdl:message>
  <wsdl:message name="BuscaBasicaLotesSoapOut">
    <wsdl:part name="parameters" element="tns:BuscaBasicaLotesResponse" />
  </wsdl:message>
  <wsdl:message name="BuscaAvancadaLotesSoapIn">
    <wsdl:part name="parameters" element="tns:BuscaAvancadaLotes" />
  </wsdl:message>
  <wsdl:message name="BuscaAvancadaLotesSoapOut">
    <wsdl:part name="parameters" element="tns:BuscaAvancadaLotesResponse" />
  </wsdl:message>
  <wsdl:message name="IntegracaoGuiaRepasseSoapIn">
    <wsdl:part name="parameters" element="tns:IntegracaoGuiaRepasse" />
  </wsdl:message>
  <wsdl:message name="IntegracaoGuiaRepasseSoapOut">
    <wsdl:part name="parameters" element="tns:IntegracaoGuiaRepasseResponse" />
  </wsdl:message>
  <wsdl:message name="GetMedicosSoapIn">
    <wsdl:part name="parameters" element="tns:GetMedicos" />
  </wsdl:message>
  <wsdl:message name="GetMedicosSoapOut">
    <wsdl:part name="parameters" element="tns:GetMedicosResponse" />
  </wsdl:message>
  <wsdl:message name="GetConveniosSoapIn">
    <wsdl:part name="parameters" element="tns:GetConvenios" />
  </wsdl:message>
  <wsdl:message name="GetConveniosSoapOut">
    <wsdl:part name="parameters" element="tns:GetConveniosResponse" />
  </wsdl:message>
  <wsdl:message name="GetProcedimentosConveniosSoapIn">
    <wsdl:part name="parameters" element="tns:GetProcedimentosConvenios" />
  </wsdl:message>
  <wsdl:message name="GetProcedimentosConveniosSoapOut">
    <wsdl:part name="parameters" element="tns:GetProcedimentosConveniosResponse" />
  </wsdl:message>
  <wsdl:message name="GetEmpresasMedicoSoapIn">
    <wsdl:part name="parameters" element="tns:GetEmpresasMedico" />
  </wsdl:message>
  <wsdl:message name="GetEmpresasMedicoSoapOut">
    <wsdl:part name="parameters" element="tns:GetEmpresasMedicoResponse" />
  </wsdl:message>
  <wsdl:message name="GetGuiaLoteRepasseSoapIn">
    <wsdl:part name="parameters" element="tns:GetGuiaLoteRepasse" />
  </wsdl:message>
  <wsdl:message name="GetGuiaLoteRepasseSoapOut">
    <wsdl:part name="parameters" element="tns:GetGuiaLoteRepasseResponse" />
  </wsdl:message>
  <wsdl:message name="GetLoteRepasseSoapIn">
    <wsdl:part name="parameters" element="tns:GetLoteRepasse" />
  </wsdl:message>
  <wsdl:message name="GetLoteRepasseSoapOut">
    <wsdl:part name="parameters" element="tns:GetLoteRepasseResponse" />
  </wsdl:message>
  <wsdl:message name="GetMedicoIntegraGuiaSoapIn">
    <wsdl:part name="parameters" element="tns:GetMedicoIntegraGuia" />
  </wsdl:message>
  <wsdl:message name="GetMedicoIntegraGuiaSoapOut">
    <wsdl:part name="parameters" element="tns:GetMedicoIntegraGuiaResponse" />
  </wsdl:message>
  <wsdl:portType name="IntegraRepasseSoap">
    <wsdl:operation name="BuscaBasicaLotes">
      <wsdl:input message="tns:BuscaBasicaLotesSoapIn" />
      <wsdl:output message="tns:BuscaBasicaLotesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="BuscaAvancadaLotes">
      <wsdl:input message="tns:BuscaAvancadaLotesSoapIn" />
      <wsdl:output message="tns:BuscaAvancadaLotesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="IntegracaoGuiaRepasse">
      <wsdl:input message="tns:IntegracaoGuiaRepasseSoapIn" />
      <wsdl:output message="tns:IntegracaoGuiaRepasseSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetMedicos">
      <wsdl:input message="tns:GetMedicosSoapIn" />
      <wsdl:output message="tns:GetMedicosSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetConvenios">
      <wsdl:input message="tns:GetConveniosSoapIn" />
      <wsdl:output message="tns:GetConveniosSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetProcedimentosConvenios">
      <wsdl:input message="tns:GetProcedimentosConveniosSoapIn" />
      <wsdl:output message="tns:GetProcedimentosConveniosSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetEmpresasMedico">
      <wsdl:input message="tns:GetEmpresasMedicoSoapIn" />
      <wsdl:output message="tns:GetEmpresasMedicoSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetGuiaLoteRepasse">
      <wsdl:input message="tns:GetGuiaLoteRepasseSoapIn" />
      <wsdl:output message="tns:GetGuiaLoteRepasseSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetLoteRepasse">
      <wsdl:input message="tns:GetLoteRepasseSoapIn" />
      <wsdl:output message="tns:GetLoteRepasseSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetMedicoIntegraGuia">
      <wsdl:input message="tns:GetMedicoIntegraGuiaSoapIn" />
      <wsdl:output message="tns:GetMedicoIntegraGuiaSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="IntegraRepasseSoap" type="tns:IntegraRepasseSoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="BuscaBasicaLotes">
      <soap:operation soapAction="http://tempuri.org/BuscaBasicaLotes" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="BuscaAvancadaLotes">
      <soap:operation soapAction="http://tempuri.org/BuscaAvancadaLotes" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="IntegracaoGuiaRepasse">
      <soap:operation soapAction="http://tempuri.org/IntegracaoGuiaRepasse" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMedicos">
      <soap:operation soapAction="http://tempuri.org/GetMedicos" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetConvenios">
      <soap:operation soapAction="http://tempuri.org/GetConvenios" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetProcedimentosConvenios">
      <soap:operation soapAction="http://tempuri.org/GetProcedimentosConvenios" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetEmpresasMedico">
      <soap:operation soapAction="http://tempuri.org/GetEmpresasMedico" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetGuiaLoteRepasse">
      <soap:operation soapAction="http://tempuri.org/GetGuiaLoteRepasse" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetLoteRepasse">
      <soap:operation soapAction="http://tempuri.org/GetLoteRepasse" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMedicoIntegraGuia">
      <soap:operation soapAction="http://tempuri.org/GetMedicoIntegraGuia" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="IntegraRepasseSoap12" type="tns:IntegraRepasseSoap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="BuscaBasicaLotes">
      <soap12:operation soapAction="http://tempuri.org/BuscaBasicaLotes" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="BuscaAvancadaLotes">
      <soap12:operation soapAction="http://tempuri.org/BuscaAvancadaLotes" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="IntegracaoGuiaRepasse">
      <soap12:operation soapAction="http://tempuri.org/IntegracaoGuiaRepasse" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMedicos">
      <soap12:operation soapAction="http://tempuri.org/GetMedicos" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetConvenios">
      <soap12:operation soapAction="http://tempuri.org/GetConvenios" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetProcedimentosConvenios">
      <soap12:operation soapAction="http://tempuri.org/GetProcedimentosConvenios" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetEmpresasMedico">
      <soap12:operation soapAction="http://tempuri.org/GetEmpresasMedico" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetGuiaLoteRepasse">
      <soap12:operation soapAction="http://tempuri.org/GetGuiaLoteRepasse" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetLoteRepasse">
      <soap12:operation soapAction="http://tempuri.org/GetLoteRepasse" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMedicoIntegraGuia">
      <soap12:operation soapAction="http://tempuri.org/GetMedicoIntegraGuia" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="IntegraRepasse">
    <wsdl:port name="IntegraRepasseSoap" binding="tns:IntegraRepasseSoap">
      <soap:address location="http://localhost/PortalCooperado/integrarepasse.asmx" />
    </wsdl:port>
    <wsdl:port name="IntegraRepasseSoap12" binding="tns:IntegraRepasseSoap12">
      <soap12:address location="http://localhost/PortalCooperado/integrarepasse.asmx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>
﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Transactions;
using RepasseConvenio.Infrastructure;
using System.Text.RegularExpressions;
using RepasseConvenio.WSPortalCooperado;
using PagedList;
using System.Configuration;
using System.IO;
using RepasseConvenio.Infrastructure.Helpers;

namespace RepasseConvenio.Services
{
  public class ProtocoloGlosaServices : ServiceBase
  {
    public ProtocoloGlosaServices()
   : base()
    { }
    public ProtocoloGlosaServices(RepasseEntities Contexto)
       : base(Contexto)
    { }

    public ProtocoloGlosaServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public ProtocoloGlosaServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public void Create(ProtocoloGlosaCreate protocoloGlosaCreate)
    {
      LoteGlosaServices loteGlosaServices = new LoteGlosaServices();
      StatusGlosaServices statusGlosaServices = new StatusGlosaServices();
      int IdStatusEnviadoConvenio = statusGlosaServices.GetIdByEnum(StatusGlosaEnum.EmAnaliseConvenio);
      R_LoteGlosa loteGlosa = loteGlosaServices.GetById(protocoloGlosaCreate.CodigoLoteGlosa);

      if (loteGlosa.LG_IdStatusGlosa == IdStatusEnviadoConvenio)
        throw new CustomException("Já enviado ao convênio, não pode mais ser criado protocolo.");
      else
      {
        loteGlosa.LG_IdStatusGlosa = IdStatusEnviadoConvenio;
        Edit(loteGlosa);
      }

      R_ProtocoloGlosa protocoloGlosa = protocoloGlosaCreate.ModelToEntityCreate(User.IdUsuario);
      using (TransactionScope scope = new TransactionScope(TransactionScopeOption.RequiresNew))
      {
        Create(protocoloGlosa);
        string PathProtocoloGlosas = ConfigurationManager.AppSettings["PathProtocoloGlosas"];
        foreach (var item in protocoloGlosaCreate.Files)
        {
          if (item != null)
          {
            R_AnexoProtocoloGlosa anexoProtocoloGlosa = new R_AnexoProtocoloGlosa();
            anexoProtocoloGlosa = anexoProtocoloGlosa.EntityCreate(item, protocoloGlosa.PG_Id);
            Create(anexoProtocoloGlosa);

            string CompletePathProtocoloGuia = Path.Combine(PathProtocoloGlosas, protocoloGlosaCreate.CodigoLoteGlosa.ToString(), protocoloGlosa.PG_Id.ToString());
            CompletePathProtocoloGuia = DirectoryHelper.CreateDiretorioIfNotExist(CompletePathProtocoloGuia);
            CompletePathProtocoloGuia = Path.Combine(CompletePathProtocoloGuia, anexoProtocoloGlosa.APG_NomeArquivoWithExtension);
            byte[] Arquivo = AnexoHelper.ConverteHttpPostedFileBaseToByteArray(item);
            File.WriteAllBytes(CompletePathProtocoloGuia, Arquivo);
          }
        }
        scope.Complete();
      }
    }

    public ProtocoloGlosaIndexCabecalho GetPagedProtocoloGlosaIndex(int IdLoteGlosa, int page)
    {
      ProtocoloGlosaIndexCabecalho protocoloGlosaIndexCabecalho = new ProtocoloGlosaIndexCabecalho();
      string query = @"SELECT
                        COUNT(1)
                       FROM R_ProtocoloGlosa WHERE PG_IdLoteGlosa = @IdLoteGlosa";

      int QuantidadeProtocolo = Contexto.Database.SqlQuery<int>(query, new SqlParameter("@IdLoteGlosa", IdLoteGlosa)).FirstOrDefault();

      List<int> ListaQuantidadeProtocolo = new List<int>();

      for (int i = 0; i < QuantidadeProtocolo; i++)
      {
        ListaQuantidadeProtocolo.Add(i);
      }

      protocoloGlosaIndexCabecalho.QuantidadeProtocoloGlosa = ListaQuantidadeProtocolo.ToPagedList(page, PageSize);

      query = @"SELECT
                  PG_Id [Codigo],
                  PG_NumeroProtocolo [NumeroProtocolo],
                  PG_DataEmissao [DataEmissao],
                  PG_DataPrazoRetornoRecurso [DataPrazo],
                  PG_DataCriacao [DataCriacao]
                  FROM R_ProtocoloGlosa
                 WHERE PG_IdLoteGlosa = @IdLoteGlosa
                 ORDER BY PG_DataCriacao DESC
                 OFFSET (@page - 1) * @PageSize ROWS
                 FETCH NEXT @PageSize ROWS ONLY";

      protocoloGlosaIndexCabecalho.ListaProtocoloGlosaIndex = Contexto.Database.SqlQuery<ProtocoloGlosaIndex>(query, new SqlParameter("@IdLoteGlosa", IdLoteGlosa)
                                                                                                                   , new SqlParameter("@page", page)
                                                                                                                   , new SqlParameter("@PageSize", PageSize)).ToList();
      protocoloGlosaIndexCabecalho.IdLoteGlosa = IdLoteGlosa;
      return protocoloGlosaIndexCabecalho;
    }

    public ProtocoloGlosaDetalhes GetProtocoloGlosaDetalhes(int IdProtocoloGlosa)
    {
      AnexoProtocoloGlosaService anexoProtocoloGlosaService = new AnexoProtocoloGlosaService();
      string query = @"SELECT 
                        PG_NumeroProtocolo [NumeroProtocolo],
                        PG_DataEmissao [DataEmissao],
                        PG_DataPrazoRetornoRecurso [DataPrazo],
                        PG_ObservacoesGerais [ObservacoesGerais],
                        PG_IdLoteGlosa [CodigoLoteGlosa]
                       FROM R_ProtocoloGlosa
                       WHERE PG_Id = @IdProtocoloGlosa";

      ProtocoloGlosaDetalhes protocoloGlosaDetalhes = Contexto.Database.SqlQuery<ProtocoloGlosaDetalhes>(query, new SqlParameter("@IdProtocoloGlosa", IdProtocoloGlosa)).FirstOrDefault();
      protocoloGlosaDetalhes.ListaAnexoProtocoloGlosaDetalhes = anexoProtocoloGlosaService.GetListaAnexoProtocoloGlosaDetalhes(IdProtocoloGlosa);
      
      return protocoloGlosaDetalhes;
    }
  }
}
﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Web;

namespace RepasseConvenio.Models
{
  public class BancoModel
  {
    public int Id { get; set; }
    public string Descricao { get; set; }
    public string Codigo { get; set; }
  }
  public static class BancoModelConversions
  {
    public static R_Banco ToBancoCreate(this BancoModel model)
    {
      R_Banco entity = new R_Banco();
      entity.B_Nome = model.Descricao;
      entity.B_Codigo = model.Codigo;
      return entity;
    }

    public static R_Banco ToBancoEdit(this BancoModel model, R_Banco entity)
    {
      entity.B_Nome = model.Descricao;
      entity.B_Codigo = model.Codigo;
      return entity;
    }

    public static BancoModel ToBancoModel(this R_Banco entity)
    {
      if (entity == null)
        return new BancoModel();

      BancoModel model = new BancoModel();
      model.Id = entity.B_Id;
      model.Descricao = entity.B_Nome;
      model.Codigo = entity.B_Codigo;

      return model;
    }
  }

}
﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

//using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Infrastructure.Controls;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Infrastructure.Mensagem;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Web;

namespace RepasseConvenio.Models
{
  public class RetornoValidacao
  {
    public RetornoValidacao()
    {
      TemErro = false;
    }
    public bool TemErro { get; set; }
    public string Mensagem { get; set; }
  }

  public class UsuarioLogado
  {
    public int IdUsuario { get; set; }
    public string Nome { get; set; }
    public string CPF { get; set; }
    public string Email { get; set; }
    public int TipoUsuario { get; set; }
    public int? IdResponsavel { get; set; }
    public List<ResponsavalConvenioUserLogged> ConveniosResponsavel { get; set; }


    public bool IsInPermissao(Guid id)
    {
      return true;
    }

  }

  public class LoginModel
  {
    public LoginModel() { }

    [Required(ErrorMessage = "Informe a Senha.")]
    [DisplayName("Senha")]
    public string Password { get; set; }

    [Required(ErrorMessage = "Informe o CPF.")]
    [DisplayName("CPF")]
    [ToolTip("Digite aqui CPF")]
    public string UserName { get; set; }
  }
  public class LookupModel
  {
    public int Id { get; set; }

    public string Codigo { get; set; }

    public string Description { get; set; }

    public string text { get; set; }
  }
  public class LookupModel2
  {
    public string Id { get; set; }

    public string Codigo { get; set; }

    public string Description { get; set; }

    public string text { get; set; }
  }
  public class LookupModel3
  {
    public Guid Id { get; set; }

    public string Codigo { get; set; }

    public string Description { get; set; }

    public string text { get; set; }
  }

  public class Attachment
  {
    public string Extensao { get; set; }
    public string Base64 { get; set; }
  }

  public class DepositCrypt
  {
    public string amount { get; set; }
    public string txid { get; set; }
    public string Coin { get; set; }
  }

  public class PaymentSlipModel
  {
    public string CodigoBarras { get; set; }
    public string ValorBoleto { get; set; }
    public string Coin { get; set; }
    public string DataVencimento { get; set; }
  }

  public class WithdrawCrypt
  {
    public string amount { get; set; }
    public string Wallet { get; set; }
    public string Coin { get; set; }
    public int TX { get; set; }
  }

  public class DepositMoney
  {
    public string Value { get; set; }
    public byte[] Attachment { get; set; }
    public string Coin { get; set; }
  }

  public class WithdrawMoney
  {
    public string Value { get; set; }
    public string Bank { get; set; }
    public string Coin { get; set; }
  }

  public class DadosBancariosAux
  {
    public string Banco { get; set; }
    public string Agencia { get; set; }
    public string Conta { get; set; }
    public string Digito { get; set; }
    public string Operacao { get; set; }
    public string TipoConta { get; set; }
  }

  public class ChangePassword
  {
    [DisplayName("Senha Antiga")]
    public string LastPassword { get; set; }

    [DisplayName("Nova Senha")]
    public string NewPassword { get; set; }

    [DisplayName("Confirmar Nova Senha")]
    public string ConfirmPassword { get; set; }

  }
  public class RetornoAjax
  {
    public RetornoAjax()
    {
      this.Erro = false;
      this.Timer = 3000;
    }

    public bool Erro { get; set; }
    public string TipoMensagem { get; set; }
    public string Mensagem { get; set; }
    public string Titulo { get; set; }
    public MessageType messageType { get; set; }

    public string Color
    {
      get
      {
        return MessageColors.GetMessageColor(this.messageType);
      }
    }

    public string Tipo
    {
      get
      {
        return MessageTipo.GetMessageTipo(this.messageType);
      }
    }

    public int Timer { get; set; }
  }

  public class Anexo
  {
    [DisplayName("Tipo de Anexo")]
    public int IdTipoAnexo { get; set; }

    public byte[] DocAnexo { get; set; }

    [DisplayName("Extensão do Anexo")]
    public string ExtensaoAnexo { get; set; }

    [DisplayName("Tipo do Anexo")]
    public string ContentType { get; set; }

    public HttpPostedFileBase PostedFile { get; set; }

    public MemoryStream StreamFile { get; set; }

    public string FileType { get; set; }

    public string FileName { get; set; }

    public byte[] Content { get; set; }
  }
  public class QuerySearch
  {
    public string value { get; set; }
  }
}
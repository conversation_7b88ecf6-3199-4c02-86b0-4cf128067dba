﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Controls;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace RepasseConvenio.Controllers
{
  [Authorize]
  [Security("B1AA79C1-1427-4D12-A797-8F2E923E9F58")]

  public class RateioMedicoController : LibController
  {

    private RateioMedicoService RateioMedicoService
    {
      get
      {
        if (_RateioMedicoService == null)
          _RateioMedicoService = new RateioMedicoService(ContextoUsuario.UserLogged);

        return _RateioMedicoService;
      }
    }
    private RateioMedicoService _RateioMedicoService;
    [Security("4FF0A03F-35CC-484A-9AC6-3E259C0F576B")]

    public ActionResult Index(int? cod, int? codRateio)
    {
      try
      {
        if ((!cod.HasValue || cod.Value == 0) && (!codRateio.HasValue || codRateio == 0))
          throw new Exception("Nenhum médico foi selecionado");

        List<RateioMedicoIndex> medico = new List<RateioMedicoIndex>();

        if (cod.HasValue && cod.Value != 0)
        {
          medico = new RateioMedicoService().GettoIndex(cod.Value);
          ViewBag.CodigoMedico = cod;
        }
        else
        {
          RateioMedicoIndex rateioMedico = RateioMedicoService.GettoIndexbyId(codRateio.Value);
          
          if (rateioMedico == null)
            throw new Exception("Não foi possivel encontrar o Rateio");

          medico = RateioMedicoService.GettoIndex(rateioMedico.CodigoMedico);
          ViewBag.CodigoMedico = rateioMedico.CodigoMedico;
        }

        return View(medico);
      }
      catch (CustomException ex)
      {
        MessageListToast.Add(new Message(MessageType.Warning, ex.Message));
        return RedirectToAction("Index","Medico");
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return RedirectToAction("Index", "Medico");
      }
    }
    [Security("F51AE4CF-F828-454A-986D-36B96846343C")]
    public ActionResult Create(int? codMedico)
    {
      try
      {
        if (codMedico == 0)
          throw new Exception("Nenhum médico selecionado");

        RateioMedicoModel model = new RateioMedicoModel();

        model.CodigoMedico = codMedico.Value;

        return View(model);
      }
      catch (CustomException ex)
      {
        MessageListToast.Add(new Message(MessageType.Warning, ex.Message));
        return RedirectToAction("Index", "Medico");
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return RedirectToAction("Index", "Medico");
      }
    }

    [HttpPost]
    [Security("F51AE4CF-F828-454A-986D-36B96846343C")]

    public ActionResult Create(RateioMedicoModel model)
    {
      try
      {
        if (model.ProcedimentoSelect != null)
          model.DescricaoProcedimento = model.ProcedimentoSelect.text.Split('-')[1];

        if (ModelState.IsValid)
        {
          if (RateioMedicoService.ExistProcedimento(model.CodigoProcedimento, model.CodigoHospital, model.CodigoMedico))
            throw new Exception("Já existe um Rateio cadastrado para o médico e para este hospital.");

          RateioMedicoService.Create(model);

          return RedirectToAction("Index", "RateioMedico", new { cod = model.CodigoMedico });
        }
        return View(model);
      }
      catch (CustomException ex)
      {
        MessageListToast.Add(new Message(MessageType.Warning, ex.Message));
        return RedirectToAction("Index", "Medico");
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return RedirectToAction("Index", "Medico");
      }
    }

    public ActionResult Edit(int? cod)
    {
      try
      {
        if (cod == 0)
          throw new Exception("Nenhum rateio foi selecionado");

        RateioMedicoModel model = new RateioMedicoService().GetbyId(cod.Value);

        if (model == null)
          throw new Exception("Rateio não encontrado");

        return View(model);
      }
      catch (CustomException ex)
      {
        MessageListToast.Add(new Message(MessageType.Warning, ex.Message));
        return RedirectToAction("Index", "Medico");
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return RedirectToAction("Index", "Medico");
      }
    }

    [HttpPost]
    public ActionResult Edit(RateioMedicoModel model)
    {
      try
      {
        if (ModelState.IsValid)
        {
          if (RateioMedicoService.ExistProcedimento(model.CodigoProcedimento, model.CodigoHospital, model.CodigoMedico))
            throw new Exception("Já existe um Rateio cadastrado para o médico e para este hospital.");

          RateioMedicoService.Edit(model);

          return RedirectToAction("Index", "RateioMedico", new { cod = model.CodigoMedico });
        }
        return View(model);
      }
      catch (CustomException ex)
      {
        MessageListToast.Add(new Message(MessageType.Warning, ex.Message));
        return RedirectToAction("Index", "Medico");
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message));
        return RedirectToAction("Index", "Medico");
      }
    }

  }
}
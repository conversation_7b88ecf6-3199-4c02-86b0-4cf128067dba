﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Controls;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using RepasseConvenio.Services;
using RepasseConvenio.WSPortalCooperado;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Transactions;
using System.Web;
using System.Web.Mvc;

namespace RepasseConvenio.Controllers
{
  [Authorize]
  [Security("82BD5D60-0EC0-42BC-AC96-51DE91625569")]
  public class RepasseController : LibController
  {
    private RepasseService RepasseService
    {
      get
      {
        if (_RepasseService == null)
          _RepasseService = new RepasseService(ContextoUsuario.UserLogged);

        return _RepasseService;
      }
    }
    private RepasseService _RepasseService;

    private NotaFiscalRepasseService NotaFiscalRepasseService
    {
      get
      {
        if (_NotaFiscalRepasseService == null)
          _NotaFiscalRepasseService = new NotaFiscalRepasseService(ContextoUsuario.UserLogged);

        return _NotaFiscalRepasseService;
      }
    }
    private NotaFiscalRepasseService _NotaFiscalRepasseService;

    [Security("465DF048-1F72-424C-8B61-E5043D64E462")]
    public ActionResult Index(int? page)
    {
      try
      {
        int pageNumber = page ?? 1;
        var list = RepasseService.GetAllRepasseModelIndex();
        return View(list);
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message, "12000"));
        return RedirectToAction("Index");
      }
    }


    [Security("978D21DD-B8E5-4A0E-8BD0-595B7BFE93AB")]
    [HttpGet]
    public ActionResult Create()
    {
      try
      {
        R_RepasseModel model = new R_RepasseModel();
        model.DataInicio = DateTime.Now;
        return View(model);
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message, "12000"));
        return RedirectToAction("Index");
      }
    }

    [Security("978D21DD-B8E5-4A0E-8BD0-595B7BFE93AB")]
    [HttpPost]
    public ActionResult Create(R_RepasseModel model)
    {
      try
      {
        int id = RepasseService.Create(model);
        MessageListToast.Add(new Message(MessageType.Success, "Repasse criado com suceso.", "12000"));
        return RedirectToAction("Edit", "Repasse", new { codigo = id });
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message, "12000"));
        return View(model);
      }
    }
    [Security("93CA62D3-25F3-4341-AA94-2AA5A70C3159")]
    [HttpGet]
    public ActionResult Edit(int codigo)
    {
      try
      {
        DemonstrativoConvenioService demonstrativoConvenioService = new DemonstrativoConvenioService();
        DepositoRepasseService depositoRepasse = new DepositoRepasseService();
        R_RepasseModel model = RepasseService.GetById(codigo).ToR_RepasseModel();
        model.ListNotaFiscalRepasse = NotaFiscalRepasseService.GetAllNotaFiscaRepasseModel(codigo);
        model.ListDemonstrativoConvenioGrid = demonstrativoConvenioService.GetListaDemonstrativoConvenio(codigo);
        model.ListDepositoRepasse = depositoRepasse.GetById(model.IdDepositoRepasse);

        return View(model);
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message, "12000"));
        return RedirectToAction("Index");
      }
    }

    [Security("93CA62D3-25F3-4341-AA94-2AA5A70C3159")]
    [HttpPost]
    public ActionResult Edit(R_RepasseModel model)
    {
      try
      {
        if (model.DataInicio == DateTime.MinValue)
        {
          model.DataInicio = DateTime.Now;
        }

        RepasseService.Edit(model);
        MessageListToast.Add(new Message(MessageType.Success, "Repasse atualizado com suceso.", "12000"));
        return RedirectToAction("Index");
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message, "12000"));
        return View(model);
      }
    }

    [Security("0DD902A7-E5D8-4D61-BFC2-BA6F455BA234")]
    [HttpGet]
    public ActionResult Details(int codigo)
    {
      try
      {
        DemonstrativoConvenioService demonstrativoConvenioService = new DemonstrativoConvenioService();
        R_RepasseModel model = RepasseService.GetById(codigo).ToR_RepasseModel();
        model.ListNotaFiscalRepasse = NotaFiscalRepasseService.GetAllNotaFiscaRepasseModel(codigo);
        model.ListDemonstrativoConvenioGrid = demonstrativoConvenioService.GetListaDemonstrativoConvenio(codigo);

        return View(model);
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Success, "Repasse editado com suceso.", "12000"));
        return RedirectToAction("Index");
      }
    }

    [HttpGet]
    public PartialViewResult GridNotaFiscalRepasse(int id)
    {
      try
      {
        NotaFiscalRepasseService notaFiscalRepasseService = new NotaFiscalRepasseService();
        var list = notaFiscalRepasseService.GetAllNotaFiscaRepasseModel(id);
        return PartialView("_GridNotaFiscalRepasse", list);
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }

    //[HttpGet]
    //public PartialViewResult GridPlanRecebimento(int id)
    //{
    //  try
    //  {
    //    PlanilhaRecebimentoService planilhaRecebimento = new PlanilhaRecebimentoService();
    //    var list = planilhaRecebimento.GetAllPlanilhaRecebimentoModel(id);
    //    return PartialView("_GridPlanRecebimento", list);
    //  }
    //  catch (Exception ex)
    //  {
    //    throw new Exception(ex.Message);
    //  }
    //}
    [Security("6B748554-C200-48A2-84EF-6EDA71040366")]
    [HttpGet]
    public PartialViewResult GetPartialNotaFiscal(int idRepasse, int? id)
    {
      try
      {
        if (id.HasValue && id != 0)
        {
          GuiaNotaFiscalService guiaNotaFiscalService = new GuiaNotaFiscalService();
          NotaFiscalRepasseModel nota = NotaFiscalRepasseService.GetById(id.Value).ToNotaFiscaRepasseModel();
          nota.ListGuiaNotaFiscalEdit = guiaNotaFiscalService.GetGuiaNotaFiscalEdit(id.Value);
          return PartialView("_PartialNotaFiscal", nota);
        }
        else
        {
          NotaFiscalRepasseModel nota = new NotaFiscalRepasseModel();
          nota.NFM_CodigoRepasse = idRepasse;
          nota.NFM_DataEmissao = DateTime.Now;
          return PartialView("_PartialNotaFiscal", nota);
        }
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }

    //[HttpGet]
    //public PartialViewResult GetPartialPlanRecebimento(int idRepasse, int? id)
    //{
    //  try
    //  {
    //    if (id.HasValue && id != 0)
    //    {
    //      PlanilhaRecebimentoModel planilha = PlanilhaRecebimentoService.GetById(id.Value).ToPlanilhaRecebimentoModel();
    //      return PartialView("_PartialPlanRecebimento", planilha);
    //    }
    //    else
    //    {
    //      PlanilhaRecebimentoModel planilha = new PlanilhaRecebimentoModel();
    //      planilha.PRM_CodigoRepasse = idRepasse;
    //      return PartialView("_PartialPlanRecebimento", planilha);
    //    }
    //  }
    //  catch (Exception ex)
    //  {
    //    throw new Exception(ex.Message);
    //  }
    //}

    [HttpGet]
    public PartialViewResult PesquisaLotes(BuscaLote model)
    {
      try
      {
        List<LotePortalModel> lista = RepasseService.BuscarLote(model);
        return PartialView("_GridLotes", lista);
      }
      catch (Exception ex)
      {
        throw new Exception(ex.Message);
      }
    }

    [HttpPost]
    public JsonResult InserirLotes(List<LotePortalModel> model)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        model = model.Where(a => a.BLM_Inserir).ToList();
        RepasseService.InserirLotes(model);
        retornoAjax.Titulo = "Sucesso";
        retornoAjax.Mensagem = "Planilha atualizada com sucesso.";
        retornoAjax.TipoMensagem = "success";
        retornoAjax.Erro = false;
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
      catch (CustomException ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
      catch (Exception ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
    }
    [Security("DA4F4162-8824-4FC7-813C-0F39B9E76053")]
    [HttpPost]
    public JsonResult ProcessarRepasse(int id)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        EnumStatusRepasse enumStatusRepasse = RepasseService.ProcessarRepasse(id);
        retornoAjax.Titulo = "Sucesso";
        retornoAjax.Mensagem = "Repasse processado com sucesso.";
        retornoAjax.TipoMensagem = "success";
        retornoAjax.Erro = false;
        return Json(new { RetornoAjax = retornoAjax, StatusRepasse = (int)enumStatusRepasse }, JsonRequestBehavior.AllowGet);
      }
      catch (CustomException ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(new { RetornoAjax = retornoAjax }, JsonRequestBehavior.AllowGet);
      }
      catch (Exception ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(new { RetornoAjax = retornoAjax }, JsonRequestBehavior.AllowGet);
      }
    }

    [HttpPost]
    public JsonResult GlosaNaoPago(DateTime DataInicial, List<int> ListIdRegistro, int IdRepasse, int IdMotivo, string DescMotivo)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        if (ListIdRegistro.Count == 0)
        {
          retornoAjax.Titulo = "Aviso";
          retornoAjax.Mensagem = "Nenhum item selecionado.";
          retornoAjax.TipoMensagem = "warning";
          retornoAjax.Erro = false;
          return Json(retornoAjax, JsonRequestBehavior.AllowGet);
        }

        new LoteGlosaServices(ContextoUsuario.UserLogged).GeraGlosaNaoPago(DataInicial, ListIdRegistro, IdRepasse, IdMotivo, DescMotivo);
        retornoAjax.Titulo = "Sucesso";
        retornoAjax.Mensagem = "Lote de glosa gerado com sucesso.";
        retornoAjax.TipoMensagem = "success";
        retornoAjax.Erro = false;
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
      catch (CustomException ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
      catch (Exception ex)
      {
        retornoAjax.Titulo = "Erro";
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Erro = true;
        return Json(retornoAjax, JsonRequestBehavior.AllowGet);
      }
    }

    [HttpGet]
    public ActionResult CancelarProcessamento(int id)
    {
      try
      {
        if (id == 0)
        {
          MessageListToast.Add(new Message(MessageType.Error, "Repasse não encontrado.", "12000"));
          return RedirectToAction("Index");
        }

        RepasseService.CancelarProcessamento(id);

        MessageListToast.Add(new Message(MessageType.Success, "Processamento de repasse cancelado com sucesso.", "12000"));
        return RedirectToAction("Index");
      }
      catch (CustomException ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, ex.Message, "12000"));
        return RedirectToAction("Index");
      }
      catch (Exception ex)
      {
        MessageListToast.Add(new Message(MessageType.Error, "Houve um erro no momento de processar sua solicitação.", "12000"));
        return RedirectToAction("Index");
      }
    }

    [HttpPost]
    public JsonResult RemoverRepasse(int codigoRepasse)
    {
      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        if (codigoRepasse == 0)
        {
          retornoAjax.Erro = true;
          retornoAjax.messageType = MessageType.Error;
          retornoAjax.Mensagem = "Repasse não encontrado.";
          retornoAjax.Titulo = "Erro.";
          return Json(retornoAjax);
        }

        RepasseService.RemoverRepasse(codigoRepasse);

        retornoAjax.Erro = false;
        retornoAjax.messageType = MessageType.Success;
        retornoAjax.Mensagem = "Repasse Removido.";
        retornoAjax.Titulo = "Sucesso.";
        return Json(retornoAjax);
      }
      catch (CustomException ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.messageType = MessageType.Error;
        retornoAjax.Mensagem = ex.Message;
        retornoAjax.Titulo = "Erro.";
        return Json(retornoAjax);
      }
      catch (Exception ex)
      {
        retornoAjax.Erro = true;
        retornoAjax.messageType = MessageType.Error;
        retornoAjax.Mensagem = "Houve um erro no momento de processar sua solicitação.";
        retornoAjax.Titulo = "Erro.";
        return Json(retornoAjax);
      }
    }
    [Security("643B1980-7FD2-494B-A836-2FCD6E428A35")]
    public PartialViewResult GetGridGuiaDemonstrativo(int CodigoDemonstrativo)
    {
      GuiaDemonstrativoService guiaDemonstrativoService = new GuiaDemonstrativoService();
      List<GuiaDemonstrativoIndex> list = guiaDemonstrativoService.GetListaGuiaDemonstrativoIndex(CodigoDemonstrativo);
      return PartialView("_GridGuiasDemonstrativo", list);
    }
    
    [Security("67D95B0C-F715-466A-B4EF-700BE61721A2")]
    public PartialViewResult GetGridGuiaAtenimento(int CodigoGuiaAtendimento)
    {
      GuiaAtendimentoService guiaAtendimentoService = new GuiaAtendimentoService();
      GuiaAtendimentoGrid guiaAtendimentoGrid = guiaAtendimentoService.GetGuiaAtendimentoGrid(CodigoGuiaAtendimento);
      return PartialView("_GridGuiaAtendimento", guiaAtendimentoGrid);
    }
    
    [Security("FA1DDFBE-D5D2-4885-B8B2-0601C925D57F")]
    public PartialViewResult GetGridExtrato(ExtratoRepasseModal extratoRepasseModal)
    {
      ExtratoMedicoServices extratoMedicoServices = new ExtratoMedicoServices();
      List<ExtratoMedicoGrid> list = extratoMedicoServices.GetExtratoMedicoGrid(extratoRepasseModal);
      return PartialView("_GridExtratos", list);
    }
    
    [Security("0A2CB6CE-09A9-484A-B451-4B02D4A071A5")]
    public PartialViewResult GetLotesRepasse(int IdRepasse)
    {
      LoteServices loteServices = new LoteServices();
      List<LoteRepassaeModal> list = loteServices.GetAllLoteByRepasse(IdRepasse);
      return PartialView("_GridLotesRepasse", list);
    }

    [Security("8A99CFFD-1201-48EA-9E93-E2323496E8F8")]
    public PartialViewResult GetGridItensLote(int CodigoLote)
    {
      ItensLoteServices itensLoteServices = new ItensLoteServices();
      List<ItensLoteModal> list = itensLoteServices.GetAllByIdLote(CodigoLote);
      return PartialView("_GridItensLote", list);
    }

    [Security("8E2D4D21-C524-4FBE-B514-05178FA40950")]
    public PartialViewResult GetGridAtdNaoPagos(int IdRepasse)
    {
      List<AtendimentoNaoPago> list = new ItensLoteServices().GetAtdNaoPagoByRepasse(IdRepasse);
      return PartialView("_GridAtdNaoPago", list);
    }
    [Security("3333F653-EB59-4E22-A3BA-D1B90A8C22C3")]
    public PartialViewResult GetGridProcedimentoDemonstrativo(int CodigoDemonstrativo)
    {
      ProcGuiaDemonstrativoService procGuiaDemonstrativoService = new ProcGuiaDemonstrativoService();
      List<ProcGuiaDemonstrativoModal> list = procGuiaDemonstrativoService.GetListaProcGuiaDemonstrativoModal(CodigoDemonstrativo);
      return PartialView("_GridProcedimentosDemonstrativo", list);
    }
    [Security("7130795C-E574-47A1-B1E0-9FB3147803A0")]
    public PartialViewResult GetPartialGuiaNotaFiscal(string NumeroGuia, string NumeroLote, int IdNotaFiscal)
    {
      GuiaNotaFiscalService guiaNotaFiscalService = new GuiaNotaFiscalService();
      List<GuiaNotaFiscalIndex> list = guiaNotaFiscalService.GetGuiaNotaFiscalIndex(NumeroLote, NumeroGuia, IdNotaFiscal);
      return PartialView("_PartialGuiaNotaFiscal", list);
    }

    [Security("7130795C-E574-47A1-B1E0-9FB3147803A0")]
    public PartialViewResult GetPartialGuiaNotaFiscalEdit(int IdNotaFiscal)
    {
      GuiaNotaFiscalService guiaNotaFiscalService = new GuiaNotaFiscalService();
      List<GuiaNotaFiscalEdit> list = guiaNotaFiscalService.GetGuiaNotaFiscalEdit(IdNotaFiscal);
      return PartialView("_PartialGuiaNotaFiscalEdit", list);
    }

    //public JsonResult CreateDepositoRepasse(DateTime DataDeposito, decimal ValorDeposito, string NumeroDoc, decimal ValorUtilizado)
    //public ActionResult CreateDepositoRepasse(DateTime DataDeposito, decimal ValorDeposito, string NumeroDoc, decimal ValorUtilizado)
    [Security("192FF602-4730-4233-AB2A-BC5601F1ABB3")]
    [HttpPost]
    public JsonResult CreateDepositoRepasse(int IdRepasse, int IdBanco, DateTime DataDeposito, decimal ValorDeposito, string NumeroDoc, decimal ValorUtilizado = 0)
    {

      RetornoAjax retornoAjax = new RetornoAjax();
      try
      {
        using (TransactionScope scope = new TransactionScope(TransactionScopeOption.RequiresNew))
        {
          R_DepositoRepasse model = new R_DepositoRepasse();
          model.DR_IdUsuarioCriacao = ContextoUsuario.UserLogged.IdUsuario;
          model.DR_DataDeposito = DataDeposito;
          model.DR_IdBancosUnicooper = IdBanco;
          model.DR_ValorDeposito = ValorDeposito;
          model.DR_NumeroDocumento = NumeroDoc;
          model.DR_ValorUtilizado = ValorUtilizado;
          model.DR_DataCriacao = DateTime.Now;
          DepositoRepasseService depositoRepasse = new DepositoRepasseService();

          model.DR_Id = depositoRepasse.Create(model);

          R_Repasse repasse = new R_Repasse();
          repasse = RepasseService.GetById(IdRepasse);
          repasse.R_Deposito = true;
          repasse.R_IdDepositoRepasse = model.DR_Id;
          RepasseService.Edit(repasse);
          scope.Complete();
          return Json(new { status = "success", message = "Depósito Criado com sucesso!", IdDeposito = model.DR_Id }, JsonRequestBehavior.AllowGet);
        }

      }

      catch (Exception ex)
      {

        return Json(new { status = "error", message = ex.Message }, JsonRequestBehavior.AllowGet);
      }
      // return Json(new { status = "success", message = "Valor Incluído com sucesso!", Id = model.DR_Id }, JsonRequestBehavior.AllowGet);

    }
    [Security("7F296BD3-BBA6-4A6A-81E2-B3A1990DD52D")]
    [HttpGet]
    public PartialViewResult GetPartialDepositoRepasse(int id)
    {
      DepositoRepasseService repasseService = new DepositoRepasseService();

      var list = repasseService.GetById(id);
      return PartialView("_GridDepositoRepasse", list);
    }
    [Security("55F7463A-D783-45D1-92E4-6565649E5F04")]
    [HttpGet]
    public ActionResult GetBancoUnicooperSelect(string term)
    {
      BancoUnicooperService BancoServices = new BancoUnicooperService();
      List<Select2Model> Lista = new List<Select2Model>();
      Lista = BancoServices.GetByTerm(term).Select(list => list.ToSelect2Model()).ToList();
      return Json(new { items = Lista }, JsonRequestBehavior.AllowGet);
    }
  }
}
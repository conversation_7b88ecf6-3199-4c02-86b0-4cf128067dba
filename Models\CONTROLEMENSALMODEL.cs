﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace RepasseConvenio.Models
{
  public class ControleMensalModel
  {
    public int Codigo { get; set; }

    public DateTime DataInicio { get; set; }

    public DateTime DataFim { get; set; }

    public int IdStatus { get; set; }

    public StatusControlheMensalEnum statusControlheMensalEnum { get; set; }
  }

  public class ControleMensalCreate
  {
    public DateTime DataInicio { get; set; }

    public DateTime DataFim { get; set; }

    public int IdStatus { get; set; }
  }

  public static class ControleMensalConversions
  {
    public static R_ControleMensal Create(this R_ControleMensal controleMensal, DateTime DataInicio, DateTime Datafim, int IdStatusControleMensagel)
    {
      return new R_ControleMensal()
      {
        CM_DataInicio = DataInicio,
        CM_DataFim = Datafim,
        CM_IdStatusControlheMensal = IdStatusControleMensagel
      };
    }
  }
}
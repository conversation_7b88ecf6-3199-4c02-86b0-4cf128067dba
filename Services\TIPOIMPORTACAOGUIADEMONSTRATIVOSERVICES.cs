﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using PagedList;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RepasseConvenio.Services
{
  public class TipoImportacaoGuiaDemonstrativoServices : ServiceBase
  {
    public TipoImportacaoGuiaDemonstrativoServices(RepasseEntities Contexto)
        : base(Contexto)
    { }
    public TipoImportacaoGuiaDemonstrativoServices()
       : base()
    { }

    public TipoImportacaoGuiaDemonstrativoServices(UsuarioLogado UsuarioLogado) : base(UsuarioLogado)
    {
    }

    public TipoImportacaoGuiaDemonstrativoServices(UsuarioLogado UsuarioLogado, RepasseEntities Contexto)
           : base(UsuarioLogado, Contexto)
    { }

    public int GetIdByEnum(TipoImportacaoGuiaDemonstrativoEnum tipoImportacaoGuiaDemonstrativoEnum)
    {
      return Contexto.R_TipoImportacaoGuiaDemonstrativo.Where(a => a.TIGD_Enum == (int)tipoImportacaoGuiaDemonstrativoEnum).Select(a => a.TIGD_Id).FirstOrDefault();
    }
    public R_TipoImportacaoGuiaDemonstrativo GetByEnum(TipoImportacaoGuiaDemonstrativoEnum tipoImportacaoGuiaDemonstrativoEnum)
    {
      return Contexto.R_TipoImportacaoGuiaDemonstrativo.Where(a => a.TIGD_Enum == (int)tipoImportacaoGuiaDemonstrativoEnum).FirstOrDefault();
    }

    public R_TipoImportacaoGuiaDemonstrativo GetById(int Id)
    {
      return Contexto.R_TipoImportacaoGuiaDemonstrativo.Where(a => a.TIGD_Id == Id).FirstOrDefault();
    }

    public List<R_TipoImportacaoGuiaDemonstrativo> GetAll()
    {
      string query = @"SELECT * FROM R_TipoImportacaoGuiaDemonstrativo";
      return Contexto.Database.SqlQuery<R_TipoImportacaoGuiaDemonstrativo>(query).ToList();
    }
  }
}


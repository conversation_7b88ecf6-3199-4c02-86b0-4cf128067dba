﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace RepasseConvenio.Models
{
  public class ProcGuiaDemonstrativoModal
  {
    public int CodigoGuiaDemonstrativo { get; set; }

    public int? CodigoGuia { get; set; }
    public int IdRepasse { get; set; }

    public string NumeroGuia { get; set; }
    public string NumeroCarteirinha { get; set; }

    public string NumeroAtendimento { get; set; }

    public DateTime DataAtendimento { get; set; }

    public decimal ValorFaturado { get; set; }

    public decimal ValorApresentado { get; set; }

    public decimal ValorGlosado { get; set; }

    public decimal ValorPago { get; set; }

    public string Procedimento { get; set; }
  }

  public class ProcGuiaDemonstrativoIntegra
  {
    public Decimal ValorFaturado { get; set; }

    public decimal TaxaAdministrativa { get; set; }

    public int IdProcGuiaAtendimento { get; set; }
  }
  public static class ProcGuiaDemonstrativoConversions
  {
    public static R_ProcGuiaDemonstrativo ModeltoEntityCreate(this GuiaDemonstrativoIntegra guiaDemonstrativoIntegra
                                                             , int IdGuiaDemonstrativo
                                                             , ProcGuiaDemonstrativoIntegra procGuiaDemonstrativoIntegra)
    {
      return new R_ProcGuiaDemonstrativo()
      {
        PGD_CodigoProcedimento = guiaDemonstrativoIntegra.CodigoProcedimento,
        PGD_DescricaoProcedimento = guiaDemonstrativoIntegra.DescricaoProcedimento,
        PGD_TotalApresentado = guiaDemonstrativoIntegra.ValorApresentado.Value,
        PGD_TotalFaturado = procGuiaDemonstrativoIntegra.ValorFaturado,
        PGD_TaxaAdministrativa = procGuiaDemonstrativoIntegra.TaxaAdministrativa,
        PGD_TotalGlosado = guiaDemonstrativoIntegra.ValorGlosado,
        PGD_TotalPago = guiaDemonstrativoIntegra.ValorPago,
        PGD_DescricaoGlosa = guiaDemonstrativoIntegra.DescricaoGlosa,
        PGD_CodigoGlosa = guiaDemonstrativoIntegra.CodigoGlosa,
        PGD_ObservacoesGerais = guiaDemonstrativoIntegra.ObservacoesGerais,
        PGD_IdGuiaDemonstrativo = IdGuiaDemonstrativo,
        PGD_IdProcGuiaAtendimento = procGuiaDemonstrativoIntegra.IdProcGuiaAtendimento
      };
    }
  }
}
﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using iTextSharp.text;
using iTextSharp.text.pdf;
using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Helpers;
using RepasseConvenio.Models;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using PortalCooperado.Domain.Infrastructure;

namespace RepasseConvenio.Infrastructure.Helpers
{
  public static class AnexoHelperRepasse
  {

    public static Byte[] ConverteHttpPostedFileBaseToByteArray(this HttpPostedFileBase imagem)
    {
      using (Stream inputStream = imagem.InputStream)
      {
        MemoryStream memoryStream = inputStream as MemoryStream;
     
        return memoryStream.ToArray();
      }
    }
    public static List<AnexoRepasse> FilesUpload(HttpRequestBase request)
    {
      if (request.Files != null && request.Files.Count > 0)
      {
        List<AnexoRepasse> anexos = new List<AnexoRepasse>();

        return anexos;
      }

      return null;
    }

    public static List<AnexoRepasse> FilesUploadSemConversao(HttpRequestBase request, string[] extensaoValida)
    {
      if (request.Files != null && request.Files.Count > 0)
      {
        List<AnexoRepasse> anexos = new List<AnexoRepasse>();
=
        return anexos;
      }

      return null;
    }

    public static void ConvertImagePDF(string outputFileName, string inputFileName)
    {
      
    }

    public static void ConvertPDFImage(string outputFileName, string inputFileName)
    {

    }

    public static void ScaleToA4(string inPDF, string outPDF)
    {
      var reader = new PdfReader(new MemoryStream(File.ReadAllBytes(inPDF)));
      var document = new Document(PageSize.A4);
      var ms = new MemoryStream();
      var writer = PdfWriter.GetInstance(document, ms);
      document.Open();
      var cb = writer.DirectContent;

      document.Close();
      File.WriteAllBytes(outPDF, ms.GetBuffer());
    }

    public static MemoryStream GerarPdf(string outputFilenamePrefix, string inputFileName, string[] options)
    {
      string inputFolder = ConfigurationManager.AppSettings["PathSaveTemp"];
      string outputFolder = ConfigurationManager.AppSettings["PathSavePDF"];

      try
      {
        string outputFilename = string.Concat(outputFolder, outputFilenamePrefix + ".pdf");

        return FileHelper.RetornaMemorySteam(outputFilename);
      }
      catch (Exception exc)
      {
        throw new Exception("Problem generating PDF from HTML, URLs: " + inputFolder + ", outputFilename: " + outputFilenamePrefix, exc);
      }
    }
  }
}
﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using RepasseConvenio.WSPortalCooperado;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Web;

namespace RepasseConvenio.Models
{
  public class ProcedimentoModel
  {
    public int Codigo { get; set; }
    public string Descricao { get; set; }
    public string CodProcedimento { get; set; }
    public int CodigoConvenio { get; set; }
  }

  public class ProcedimentoIndex
  {
    public int Codigo { get; set; }
    public string Descricao { get; set; }
    public string CodProcedimento { get; set; }
    public int CodigoConvenio { get; set; }
    public string RazaoSocial { get; set; }
    public string CNPJ { get; set; }
    public string CodANS { get; set; }
  }

  public static class ProcedimentoModelConversions
  {
    public static R_Procedimentos ToProcedimentoCreate(this ProcedimentoModel model)
    {
      R_Procedimentos entity = new R_Procedimentos();
      entity.P_Codigo = model.CodProcedimento;
      entity.P_Descricao = model.Descricao;
      entity.P_IdConvenio = model.CodigoConvenio;
      return entity;
    }

    public static R_Procedimentos ToProcedimentoEdit(this ProcedimentoModel model)
    {
      R_Procedimentos entity = new R_Procedimentos();
      entity.P_Id = model.Codigo;
      entity.P_Codigo = model.CodProcedimento;
      entity.P_Descricao = model.Descricao;
      entity.P_IdConvenio = model.CodigoConvenio;
      return entity;
    }

    public static R_Procedimentos ProcedimentosConvenioRepasseToEntityCreate(this R_Procedimentos procedimentos, ProcedimentosConvenioRepasse procedimentosConvenioRepasse, int idConvenio)
    {
      return new R_Procedimentos()
      {
        P_Codigo = procedimentosConvenioRepasse.Codigo,
        P_Descricao = procedimentosConvenioRepasse.Descricao,
        P_IdConvenio = idConvenio
      };
    }
  }
}
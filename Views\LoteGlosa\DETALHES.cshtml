﻿@using RepasseConvenio.Models
@model List<ItensLoteGlosaIndex>

@{
  ViewBag.Title = "Itens Lote Glosa";
}

@Scripts.Render("~/Views/LoteGlosa/LoteGlosa.js?aa=a")
@Styles.Render("~/Views/LoteGlosa/LoteGlosa.css?a=a")

<div class="row">
  <section class="col-lg-12 connectedSortable ui-sortable">
    <div class="card">
      <div class="card-header ui-sortable-handle">
        <h3 class="card-title">
          <i class="fas fa-chart-pie mr-1"></i>
          Itens Lotes Glosas
        </h3>
        <input type="hidden" value="@(ViewBag.IdLote)" id="CodigoLote" />
        <div class="card-tools">
          @if ((StatusGlosaEnum)ViewBag.StatusLote != StatusGlosaEnum.EmAnaliseConvenio)
          {
            @*<button class="btn btn-outline-success disabled" disabled id="AceitarGlosa">Aceitar Glosa</button>*@
            <button class="btn btn-outline-primary" id="EnviarConvenio">Enviar Convênio</button>
            <button class="btn btn-outline-primary disabled" disabled id="AceitarJustificarGlosa">Analise Detalhada da Guia</button>
          }
        </div>
      </div>
      <div class="card-body">
        <div class="tab-content p-0">
          <div class="box-body">
            <div class="row">
              <div class="col-md-12 table-responsive p-0" id="GridItens">
                @Html.Partial("_GridItensLote", Model)
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer" style="display: flex; justify-content: flex-start;">
        <button type="button" class="btn btn-primary" onclick="location.href='@Url.Action("Index", "LoteGlosa")'">Voltar</button>
      </div>
    </div>
  </section>
</div>

@Html.Partial("_ModalAnaliseDetalhadaGlosa", new AnaliseDetalhada())
@Html.Partial("_ModalAnaliseLote", new JustificativaItemGlosaModel())
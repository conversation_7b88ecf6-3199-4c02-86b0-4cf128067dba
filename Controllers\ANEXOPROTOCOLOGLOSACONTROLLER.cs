﻿/* Desenvolvido por MW Software LTDA - CNPJ 07.167.378.0001-23
 * Não sendo permitida a edição, alteração e reprodução desde código fonte em outros sistemas por outras entidades, 
 * a não ser com a autorização da MW Software ou seus representantes legais.
 * MW Software LTDA - CNPJ 07.167.378.0001-23
 */

using PagedList;
using RepasseConvenio.Infrastructure;
using RepasseConvenio.Infrastructure.Controls;
using RepasseConvenio.Models;
using RepasseConvenio.Services;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace RepasseConvenio.Controllers
{
  [Authorize]
  public class AnexoProtocoloGlosaController : LibController
  {
    public FileResult Download(int Id)
    {
      try
      {
        AnexoProtocoloGlosaService anexoProtocoloGlosaService = new AnexoProtocoloGlosaService();
        AnexoProtocoloDownload anexoProtocoloDownload = anexoProtocoloGlosaService.GetAnexoProtocoloGlosa(Id);
        string PathProtocoloGlosas = ConfigurationManager.AppSettings["PathProtocoloGlosas"];
        PathProtocoloGlosas = Path.Combine(PathProtocoloGlosas, anexoProtocoloDownload.IdLoteGlosa.ToString(), anexoProtocoloDownload.APG_IdProtocoloGlosa.ToString(), anexoProtocoloDownload.APG_NomeArquivoWithExtension);
        byte[] reportData = System.IO.File.ReadAllBytes(PathProtocoloGlosas);
        string NomeArquivo = anexoProtocoloDownload.APG_NomeArquivoWithExtension.Replace(string.Format("Protocolo_{0}_", anexoProtocoloDownload.APG_IdProtocoloGlosa), "");
        return File(reportData, anexoProtocoloDownload.APG_MimeType, NomeArquivo);
      }
      catch (Exception ex)
      {
        byte[] vazio = new byte[0];
        return new FileContentResult(vazio, "application/pdf");
      }
    }

  }
}
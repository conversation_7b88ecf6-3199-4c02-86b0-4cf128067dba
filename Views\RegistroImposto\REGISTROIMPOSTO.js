﻿var RegistroImposto = function () { };

RegistroImposto.init = function () {
  $(document).on('click', '#DetalhesRegistro', function () {
    RegistroImposto.GetExtratos($(this).data("idregistro"));
  });

  $(document).on('click', '#PagarSelecionado', function () {
    var listRegistroImposto = [];

    $('.isSelected').each(function (index, element) {
      if ($(element).is(':checked'))
        listRegistroImposto.push($(element).data('codigoregistroimposto'));
    });
    RegistroImposto.RealizarPagamento(listRegistroImposto);
  });

  $(document).on('click', '#RemoverSelecionado', function () {
    var listRegistroImposto = [];

    $('.isSelected').each(function (index, element) {
      if ($(element).is(':checked'))
        listRegistroImposto.push($(element).data('codigoregistroimposto'));
    });
    RegistroImposto.RemoverRegistros(listRegistroImposto);
  });

  $(document).on("click", "#CheckAllRegPagamento", function () {
    if ($(this).prop('checked')) {
      $('#PagarSelecionado').removeAttr("readonly");
      $('#PagarSelecionado').removeAttr("disabled");
      $('#RemoverSelecionado').removeAttr("readonly");
      $('#RemoverSelecionado').removeAttr("disabled");
    }
    else {
      $('#PagarSelecionado').attr("readonly", "readonly");
      $('#PagarSelecionado').attr("disabled", true);
      $('#RemoverSelecionado').attr("readonly", "readonly");
      $('#RemoverSelecionado').attr("disabled", true);
    }

    $(".isSelected").prop('checked', $(this).prop('checked'));
  });

  $(document).on("click", ".isSelected", function () {
    if ($(this).prop('checked')) {
      $('#PagarSelecionado').removeAttr("readonly");
      $('#PagarSelecionado').removeAttr("disabled");
      $('#RemoverSelecionado').removeAttr("readonly");
      $('#RemoverSelecionado').removeAttr("disabled");
    }
    else {
      $('#PagarSelecionado').attr("readonly", "readonly");
      $('#PagarSelecionado').attr("disabled", true);
      $('#RemoverSelecionado').attr("readonly", "readonly");
      $('#RemoverSelecionado').attr("disabled", true);
    }

    var QuantidadeSelecionada = $('.isSelected[type=checkbox]:checked').length;
    var quantidadeCheck = $('.isSelected').length;

    if (QuantidadeSelecionada == quantidadeCheck)
      $('#CheckAllRegPagamento').prop('checked', true);
    else
      $('#CheckAllRegPagamento').prop('checked', false);
  });
}

RegistroImposto.RemoverRegistros = async function (ListIdRegistro) {
  await Swal.fire({
    title: "Excluir Registros de Imposto",
    html: "Deseja continuar a exclusão dos " + ListIdRegistro.length + " registros selecionados? Ao excluir também removerá os pagamentos dos extratos médicos.",
    icon: "warning",
    confirmButtonText: "Excluir",
    showCancelButton: "Cancelar",
    showLoaderOnConfirm: true,
    dangerMode: true
  })
    .then((willDelete) => {
      if (willDelete.isConfirmed) {
        $.ajax({
          type: 'POST',
          url: GetURLBaseComplete() + '/RegistroImposto/Delete',
          data: { idsRegistro: ListIdRegistro },
          dataType: 'json',
          success: function (data) {
            if (!data.Erro) {
              Alerta(data.Titulo, data.Mensagem, "success", "Fechar");
              location.href = GetURLBaseComplete() + '/RegistroImposto/Index';
            }
            else
              Alerta(data.Titulo, data.Mensagem, "error", "Fechar");
          },
          error: function (err) {
            AlertaSwal("Erro", "Gentileza tentar mais tarde.", "error", "Fechar");
          }
        });
      }
    });
}

RegistroImposto.FiltroGrid = function () {
  const medico = $("#MedicoFilter").val();
  const data = $("#DtPagFilter").val();
  /*if (medico || data) {*/
    $.ajax({
      type: 'GET',
      url: GetURLBaseComplete() + '/RegistroImposto/GetPartialGrid',
      dataType: 'html',
      data: {
        Page: 1,
        search: medico,
        data: data
      },
      success: function (data) {
        $("#GridPartial").html(data);
      },
    });
  //} else {
  //  Alerta("Atenção", "É necessário preencher os campos Médico e/ou Dt Pagamento", "error", "Ok");
  //}
}

RegistroImposto.GetPartial = function () {
  $.ajax({
    type: 'GET',
    url: GetURLBaseComplete() + '/RegistroImposto/GetPartialGrid',
    dataType: 'html',
    success: function (data) {
      $("#GridPartial").html(data);
    },
  });
}

RegistroImposto.GetExtratos = function (idRegistro) {
  $.ajax({
    type: 'GET',
    url: GetURLBaseComplete() + '/RegistroImposto/GetExtratos/' + idRegistro,
    dataType: 'html',
    success: function (data) {
      $("#ModalExtratoRegistroImposto #GridDetalhesExtratos").html(data);
      $("#ModalExtratoRegistroImposto").modal("show");
    },
  });
}

RegistroImposto.RealizarPagamento = async function (ListIdRegistro) {
  var data = new Date();
  var DataAtual = ('0' + data.getDate()).slice(-2) + '/' + ('0' + (data.getMonth() + 1)).slice(-2) + '/' + data.getFullYear();

  await Swal.fire({
    customClass: {
      input: 'Data DataVencimento',
    },
    title: 'Digite a data de pagamento',
    input: 'text',
    inputValue: DataAtual,
    showCancelButton: true,
    showLoaderOnConfirm: true,
    onOpen: function (el) {
      RepasseConvenio.IniciaMascaras();
    },
    inputValidator: (value) => {
      if (!value) {
        return 'Você precisa digitar uma data!'
      }
      else if ($('.DataVencimento').inputmask('unmaskedvalue').length != 8)
        return 'Data inválida!'
      RepasseConvenio.IniciaMascaras();
    },
    preConfirm: async (value) => {
      var res = await RegistroImposto.RealizarPagamentoAjax(ListIdRegistro, value);
    },
  });
}

RegistroImposto.RealizarPagamentoAjax = function (listIdRegistro, DataPagamento) {
  var data = {
    DataPagamento: DataPagamento,
    ListIdRegistro: listIdRegistro
  };

  return $.ajax({
    type: 'POST',
    url: GetURLBaseComplete() + '/RegistroImposto/RealizarPagamento',
    data: data,
    dataType: 'json',
    success: function (data) {
      if (!data.Erro) {
        Alerta(data.Titulo, data.Mensagem, "success", "Fechar");
        RegistroImposto.GetPartial();
      }
      else
        Alerta(data.Titulo, data.Mensagem, "error", "Fechar");
    },
    error: function (err) {
      AlertaSwal("Erro", "Gentileza tentar mais tarde.", "error", "Fechar");
    }
  });
}

$(document).ready(function () {
  RegistroImposto.init();
});
﻿var Convenio;

Convenio = function () {
};

Convenio.init = function () {
  $(document).on("click", "#IntegrarConvenios", function () {
    var connectionid = $.connection.hub.id;
    $('#bodyModalRetornoUsuario').html('');
    var model = {
      ConnectionId: connectionid
    };

    $.ajax({
      beforeSend: function (xhr) {
      },
      complete: function (xhr, status) {
      },
      type: 'POST',
      url: GetURLBaseComplete() + '/Convenio/IntegraConvenio',
      data: model,
      success: function (data) {
        if (!data.Erro) {
        }
        else {
        }
      },
      error: function (err) {
      }
    });
  });
}

$(document).on("click", ".CrSelecionavel", function () {
  var idConvenio = $(this).data("codigoconvenio");
  var urlEdit = GetURLBaseComplete() + "/Convenio/Edit?codigoConvenio=" + idConvenio;
  var urlDetalhes = GetURLBaseComplete() + "/Convenio/Details?codigoConvenio=" + idConvenio;
  var urlResponsavel = GetURLBaseComplete() + "/ResponsavelConvenio/Index?CodigoConvenio=" + idConvenio;
  

  if (!$(this).hasClass("Selected")) {
    $('.Selected').each(function (index, element) {
      $(element).removeClass("Selected")
      $(element).addClass("UnSelected")
    });

    if ($(this).hasClass("Selected")) {
      $(this).removeClass("Selected")
      $(this).addClass("UnSelected")
    }
    else {
      $(this).addClass("Selected")
      $(this).removeClass("UnSelected")
    }
  }
  else {
    $(this).removeClass("Selected")
    $(this).addClass("UnSelected")
  }

  var countSelected = 0;
  $('.Selected').each(function (index, element) {
    if ($(element).hasClass("Selected")) {
      $('#Editar').attr("href", urlEdit);
      $('#Detalhes').attr("href", urlDetalhes);
      $('#Responsavel').attr("href", urlResponsavel);
      
      countSelected++;
    }
  });

  if (countSelected == 0) {
    $('#Editar').removeAttr("href");
    $('#Detalhes').removeAttr("href");
    $('#Responsavel').removeAttr("href");
  

    $('#Editar').attr("disabled", true);
    $('#Detalhes').attr("disabled", true);
    $('#Responsavel').attr("disabled", true);
    

    $('#Editar').addClass("disabled");
    $('#Detalhes').addClass("disabled");
    $('#Responsavel').addClass("disabled");
   
  }
  else {
    $('#Editar').attr("disabled", false);
    $('#Detalhes').attr("disabled", false);
    $('#Responsavel').attr("disabled", false);
    
    $('#Editar').removeClass("disabled");
    $('#Detalhes').removeClass("disabled");
    $('#Responsavel').removeClass("disabled");
   
  }
});






$(document).ready(function () {
  Convenio.init();
});

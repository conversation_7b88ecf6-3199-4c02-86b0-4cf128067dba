﻿<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <meta charset="utf-8" />
    @using RepasseConvenio.Infrastructure.Controls
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>@ViewBag.Title - Repasse Convênio</title>
  @Styles.Render("~/Content/css")
  @Scripts.Render("~/bundles/modernizr")
  @Scripts.Render("~/bundles/jquery")
  @Scripts.Render("~/bundles/bootstrap")
  @Scripts.Render("~/bundles/inputmask")
  @Scripts.Render("~/bundles/plugins")

  @RenderSection("scripts", required: false)
  @RenderSection("Styles", required: false)
</head>
<body>
  <div class="container body-content">
    @RenderBody()
    <br />
    <footer>
      <p>&copy; @DateTime.Now.Year - Repasse Convênio</p>
    </footer>
  </div>

  @Html.RepasseAlertToast();
</body>
</html>

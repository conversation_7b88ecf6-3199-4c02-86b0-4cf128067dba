﻿
@using RepasseConvenio.Infrastructure.Controls
@using RepasseConvenio.Models
@model DepositoRepasseModel

@{
  ViewBag.Title = "Novo Deposito Repasse";
  //ViewBag.DescricaoTela = "Nota Fiscal Repasse";
  //ViewBag.ResumoTela = "Novo Nota Fiscal Repasse";
}

@using (Html.BeginForm("CreateDeposito", "Repasse", FormMethod.Post, new { id = "postDeposito", @style = "" }))
{
  @Html.AntiForgeryToken()
  @Html.HiddenFor(model => model.Codigo)
  @Html.HiddenFor(model => model.IdBancoUnicooper)
  
  <div class="box box-primary">
    <div class="box-body">
      @Html.ValidationSummary(true, "", new { @class = "text-danger" })
      <div class="row">
       

        <div class="col-md-3">
          <div class="checkbox form-group">
            @Html.LabelFor(model => model.NumeroDocumento)
            @Html.EditorFor(model => model.NumeroDocumento, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.NumeroDocumento, "", new { @class = "text-danger" })
          </div>
        </div>
        <div class="col-md-2">
          <div class="form-group">
            @Html.LabelFor(model => model.DataDeposito)
            @Html.EditorFor(model => model.DataDeposito, new { htmlAttributes = new { @class = "form-control airDatePickerDate Data" } })
            @Html.ValidationMessageFor(model => model.DataDeposito, "", new { @class = "text-danger" })
          </div>
        </div>
        <div class="col-md-2">
          <div class="form-group">
            @Html.LabelFor(model => model.DataCriacao)
            @Html.EditorFor(model => model.DataCriacao, new { htmlAttributes = new { @class = "form-control money" } })
            @Html.ValidationMessageFor(model => model.DataCriacao, "", new { @class = "text-danger" })
          </div>
        </div>
        
      </div>

     

     

    </div>
  </div>
}

﻿@using PagedList.Mvc
@using RepasseConvenio.Models
@using RepasseConvenio.Infrastructure
@using RepasseConvenio.Infrastructure.Controls
@model UsuarioModel

@{
  ViewBag.Title = "Criar Usuário";
  Layout = "~/Views/Shared/_Layout.cshtml";
}

@Styles.Render("~/Views/Usuario/Usuario.css?bbb=ccc")

@using (Html.BeginForm("Create", "Usuario", FormMethod.Post))
{
  <div class="form-background">
    <div class="container">
      <div class="row">
        <div class="col-md-4">
          <div class="form-group">
            @Html.LabelFor(m => m.Nome)
            @Html.LibEditorFor(m => m.Nome, new { @class = "form-control" })
            @Html.ValidationMessageFor(m => m.Nome, "", new { @class = "text-danger" })
          </div>
        </div>
        <div class="col-md-3">
          <div class="form-group">
            @Html.LabelFor(m => m.CPF)
            @Html.LibEditorFor(m => m.CPF, new { @class = "form-control" })
            @Html.ValidationMessageFor(m => m.CPF, "", new { @class = "text-danger" })
          </div>
        </div>
        <div class="col-md-3">
          <div class="form-group">
            @Html.LabelFor(m => m.Email)
            @Html.LibEditorFor(m => m.Email, new { @class = "form-control" })
            @Html.ValidationMessageFor(m => m.Email, "", new { @class = "text-danger" })
          </div>
        </div>
        <div class="col-md-3">
          <div class="form-group">
            @Html.LabelFor(m => m.TipoUsuarioEnum)
            @Html.EnumDropDownListFor(m => m.TipoUsuarioEnum, new { @class = "form-control" })
            @Html.ValidationMessageFor(m => m.TipoUsuarioEnum, "", new { @class = "text-danger" })
          </div>
        </div>
        <div class="col-md-3">
          <div class="form-group">
            @Html.LabelFor(m => m.Senha)
            @Html.LibEditorFor(m => m.Senha, new { @class = "form-control", @Type="password" })
            @Html.ValidationMessageFor(m => m.Senha, "", new { @class = "text-danger" })
          </div>
        </div>
      </div>
      <div>
        <button type="submit" class="btn btn-primary" style="width: 150px;display: table;padding: 7.5px 10px !important;">Salvar</button>
        <a class="btn" href="@Url.Action("Index","Usuario")" style="margin-top:5%">Voltar</a>
      </div>
    </div>
  </div>
}